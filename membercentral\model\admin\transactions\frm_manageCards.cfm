<cfset local.selectedTab = event.getTrimValue("tab","current")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>

<cfsavecontent variable="local.manageCardsJS">
	<cfoutput>
	<script language="javascript">
		$(function() {
			mca_initNavPills('manageCardTabs', '#local.selectedTab#', '#local.lockTab#');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.manageCardsJS)#">

<cfoutput>
<div class="p-3">
	<div id="managePayPayer" class="mb-3">
		<h5 class="text-primary mb-0">#encodeForHTML("#local.qryPayee.firstName# #local.qryPayee.lastName# (#local.qryPayee.memberNumber#)")#</h5>
		<cfif len(local.qryPayee.company)><div class="text-dim small">#encodeForHTML(local.qryPayee.company)#</div></cfif>
	</div>
	<div id="manageCardTabsContainer">
		<ul class="nav nav-pills nav-pills-dotted" id="manageCardTabs">
			<cfloop query="local.qryMerchantProfiles">
				<cfset local.thisTabName = local.qryMerchantProfiles.profileCode>
				<cfset local.thisTabID = local.qryMerchantProfiles.profileCode>
				<li class="nav-item">
					<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab"
							aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
						<cfif local.qryMerchantProfiles.cofBadCount gt 0><i class="fa-solid fa-circle-exclamation text-danger mr-1"></i></cfif>
						<span <cfif local.qryMerchantProfiles.cofBadCount gt 0>class="text-danger"</cfif>>
							#local.qryMerchantProfiles.tabTitle#
						</span>
					</a>
				</li>
			</cfloop>
		</ul>
		<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
			<cfloop query="local.qryMerchantProfiles">
				<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>
				<cfset local.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfiles.profileCode,
													pmid=local.qryPayee.memberid, showCOF=true, usePopup=false, usePopupDIVName="divPaymentTable#local.thisProfileID#", 
													hideSelect=true, offerDelete=true, autoShowForm=0, adminForm=1, editMode='controlPanelManage')>

				<cfif len(local.strPaymentForm.headcode)>
					<cfhtmlhead text="#application.objCommon.minText(local.strPaymentForm.headcode)#">
				</cfif>

				<div class="tab-pane fade" id="pills-#local.qryMerchantProfiles.profileCode#" role="tabpanel" aria-labelledby="#local.qryMerchantProfiles.profileCode#">
					<div id="divPaymentTable#local.thisProfileID#">
						<cfif local.qryMerchantProfiles.gatewayType EQ 'bankdraft' AND len(local.sharedBankDraftProfileMessage)>
							#local.sharedBankDraftProfileMessage#
						</cfif>
						<cfif len(local.strPaymentForm.inputForm)>
							<div>
								#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_#local.thisProfileID#_fld_','ALL')#
							</div>
						</cfif>

						<cfif len(local.strPaymentForm.jsvalidation)>
							<cfsavecontent variable="local.extrapayJS">
								<cfoutput>
								#local.extrapayJS#
								if ($('##profileid').val()==#local.thisProfileID#) {
									#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_#local.thisProfileID#_fld_','ALL')#
								}
								</cfoutput>
							</cfsavecontent>
						</cfif>
						<div class="mt-2 text-dim small">Note: Pay Methods scheduled for future invoices cannot be removed.</div>
					</div>
				</div>
			</cfloop>
		</div>
	</div>
</div>
</cfoutput>