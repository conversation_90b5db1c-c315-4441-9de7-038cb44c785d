<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		
		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		this.link.message = buildCurrentLink(arguments.event,"message") & "&mode=direct";
		this.link.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
		this.link.viewTransactionInfo = buildCurrentLink(arguments.event,"viewTransactionInfo") & "&mode=direct";
		this.link.viewTransactionInfoRelated = buildCurrentLink(arguments.event,"viewTransactionInfoRelated") & "&mode=stream";
		this.link.downloadInvoice = buildLinkToTool(toolType='InvoiceAdmin',mca_ta='downloadInvoice') & "&mode=stream";
		this.link.downloadInvoiceBundle = buildLinkToTool(toolType='InvoiceAdmin',mca_ta='downloadInvoiceBundle') & "&mode=stream";
		this.link.emailInvoiceBundle = buildLinkToTool(toolType='InvoiceAdmin',mca_ta='emailInvoice') & "&emailMode=bundle&mode=direct";
		this.link.viewInvoiceInfo = buildLinkToTool(toolType='InvoiceAdmin',mca_ta='viewInvoiceInfo') & "&mode=direct";
        this.link.downloadRefundStatement = buildCurrentLink(arguments.event,"downloadRefundStatement") & "&mode=stream";
        this.link.emailRefundStatement = buildCurrentLink(arguments.event,"emailRefundStatement") & "&mode=direct";

		this.link.allocatePayment = buildCurrentLink(arguments.event,"allocatePayment") & "&mode=direct";
		this.link.deallocatePayment = buildCurrentLink(arguments.event,"deallocatePayment") & "&mode=direct";
		this.link.refundPayment = buildCurrentLink(arguments.event,"refundPayment") & "&mode=direct";
		this.link.editTransaction = buildCurrentLink(arguments.event,"editTransaction") & "&mode=direct";
		this.link.writeOffTransaction = buildCurrentLink(arguments.event,"writeOffTransaction") & "&mode=direct";
		this.link.chargebackTransaction = buildCurrentLink(arguments.event,"chargebackTransaction") & "&mode=direct";
		this.link.adjustTransaction = buildCurrentLink(arguments.event,"adjustTransaction") & "&mode=direct";
		this.link.reclassTransaction = buildCurrentLink(arguments.event,"reclassTransaction") & "&mode=direct";
		this.link.voidTransaction = buildCurrentLink(arguments.event,"voidTransaction") & "&mode=direct";
		this.link.changeRecognitionSchedule = buildCurrentLink(arguments.event,"changeRecognitionSchedule") & "&mode=direct";
		this.link.downloadRefundStatementPrompt = buildCurrentLink(arguments.event,"downloadRefundStatementPrompt") & "&mode=direct"

		// method to run
		local.methodToRun = this[arguments.event.getValue('mca_ta')];

		// pass the argument collection to the current method and execute it.
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getPayeeInfo" access="public" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryPayee = "">

		<cfquery name="qryPayee" datasource="#application.dsn.membercentral.dsn#">
			SELECT m2.memberid, m2.firstname, m2.lastname, m2.membernumber, m2.company
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.memberID = m.activememberID
			WHERE m.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryPayee>
	</cffunction>

	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateID = "">

		<cfquery name="qryStateID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="cf_sql_integer">;

			select ma.stateID, ma.postalCode
			from dbo.ams_memberAddresses as ma
			inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
				AND matag.memberID = ma.memberID 
				AND matag.addressTypeID = ma.addressTypeID
			inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
				AND matagt.addressTagTypeID = matag.addressTagTypeID 
				AND matagt.addressTagType = 'Billing'
			where ma.orgID = @orgID
			and ma.memberid = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateID>
	</cffunction>
	
	<cffunction name="importAuthorizeNetPayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.lookupAuthNetPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=lookupAuthorizeNetPayment&mode=stream">
		<cfset local.importAuthNetPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=doImportAuthorizeNetPayment&mode=stream">

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddPayment') is not 1>
			<cflocation url="#this.link.message#&ec=IMPANNP" addtoken="no">
		</cfif>

		<!--- get payer info --->
		<cfset local.qryPayee = getPayeeInfo(memberID=arguments.event.getValue('mid'))>
		<cfif local.qryPayee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=PAYMEMNF" addtoken="no">
		</cfif>

		<!--- get a site's merchant profiles --->
		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			select mp.profileID, mp.profileName
			from dbo.mp_profiles as mp
			inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
			where mp.siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
			and mg.gatewayType = 'AuthorizeCCCIM'
			and mp.status = 'A'
			order by mp.profileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importAuthorizeNet.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="lookupAuthorizeNetPayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.mpid = arguments.event.getTrimValue('x_mpid',0)>
		<cfset local.antid = arguments.event.getTrimValue('x_antid',0)>

		<!--- get connection info --->
		<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
			select mp.profileID, mp.gatewayUsername, mp.gatewayPassword
			from dbo.mp_profiles as mp
			inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
			where mp.siteID = <cfqueryparam value='#arguments.event.getValue('mc_siteinfo.siteid')#' cfsqltype="cf_sql_integer">
			and mp.profileid = <cfqueryparam value='#local.mpid#' cfsqltype="cf_sql_integer">
			and mg.gatewayType = 'AuthorizeCCCIM'
			and mp.status = 'A'
		</cfquery>

		<!--- see if we already have that transaction, but not in held for review status (4)  --->
		<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TOP 1 historyID
			FROM dbo.tr_paymentHistory
			WHERE orgID = <cfqueryparam value='#arguments.event.getValue('mc_siteinfo.orgID')#' cfsqltype="cf_sql_integer">
			AND profileID = <cfqueryparam value='#local.mpid#' cfsqltype="cf_sql_integer">
			AND gatewayTransactionID = <cfqueryparam value='#local.antid#' cfsqltype="cf_sql_varchar">
			AND isnull(responseReasonCode,'') <> '4';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryLookup.recordcount is 1>
			<cfset local.stErrorMessage = "That payment has already been captured in Control Panel.">
			<cfset local.strTransactionInfo = { "success":false }>
		<cfelse>
			<cfset local.strTransactionInfo = createObject("component","model.system.platform.gateways.AuthorizeCCCIM").getTransaction(qryGateWayID=local.qryGateWayID, x_transid=local.antid)>

			<cfset local.stErrorMessage = "">
			<cfif local.strTransactionInfo.success>
				<cfset local.transaction = local.strTransactionInfo.data.transaction>
				<cfif NOT listFindNoCase("capturedPendingSettlement,settledSuccessfully",local.transaction.transactionStatus)>
					<cfset local.stErrorMessage = "We found the transaction, but the payment is not in the capturedPendingSettlement or settledSuccessfully status, so it cannot be imported.">
				<cfelseif local.transaction.responseCode neq "1">
					<cfset local.stErrorMessage = "We found the transaction, but the payment was not successful, so it cannot be imported.">
				<cfelseif NOT structKeyExists(local.transaction, "profile")>
					<cfset local.stErrorMessage = "We found the transaction, but the payment was not made using an Authorize.Net CIM profile, so it cannot be imported.">
				</cfif>
				<cfset local.transactionDate = replaceNoCase(local.transaction.submitTimeLocal,"T"," ")>

				<cfset local.stTransData = Replace(URLEncodedFormat(ToBase64(Encrypt(local.strTransactionInfo.rawdata,"20.18_CenTR@l"))),"%","xPcmKx","ALL")>
			<cfelse>
				<cfset local.stErrorMessage = "">
				<cfloop array="#local.strTransactionInfo.errors#" index="local.thisErr">
					<cfset local.stErrorMessage = "#local.thisErr.text#<br/>">
				</cfloop>
				<cfif findNoCase("permissions to call the Transaction Details API",local.stErrorMessage)>
					<cfset local.stErrorMessage = local.stErrorMessage & "<br/><br/>You will need to enable the Transaction Details API in your Authorize.Net account in order to use this import tool.">
				</cfif>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importAuthorizeNetResult.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doImportAuthorizeNetPayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.mid = arguments.event.getTrimValue('mid',0)>
		<cfset local.tdata = arguments.event.getTrimValue('tdata','')>
		<cfset local.mpid = arguments.event.getTrimValue('x_mpid',0)>

		<cftry>
			<cfset local.strTransInfo = deserializeJSON(decrypt(toString(toBinary(URLDecode(replace(local.tdata,"xPcmKx","%","ALL")))),"20.18_CenTR@l"))>
			
			<!--- get profile info --->
			<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
				SELECT mp.profileID, mg.gatewayID, mp.GLAccountID, mp.profileCode, mp.gatewayUsername, mp.gatewayPassword,
					s.orgID, s.siteID, ct.currencyType
				FROM dbo.mp_profiles AS mp
				INNER JOIN dbo.mp_gateways AS mg ON mg.gatewayID = mp.gatewayID
				INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
				INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
				WHERE mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value='#arguments.event.getValue('mc_siteinfo.siteid')#'>
				AND mp.profileid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value='#local.mpid#'>
				AND mg.gatewayType = 'AuthorizeCCCIM'
				AND mp.status = 'A'
			</cfquery>

			<cfset local.recordSuccess = createObject("component","model.system.platform.gateways.AuthorizeCCCIM").recordMissingTransaction(
										qryGateWayID=local.qryGateWayID, memberID=local.mid, profileID=local.mpid, transaction=local.strTransInfo.transaction)>

			<cfset local.success = local.recordSuccess>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif local.success>
					importAuthNetTIDSuccess();
				<cfelse>
					importAuthNetTIDFail();
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addSale" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.saveSaleLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveSale&mode=stream">
		
		<!--- Initialize GL Account widget for Revenue GL Account --->
		<cfset local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
		<cfset local.strGLAcctWidgetData = {
			label="Revenue Account",
			btnTxt="Choose Revenue Account",
			glatid=3,
			widgetMode='GLSelector',
			idFldName="GLAccountID",
			idFldValue=0,
			pathFldValue="",
			pathNoneTxt="(no account selected)"
		}>
		<cfset local.strGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strGLAcctWidgetData)>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddSale') is not 1>
			<cflocation url="#this.link.message#&ec=SALENP" addtoken="no">
		</cfif>

		<!--- get assignee info --->
		<cfquery name="local.qryAssignee" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">,
				@memberID int = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">;

			SELECT m2.memberid, 
				m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullIf(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as assigneeName,
				m2.company as assigneeCompany,
				isnull(ma.stateID,0) as stateIDForTax,
				isnull(ma.postalCode,'') as zipForTax
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
			LEFT OUTER JOIN dbo.ams_memberAddresses as ma 
				inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
				inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
				on ma.orgID = @orgID and ma.memberid = m2.memberID
			WHERE m.orgID = @orgID 
			AND m.memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qryAssignee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SALEMEMNF" addtoken="no">
		</cfif>

		<cfset local.stateIDForTax = val(local.qryAssignee.stateIDForTax)>
		<cfset local.zipForTax = local.qryAssignee.zipForTax>
		<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
			<cfset local.zipForTax = "">
		</cfif>

		<!--- invoice Admin permissions --->
		<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 sr.siteResourceID
			from dbo.cms_siteResources as sr
			inner join dbo.admin_toolTypes as tt on tt.resourceTypeID = sr.resourceTypeID
				and tt.toolType = 'InvoiceAdmin'
			where sr.siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
			and sr.siteResourceStatusID = 1
		</cfquery>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.qryGetSRID.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_sale.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSale" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.saleTransactionID = 0>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddSale') is not 1>
			<cflocation url="#this.link.message#&ec=SALENP" addtoken="no">
		</cfif>

		<!--- get assignee --->
		<cfset local.activeMemberID = application.objMember.getActiveMemberID(val(arguments.event.getValue('mid',0)))>
		<cfif local.activeMemberID is 0>
			<cflocation url="#this.link.message#&ec=SVSALEMEMNF" addtoken="no">
		</cfif>

		<!--- validation checks --->
		<cfset local.saleDate = arguments.event.getValue('transactionDate','')>
		<cfset local.detail = arguments.event.getTrimValue('detail','')>
		<cfset local.saleAmount = val(ReReplace(arguments.event.getValue('amount',0),'[^0-9\.\-]','','ALL'))>
		<cfset local.revenueGL = val(arguments.event.getValue('GLAccountID',0))>
		<cfset local.stateIDforTax = val(arguments.event.getTrimValue('stateIDforTax',0))>
		<cfset local.zipForTax = arguments.event.getTrimValue('zipForTax','')>

		<cfif NOT len(local.saleDate) OR NOT isDate(local.saleDate)>
			<cflocation url="#this.link.message#&ec=SVSALEDATE" addtoken="no">
		<cfelse>
			<!--- put now's time as the time for the sale --->
			<cfset local.saleDate = "#dateformat(local.saleDate,'m/d/yyyy')# #timeformat(now(),'h:mm tt')#">
		</cfif>
		<cfif NOT len(local.detail)>
			<cflocation url="#this.link.message#&ec=SVSALEDETL" addtoken="no">
		<cfelseif local.saleAmount lte 0>
			<cflocation url="#this.link.message#&ec=SVSALEAMT" addtoken="no">
		<cfelseif local.revenueGL is 0>
			<cflocation url="#this.link.message#&ec=SVSALEGL" addtoken="no">
		<cfelseif local.stateIDforTax is 0>
			<cflocation url="#this.link.message#&ec=SVSALETAXST" addtoken="no">
		<cfelseif NOT len(local.zipForTax)>
			<cflocation url="#this.link.message#&ec=SVSALETAXPC" addtoken="no">
		</cfif>

		<!--- check billing zip --->
		<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax)>
		<cfif local.strBillingZip.isValidZip>
			<cfset local.zipForTax = local.strBillingZip.billingzip>
		<cfelse>
			<cflocation url="#this.link.message#&ec=SVSALETAXPC" addtoken="no">
		</cfif>

		<!--- validate deferred schedule if it exists --->
		<cfset local.arrDeferredSchedule = arrayNew(1)>
		<cfset local.DefSchAmtSum = 0>
		<cfset local.scheduleRow = 1>
		<cfloop condition="#arguments.event.valueExists('defsch_#local.scheduleRow#_dt')#">
			<cfset local.schRowAmount = val(ReReplace(arguments.event.getValue('defsch_#local.scheduleRow#_amt',0),'[^0-9\.\-]','','ALL'))>
			<cfset local.schRowDate = arguments.event.getValue('defsch_#local.scheduleRow#_dt','')>
			<cfif local.schRowAmount gt 0 and len(local.schRowDate) and dateCompare(local.schRowDate,arguments.event.getValue('transactionDate'),"d") gte 0>
				<cfset local.tmpStr = { amt=local.schRowAmount, dt=local.schRowDate }>
				<cfset arrayAppend(local.arrDeferredSchedule,local.tmpStr)>
				<cfset local.DefSchAmtSum = local.DefSchAmtSum + local.schRowAmount>
			</cfif>
			<cfset local.scheduleRow = local.scheduleRow + 1>
		</cfloop>

		<cfset local.DefSchAmtSum = val(local.DefSchAmtSum)>
		<cfset local.saleAmount = val(local.saleAmount)>

		<cfif arrayLen(local.arrDeferredSchedule) and local.DefSchAmtSum neq local.saleAmount>
			<cflocation url="#this.link.message#&ec=SVSALEDEFSCH" addtoken="no">
		<cfelse>
			<cfset local.xmlSchedule = convertDeferredScheduledArrayToXML(arrSchedule=local.arrDeferredSchedule)>
		</cfif>

		<!--- get tax --->
		<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.revenueGL, 
										saleAmount=local.saleAmount, transactionDate=local.saleDate, stateIDForTax=local.stateIDforTax,
										zipForTax=local.zipForTax)>

		<cftry>
			<cfquery name="local.qrySaveSale" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @profileID int, @enteredByMemberID int, @assignedToMemberID int, @statsSessionID int, 
						@dateBilled datetime, @dateDue datetime, @detail varchar(500), @invoiceID int, @invoiceNumber varchar(18), 
						@transactionID int, @amount decimal(18,2), @transactionDate datetime, @creditGLAccountID int, 
						@stateIDForTax int, @zipForTax varchar(25), @taxAmount decimal(18,2), @xmlSchedule xml;

					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
					SET @assignedToMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.activeMemberID#">;
					SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">;
					SET @dateBilled = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.saleDate#">;
					SET @dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.saleDate#">;
					SET @transactionDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.saleDate#">;
					SET @detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(local.detail,500)#">;
					SET @amount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.saleAmount#">;
					SET @creditGLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.revenueGL#">;
					<cfif val(local.stateIDforTax)>
						SET @stateIDForTax = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.stateIDforTax#">;
					</cfif>
					<cfif len(local.zipForTax)>
						SET @zipForTax = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.zipForTax#">;
					</cfif>
					SET @taxAmount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(local.strTaxIndiv.totalTaxAmt)#">;
					SET @xmlSchedule = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#ToString(local.xmlSchedule)#">;

					BEGIN TRAN;
						<!--- create invoice if needed --->
						<cfif arguments.event.getValue('invoiceID',0) is 0>
							SELECT @profileID = profileID from dbo.fn_tr_getInvoiceProfileForGL(@creditGLAccountID);
							EXEC dbo.tr_createInvoice @invoiceProfileID=@profileID, @enteredByMemberID=@enteredByMemberID,
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@dateBilled, @dateDue=@dateDue,
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
						<cfelse>
							SET @invoiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('invoiceID',0)#">;
						</cfif>
	
						<!--- record sale --->
						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, 
							@recordedByMemberID=@enteredByMemberID, @statsSessionID=@statsSessionID, 
							@status='Active', @detail=@detail, @parentTransactionID=null, @amount=@amount, 
							@transactionDate=@transactionDate, @creditGLAccountID=@creditGLAccountID, 
							@invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
							@taxAmount=@taxAmount, @byPassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, 
							@xmlSchedule=@xmlSchedule, @transactionID=@transactionID OUTPUT;
				
						<!--- close invoice if necessary --->
						<cfif int(val(arguments.event.getValue('invoiceAutoClose',0)))>
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID;
						</cfif>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cflocation url="#this.link.message#&ec=SVSALE" addtoken="no">
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeAddSale) top.closeAddSale();
				else top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="adjustTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.saveAdjustmentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveAdjustedTransaction&mode=stream";
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAdjustSale') is not 1>
			<cflocation url="#this.link.message#&ec=ADJUSTNP" addtoken="no">
		</cfif>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>

		<!--- get sale transaction we are adjusting --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @tr_AdjustTrans int;
			set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;
			select @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');

			select top 1 t.transactionID, t.assignedToMemberID, t.transactionDate, t.detail, t.creditGLAccountID, 
				tsFull.cache_amountAfterAdjustment as amountAfterAdjustment,
				tsFull.cache_amountAfterAdjustment - wo.writeOffAmount as maxAmountForNegativeAdjustment,
				c.couponCode, td_t.amount as couponDiscount
			from dbo.tr_transactions as t
			cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
			cross apply dbo.fn_tr_getWriteOffAmountofSale(@orgID,t.transactionID) as wo
			left outer join dbo.tr_relationships as tr 
					inner join dbo.tr_transactionDiscounts as td on td.transactionID = tr.transactionID
					inner join dbo.tr_transactions as td_t on td_t.ownedByOrgID = @orgID 
						and td_t.transactionID = td.transactionID
						and td_t.statusID = 1
					inner join dbo.tr_coupons as c on c.couponID = td.couponID
				on tr.orgID = @orgID and tr.typeID = @tr_AdjustTrans and tr.appliedToTransactionID = t.transactionID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = @orgID
			and t.typeID = 1
			and t.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ADJUSTNF" addtoken="no">
		<cfelseif val(local.qryTransaction.couponDiscount) gt 0>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<h4>Adjust Sale</h4>
					<div class="alert alert-warning">
						This sale transaction is linked to a coupon (<b>#local.qryTransaction.couponCode#</b>) with a discount of <b>#DollarFormat(local.qryTransaction.couponDiscount)#</b> and cannot be manually adjusted. 
						If necessary, you may void the coupon adjustment in order to further adjust this sale transaction.
					</div>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<!--- get current amount of sale to adjust --->
		<cfset local.currentSaleAmount = local.qryTransaction.amountAfterAdjustment>
		<cfset local.MaxNegativeAdjustment = local.qryTransaction.maxAmountForNegativeAdjustment>

		<!--- get assignee info --->
		<cfquery name="local.qryAssignee" datasource="#application.dsn.membercentral.dsn#">
			SELECT m2.memberid, 
				m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullIf(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as assigneeName,
				m2.company as assigneeCompany
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.memberID = m.activememberID
			WHERE m.memberID = <cfqueryparam value="#local.qryTransaction.assignedToMemberID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryAssignee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ADJUSTMNF" addtoken="no">
		</cfif>

		<!--- invoice Admin permissions --->
		<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 sr.siteResourceID
			from dbo.cms_siteResources as sr
			inner join dbo.admin_toolTypes as tt on tt.resourceTypeID = sr.resourceTypeID
				and tt.toolType = 'InvoiceAdmin'
			where sr.siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
			and sr.siteResourceStatusID = 1
		</cfquery>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.qryGetSRID.siteResourceID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- get open invoices with same invoice profile --->
		<cfset local.invoiceOptions = getOpenInvoicesAndDeferredSchedForAddSale(mcproxy_orgID=arguments.event.getValue('mc_siteinfo.orgid'), memberID=local.qryTransaction.assignedToMemberID, revenueglaccountid=local.qryTransaction.creditGLAccountID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_adjustTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveAdjustedTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.adjustmentTransactionID = 0>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAdjustSale') is not 1>
			<cflocation url="#this.link.message#&ec=ADJUSTNP" addtoken="no">
		</cfif>

		<!--- get sale transaction we are adjusting --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @tr_AdjustTrans int;
			set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;
			select @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');

			select top 1 t.transactionID, t.transactionDate, t.creditGLAccountID, ts.stateIDForTax, ts.zipForTax, 
				tsFull.cache_amountAfterAdjustment - wo.writeOffAmount as maxAmountForNegativeAdjustment,
				td_t.amount as couponDiscount
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
			cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
			cross apply dbo.fn_tr_getWriteOffAmountofSale(@orgID,t.transactionID) as wo
			left outer join dbo.tr_relationships as tr 
					inner join dbo.tr_transactionDiscounts as td on td.transactionID = tr.transactionID
					inner join dbo.tr_transactions as td_t on td_t.ownedByOrgID = @orgID 
						and td_t.transactionID = td.transactionID
						and td_t.statusID = 1
					inner join dbo.tr_coupons as c on c.couponID = td.couponID
				on tr.orgID = @orgID and tr.typeID = @tr_AdjustTrans and tr.appliedToTransactionID = t.transactionID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid',0)#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = @orgID
			and t.typeID = 1
			and t.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SATNOSALE" addtoken="no">
		<cfelseif val(local.qryTransaction.couponDiscount) gt 0>
			<cflocation url="#this.link.message#&ec=SATADJPROMOSALE" addtoken="no">
		</cfif>

		<!--- validation checks --->
		<cfset local.adjustmentAmount = val(ReReplace(arguments.event.getValue('amount',0),'[^0-9\.\-]','','ALL'))>
		<cfif local.adjustmentAmount is 0>
			<cflocation url="#this.link.message#&ec=SATAMTZ" addtoken="no">
		<cfelseif local.adjustmentAmount lt 0 and abs(local.adjustmentAmount) gt local.qryTransaction.maxAmountForNegativeAdjustment>
			<cflocation url="#this.link.message#&ec=SATMAX" addtoken="no">
		</cfif>

		<!--- validate deferred schedule if it exists --->
		<cfset local.arrDeferredSchedule = arrayNew(1)>
		<cfset local.DefSchAmtSum = 0>
		<cfset local.scheduleRow = 1>
		<cfloop condition="#arguments.event.valueExists('defsch_#local.scheduleRow#_dt')#">
			<cfset local.schRowAmount = val(ReReplace(arguments.event.getValue('defsch_#local.scheduleRow#_amt',0),'[^0-9\.\-]','','ALL'))>
			<cfset local.schRowDate = arguments.event.getValue('defsch_#local.scheduleRow#_dt','')>
			<cfif local.schRowAmount gt 0 and len(local.schRowDate) and dateCompare(local.schRowDate,local.qryTransaction.transactionDate,"d") gte 0>
				<cfset local.tmpStr = { amt=local.schRowAmount, dt=local.schRowDate }>
				<cfset arrayAppend(local.arrDeferredSchedule,local.tmpStr)>
				<cfset local.DefSchAmtSum = local.DefSchAmtSum + local.schRowAmount>
			</cfif>
			<cfset local.scheduleRow = local.scheduleRow + 1>
		</cfloop>
		<cfif arrayLen(local.arrDeferredSchedule) and local.DefSchAmtSum is not local.adjustmentAmount>
			<cflocation url="#this.link.message#&ec=SATDEFSCH" addtoken="no">
		<cfelse>
			<cfset local.xmlSchedule = convertDeferredScheduledArrayToXML(arrSchedule=local.arrDeferredSchedule)>
		</cfif>

		<!--- get tax --->
		<cfif local.adjustmentAmount gt 0>
			<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.qryTransaction.creditGLAccountID, 
											saleAmount=local.adjustmentAmount, transactionDate=now(), stateIDForTax=val(local.qryTransaction.stateIDforTax),
											zipForTax=local.qryTransaction.zipForTax)>
		</cfif>

		<cftry>
			<cfquery name="local.qryAdjustment" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY		

					DECLARE @invoiceID int, @nowDate datetime, @adjTransactionID int, @orgID int;
					SET @nowDate = getdate();
					SET @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;

					BEGIN TRAN;
						EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=#arguments.event.getValue('mc_siteinfo.siteid')#, 
							@recordedByMemberID=#session.cfcuser.memberdata.memberid#, @statsSessionID=#session.cfcuser.statsSessionID#, 
							@amount=#local.adjustmentAmount#, @taxAmount=<cfif local.adjustmentAmount gt 0>#val(local.strTaxIndiv.totalTaxAmt)#<cfelse>null</cfif>, 
							@transactionDate=@nowDate, @autoAdjustTransactionDate=1, @saleTransactionID=#local.qryTransaction.transactionID#, 
							@invoiceID=<cfif val(arguments.event.getValue('invoiceID',0)) is 0>null<cfelse>#val(arguments.event.getValue('invoiceID',0))#</cfif>,
							@byPassTax=0, @byPassAccrual=0, @xmlSchedule='#ToString(local.xmlSchedule)#', @transactionID=@adjTransactionID OUTPUT;

						<cfif int(val(arguments.event.getValue('invoiceAutoClose',0)))>
							<cfif val(arguments.event.getValue('invoiceID',0)) is 0>
								select @invoiceID = invoiceID
								from dbo.tr_invoiceTransactions
								where orgID = @orgID
								and transactionID = @adjTransactionID
							<cfelse>
								select @invoiceID=#val(arguments.event.getValue('invoiceID',0))#;
							</cfif>
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=#session.cfcuser.memberdata.memberid#, @invoiceIDList=@invoiceID;
						</cfif>
					COMMIT TRAN;
						
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cflocation url="#this.link.message#&ec=SATADJ" addtoken="no">
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeAdjustTransaction) top.closeAdjustTransaction();
				else top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="reclassTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.saveReclassLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveReclassTransaction&mode=stream";
		</cfscript>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>

		<!--- get sale transaction we are adjusting --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select top 1 t.transactionID, t.assignedToMemberID, t.transactionDate, t.detail, t.creditGLAccountID, 
				rgl.AccountCode, rgl.thePathExpanded as glAccountPath, tsFull.cache_amountAfterAdjustment as amountAfterAdjustment
			from dbo.tr_transactions as t
			cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
			inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = t.creditGLAccountID
			inner join dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID = 1
			and t.statusID = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=RECLASSNF" addtoken="no">
		</cfif>

		<cfset local.currentSaleAmount = local.qryTransaction.amountAfterAdjustment>

		<!--- get assignee info --->
		<cfquery name="local.qryAssignee" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT m2.memberid, 
				m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullIf(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as assigneeName,
				m2.company as assigneeCompany
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.memberID = m.activememberID
			WHERE m.memberID = <cfqueryparam value="#local.qryTransaction.assignedToMemberID#" cfsqltype="CF_SQL_INTEGER">

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
		</cfquery>
		<cfif local.qryAssignee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=RECLASSMNF" addtoken="no">
		</cfif>

		<cfstoredproc procedure="tr_getReclassAmountAndEarliestDate" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.transactionID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" scale="2" variable="local.reclassAmount">
			<cfprocparam type="Out" cfsqltype="CF_SQL_DATE" variable="local.earliestDate">
		</cfstoredproc>

		<!--- Initialize GL Account widget for Revenue GL Account --->
		<cfset local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
		<cfset local.strGLAcctWidgetData = {
			label="GL Account",
			btnTxt="Choose GL Account",
			glatid=3,
			widgetMode='GLSelector',
			idFldName="reclassAmt_1_gl",
			idFldValue=0,
			pathFldValue="",
			pathNoneTxt="(no account selected)"
		}>
		<cfset local.strGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strGLAcctWidgetData)>
		
		<cfset local.DisallowSplitEarliestDate = false>
		<cfif local.earliestDate gt now()>
			<cfset local.DisallowSplitEarliestDate = true>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_reclassTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveReclassTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.paramValue('reclassSplitDate',DateFormat(now(),'m/d/yyyy'))>
		<cfset arguments.event.paramValue('invoiceDueDate',DateFormat(now(),'m/d/yyyy'))>
		<cfset arguments.event.paramValue('currrowid',1)>

		<cfset local.splitXML = "">
		<cfloop from="1" to="#arguments.event.getTrimValue('currrowid')#" index="local.thisRow">
			<cfset local.gl = val(arguments.event.getTrimValue('reclassAmt_#local.thisRow#_gl',0))>
			<cfset local.amt = val(ReReplace(arguments.event.getTrimValue('reclassAmt_#local.thisRow#_amt',0),'[^0-9\.]','','ALL'))>
			<cfif local.gl gt 0 and local.amt gt 0>
				<cfset local.splitXML = local.splitXML & '<s amt="#local.amt#" gl="#local.gl#" />'>
			</cfif>
		</cfloop>
		<cfset local.splitXML = "<split>#local.splitXML#</split>">

		<cftry>
			<!--- had to do it this way because it returns XML as a variable and cfsqltype varchar or longvarchar cant handle that --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryReclass">
				SET NOCOUNT ON;

				declare @transactionID int, @reclassDate date, @invoiceDueDate date, @recordedByMemberID int, @statsSessionID int, @splitXML xml, @tids xml;
				set @transactionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('tid')#">;
				set @reclassDate = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('reclassSplitDate')#">;
				set @invoiceDueDate = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('invoiceDueDate')#">;
				set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberid#">;
				set @statsSessionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(session.cfcuser.statsSessionID)#">;
				set @splitXML = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.splitXML#">;

				EXEC dbo.tr_splitAndReclassSale @transactionID=@transactionID, @reclassDate=@reclassDate, @invoiceDueDate=@invoiceDueDate, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @splitXML=@splitXML, @tids=@tids OUTPUT;
			</cfquery>
		<cfcatch type="Any">
			<cfset local.tmpCatch = { type="", message="Unable to split/reclass this sale. TA-SVRECLASS", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
			<cfset local.tmpErr = { tid=arguments.event.getValue('tid'), reclassSplitDate=arguments.event.getTrimValue('reclassSplitDate'), invoiceDueDate=arguments.event.getTrimValue('invoiceDueDate') } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cflocation url="#this.link.message#&ec=SVRECLASS" addtoken="no">
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeReclassTransaction) top.closeReclassTransaction();
				else top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="changeRecognitionSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.saveScheduleLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveRecognitionSchedule&mode=stream";
		</cfscript>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>

		<!--- get transaction --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @tr_WriteOffSaleTrans int = dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans');
			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select top 1 t.transactionID, t.assignedToMemberID, t.transactionDate, t.detail,
				hasWriteOff = case when exists (
					select wo.transactionID
					from dbo.tr_transactions as wo
					inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_WriteOffSaleTrans and rInner.transactionID = wo.transactionID and rInner.appliedToTransactionID = t.transactionID
					where wo.ownedByOrgID = @orgID 
					and wo.statusID = 1
					) then 1 else 0 end
			from dbo.tr_transactions as t
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = @orgID
			and t.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=CHSCHNF" addtoken="no">
		<cfelseif local.qryTransaction.hasWriteOff is 1>
			<cflocation url="#this.link.message#&ec=CHSCHWO" addtoken="no">
		</cfif>

		<!--- get assignee info --->
		<cfquery name="local.qryAssignee" datasource="#application.dsn.membercentral.dsn#">
			SELECT m2.memberid, 
				m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullIf(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as assigneeName,
				m2.company as assigneeCompany
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.memberID = m.activememberID
			WHERE m.memberID = <cfqueryparam value="#local.qryTransaction.assignedToMemberID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryAssignee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=CHSCHMNF" addtoken="no">
		</cfif>

		<!--- get schedule --->
		<cfquery name="local.qrySchedule" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @tr_DITSaleTrans int = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select dit.recognitionDate, sum(t.amount) as recogAmt
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as r on r.orgID = @orgID 
				and r.typeID = @tr_DITSaleTrans 
				and r.transactionID = t.transactionID 
				and r.appliedToTransactionID = <cfqueryparam value="#local.qryTransaction.transactionid#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.tr_transactionDIT as dit on dit.orgID = @orgID and dit.transactionID = t.transactionID
			where t.ownedByOrgID = @orgID
			and dit.isActive = 1
			group by dit.recognitionDate
			order by dit.recognitionDate;
		</cfquery>
		<cfquery name="local.qryScheduleSum" dbtype="query">
			select sum(recogAmt) as recogAmt
			from [local].qrySchedule
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeRecognitionSchedule.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRecognitionSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- get transaction we are changing --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			select top 1 t.transactionID, t.transactionDate
			from dbo.tr_transactions as t
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid',0)#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
			and t.statusID = 1
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=CHSCHNF" addtoken="no">
		</cfif>

		<!--- get existing schedule sum --->
		<cfquery name="local.qrySchedule" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @tr_DITSaleTrans int = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			select sum(t.amount) as recogAmt
			from dbo.tr_transactions as t
			inner join dbo.tr_relationships as r on r.orgID = @orgID
				and r.typeID = @tr_DITSaleTrans 
				and r.transactionID = t.transactionID 
				and r.appliedToTransactionID = <cfqueryparam value="#local.qryTransaction.transactionid#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.tr_transactionDIT as dit on dit.orgID = @orgID and dit.transactionID = t.transactionID
			where t.ownedByOrgID = @orgID
			and dit.isActive = 1;
		</cfquery>

		<!--- validate deferred schedule if it exists --->
		<cfset local.arrDeferredSchedule = arrayNew(1)>
		<cfset local.DefSchAmtSum = 0>
		<cfset local.scheduleRow = 1>
		<cfloop condition="#arguments.event.valueExists('defsch_#local.scheduleRow#_dt')#">
			<cfset local.schRowAmount = val(ReReplace(arguments.event.getValue('defsch_#local.scheduleRow#_amt',0),'[^0-9\.\-]','','ALL'))>
			<cfset local.schRowDate = arguments.event.getValue('defsch_#local.scheduleRow#_dt','')>
			<cfif local.schRowAmount gt 0 and len(local.schRowDate) and dateCompare(local.schRowDate,local.qryTransaction.transactionDate,"d") gte 0>
				<cfset local.tmpStr = { amt=local.schRowAmount, dt=local.schRowDate }>
				<cfset arrayAppend(local.arrDeferredSchedule,local.tmpStr)>
				<cfset local.DefSchAmtSum = local.DefSchAmtSum + local.schRowAmount>
			</cfif>
			<cfset local.scheduleRow = local.scheduleRow + 1>
		</cfloop>
		<cfif arrayLen(local.arrDeferredSchedule) and dollarFormat(local.DefSchAmtSum) is not dollarFormat(local.qrySchedule.recogAmt)>
			<cflocation url="#this.link.message#&ec=SRSDEFSCH" addtoken="no">
		<cfelse>
			<cfset local.xmlSchedule = convertDeferredScheduledArrayToXML(arrSchedule=local.arrDeferredSchedule)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="tr_changeRecognitionSchedule" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.transactionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#ToString(local.xmlSchedule)#">
			</cfstoredproc>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cflocation url="#this.link.message#&ec=SRSSP" addtoken="no">
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadRefundStatementPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_downloadRefundStatementPrompt.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generatePOForAddPayment" access="public" output="false" returntype="string">
		<cfset var local = structNew()>
		<cfreturn Replace(URLEncodedFormat(ToBase64(Encrypt(SerializeJSON(arguments),"20.15_CenTR@l"))),"%","xPcmKx","ALL")>
	</cffunction>

	<cffunction name="parsePOForAddPayment" access="private" output="false" returntype="struct">
		<cfargument name="PO" type="string" required="true">

		<cfset var local = structNew()>

		<!--- 
		Key:
		pmid = payer member id
		t = detail to display at top of page
		ta = amount due
		tmid = who transaction is assigned to after identifying
		ad = allocation data (sale or invoice, etc)
		ptid = used when allocating. this is the payment transaction id
		xcof = if 1 we wont show cards on file
		lckm = local to this payer and dont offer selecting another payer
		ocid = overrideCustomerID
		pa = payment amount (what gets put in the Amount box on the payment form)
		--->

		<cfset local.strPayObj = { pmid=0, t='', ta=0, tmid=0, ad='', ptid=0, xcof=0, lckm=0, ocid='', pa=0 }>
		<cfif len(arguments.po)>
			<cftry>
				<cfset local.strPayObj = deserializeJSON(decrypt(toString(toBinary(URLDecode(replace(arguments.po,"xPcmKx","%","ALL")))),"20.15_CenTR@l"))>
				<cfif StructKeyExists(local.strPayObj,"pmid")>
					<cfset local.strPayObj.pmid = int(val(local.strPayObj.pmid))>
				<cfelse>
					<cfset local.strPayObj.pmid = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"t") and len(trim(local.strPayObj.t))>
					<cfset local.strPayObj.t = trim(local.strPayObj.t)>
				<cfelse>
					<cfset local.strPayObj.t = ''>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"ta")>
					<cfset local.strPayObj.ta = val(local.strPayObj.ta)>
				<cfelse>
					<cfset local.strPayObj.ta = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"tmid")>
					<cfset local.strPayObj.tmid = int(val(local.strPayObj.tmid))>
				<cfelse>
					<cfset local.strPayObj.tmid = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"ad") and len(trim(local.strPayObj.ad))>
					<cfset local.strPayObj.ad = trim(local.strPayObj.ad)>
				<cfelse>
					<cfset local.strPayObj.ad = ''>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"ptid")>
					<cfset local.strPayObj.ptid = int(val(local.strPayObj.ptid))>
				<cfelse>
					<cfset local.strPayObj.ptid = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"xcof")>
					<cfset local.strPayObj.xcof = int(val(local.strPayObj.xcof))>
				<cfelse>
					<cfset local.strPayObj.xcof = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"lckm")>
					<cfset local.strPayObj.lckm = int(val(local.strPayObj.lckm))>
				<cfelse>
					<cfset local.strPayObj.lckm = 0>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"ocid") and len(trim(local.strPayObj.ocid))>
					<cfset local.strPayObj.ocid = trim(local.strPayObj.ocid)>
				<cfelse>
					<cfset local.strPayObj.ocid = ''>
				</cfif>
				<cfif StructKeyExists(local.strPayObj,"pa") and val(local.strPayObj.pa) gt 0>
					<cfset local.strPayObj.pa = val(local.strPayObj.pa)>
				<cfelse>
					<cfset local.strPayObj.pa = local.strPayObj.ta>
				</cfif>
			<cfcatch type="any">
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.strPayObj>
	</cffunction>

	<cffunction name="addPayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.toolIDByName = getToolIDByName('TransactionAdmin');
		local.addPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & local.toolIDByName & "&mca_ta=addPayment&mode=direct";

		arguments.event.paramValue('pa','pay');
		arguments.event.setValue('po',arguments.event.getTrimValue('po',''));
		</cfscript>

		<!--- decrypt and parse Pay Object --->
		<cfset local.strPayObj = parsePOForAddPayment(po=arguments.event.getValue('po'))>

		<cfswitch expression="#arguments.event.getValue('pa')#">
			<!--- when showing the credit balances to pick from --->
			<cfcase value="selcredit">
				<!--- if lock member, can only access the pay screen, not this one--->
				<cfif local.strPayObj.lckm is 1>
					<cflocation url="#local.addPaymentLink#&pa=pay&po=#arguments.event.getValue('po')#" addtoken="false">
				</cfif>

				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
					<cflocation url="#this.link.message#&ec=ALLOCNP" addtoken="no">
				</cfif>

				<!--- get payer credit balance --->
				<cfset local.strMemberCredit = CreateObject("component","model.admin.members.members").getMemberCreditAmount(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.strPayObj.pmid)>
				<cfif NOT local.strMemberCredit.success>
					<cfset local.strMemberCredit.unallocatedamount = 0>
				</cfif>

				<!--- get assigned to info --->
				<cfif local.strPayObj.tmid gt 0>
					<cfset local.qryAssignedTo = getPayeeInfo(memberID=local.strPayObj.tmid)>
				<cfelse>
					<cfset local.qryAssignedTo = { memberid=0, lastname='', firstname='', membernumber='', company='' }>
				</cfif>

				<!--- get members with deposits --->
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_getCreditBalances">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
					<cfprocresult name="local.qryBalances" resultset="1">
				</cfstoredproc>
				<cfif local.qryBalances.recordcount is 0>
					<cflocation url="#local.addPaymentLink#&pa=pay&po=#arguments.event.getValue('po')#&ncmsg=1" addtoken="false">
				</cfif>

				<cfsavecontent variable="local.data">
					<cfinclude template="frm_payment_credit.cfm">
				</cfsavecontent>
			</cfcase>

			<!--- when showing the credit balances indiv payments to pick from --->
			<cfcase value="selcreditpay">
				<cfif local.strPayObj.pmid gt 0>
					<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
						<cflocation url="#this.link.message#&ec=ALLOCNP" addtoken="no">
					</cfif>

					<!--- get payee info --->
					<cfset local.qryPayee = getPayeeInfo(memberID=local.strPayObj.pmid)>
					<cfif local.qryPayee.recordcount is 0>
						<cflocation url="#this.link.message#&ec=PAYMEMUCNF" addtoken="no">
					</cfif>

					<!--- get assigned to info --->
					<cfif local.strPayObj.tmid gt 0>
						<cfset local.qryAssignedTo = getPayeeInfo(memberID=local.strPayObj.tmid)>
					<cfelse>
						<cfset local.qryAssignedTo = { memberid=0, lastname='', firstname='', membernumber='', company='' }>
					</cfif>

					<!--- get member's payments with amount to allocate --->
					<cfquery name="local.qryUnallocatedPayments" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;

						declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;

						select t.transactionID, mp.profileID, t.origAmount, t.unallocatedAmount, t.refundableAmount, t.dateRecorded, t.transactionDate, 
							case when t.statusID = 3 then '**PENDING** ' + t.detail else t.detail end as detail, mp.profileName
						from dbo.fn_tr_paymentsWithAvailableAmounts(#local.qryPayee.memberID#,@orgID) as t
						inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
						inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
						order by mp.profileID, t.transactionDate;
					</cfquery>

					<!--- if 0, return to credit list. If 1, auto select it. If more than 1, ask for which one --->
					<cfif local.qryUnallocatedPayments.recordcount is 0>
						<cflocation url="#local.addPaymentLink#&pa=selcredit&po=#arguments.event.getValue('po')#" addtoken="no">
					<cfelseif local.qryUnallocatedPayments.recordcount is 1>
						<cfset local.addPaymentEncString = generatePOForAddPayment(pmid=local.strPayObj.pmid, t=local.strPayObj.t, ta=local.strPayObj.ta, tmid=local.strPayObj.tmid, ad=local.strPayObj.ad, ptid=local.qryUnallocatedPayments.transactionID)>
						<cflocation url="#this.link.allocatePayment#&po=#local.addPaymentEncString#" addtoken="no">
					<cfelse>
						<cfsavecontent variable="local.data">
							<cfinclude template="frm_payment_creditpay.cfm">
						</cfsavecontent>
					</cfif>
				<cfelse>
					<cflocation url="#this.link.message#&ec=ADDPAYUCNOM" addtoken="no">
				</cfif>
			</cfcase>

			<!--- when showing the locator search form --->
			<cfcase value="selmember">
				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddPayment') is not 1>
					<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
						<cflocation url="#this.link.message#&ec=PAYMEMNP" addtoken="no">
					<cfelse>
						<cflocation url="#local.addPaymentLink#&pa=selcredit&po=#arguments.event.getValue('po')#" addtoken="no">
					</cfif>
				</cfif>

				<!--- if lock member, can only access the pay screen, not this one--->
				<cfif local.strPayObj.lckm is 1>
					<cflocation url="#local.addPaymentLink#&pa=pay&po=#arguments.event.getValue('po')#" addtoken="false">
				</cfif>

				<!--- get assigned to info --->
				<cfif local.strPayObj.tmid gt 0>
					<cfset local.qryAssignedTo = getPayeeInfo(memberID=local.strPayObj.tmid)>
				<cfelse>
					<cfset local.qryAssignedTo = { memberid=0, lastname='', firstname='', membernumber='', company='' }>
				</cfif>

				<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>

				<!--- get fieldset used by member admin search form. if not found, use acct locator search form fieldset --->
				<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#">
					SELECT top 1 sr.siteResourceID
					from dbo.cms_siteResources as sr
					inner join dbo.admin_toolTypes as tt on tt.resourceTypeID = sr.resourceTypeID
						and tt.toolType = 'MemberAdmin'
					where sr.siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
					and sr.siteResourceStatusID = 1
				</cfquery>
				<cfif local.qryGetSRID.recordcount is 1>
					<cfset local.qryGetFS = CreateObject("component","model.admin.members.memberAdmin").getLocatorFieldsetID(siteresourceID=local.qryGetSRID.siteResourceID, area='search')>
					<cfset local.fieldsetID = local.qryGetFS.fieldsetID>
				<cfelse>
					<cfset local.fieldsetID = local.objLocator.getLocatorFieldsetID(siteid=arguments.event.getValue('mc_siteinfo.siteid'), area='search')>
				</cfif>

				<cfset local.searchPostLink = "#local.addPaymentLink#&pa=selmemberlocate&po=#arguments.event.getValue('po')#">

				<cfsavecontent variable="local.data">
					<cfinclude template="frm_payment_member.cfm">
				</cfsavecontent>
			</cfcase>

			<!--- running the account locator --->
			<cfcase value="selmemberlocate">
				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddPayment') is not 1>
					<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
						<cflocation url="#this.link.message#&ec=PAYMEMNP" addtoken="no">
					<cfelse>
						<cflocation url="#local.addPaymentLink#&pa=selcredit&po=#arguments.event.getValue('po')#" addtoken="no">
					</cfif>
				</cfif>

				<!--- if lock member, can only access the pay screen, not this one--->
				<cfif local.strPayObj.lckm is 1>
					<cflocation url="#local.addPaymentLink#&pa=pay&po=#arguments.event.getValue('po')#" addtoken="false">
				</cfif>

				<!--- get assigned to info --->
				<cfif local.strPayObj.tmid gt 0>
					<cfset local.qryAssignedTo = getPayeeInfo(memberID=local.strPayObj.tmid)>
				<cfelse>
					<cfset local.qryAssignedTo = { memberid=0, lastname='', firstname='', membernumber='', company='' }>
				</cfif>

				<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>
				<cfset local.resultsData = local.objLocator.locateMember(event=arguments.event,numResults=100)>
				<cfif local.resultsData.qryMemberLocator.recordcount>
					<cfset local.resultsFieldsetID = local.objLocator.getLocatorFieldsetID(siteid=arguments.event.getValue('mc_siteinfo.siteid'), area='results')>
					<cfset local.xmlResultFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=local.resultsFieldsetID, usage="accountLocaterResults")>

					<!--- col spacing --->
					<cfset local.arrColWidthPct = [ "20" ]>
					<cfset local.nameFieldFound = false>
					<cfset local.numEqualWidthCols = 0>
					<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
						<cfif ListFindNoCase("m_prefix,m_firstname,m_middlename,m_lastname,m_suffix,m_professionalsuffix",local.thisField.xmlAttributes.fieldcode)>
							<cfif NOT local.nameFieldFound>
								<cfset arrayAppend(local.arrColWidthPct,"30")>
								<cfset local.nameFieldFound = true>
							</cfif>
						<cfelse>
							<cfset arrayAppend(local.arrColWidthPct,"0")>
							<cfset local.numEqualWidthCols = local.numEqualWidthCols + 1>
						</cfif>
					</cfloop>
					<cfset local.unknownColsWidthPct = 100 - arraySum(local.arrColWidthPct)>
				<cfelse>
					<cflocation url="#local.addPaymentLink#&pa=selmember&po=#arguments.event.getValue('po')#&err=nomatch" addtoken="no">
				</cfif>

				<cfsavecontent variable="local.data">
					<cfinclude template="frm_payment_selmemberlocate.cfm">
				</cfsavecontent>
			</cfcase>

			<cfcase value="pay">
				<cfif local.strPayObj.pmid gt 0>
					<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAddPayment') is not 1>
						<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
							<cflocation url="#this.link.message#&ec=PAYMEMNP" addtoken="no">
						<cfelse>
							<cflocation url="#local.addPaymentLink#&pa=selcredit&po=#arguments.event.getValue('po')#" addtoken="no">
						</cfif>
					</cfif>

					<!--- get payer info --->
					<cfset local.qryPayee = getPayeeInfo(memberID=local.strPayObj.pmid)>
					<cfif local.qryPayee.recordcount is 0>
						<cflocation url="#this.link.message#&ec=PAYMEMNF" addtoken="no">
					</cfif>
					<cfset local.qryTaxStateZIP = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.strPayObj.pmid)>

					<!--- get payer credit balance --->
					<cfset local.strMemberCredit = CreateObject("component","model.admin.members.members").getMemberCreditAmount(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.strPayObj.pmid)>
					<cfif NOT local.strMemberCredit.success>
						<cfset local.strMemberCredit.unallocatedamount = 0>
					</cfif>

					<!--- get assigned to info --->
					<cfif local.strPayObj.tmid gt 0>
						<cfset local.qryAssignedTo = getPayeeInfo(memberID=local.strPayObj.tmid)>
					<cfelse>
						<cfset local.qryAssignedTo = { memberid=0, lastname='', firstname='', membernumber='', company='' }>
					</cfif>

					<!--- get a site's merchant profiles --->
					<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
						
						select p.siteID, s.orgID, p.profileID, p.profileName, p.profileCode, p.enableProcessingFeeDonation, 
							p.processFeeDonationFeePercent, pfm.title as processFeeDonationFETitle, pfm.message as processFeeDonationFEMsg, 
							p.processFeeDonationDefaultSelect, p.processFeeOtherPaymentsFELabel, p.processFeeOtherPaymentsFEDenyLabel,
							p.processFeeDonationRenevueGLAccountID, g.gatewayClass, g.gatewayID, g.gatewayType, gl.glAccountID, gl.accountName as GLAccountName,
							p.enableSurcharge, p.surchargePercent, p.surchargeRevenueGLAccountID
						from dbo.mp_profiles as p
						inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
						inner join dbo.sites as s on s.siteID = p.siteID
						left outer join dbo.tr_GLAccounts as gl on gl.glAccountID = p.glAccountID
						left outer join dbo.tr_solicitationMessages as pfm on pfm.siteID = @siteID
							and pfm.messageID = p.solicitationMessageID
						where p.siteID = @siteID
						and p.status = 'A'
						and g.isActive = 1
						and p.allowPayments = 1
						and g.gatewayType <> 'PayLater'
						order by p.profileName;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfset local.sharedBankDraftProfileMessage = CreateObject("component","model.system.platform.gateways.BankDraft").generateSharedProfileMessage(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

					<cfset local.savePaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & local.toolIDByName & "&mca_ta=savePayment&mode=stream">
					<cfset local.extrapayJS = "">
					<cfset local.useBatches = arguments.event.getValue('mc_siteinfo.useBatches')>
					<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
					<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
					<cfset local.checkPendingBox = arguments.event.getValue('mc_siteinfo.defaultPending')>

					<cfif local.useBatches is 1>
						<cfset local.batchAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='BatchAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfset local.hasBatchCreateRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.batchAdminSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid')).batchCreate IS 1>
					</cfif>
					<cfset local.lastSavePayment = application.mcCacheManager.sessionGetValue(keyname='lastSavePayment', defaultValue={})>

					<cfsavecontent variable="local.data">
						<cfinclude template="frm_payment_pay.cfm">
					</cfsavecontent>
				<cfelse>
					<cflocation url="#this.link.message#&ec=ADDPAYNOM" addtoken="no">
				</cfif>
			</cfcase>
			<cfdefaultcase>
				<cflocation url="#this.link.message#&ec=ADDPAYPA" addtoken="no">
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="savePayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.paymentTransactionID = 0>

		<!--- decrypt and parse Pay Object --->
		<cfset arguments.event.setValue('po',arguments.event.getTrimValue('po',''))>
		<cfset local.strPayObj = parsePOForAddPayment(po=arguments.event.getValue('po'))>

		<!--- init --->
		<cfset arguments.event.setValue('profileid',int(val(arguments.event.getValue('profileid',0))))>
		<cfset arguments.event.setValue('amount',val(ReReplace(arguments.event.getValue('amount',0),'[^0-9\.]','','ALL')))>
		<cfset arguments.event.paramValue('transactionDate',dateformat(now(),'m/d/yyyy'))>

		<!--- get payee info --->
		<cfset local.qryPayee = getPayeeInfo(memberID=local.strPayObj.pmid)>
		<cfif local.qryPayee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SVPAYMEMNF" addtoken="no">
		</cfif>
		<cfset local.qryTaxStateZIP = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.strPayObj.pmid)>

		<!--- get profile --->
		<cfquery name="local.qryMerchantProfile" datasource="#application.dsn.membercentral.dsn#">
			select profileID, profileCode, gatewayid, enableProcessingFeeDonation, processFeeDonationFeePercent, 
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, 
				enableSurcharge, surchargePercent, surchargeRevenueGLAccountID, processingFeeLabel
			from dbo.mp_profiles
			where siteID = #arguments.event.getValue('mc_siteinfo.siteid')#
			and [status] = 'A'
			and profileID = #arguments.event.getValue('profileid')#
		</cfquery>
		<cfif local.qryMerchantProfile.recordcount is not 1>
			<cflocation url="#this.link.message#&ec=SVPAYNOMP" addtoken="no">
		</cfif>

		<!--- get fields --->
		<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
		<cfset local.tmpFields = structNew()>
		<cfloop query="local.qryGatewayProfileFields">
			<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
		</cfloop>
		
		<!--- get info on file if applicable --->
		<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
		<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
			<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=local.strPayObj.pmid, profileID=local.qryMerchantProfile.profileID)>
			<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
		</cfif>

		<cfset local.totalAmount = arguments.event.getValue('amount')>

		<!--- Surcharge / Processing Fee Donation--->
		<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.totalAmount, 
			stateIDForTax=val(local.qryTaxStateZIP.stateID), zipForTax=local.qryTaxStateZIP.postalCode,
			processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
			surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

		<!--- failed --->
		<cfif NOT local.additionalFeesInfo.success>
			<cfset local.strResponse.response = "Unable to get additional payment fees info.">
			<cfreturn local.strResponse>
		</cfif>
	
		<cfset local.totalAmount = local.additionalFeesInfo.finalAmountToCharge>

		<!--- prepare fields for gateway and send --->
		<!--- put now's time as the time for the payment --->
		<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), adminForm=1, 
				overrideCreatedByMemberID=session.cfcuser.memberdata.memberid,
				overrideTransactionDate="#dateformat(arguments.event.getValue('transactionDate'),'m/d/yyyy')# #timeformat(now(),'h:mm tt')#",
				profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.strPayObj.pmid, recordedByMemberID=session.cfcuser.memberdata.memberid, 
				statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.totalAmount, x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Payment',
				offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>

		<!--- auto accept pending payment if flag is set --->
		<cfif arguments.event.valueExists("pf_#local.qryMerchantProfile.profileid#_acceptPending")>
			<cfset StructInsert(local.strTemp,"overrideAcceptPending",1)>
		</cfif>

		<!--- if not auto batching, and gateway 1,2, and overrideAcceptPending is 1, user was asked for a batch, so pass it along --->
		<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1 and listFind("1,2",local.qryMerchantProfile.gatewayID) and arguments.event.valueExists("pf_#local.qryMerchantProfile.profileid#_acceptPending")>
			<cfset StructInsert(local.strTemp,"overrideBatchID",int(val(arguments.event.getValue('batchid',0))))>
		</cfif>
		<cfset structAppend(local.strTemp,local.tmpFields)>
		<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

		<!--- if payment not successful --->
		<cfif local.paymentResponse.responseCode is not 1>
			<cfset local.data = "<div>Unable to save payment. Payment failed. #local.paymentResponse.responseReasonText#</div>">
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<!--- put selections into cache incuding batch if they override it --->
		<cfset local.lastSavePayment = { "transDate"=dateformat(arguments.event.getValue('transactionDate'),'m/d/yyyy'), "profileid"=local.qryMerchantProfile.profileid } >
		<cfif structKeyExists(local.strTemp,"overrideBatchID")>
			<cfset local.lastSavePayment["batchid"] = local.strTemp.overrideBatchID>
		</cfif>
		<cfset application.mcCacheManager.sessionSetValue(keyname='lastSavePayment', value=local.lastSavePayment)>

		<!--- Record Surcharge / Processing Fee Donation --->
		<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
			<cfset local.strRecordAdditionalPmtFees = CreateObject("component","model.system.platform.accounting").recordAdditionalPaymentFees(
				orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.strPayObj.pmid, 
				recordedByMemberID=session.cfcuser.memberdata.memberid, statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
				GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
			
			<!--- if not successful --->
			<cfif NOT local.strRecordAdditionalPmtFees.success>
				<cfset local.data = "<div>Unable to record payment processing fees. #local.strRecordAdditionalPmtFees.errmsg#</div>">
				<cfreturn returnAppStruct(local.data,"echo")>
			</cfif>
		</cfif>
		
		<!--- get new enc string --->
		<cfset local.addPaymentEncString = generatePOForAddPayment(pmid=local.strPayObj.pmid, t=local.strPayObj.t, ta=local.strPayObj.ta, 
			tmid=local.strPayObj.tmid, ad=local.strPayObj.ad, ptid=local.paymentResponse.mc_transactionID)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.closeAddPayment('#local.addPaymentEncString#');
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="manageCards" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objMemberAdmin = CreateObject("component","model.admin.members.memberAdmin");

		arguments.event.paramValue('mngAction','');
		arguments.event.setValue('mid',int(val(arguments.event.getValue('mid',0))));
		arguments.event.setValue('currentURL','/?pg=admin&mca_s=0&mca_a=0&mca_tt=' & getToolIDByName('TransactionAdmin') & '&mca_ta=manageCards&mode=direct');
		</cfscript>

		<!--- get the SRID and permissions of MemberAdmin. This function is called directly from other applications and may not have the rights set. --->
		<cfset local.MemberAdminSRID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID')>
		<cfset local.myRightsMemberAdmin = buildRightAssignments(local.MemberAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfif local.myRightsMemberAdmin.memberPayProfileManage is not 1>
			<cflocation url="#this.link.message#&ec=MNGCNP" addtoken="no">
		</cfif>

		<!--- get member info --->
		<cfset local.qryPayee = getPayeeInfo(memberID=arguments.event.getValue('mid'))>
		<cfif local.qryPayee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=MNGCMEMNF" addtoken="no">
		</cfif>

		<cfset local.sharedBankDraftProfileMessage = CreateObject("component","model.system.platform.gateways.BankDraft").generateSharedProfileMessage(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<!--- get a site's merchant profiles that can store credit cards or bank accounts --->
		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPayee.memberID#">;

			SELECT p.profileID, p.profileName, p.profileCode, g.gatewayClass, g.gatewayID, g.gatewayType, p.tabTitle, 
				(
				select count(distinct mpp.payProfileID)
				from dbo.ams_memberPaymentProfiles as mpp
				where mpp.profileID = p.profileID 
				and mpp.memberID = @memberID
				and mpp.status = 'A'
				and mpp.failedLastDate is not null
				) as cofBadCount,
				ROW_NUMBER() OVER (ORDER BY p.profilename) AS sortOrder
			FROM dbo.mp_profiles as p
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			WHERE p.siteID = @siteID
			AND p.status = 'A'
			AND g.gatewayType in ('AuthorizeCCCIM','SageCCCIM','AffiniPayCC')
			AND g.isActive = 1
				UNION ALL
			SELECT TOP 1 p.profileID, p.profileName, p.profileCode, g.gatewayClass, g.gatewayID, g.gatewayType, 
				'Bank Accounts' AS tabTitle, 
				(
				select count(distinct mpp.payProfileID)
				from dbo.ams_memberPaymentProfiles as mpp
				where mpp.profileID = p.profileID 
				and mpp.memberID = @memberID
				and mpp.status = 'A'
				and mpp.failedLastDate is not null
				) as cofBadCount,
				99 AS sortOrder
			FROM dbo.mp_profiles as p
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			WHERE p.siteID = @siteID
			AND p.status = 'A'
			AND g.tokenStore = 'BankDraft'
			AND g.isActive = 1
			ORDER BY sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.extrapayJS = "">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_manageCards.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deallocatePayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveDeallocatePayment&mode=stream";

		arguments.event.paramValue('pmid',0);
		arguments.event.paramValue('stid',0);
		arguments.event.paramValue('ptid',0);
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
			<cflocation url="#this.link.message#&ec=ALLOCNP" addtoken="no">
		</cfif>

		<!--- get member info --->
		<cfset local.qryMember = application.objMember.getMemberInfo(arguments.event.getValue('pmid'))>
		<cfif local.qryMember.recordcount is 0>
			<cflocation url="#this.link.message#&ec=DEALLOCPAYBYSMNF" addtoken="no">
		</cfif>

		<!--- get payment info --->
		<cfquery name="local.qryPayment" datasource="#application.dsn.memberCentral.dsn#">
			select TOP 1 t.transactionid, t.detail, t.amount, t.transactionDate, t.assignedTomemberID, 
				mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
				mAss2.company as assignedToMemberCompany
			from dbo.tr_transactions as t
			inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
			inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('ptid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryPayment.recordcount is 0>
			<cfset local.tmpCatch = { type="", message="The payment selected was not found. TA-DEALLOCPAYBYSNF", detail="", tagContext=arrayNew(1) } >
			<cfset local.tmpErr = { pmid=arguments.event.getValue('pmid'), stid=arguments.event.getValue('stid'), ptid=arguments.event.getValue('ptid') } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cflocation url="#this.link.message#&ec=DEALLOCPAYBYSNF" addtoken="no">
		</cfif>

		<cfset local.qrySale = getSaleInfo(orgID=arguments.event.getValue('mc_siteInfo.orgID'), saleTID=arguments.event.getValue('stid'), paymentTID=local.qryPayment.transactionID)>
		<cfif local.qrySale.recordcount is 0>
			<cfset local.tmpCatch = { type="", message="The revenue transaction selected was not found or is not currently allocated to the payment selected. TA-DEALLOCPAYBYSPNF", detail="", tagContext=arrayNew(1) } >
			<cfset local.tmpErr = { pmid=arguments.event.getValue('pmid'), stid=arguments.event.getValue('stid'), ptid=arguments.event.getValue('ptid') } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cflocation url="#this.link.message#&ec=DEALLOCPAYBYSPNF" addtoken="no">
		</cfif>

		<cfif arguments.event.getValue('doDeallocate',0) is 1>
			<cfset local.AmtToDeallocate = arguments.event.getValue('amt',0)>
			<cfif local.AmtToDeallocate gt local.qrySale.allocAmount>
				<cfset local.AmtToDeallocate = local.qrySale.allocAmount>
			</cfif>

			<cftry>
				<cfstoredproc procedure="tr_deallocateFromSale" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getValue('mc_siteinfo.siteid')))#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.memberdata.memberid)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(local.AmtToDeallocate)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryPayment.transactionID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySale.transactionID#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Deallocation from this sale was not successful. TA-DEALLOCPAYBYSALL", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local)>
				<cflocation url="#this.link.message#&ec=DEALLOCPAYBYSALL" addtoken="no">
			</cfcatch>
			</cftry>
			
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.closeAllocPayment();
				</script>
				</cfoutput>
			</cfsavecontent>

		<cfelse>
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_deallocatePayment.cfm">
			</cfsavecontent>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveDeallocatePayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('doDeallocate',1)>

		<cfreturn deallocatePayment(arguments.event)>
	</cffunction>

	<cffunction name="allocatePayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.saveAllocationLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveAllocatePayment&mode=stream";

		arguments.event.setValue('po',arguments.event.getTrimValue('po',''));
		</cfscript>

		<!--- decrypt and parse Pay Object --->
		<cfset local.strPayObj = parsePOForAddPayment(po=arguments.event.getValue('po'))>
		
		<!--- check permission --->
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
			<cflocation url="#this.link.message#&ec=ALLOCNP" addtoken="no">
		</cfif>

		<!--- get payee info --->
		<cfset local.qryPayee = getPayeeInfo(memberID=local.strPayObj.pmid)>
		<cfif local.qryPayee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ALLOCPAYBYPMNF" addtoken="no">
		</cfif>

		<!--- get payment info --->
		<cfquery name="local.qryPayment" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

			select top 1 t.transactionID, t.amount, t.transactionDate, tp.cache_allocatedAmountOfPayment, 
				tp.cache_refundableAmountOfPayment, tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment as AmtCanAllocate, 
				detail = case when t.statusID = 3 then '**PENDING** ' + t.detail else t.detail end,
				b.batchName, b.depositDate, bs.status as batchStatus
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
			inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID
			inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strPayObj.ptid#">
			and t.statusID in (1,3)
			and t.typeID = 2;
		</cfquery>
		<cfif local.qryPayment.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ALLOCPAYBYPPNF" addtoken="no">
		</cfif>

		<!--- get the SRID and permissions of InvoiceAdmin. --->
		<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- defaults to m|memberid --->
		<cfif NOT len(local.strPayObj.ad)>
			<cfset local.strPayObj.ad = "m|#local.strPayObj.pmid#">
		</cfif>

		<!--- split into type and value --->
		<cfset local.adt = GetToken(local.strPayObj.ad,1,'|')>
		<cfset local.adv = GetToken(local.strPayObj.ad,2,'|')>

		<!--- switch based on adt. defaults to m --->
		<cfswitch expression="#local.adt#">
			<cfcase value="v">
				<cfquery name="local.qryInvoicesDue" datasource="#application.dsn.memberCentral.dsn#">
					set nocount on;

					declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
						DROP TABLE ##tblInner;

					select invoiceID, invoiceNumber, dateDue, assignedToMemberID, memberName, company, invoiceStatus, invoiceProfile, ROW_NUMBER() OVER (ORDER BY invoiceNumber) as row 
					into ##tblInner
					from (
						select i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue,
							m2.memberid as assignedToMemberID, istat.status as invoiceStatus,
							m2.lastname + ', ' + m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as memberName,
							m2.company, ip.profileName as invoiceProfile
						from dbo.tr_invoices as i
						inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
						inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
						inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
						inner join dbo.organizations as o on o.orgID = @orgID
						where i.orgID = @orgID
						and i.invoiceID in (<cfqueryparam value="#local.adv#" cfsqltype="CF_SQL_BIGINT" list="yes">)
					) as tmp;

					select tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
						tmp2.assignedToMemberID as memberID, tmp2.memberName, tmp2.company, 
						sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
						sum(it.cache_pendingPaymentAllocatedAmount) as InvPending,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvUnAll,
						inPaymentQueue = cast(case when api.invoiceID is not null then 1 else 0 end as bit)
					from ##tblInner as tmp2
					left outer join dbo.tr_invoiceTransactions as it 
						inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
						on it.orgID = @orgID and it.invoiceID = tmp2.invoiceID
					left outer join (
						select qpid.invoiceID
						from platformQueue.dbo.queue_payInvoicesDetail as qpid
						inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
						inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
						where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
						) api on api.invoiceID = tmp2.invoiceID
					group by tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
						tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, api.invoiceID, tmp2.row
					having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0
					order by tmp2.row;

					IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
						DROP TABLE ##tblInner;
				</cfquery>
				<cfif local.qryInvoicesDue.recordcount is 0>
					<cflocation url="#this.link.message#&ec=ALLOCPAYINF" addtoken="no">
				</cfif>
			</cfcase>
			<cfcase value="s">
				<!--- get sales passed in and child sale transactions, and all taxes lumped together --->
				<cfquery name="local.qrySalesDue" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
						DROP TABLE ##tblInner;

					declare @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
					declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

					select t.transactionID, t.transactionID as mainTransactionID, t.assignedToMemberId, 
						tsFull.cache_amountAfterAdjustment as salesAmt,
						tsFull.cache_pendingPaymentAllocatedAmount as salesPending,
						tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
					into ##tblInner
					from dbo.tr_transactions as t
					cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
					where t.ownedByOrgID = @orgID
					and t.typeID = 1
					and t.statusID = 1
					and t.transactionID in (<cfqueryparam value="#local.adv#" cfsqltype="cf_sql_bigint" list="yes">);

					insert into ##tblInner
					select t.transactionid, innerTbl.transactionID as mainTransactionID, innerTbl.assignedToMemberId, 
						tsFull.cache_amountAfterAdjustment as salesAmt,
						tsFull.cache_pendingPaymentAllocatedAmount as salesPending,
						tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
					from dbo.tr_transactions as t
					cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
					inner join ##tblInner as innerTbl on innerTbl.transactionID = t.parentTransactionID
					where t.ownedByOrgID = @orgID
					and t.typeID = 1
					and t.statusID = 1;

					insert into ##tblInner
					select t.transactionID, innerTbl.mainTransactionID, innerTbl.assignedToMemberId, 
						tsFull.cache_amountAfterAdjustment as salesAmt,
						tsFull.cache_pendingPaymentAllocatedAmount as SalesPending,
						tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
					from dbo.tr_transactions as t
					cross apply dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
					inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_SalesTaxTrans and r.transactionID = t.transactionID
					inner join ##tblInner as innerTbl on innerTbl.transactionID = r.appliedToTransactionID
					where t.ownedByOrgID = @orgID
					and t.typeID = 7
					and t.statusID = 1;

					select t.transactionID, t.detail, t.transactionDate, 
						m2.lastname + ', ' + m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as memberName,
						m2.company,
						sum(innerTbl.salesAmt) as salesAmt, 
						sum(innerTbl.SalesPending) as SalesPending,
						sum(innerTbl.SalesDue) as SalesDue, 
						sum(innerTbl.SalesDue-innerTbl.SalesPending) as SalesUnAlloc
					from ##tblInner as innerTbl
					inner join dbo.tr_transactions as t on t.transactionID = innerTbl.mainTransactionID
					inner join dbo.ams_members as m on m.memberid = innerTbl.assignedToMemberID
					inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
					group by t.transactionID, t.detail, t.transactionDate, m2.lastname, m2.firstname, m2.middlename, m2.membernumber, m2.company
					having sum(innerTbl.SalesDue) > 0
					order by t.transactionDate;

					IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
						DROP TABLE ##tblInner;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfif local.qrySalesDue.recordcount is 0>
					<cflocation url="#this.link.message#&ec=ALLOCPAYSNF" addtoken="no">
				</cfif>
			</cfcase>
			<cfdefaultcase>
				<cfset local.qryInvoicesDue = getInvoicesDueByFilters(orgID=arguments.event.getValue('mc_siteInfo.orgID'), 
					siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.adv, strFilters=arguments.event.getCollection())>
				<cfif local.qryInvoicesDue.recordcount is 0>
					<cfif len(local.strPayObj.t)>
						<cflocation url="#this.link.message#&ec=ALLOCPAYMNF" addtoken="no">
					<cfelse>
						<cfsavecontent variable="local.data">
							<cfoutput>
							<script language="javascript">
								top.closeAllocPayment();
							</script>
							</cfoutput>
						</cfsavecontent>
						<cfreturn returnAppStruct(local.data,"echo")>
					</cfif>
				</cfif>
			</cfdefaultcase>
		</cfswitch>

		<cfif local.adt eq "m">
			<cfset local.objInvoice = createObject("component","invoice")>
			<cfset local.qryStatus = local.objInvoice.getInvoiceStatuses()>
			<cfset local.qryInvoiceProfiles = local.objInvoice.getInvoiceProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfset local.qryPayProfiles = local.objInvoice.getPaymentProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>

			<cfset local.allocatePaymentInvLink = buildCurrentLink(arguments.event,"getInvoicesGridForAllocation") & "&memberID=#local.adv#&mode=stream">
		</cfif>

		<cfset local.amtCanAllocateForPrefill = local.qryPayment.AmtCanAllocate>
		<cfset local.showCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		<cfset local.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_allocatePayment.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getInvoicesGridForAllocation" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.adt = "m">

		<!--- get the SRID and permissions of InvoiceAdmin. --->
		<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfset local.qryInvoicesDue = getInvoicesDueByFilters(orgID=arguments.event.getValue('mc_siteInfo.orgID'), 
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=arguments.event.getValue('memberID',0), 
			strFilters=arguments.event.getCollection())>
		<cfset local.amtCanAllocateForPrefill = arguments.event.getValue('amtCanAllocateForPrefill',0)>

		<cfsavecontent variable="local.data">
			<cfif local.qryInvoicesDue.recordcount>
				<cfinclude template="frm_allocatePayment_inv.cfm">
			<cfelse>
				<cfoutput>
					<div class="p-2 alert alert-danger">No invoices found based on the selected filters.</div>
					<script language="javascript">
						invoicesDueArr = [];
						toggleInvoiceFilters(false);
					</script>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getInvoicesDueByFilters" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="strFilters" type="struct" required="yes">

		<cfset var local = structNew()>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.strFilters.cardOnFile?:''>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>
		
		<cfquery name="local.qryInvoicesDue" datasource="#application.dsn.memberCentral.dsn#">
			set nocount on;

			declare @memberID int, @orgID int, @siteID int;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
			set @orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
			set @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
				DROP TABLE ##tblInner;
			IF OBJECT_ID('tempdb..##tblMemberInvoices') IS NOT NULL
				DROP TABLE ##tblMemberInvoices;
			IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
				DROP TABLE ##tblInvoices;
			CREATE TABLE ##tblMemberInvoices (invoiceID int PRIMARY KEY);
			CREATE TABLE ##tblInvoices (invoiceID int PRIMARY KEY);

			INSERT INTO ##tblMemberInvoices (invoiceID)
			EXEC dbo.tr_getInvoicesAssociatedToMember @memberID=@memberID;

			INSERT INTO ##tblInvoices (invoiceID)
			SELECT i.invoiceID
			FROM ##tblMemberInvoices as invoices
			INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = invoices.invoiceID
			where i.orgID = @orgID
			<cfif len(arguments.strFilters.statusID?:'')>
				and i.statusID in (<cfqueryparam value="#arguments.strFilters.statusID#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			</cfif>
			<cfif len(arguments.strFilters.invProfile?:'')>
				and i.invoiceProfileID in (<cfqueryparam value="#arguments.strFilters.invProfile#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			</cfif>
			<cfif val(arguments.strFilters.invoiceNumber?:'') gt 0 and isValid("integer",val(arguments.strFilters.invoiceNumber))>
				and i.invoiceNumber = <cfqueryparam value="#val(arguments.strFilters.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
			<cfelseif val(arguments.strFilters.invoiceNumber?:'') gt 0>
				and i.invoiceNumber = 0
			</cfif>
			<cfif len(arguments.strFilters.duedateStart?:'')>
				and i.dateDue >= <cfqueryparam value="#arguments.strFilters.duedateStart#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.strFilters.duedateEnd?:'')>
				and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.strFilters.duedateEnd)#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.strFilters.billeddateStart?:'')>
				and i.dateBilled >= <cfqueryparam value="#arguments.strFilters.billeddateStart#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.strFilters.billeddateEnd?:'')>
				and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.strFilters.billeddateEnd)#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(local.cofList)>
				and (
					<cfif local.cof0>
						i.payProfileID is null
					</cfif>
					<cfif local.cof0 and listLen(local.cofList)>
						or 
					</cfif>
					<cfif listLen(local.cofList)>
						i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
				)
			</cfif>;

			select invoiceID, invoiceNumber, dateDue, assignedToMemberID, memberName, company, invoiceStatus, invoiceProfile, ROW_NUMBER() OVER (ORDER BY invoiceNumber) as row 
			into ##tblInner
			from (
				select i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue,
					m2.memberid as assignedToMemberID, istat.status as invoiceStatus,
					m2.lastname + ', ' + m2.firstname + isnull(' ' + nullIf(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as memberName,
					m2.company, ip.profileName as invoiceProfile
				from ##tblInvoices as invoices
				inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = invoices.invoiceID
				inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = m2.orgID
			) as tmp;

			select tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
				tmp2.assignedToMemberID as memberID, tmp2.memberName, tmp2.company, 
				sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_pendingPaymentAllocatedAmount) as InvPending,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvUnAll,
				inPaymentQueue = cast(case when api.invoiceID is not null then 1 else 0 end as bit)
			from ##tblInner as tmp2
			left outer join dbo.tr_invoiceTransactions as it 
				inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
				on it.orgID = @orgID and it.invoiceID = tmp2.invoiceID
			left outer join (
				select qpid.invoiceID
				from platformQueue.dbo.queue_payInvoicesDetail as qpid
				inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
				inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
				where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
				) api on api.invoiceID = tmp2.invoiceID
			group by tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
				tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, api.invoiceID, tmp2.row
			having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0
			<cfif len(arguments.strFilters.dueAmtStart?:'')>
				and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#rereplace(arguments.strFilters.dueAmtStart,"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
			</cfif>
			<cfif len(arguments.strFilters.dueAmtEnd?:'')>
				and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#rereplace(arguments.strFilters.dueAmtEnd,"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
			</cfif>
			order by tmp2.row;

			IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
				DROP TABLE ##tblInner;
			IF OBJECT_ID('tempdb..##tblMemberInvoices') IS NOT NULL
				DROP TABLE ##tblMemberInvoices;
			IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
				DROP TABLE ##tblInvoices;
		</cfquery>

		<cfreturn local.qryInvoicesDue>
	</cffunction>

	<cffunction name="saveAllocatePayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- there could be a lot of allocations --->
		<cfsetting requesttimeout="500">

		<!--- decrypt and parse Pay Object --->
		<cfset arguments.event.setValue('po',arguments.event.getTrimValue('po',''))>
		<cfset local.strPayObj = parsePOForAddPayment(po=arguments.event.getValue('po'))>
		
		<!--- check permission --->
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is not 1>
			<cflocation url="#this.link.message#&ec=ALLOCNP" addtoken="no">
		</cfif>

		<!--- get payee info --->
		<cfset local.qryPayee = getPayeeInfo(memberID=local.strPayObj.pmid)>
		<cfif local.qryPayee.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ALLOCPAYBYPMNFSV" addtoken="no">
		</cfif>

		<!--- get payment info --->
		<cfquery name="local.qryPayment" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

			select top 1 t.transactionID, t.amount, t.transactionDate, tp.cache_allocatedAmountOfPayment, 
				tp.cache_refundableAmountOfPayment, tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment as AmtCanAllocate, 
				detail = case when t.statusID = 3 then '**PENDING** ' + t.detail else t.detail end,
				b.batchName, b.depositDate, bs.status as batchStatus
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
			inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID
			inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strPayObj.ptid#">
			and t.statusID in (1,3)
			and t.typeID = 2;
		</cfquery>
		<cfif local.qryPayment.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ALLOCPAYBYPNFSV" addtoken="no">
		</cfif>

		<!--- get the SRID and permissions of InvoiceAdmin. --->
		<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- see what invoices/sales we are attempting to allocate to --->
		<cfset local.strAllocationsI = structNew()>
		<cfset local.strAllocationsS = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisKey">
			<cfif listFindNoCase("alloc_i_,alloc_s_",left(local.thisKey,8))>
				<cfset arguments.event.setValue('#local.thisKey#',abs(val(ReReplace(arguments.event.getValue('#local.thisKey#',0),'[^0-9\.]','','ALL'))))>
				<cfif arguments.event.getValue('#local.thisKey#') gt 0>
					<cfif left(local.thisKey,8) eq "alloc_i_">
						<cfset structInsert(local.strAllocationsI,listGetAt(local.thisKey,3,'_'),arguments.event.getValue('#local.thisKey#'))>
					<cfelse>
						<cfset structInsert(local.strAllocationsS,listGetAt(local.thisKey,3,'_'),arguments.event.getValue('#local.thisKey#'))>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>

		<cfset local.totalAllocAmount = 0>

		<!--- if invoices, get current amt due --->
		<cfif structCount(local.strAllocationsI)>
			<cfquery name="local.qryInvoicesDue" datasource="#application.dsn.memberCentral.dsn#">
				set nocount on;

				declare @tblInvoices TABLE (invoiceID int PRIMARY KEY, reqAllocAmount decimal(18,2));
				<cfloop collection="#local.strAllocationsI#" item="local.thisInvoice">
					insert into @tblInvoices (invoiceID, reqAllocAmount) VALUES (#local.thisInvoice#, #NumberFormat(local.strAllocationsI[local.thisInvoice],"0.00")#);
				</cfloop>

				IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
					DROP TABLE ##tblInner;
				IF OBJECT_ID('tempdb..##tblFinal') IS NOT NULL
					DROP TABLE ##tblFinal;
				CREATE TABLE ##tblInner (invoiceID int PRIMARY KEY, status varchar(10));
				CREATE TABLE ##tblFinal (invoiceID int PRIMARY KEY, status varchar(10), allocAmount decimal(18,2));

				DECLARE @totalAllocAmount decimal(18,2), @orgID int;
				set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

				insert into ##tblInner (invoiceID, status)
				select distinct i.invoiceID, istat.status
				from @tblInvoices as tmp
				inner join dbo.tr_invoices as i on i.orgID = @orgID and tmp.invoiceID = i.invoiceID
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID;

				insert into ##tblFinal (invoiceID, status, allocAmount)
				select tmp2.invoiceID, tmp2.status, 
					sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvUnAll
				from ##tblInner as tmp2
				left outer join dbo.tr_invoiceTransactions as it 
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					on it.orgID = @orgID and it.invoiceID = tmp2.invoiceID
				left outer join (
					select qpid.invoiceID
					from platformQueue.dbo.queue_payInvoicesDetail as qpid
					inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
					where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
					) api on api.invoiceID = tmp2.invoiceID
				where api.invoiceID is null
				group by tmp2.invoiceID, tmp2.status
				having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0;

				update tf
				set tf.allocAmount = tmp.reqAllocAmount
				from ##tblFinal as tf
				inner join @tblInvoices as tmp on tmp.invoiceID = tf.invoiceID
				where tmp.reqAllocAmount < tf.allocAmount;

				select @totalAllocAmount = sum(allocAmount) from ##tblFinal;

				select invoiceID, status, allocAmount, @totalAllocAmount as totalAllocAmount
				from ##tblFinal;

				IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
					DROP TABLE ##tblInner;
				IF OBJECT_ID('tempdb..##tblFinal') IS NOT NULL
					DROP TABLE ##tblFinal;
			</cfquery>

			<!--- rebuild struct based on the invoices I can actually allocate to with the appropriate amounts --->
			<cfset local.strAllocationsI = structNew()>
			<cfloop query="local.qryInvoicesDue">
				<cfset structInsert(local.strAllocationsI,local.qryInvoicesDue.invoiceID,local.qryInvoicesDue.allocAmount)>
			</cfloop>
			<cfset local.totalAllocAmount = local.totalAllocAmount + local.qryInvoicesDue.totalAllocAmount>
		</cfif>

		<!--- if sales, get current amt due --->
		<cfif structCount(local.strAllocationsS)>
			<cfquery name="local.qrySalesDue" datasource="#application.dsn.memberCentral.dsn#">
				set nocount on;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

				declare @tblSales TABLE (transactionID int PRIMARY KEY, reqAllocAmount decimal(18,2));
				<cfloop collection="#local.strAllocationsS#" item="local.thisSale">
					insert into @tblSales (transactionID, reqAllocAmount) VALUES (#local.thisSale#, #NumberFormat(local.strAllocationsS[local.thisSale],"0.00")#);
				</cfloop>

				IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
					DROP TABLE ##tblInner;
				IF OBJECT_ID('tempdb..##tblFinal') IS NOT NULL
					DROP TABLE ##tblFinal;
				CREATE TABLE ##tblInner (transactionID int PRIMARY KEY, mainTransactionID int, salesPending decimal(18,2), SalesDue decimal(18,2));
				CREATE TABLE ##tblFinal (transactionID int PRIMARY KEY, allocAmount decimal(18,2));
				DECLARE @totalAllocAmount decimal(18,2);
				DECLARE @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

				insert into ##tblInner (transactionID, mainTransactionID, salesPending, SalesDue)
				select t.transactionID, t.transactionID as mainTransactionID, 
					tsFull.cache_pendingPaymentAllocatedAmount as salesPending,
					tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
				from @tblSales as tmp
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tmp.transactionID
				cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
				where t.typeID = 1
				and t.statusID = 1;

				insert into ##tblInner
				select t.transactionid, innerTbl.transactionID as mainTransactionID,
					tsFull.cache_pendingPaymentAllocatedAmount as salesPending,
					tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
				from dbo.tr_transactions as t
				cross apply dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
				inner join ##tblInner as innerTbl on innerTbl.transactionID = t.parentTransactionID
				where t.ownedByOrgID = @orgID
				and t.typeID = 1
				and t.statusID = 1;

				insert into ##tblInner
				select t.transactionID, innerTbl.mainTransactionID, 
					tsFull.cache_pendingPaymentAllocatedAmount as SalesPending,
					tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount as SalesDue
				from dbo.tr_transactions as t
				cross apply dbo.fn_tr_transactionSalesWithDIT(t.ownedByOrgID,t.transactionID) as tsFull
				inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_SalesTaxTrans and r.transactionID = t.transactionID
				inner join ##tblInner as innerTbl on innerTbl.transactionID = r.appliedToTransactionID
				where t.ownedByOrgID = @orgID
				and t.typeID = 7
				and t.statusID = 1;

				insert into ##tblFinal (transactionID, allocAmount)
				select mainTransactionID as transactionID, sum(SalesDue-SalesPending) as SalesUnAlloc
				from ##tblInner
				group by mainTransactionID
				having sum(SalesDue-SalesPending) > 0;

				update tf
				set tf.allocAmount = tmp.reqAllocAmount
				from ##tblFinal as tf
				inner join @tblSales as tmp on tmp.transactionID = tf.transactionID
				where tmp.reqAllocAmount < tf.allocAmount;

				select @totalAllocAmount = sum(allocAmount) from ##tblFinal;

				select transactionID, allocAmount, @totalAllocAmount as totalAllocAmount
				from ##tblFinal;

				IF OBJECT_ID('tempdb..##tblInner') IS NOT NULL
					DROP TABLE ##tblInner;
				IF OBJECT_ID('tempdb..##tblFinal') IS NOT NULL
					DROP TABLE ##tblFinal;
			</cfquery>

			<!--- rebuild struct based on the sales I can actually allocate to with the appropriate amounts --->
			<cfset local.strAllocationsS = structNew()>
			<cfloop query="local.qrySalesDue">
				<cfset structInsert(local.strAllocationsS,local.qrySalesDue.transactionID,local.qrySalesDue.allocAmount)>
			</cfloop>
			<cfset local.totalAllocAmount = local.totalAllocAmount + local.qrySalesDue.totalAllocAmount>
		</cfif>

		<!--- are we overallocating? --->
		<cfif numberFormat(local.totalAllocAmount,"0.00") gt numberFormat(local.qryPayment.AmtCanAllocate,"0.00")>
			<cflocation url="#this.link.message#&ec=ALPAYBYISVTTL" addtoken="no">
		</cfif>

		<!--- allocate to invoices --->
		<cfif structCount(local.strAllocationsI)>
			<cfloop query="local.qryInvoicesDue">
				<cftry>
					<cfquery name="local.qryAllocate" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							declare @nowDate datetime = getdate();
							declare @orgID int = #arguments.event.getValue('mc_siteinfo.orgID')#;

							<cfif NOT listFindNoCase("Closed,Delinquent",local.qryInvoicesDue.status)>
								BEGIN TRAN;
									EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=#session.cfcuser.memberdata.memberid#, @invoiceIDList='#local.qryInvoicesDue.invoiceID#';
							</cfif>

							EXEC dbo.tr_allocateToInvoice @recordedOnSiteID=#arguments.event.getValue('mc_siteinfo.siteid')#,
								@recordedByMemberID=#session.cfcuser.memberdata.memberid#, @statsSessionID=#val(session.cfcuser.statsSessionID)#,
								@amount=#local.qryInvoicesDue.allocAmount#, @transactionDate=@nowDate, 
								@paymentTransactionID=#local.qryPayment.transactionID#, @invoiceID=#local.qryInvoicesDue.invoiceID#;

							<cfif NOT listFindNoCase("Closed,Delinquent",local.qryInvoicesDue.status)>
								COMMIT TRAN;
							</cfif>

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				<cfcatch type="Any">
					<cfset local.tmpCatch = { type="", message="Allocation to this invoice was not successful. TA-ALLOCPAYBYIALL", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
					<cfset local.tmpErr = { ptid=local.qryPayment.transactionID, invoiceID=local.qryInvoicesDue.invoiceID, amt=local.qryInvoicesDue.allocAmount, strAllocationsI=local.strAllocationsI } >
					<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
					<cflocation url="#this.link.message#&ec=ALLOCPAYBYIALL" addtoken="no">
				</cfcatch>
				</cftry>
			</cfloop>
		</cfif>

		<!--- allocate to sales --->
		<cfif structCount(local.strAllocationsS)>
			<cfloop query="local.qrySalesDue">
				<cftry>
					<cfstoredproc procedure="tr_allocateToSale" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.qrySalesDue.allocAmount#">
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryPayment.transactionID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qrySalesDue.transactionID#">
					</cfstoredproc>
				<cfcatch type="Any">
					<cfset local.tmpCatch = { type="", message="Allocation to this sale was not successful. TA-ALLOCPAYBYSALL", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
					<cfset local.tmpErr = { ptid=local.qryPayment.transactionID, stid=local.qrySalesDue.transactionID, amt=local.qrySalesDue.allocAmount, strAllocationsS=local.strAllocationsS } >
					<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
					<cflocation url="#this.link.message#&ec=ALLOCPAYBYSALL" addtoken="no">
				</cfcatch>
				</cftry>
			</cfloop>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.closeAllocPayment();
			</script>
			Please wait while we finalize allocations...
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="voidTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveVoidedTransaction&mode=stream";
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transVoid') is not 1>
			<cflocation url="#this.link.message#&ec=VOIDNP" addtoken="no">
		</cfif>

		<!--- init --->
		<cfset arguments.event.paramValue('mid',0)>
		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.paramValue('vid',0)>

		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @ARGLAID int, @DEPGLAID int, @t_Adj int, @t_Alloc int, @t_Tax int, @t_VO int, @t_DIT int;

			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SET @t_Adj = dbo.fn_tr_getTypeID('Adjustment');
			SET @t_Alloc = dbo.fn_tr_getTypeID('Allocation');
			set @t_Tax = dbo.fn_tr_getTypeID('Sales Tax');
			set @t_VO = dbo.fn_tr_getTypeID('VoidOffset');
			set @t_DIT = dbo.fn_tr_getTypeID('Deferred Transfer');

			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;
			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='DEPOSITS', @GLAccountID=@DEPGLAID OUTPUT;

			select t.transactionid, tt.type, t.transactionDate,
				detail = case 
					when t.statusID = 3 then '**PENDING** ' + isnull(t.detail,'') 
					when t.statusID = 2 then '**VOID** ' + isnull(t.detail,'') 
					else t.detail end, 
				amount = case
					when t.typeID = @t_Adj and t.creditGLAccountID = @ARGLAID then t.amount * -1
					when t.typeID = @t_Alloc and t.creditGLAccountID = @DEPGLAID then t.amount * -1
					else t.amount end 
			from dbo.tr_transactions as t
			inner join dbo.tr_types as tt on tt.typeID = t.typeID
			inner join dbo.ams_members as m on m.memberID = t.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and m2.MemberID = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">
			and t.statusID in (1,3)
			and t.typeID not in (@t_Tax,@t_VO,@t_DIT);
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=VTNF" addtoken="no">
		</cfif>

		<!--- don't show DITs here. It will just be confusing to the user. --->
		<cfquery name="local.qryRelatedTransactions" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @ARGLAID int, @DEPGLAID int, @t_Adj int, @t_Alloc int, @t_DIT int;

			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SET @t_Adj = dbo.fn_tr_getTypeID('Adjustment');
			SET @t_Alloc = dbo.fn_tr_getTypeID('Allocation');
			set @t_DIT = dbo.fn_tr_getTypeID('Deferred Transfer');

			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;
			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='DEPOSITS', @GLAccountID=@DEPGLAID OUTPUT;
			
			WITH tids AS (
				select transactionID, dateRecorded
				<cfif local.qryTransaction.type eq "Payment">
					from dbo.fn_tr_getRelatedTransactionsToVoid_payment(@orgID,<cfqueryparam value="#local.qryTransaction.transactionID#" cfsqltype="CF_SQL_INTEGER">)
				<cfelseif local.qryTransaction.type eq "Sale">
					from dbo.fn_tr_getRelatedTransactionsToVoid_sale(@orgID,<cfqueryparam value="#local.qryTransaction.transactionID#" cfsqltype="CF_SQL_INTEGER">)
				<cfelseif local.qryTransaction.type eq "Adjustment">
					from dbo.fn_tr_getRelatedTransactionsToVoid_adjustment(<cfqueryparam value="#local.qryTransaction.transactionID#" cfsqltype="CF_SQL_INTEGER">)
				<cfelseif local.qryTransaction.type eq "Allocation">
					from dbo.fn_tr_getRelatedTransactionsToVoid_allocation(@orgID,<cfqueryparam value="#local.qryTransaction.transactionID#" cfsqltype="CF_SQL_INTEGER">)
				<cfelse>
					from dbo.tr_transactions
					where 1=0
				</cfif>
			)
			select tt.type, t.transactionDate, 
				detail = case 
					when t.statusID = 3 then '**PENDING** ' + isnull(t.detail,'') 
					when t.statusID = 2 then '**VOID** ' + isnull(t.detail,'') 
					else t.detail end, 
				amount = case 
					when t.typeID = @t_Adj and t.creditGLAccountID = @ARGLAID then t.amount * -1
					when t.typeID = @t_Alloc and t.creditGLAccountID = @DEPGLAID then t.amount * -1
					else t.amount end
			from dbo.tr_transactions as t
			inner join dbo.tr_types as tt on tt.typeID = t.typeID
			inner join tids on tids.transactionID = t.transactionID
			where t.typeID <> @t_DIT
			order by t.dateRecorded desc, t.transactionID desc;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_voidTransaction.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveVoidedTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.okToVoid = false>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transVoid') is not 1>
			<cflocation url="#this.link.message#&ec=VOIDNP" addtoken="no">
		</cfif>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.paramValue('vid',0)>

		<!--- get the type of transaction --->
		<cfquery name="local.qryType" datasource="#application.dsn.membercentral.dsn#">
			select transactionID, amount, statusID, typeID, recordedOnSiteID
			from dbo.tr_transactions
			where transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- sales / adjustments / allocations / write off / nsf ok to void as is --->
		<cfif listFind("1,3,5,6,9",local.qryType.typeID) and not listFind("2,4",local.qryType.statusID)>
			<cfset local.okToVoid = true>
		
		<!--- payments/refunds need to run thru gateway first --->
		<cfelseif listFind("2,4",local.qryType.typeID) and not listFind("2,4",local.qryType.statusID)>

			<!--- get profile used --->
			<cfquery name="local.qryMerchantProfile" datasource="#application.dsn.membercentral.dsn#">
				select top 1 mp.profileID, mp.profileCode
				from dbo.mp_profiles as mp
				inner join dbo.tr_transactionPayments as tp on tp.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER"> 
					and tp.profileID = mp.profileID
					and mp.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
					and mp.status = 'A'
					and tp.transactionID = <cfqueryparam value="#local.qryType.transactionID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<!--- prepare fields for gateway and send --->
			<cfset local.strTemp = { siteid=local.qryType.recordedOnSiteID, profileCode=local.qryMerchantProfile.profileCode, 
					paymentTransactionID=local.qryType.transactionID, x_amount=local.qryType.amount, x_description='Void payment' }>
			<cfinvoke component="#application.objPayments#" method="voidAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.voidResponse">

			<cfif local.voidResponse.responseCode is 1>
				<cfset local.okToVoid = true>
			</cfif>
		
		</cfif>
		
		<cfif local.okToVoid>
			<!--- had to do this in a cfquery because the cfstoredproc wont return xml as a longvarchar. --->
			<cftry>
				<cfquery name="local.qryVoid" datasource="#application.dsn.membercentral.dsn#">
					set nocount on
	
					declare @rc int, @recordedOnSiteID int, @recordedByMemberID int, @statsSessionID int, @transactionID int
					declare @tids xml, @vidPool xml
					select @recordedOnSiteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">
					select @recordedByMemberID = <cfqueryparam value="#val(session.cfcuser.memberdata.memberid)#" cfsqltype="CF_SQL_INTEGER">
					select @statsSessionID = <cfqueryparam value="#val(session.cfcuser.statsSessionID)#" cfsqltype="CF_SQL_INTEGER">
					select @transactionID = <cfqueryparam value="#local.qryType.transactionID#" cfsqltype="CF_SQL_INTEGER">
	
					EXEC dbo.tr_voidTransaction @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @transactionID=@transactionID, @checkInBounds=1, @vidPool=@vidPool OUTPUT, 
						@tids=@tids OUTPUT
					
					set nocount off
				</cfquery>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Unable to void or record the void of this transaction. TA-SVTOKVT", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset local.tmpErr = { tid=arguments.event.getValue('tid'), qryType=local.qryType } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
				<cflocation url="#this.link.message#&ec=SVTOKVT" addtoken="no">
			</cfcatch>
			</cftry>
		<cfelse>
			<cflocation url="#this.link.message#&ec=SVTOKNO" addtoken="no">
		</cfif>

		<cfset local.vid = ''>
		<cfif int(val(arguments.event.getValue('vid'))) gt 0>
			<cfset local.vid = int(val(arguments.event.getValue('vid')))>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeVoidTransaction) {
					top.closeVoidTransaction(#local.vid#);
					self.location.href = '#this.link.viewInvoiceInfo#&vid=#local.vid#';
				} else {
					top.MCModalUtils.hideModal();
				}
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="chargebackTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transNSF') is not 1>
			<cflocation url="#this.link.message#&ec=CBNP" addtoken="no">
		</cfif>

		<cfscript>
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveChargeback&mode=stream";

		// Initialize GL Account widget for Revenue GL Account
		local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget");
		local.strGLAcctWidgetData = {
			label="Revenue Account",
			btnTxt="Choose GL Account",
			glatid=3,
			widgetMode='GLSelector',
			idFldName="GLAccountID",
			idFldValue=0,
			pathFldValue="",
			pathNoneTxt="(no account selected)"
		};
		local.strGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strGLAcctWidgetData);
		</cfscript>

		<!--- payment must be non-pending --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select t.transactionid, mpg.gatewayClass, t.detail, t.amount, t.transactionDate, tp.cache_refundableAmountOfPayment as maxNSFAmount,
				isnull(ma.stateID,0) as stateIDForTax,
				isnull(ma.postalCode,'') as zipForTax
			from dbo.tr_transactions as t
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
				and m2.memberID = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.mp_profiles as mpp on mpp.profileID = tp.profileID
			inner join dbo.mp_gateways as mpg on mpg.gatewayID = mpp.gatewayID
			LEFT OUTER JOIN dbo.ams_memberAddresses as ma 
				inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID AND matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
				inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID AND matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
				on ma.orgID = @orgID and ma.memberid = m2.memberID
			where t.ownedByOrgID = @orgID 
			and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID = 2
			and t.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=CBNF" addtoken="no">
		</cfif>

		<cfset local.stateIDForTax = val(local.qryTransaction.stateIDForTax)>
		<cfset local.zipForTax = local.qryTransaction.zipForTax>
		<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
			<cfset local.zipForTax = "">
		</cfif>
		
		<!--- get open invoices tied to member --->
		<cfquery name="local.qryInvoices" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @memberID int = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_BIGINT">;
			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

			IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
				DROP TABLE ##tblInvoices;
			CREATE TABLE ##tblInvoices (invoiceID int PRIMARY KEY);

			INSERT INTO ##tblInvoices (invoiceID)
			EXEC dbo.tr_getInvoicesAssociatedToMember @memberID=@memberID;

			select tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue, isnull(sum(it.cache_invoiceAmountAfterAdjustment),0) as InvAmt
			from (
				select i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue
				from ##tblInvoices as invoices
				inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = invoices.invoiceID
				inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID and invs.status = 'Open'
				inner join dbo.organizations as o on o.orgID = i.orgID
			) as tmp
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
			group by tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue
			order by tmp.invoiceNumber;

			IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
				DROP TABLE ##tblInvoices;
		</cfquery>

		<cfif local.qryTransaction.gatewayClass eq "creditcard">
			<cfset local.NSFWord = "Chargeback">
		<cfelse>
			<cfset local.NSFWord = "NSF">
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput><cfinclude template="frm_chargebackTransaction.cfm"></cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveChargeback" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.setValue('nsfAmount',val(replace(arguments.event.getValue('nsfAmount',0),',','','ALL')))>
		<cfset arguments.event.setValue('fee',val(replace(arguments.event.getValue('fee',0),',','','ALL')))>

		<!--- put now's time as the time for the nsf --->
		<cfset local.nsfDate = "#dateformat(arguments.event.getValue('batchDate'),'m/d/yyyy')# #timeformat(now(),'h:mm tt')#">

		<!--- clean amount of chargeback --->
		<cfset local.nsfAmount = val(ReReplace(arguments.event.getValue('nsfAmount',0),'[^0-9\.]','','ALL'))>

		<!--- check billing zip --->
		<cfif arguments.event.getValue('rdoFee',0) is 1 and arguments.event.getValue('fee',0) gt 0>
			<cfset local.stateIDForTax = val(arguments.event.getValue('stateIDforTax',0))>
			<cfset local.zipForTax = arguments.event.getTrimValue('zipForTax','')>
			<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax)>
			<cfif local.strBillingZip.isValidZip>
				<cfset local.zipForTax = local.strBillingZip.billingzip>
			<cfelse>
				<cfthrow message="Invalid State/Zip.">
			</cfif>
		</cfif>

		<!--- payment must be non-pending --->
		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select t.transactionid, t.debitGLAccountID, t.assignedToMemberID, mpp.profileID, mpg.gatewayID,
				mpg.gatewayClass, t.detail, t.amount, t.transactionDate, mpp.profileCode
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.mp_profiles as mpp on mpp.profileID = tp.profileID
			inner join dbo.mp_gateways as mpg on mpg.gatewayID = mpp.gatewayID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID = 2
			and t.statusID = 1;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SCBNF" addtoken="no">
		</cfif>

		<cfset local.strGLAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryTransaction.debitGLAccountID, orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfset local.batchCode = "#dateformat(local.nsfDate,"YYYYMMDD")#_#local.qryTransaction.profileID#_#local.qryTransaction.debitGLAccountID#_CB">
		<cfset local.batchName = "#dateformat(local.nsfDate,"YYYYMMDD")# #local.qryTransaction.profileCode# #local.strGLAccount.qryAccount.accountName# Chargeback">
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFindOrCreateNSFBatch">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
	
					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">,
						@payProfileID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.profileID#">,
						@batchCode varchar(40) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.batchCode#">, 
						@batchName varchar(400) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.batchName#">, 
						@depositDate date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.nsfDate#">, 
						@batchTypeID int, @batchID int, @statusID int, @useBatchCode varchar(40), 
						@batchIncrement int = 0, @batchFound bit = 0;
	
					SELECT @batchTypeID = batchTypeID 
					FROM dbo.tr_batchTypes 
					WHERE batchType = 'Chargebacks';
	
					<!--- ACCT-134 wants each NSF to be on its own batch. so create one now --->
					WHILE @batchFound = 0 BEGIN
						SET @batchID = NULL;
						SET @batchIncrement = @batchIncrement + 1;

						SET @useBatchCode = @batchCode + '_' + cast(@batchIncrement as varchar(3));

						SELECT @batchID = batchID
						FROM dbo.tr_batches
						WHERE orgID = @orgID
						AND batchTypeID = @batchTypeID
						AND batchCode = @useBatchCode
						AND isSystemCreated = 1;

						IF @batchID IS NULL BEGIN
							SET @batchName = LEFT(@batchName,396) + '_' + cast(@batchIncrement as varchar(3));

							EXEC dbo.tr_createBatch @orgID=@orgID, @payProfileID=@payProfileID, @batchTypeID=@batchTypeID, 
								@batchCode=@useBatchCode, @batchName=@batchName, @controlAmt=0, @controlCount=0, @depositDate=@depositDate, 
								@isSystemCreated=1, @createdByMemberID=NULL, @batchID=@batchID OUTPUT;
							SET @batchFound = 1;
						END
					END
	
					SELECT @batchID AS batchID;
	
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.batchID = val(local.qryFindOrCreateNSFBatch.batchID)>
		<cfcatch type="Any">
			<cfset local.tmpCatch = { type="", message="Unable to create necessary batch for chargeback. TA-SVCBCB", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
			<cfset local.tmpErr = { "batchCode":local.batchCode, "batchName":local.batchName }>
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cflocation url="#this.link.message#&ec=SVCBCB" addtoken="no">
		</cfcatch>
		</cftry>

		<cfquery name="local.qryProcessingFeeSales" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@tr_SalesTaxTrans int;

			SET @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			SELECT ts.transactionID AS saleTransactionID, ts.cache_activePaymentAllocatedAmount AS processingFeesExcTax, 
				ts.cache_activePaymentAllocatedAmount + ISNULL(ts2.cache_activePaymentAllocatedAmount,0) AS processingFees
			FROM dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.transactionid#">) AS atop
			INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID AND ts.transactionID = atop.transactionID
			LEFT OUTER JOIN dbo.tr_relationships AS tr2 ON tr2.orgID = @orgID AND tr2.typeID = @tr_SalesTaxTrans AND tr2.appliedToTransactionID = ts.transactionID
			LEFT OUTER JOIN dbo.tr_transactionSales AS ts2 ON ts2.orgID = @orgID AND ts2.transactionID = tr2.transactionID
			WHERE ts.paymentFeeTypeID IN (1,2)
			AND ts.cache_activePaymentAllocatedAmount > 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryProcessingFeeSales">
			<!--- adjust revenue to 0, which will deallocate automatically --->
			<cfset arguments.event.setValue("tid",local.qryProcessingFeeSales.saleTransactionID)>
			<cfset arguments.event.setValue("amount",local.qryProcessingFeeSales.processingFeesExcTax * -1)>
			<cfset saveAdjustedTransaction(event=arguments.event)>
		</cfloop>

		<cftry>
			<cfstoredproc procedure="tr_createTransaction_nsf" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.nsfAmount#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryTransaction.detail#">
				<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.nsfDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.batchID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.transactionID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.NSFTransactionID">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset local.tmpCatch = { type="", message="Recording chargeback of this transaction was not successful. TA-SCBCTNSF", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
			<cfset local.tmpErr = { tid=local.qryTransaction.transactionID, batchID=val(local.batchID), amt=local.nsfAmount } >
			<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
			<cflocation url="#this.link.message#&ec=SCBCTNSF" addtoken="no">
		</cfcatch>
		</cftry>

		<cfif arguments.event.getValue('rdoFee',0) is 1 and arguments.event.getValue('fee',0) gt 0>
			<cfset local.feeAmount = val(ReReplace(arguments.event.getValue('fee',0),'[^0-9\.]','','ALL'))>
			<cfset local.objEventCopy = duplicate(arguments.event)>
			<cfset local.objEventCopy.setValue('mid',local.qryTransaction.assignedToMemberID)>
			<cfset local.objEventCopy.setValue('transactionDate',local.nsfDate)>
			<cfset local.objEventCopy.setValue('amount',local.feeAmount)>
			<cfset local.objEventCopy.setValue('detail','NSF/Chargeback Fee')>
			<cfset local.objEventCopy.setValue('GLAccountID',arguments.event.getValue('GLAccountID',0))>
			<cfset local.objEventCopy.setValue('invoiceID',arguments.event.getValue('invoiceID',0))>
			<cfset local.objEventCopy.setValue('invoiceAutoClose',arguments.event.getValue('invoiceAutoClose',0))>
			<cfset local.objEventCopy.setValue('stateIDforTax',local.stateIDforTax)>
			<cfset local.objEventCopy.setValue('zipForTax',local.zipForTax)>
			<cfset saveSale(event=local.objEventCopy)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeChargebackTransaction) top.closeChargebackTransaction();
				else top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="writeOffTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveWriteOff&mode=stream";
		</cfscript>

		<cfquery name="local.qryType" datasource="#application.dsn.membercentral.dsn#">
			select t.transactionid, t.typeID, m.activeMemberID as memberID
			from dbo.tr_transactions as t
			inner join dbo.ams_members as m on m.memberID = t.assignedToMemberID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and m.activeMemberID = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID in (1,2)
			and t.statusID = 1
		</cfquery>
		<cfif local.qryType.recordcount is 0>
			<cflocation url="#this.link.message#&ec=WONF" addtoken="no">
		</cfif>
		
		<cfif local.qryType.typeID is not 1>
			<cfset local.objGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
			<cfset local.strGLAcctWidgetData = {
				label="Revenue Account",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(no account selected)"
			}>
			<cfset local.strGLAcctWidget = local.objGLAcctWidget.renderWidget(strWidgetData=local.strGLAcctWidgetData)>
		</cfif>
		
		<cfif local.qryType.typeID is 1 and arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffSale') is not 1>
			<cflocation url="#this.link.message#&ec=WONP" addtoken="no">
		<cfelseif local.qryType.typeID is not 1 and arguments.event.getValue('mc_admintoolInfo.myRights.transWriteOffPayment') is not 1>
			<cflocation url="#this.link.message#&ec=WONNP" addtoken="no">
		</cfif>

		<cfif local.qryType.typeID is 1>
			<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;

				select t.transactionid, t.transactionDate, t.detail, t.amount, ad.paymentDueAmount-ad.pendingPaymentAllocatedAmount as SaleAmountDue, 
					ad.paymentDueAmountRecog-ad.pendingPaymentAllocatedAmountRecog as maxWriteOffAmt
				from dbo.tr_transactions as t
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
				inner join dbo.fn_tr_salesWithAmountDue(#local.qryType.memberID#,@orgID) as ad on ad.transactionID = t.transactionID
				where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>
		<cfelse>
			<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

				select t.transactionid, t.transactionDate, t.detail, t.amount, pmts.unallocatedAmount, pmts.refundableAmount
				from dbo.tr_transactions as t
				inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
				inner join dbo.fn_tr_paymentsWithAvailableAmounts(<cfqueryparam value="#local.qryType.memberID#" cfsqltype="CF_SQL_INTEGER">,@orgID) as pmts on pmts.transactionID = t.transactionID
				where t.ownedByOrgID = @orgID
				and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">;
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_writeOffTransaction.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveWriteOff" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>

		<!--- put now's time as the time for the write off --->
		<cfset local.woDate = "#dateformat(arguments.event.getValue('transactionDate'),'m/d/yyyy')# #timeformat(now(),'h:mm tt')#">

		<!--- clean the amount --->
		<cfset local.woAmount = val(ReReplace(arguments.event.getValue('amount',0),'[^0-9\.]','','ALL'))>

		<!--- get the type of transaction --->
		<cfquery name="local.qryType" datasource="#application.dsn.membercentral.dsn#">
			select t.transactionid, t.typeID
			from dbo.tr_transactions as t
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID in (1,2)
			and t.statusID = 1
		</cfquery>
		<cfif local.qryType.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SWONF" addtoken="no">
		</cfif>

		<cfif local.qryType.typeid is 1>
			<cftry>
				<cfstoredproc procedure="tr_writeOffSale" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getValue('mc_siteinfo.siteid')))#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.memberdata.memberid)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.woAmount#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.woDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryType.transactionid#">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Write-off of this transaction was not successful. TA-SWOWOS", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset local.tmpErr = { saleTransactionID=local.qryType.transactionid, amount=local.woAmount } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
				<cflocation url="#this.link.message#&ec=SWOWOS" addtoken="no">
			</cfcatch>
			</cftry>
		<cfelse>
			<cftry>
				<cfstoredproc procedure="tr_createTransaction_writeoff_payment" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getValue('mc_siteinfo.siteid')))#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.memberdata.memberid)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="Active">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.woAmount#">
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#local.woDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryType.transactionid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('GLAccountID')#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.transactionid">
				</cfstoredproc>
			<cfcatch type="Any">
				<cfset local.tmpCatch = { type="", message="Write-off of this transaction was not successful. TA-SWOWOP", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset local.tmpErr = { paymentTransactionID=local.qryType.transactionid, amount=local.woAmount } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
				<cflocation url="#this.link.message#&ec=SWOWOP" addtoken="no">
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeWriteOffTransaction) top.closeWriteOffTransaction();
				else top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewTransactionInfo" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qryTransactionCheck" datasource="#application.dsn.membercentral.dsn#">
			select t.transactionID, t.typeID, gl.GLCode as debitGLCode
			from dbo.tr_transactions as t
			inner join dbo.tr_glAccounts as gl on gl.glAccountID = t.debitGLAccountID
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid',0)#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryTransactionCheck.recordcount is 0>
			<cflocation url="#this.link.message#&ec=VTINF" addtoken="no">
		</cfif>

		<cfswitch expression="#local.qryTransactionCheck.typeID#">
			<cfcase value="1">
				<cfstoredproc procedure="tr_viewTransaction_sale" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryInvoices" resultset="3">
					<cfprocresult name="local.qryCurrentAllocations" resultset="4">
					<cfprocresult name="local.qryGLActivity" resultset="5">
					<cfprocresult name="local.qryDeferredSchedule" resultset="6">
				</cfstoredproc>
				<cfstoredproc procedure="tr_getReclassAmountAndEarliestDate" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_DECIMAL" scale="2" variable="local.reclassAmount">
					<cfprocparam type="Out" cfsqltype="CF_SQL_DATE" variable="local.earliestDate">
				</cfstoredproc>
			</cfcase>
			<cfcase value="2">
				<cfstoredproc procedure="tr_viewTransaction_payment" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryCurrentAllocations" resultset="3">
					<cfprocresult name="local.qryGLActivity" resultset="4">
				</cfstoredproc>

				<!--- sensitive info hidden unless pending transaction --->
				<cfset local.maskSensitive = true>
				<cfif local.qryTransaction.status eq "pending">
					<cfset local.maskSensitive = false>
				</cfif>
				<cfset local.strPaymentGatewayInfo = application.objPayments.getHistoryStructByGateway(transactionID=local.qryTransaction.transactionid,maskSensitive=local.maskSensitive)>

				<!--- get the SRID and permissions of InvoiceAdmin. --->
				<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
				<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

				<!--- processing fee donation --->
				<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1>
					<cfset local.allowRefundPFD = false>
					<cfset local.qryProcessingFees = QueryFilter(local.qryCurrentAllocations, function(thisRow) { return arguments.thisRow.paymentFeeTypeID EQ 1; })>
					<cfif local.qryProcessingFees.recordCount>
						<cfif (local.qryTypeInfo.isApplePay EQ 1 OR local.qryTypeInfo.isGooglePay EQ 1) AND local.qryTransaction.paidToday EQ 1>
							<cfset local.allowRefundPFD = false>
						<cfelse>
							<cfset local.allowRefundPFD = true>
						</cfif>
						<cfif local.allowRefundPFD>
							<cfset local.additionalPaymentFeeLabel = local.qryTypeInfo.processingFeeLabel>
							<cfset local.refundPFDLink = buildCurrentLink(arguments.event,"refundProcessingFees") & "&mode=direct">
						</cfif>
					</cfif>
				</cfif>
			</cfcase>
			<cfcase value="3">
				<cfstoredproc procedure="tr_viewTransaction_adjustment" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryCouponInfo" resultset="3">
					<cfprocresult name="local.qryAdjusting" resultset="4">
					<cfprocresult name="local.qryGLActivity" resultset="5">
					<cfprocresult name="local.qryDeferredSchedule" resultset="6">
				</cfstoredproc>
			</cfcase>
			<cfcase value="4">
				<!--- get the SRID and permissions of InvoiceAdmin. --->
				<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
				<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

				<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryGLActivity" resultset="3">
				</cfstoredproc>

				<!--- sensitive info hidden unless pending transaction --->
				<cfset local.maskSensitive = true>
				<cfif local.qryTransaction.status eq "pending">
					<cfset local.maskSensitive = false>
				</cfif>
				<cfset local.strPaymentGatewayInfo = application.objPayments.getHistoryStructByGateway(transactionID=local.qryTransaction.transactionid,maskSensitive=local.maskSensitive)>
			</cfcase>
			<cfcase value="5">
				<cfstoredproc procedure="tr_viewTransaction_allocation" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryCurrentAllocations" resultset="3">
					<cfprocresult name="local.qryGLActivity" resultset="4">
				</cfstoredproc>
			</cfcase>
			<cfcase value="6">
				<!--- if debit acct is DEP, then it is a write off of a payment --->
				<cfif local.qryTransactionCheck.debitGLCode eq "DEPOSITS">
					<cfstoredproc procedure="tr_viewTransaction_writeoff_payment" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
						<cfprocresult name="local.qryTransaction" resultset="1">
						<cfprocresult name="local.qryTypeInfo" resultset="2">
						<cfprocresult name="local.qryGLActivity" resultset="3">
					</cfstoredproc>
				<cfelse>
					<cfstoredproc procedure="tr_viewTransaction_writeoff_sale" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
						<cfprocresult name="local.qryTransaction" resultset="1">
						<cfprocresult name="local.qryTypeInfo" resultset="2">
						<cfprocresult name="local.qryGLActivity" resultset="3">
					</cfstoredproc>
				</cfif>
			</cfcase>
			<cfcase value="7">
				<cfstoredproc procedure="tr_viewTransaction_salestax" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryInvoices" resultset="3">
					<cfprocresult name="local.qryCurrentAllocations" resultset="4">
					<cfprocresult name="local.qryGLActivity" resultset="5">
					<cfprocresult name="local.qryDeferredSchedule" resultset="6">
					<cfprocresult name="local.qrySale" resultset="7">
				</cfstoredproc>
			</cfcase>
			<cfcase value="8">
				<cfstoredproc procedure="tr_viewTransaction_void" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryGLActivity" resultset="3">
				</cfstoredproc>
			</cfcase>
			<cfcase value="9">
				<cfstoredproc procedure="tr_viewTransaction_nsf" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryGLActivity" resultset="3">
				</cfstoredproc>
			</cfcase>
			<cfcase value="10">
				<cfstoredproc procedure="tr_viewTransaction_dit" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qrySale" resultset="2">
					<cfprocresult name="local.qryGLActivity" resultset="3">
				</cfstoredproc>
			</cfcase>
			<cfcase value="11">
				<cfstoredproc procedure="tr_viewTransaction_installment" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
					<cfprocresult name="local.qryTransaction" resultset="1">
					<cfprocresult name="local.qryTypeInfo" resultset="2">
					<cfprocresult name="local.qryGLActivity" resultset="3">
				</cfstoredproc>
			</cfcase>
		</cfswitch>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transAllocatePayment') is 1 and isDefined("local.qryTypeInfo.btn_canApplyPayment") and local.qryTypeInfo.btn_canApplyPayment is 1>
			<cfset local.addPaymentEncString = CreateObject("component","transactionAdmin").generatePOForAddPayment(pmid=local.qryTransaction.assignedTomemberID, t=local.qryTransaction.detail, ta=local.qryTypeInfo.paymentDueAmount, tmid=local.qryTransaction.assignedTomemberID, ad="s|#local.qryTransaction.transactionid#")>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_transactionInfo.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewTransactionInfoRelated" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qryTransactionCheck" datasource="#application.dsn.membercentral.dsn#">
			select t.transactionID
			from dbo.tr_transactions as t
			where t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid',0)#" cfsqltype="CF_SQL_INTEGER">
			and t.ownedByOrgID = #arguments.event.getValue('mc_siteinfo.orgID')#
		</cfquery>
		
		<cfif local.qryTransactionCheck.recordcount is 0>
			<cfset local.data = "">
		<cfelse>
			<cfstoredproc procedure="tr_viewTransaction_related" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
				<cfprocresult name="local.qryRelatedTransactions" resultset="1">
			</cfstoredproc>

			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_transactionInfoRelated.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=updateTransaction&mode=stream";
		</cfscript>

		<!--- init --->
		<cfset arguments.event.paramValue('mid',0)>
		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.paramValue('vid',0)>

		<cfquery name="local.qryTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @ARGLAID int, @DEPGLAID int, @t_Pay int, @t_Adj int, @t_Allocation int;

			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			set @t_Pay = dbo.fn_tr_getTypeID('Payment');
			set @t_Adj = dbo.fn_tr_getTypeID('Adjustment');
			set @t_Allocation = dbo.fn_tr_getTypeID('Allocation');

			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;
			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='DEPOSITS', @GLAccountID=@DEPGLAID OUTPUT;

			select t.transactionid, t.statusid, tp.profileID, 
				amount = case
					when t.typeID = @t_Adj and t.creditGLAccountID = @ARGLAID then t.amount * -1
					when t.typeID = @t_Allocation and t.creditGLAccountID = @DEPGLAID then t.amount * -1
					else t.amount end, t.daterecorded, t.transactionDate, t.detail, 'Payment' as [type]
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and m2.MemberID = <cfqueryparam value="#arguments.event.getValue('mid')#" cfsqltype="CF_SQL_INTEGER">
			and t.statusID = 3
			and t.typeID = @t_Pay;
		</cfquery>
		<cfif local.qryTransaction.recordcount is 0>
			<cflocation url="#this.link.message#&ec=ET" addtoken="no">
		</cfif>

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_getOpenBatches">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.profileID#">
			<cfprocresult name="local.qryOpenBatches">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfinclude template="frm_editTransaction.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="updateTransaction" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.paramValue('tid',0)>
		<cfset arguments.event.paramValue('vid',0)>
		<cfset arguments.event.paramValue('batchid','0')>
		<cfset arguments.event.paramValue('realpaymentdate','')>

		<!--- get transaction type --->
		<cfquery name="local.qryTransactionType" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

			select t.typeID, t.transactionID, t.debitGLAccountID, mp.profileID, mp.profileCode
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
			where t.ownedByOrgID = @orgID
			and t.transactionID = <cfqueryparam value="#arguments.event.getValue('tid')#" cfsqltype="CF_SQL_INTEGER">
			and t.typeid = 2
			and t.statusID = 3;
		</cfquery>
		<cfif local.qryTransactionType.recordcount is 1>

			<!--- get batch to put payment on --->
			<cftry>
				<cfset local.batchID = 0>
				<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>
					<cfset local.batchID = int(val(arguments.event.getValue('batchid',0)))>
				</cfif>
	
				<cfif local.batchID is 0>
					<cfset local.payDate = arguments.event.getValue('realpaymentdate')>
					
					<!--- find batch to put it on. if not there, auto create it --->
					<cfset local.strGLAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryTransactionType.debitGLAccountID, orgID=int(val(arguments.event.getValue('mc_siteinfo.orgid'))))>
					<cfset local.batchCode = "#dateformat(local.payDate,"YYYYMMDD")#_#local.qryTransactionType.profileID#_#local.qryTransactionType.debitGLAccountID#">
					<cfset local.batchName = "#dateformat(local.payDate,"YYYYMMDD")# #local.qryTransactionType.profileCode# #local.strGLAccount.qryAccount.accountName#">
					
					<cfquery name="local.qryFindBatch" datasource="#application.dsn.membercentral.dsn#">
						select batchID
						from dbo.tr_batches
						where orgID = <cfqueryparam value="#int(val(arguments.event.getValue('mc_siteinfo.orgid')))#" cfsqltype="CF_SQL_INTEGER">
						and batchTypeID = 1
						and batchCode = <cfqueryparam value="#local.batchCode#" cfsqltype="CF_SQL_VARCHAR">
						and statusID = 1
						and isSystemCreated = 1
					</cfquery>
					<cfif local.qryFindBatch.recordcount is 0>
						<cftry>
							<cfstoredproc procedure="tr_createBatch" datasource="#application.dsn.membercentral.dsn#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#int(val(arguments.event.getValue('mc_siteinfo.orgid')))#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionType.profileID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.batchCode#">
								<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(local.batchName,400)#">
								<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="0">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="0">
								<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.payDate#">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
								<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.batchID">
							</cfstoredproc>
						<cfcatch type="Any">
							<cfset local.tmpCatch = { type="", message="Unable to create batch. TA-UTCB", detail="#CFCatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
							<cfset local.tmpErr = { batchCode=local.batchCode, batchName=left(local.batchName,400) }>
							<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local.tmpErr)>
							<cflocation url="#this.link.message#&ec=UTCB" addtoken="no">
						</cfcatch>
						</cftry>
					<cfelse>
						<cfset local.batchID = val(local.qryFindBatch.batchID)>
					</cfif>
				</cfif>
			<cfcatch type="any">
				<cfset local.tmpErr = { batchid=local.batchID, batchName=local.batchName } >
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local.tmpErr)>
				<cflocation url="#this.link.message#&ec=UTCB2" addtoken="no">
			</cfcatch>
			</cftry>

			<cftry>
				<cfstoredproc procedure="tr_acceptPaymentTransaction" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionType.transactionID#">
					<cfif len(arguments.event.getValue('realpaymentdate')) and isValid("date",arguments.event.getValue('realpaymentdate'))>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('realpaymentdate')# #timeformat(now(),'h:mm tt')#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.batchID#">
				</cfstoredproc>
			<cfcatch type="any">
				<cfset local.tmpCatch = { type="", message="Unable to accept payment transaction. TA-UTAPT", detail="#cfcatch.message# #cfcatch.detail#", tagContext=cfcatch.tagContext } >
				<cfset application.objError.sendError(cfcatch=local.tmpCatch, objectToDump=local)>
				<cflocation url="#this.link.message#&ec=UTAPT" addtoken="no">
			</cfcatch>
			</cftry>
		</cfif>

		<cfset local.vid = ''>
		<cfif int(val(arguments.event.getValue('vid'))) gt 0>
			<cfset local.vid = int(val(arguments.event.getValue('vid')))>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				if (top.closeEditTransaction) {
					top.closeEditTransaction(#val(local.vid)#);
					<cfif val(local.vid)>
						self.location.href = '#this.link.viewInvoiceInfo#&vid=#local.vid#';
					</cfif>
				} else {
					top.MCModalUtils.hideModal();
				}
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="refundPayment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.extrapayJS = "";

		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=saveRefund&mode=stream";

		if (arguments.event.valueExists('ptid') and arguments.event.getTrimValue('ptid') neq "0")
			local.ptid = arguments.event.getTrimValue('ptid');
		else 
			local.ptid = "";
		</cfscript>

		<cfset local.tabMode = arguments.event.getValue('tabMode','')>
		<cfset local.hasRefundRights = arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is 1>
		
		<cfif not local.hasRefundRights and NOT listFindNoCase("subscriptions,registrants",local.tabMode)>
			<cflocation url="#this.link.message#&ec=RFNP" addtoken="no">
		</cfif>

		<!--- get member info --->
		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
		
			SELECT m2.memberid, m2.lastname, case when o.hasMiddleName = 1 then m2.middlename else '' end as middlename, m2.firstname,
				case when o.hasSuffix = 1 then m2.suffix else '' end as suffix, m2.membernumber
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			INNER JOIN dbo.organizations as o on o.orgID = m.orgID
			WHERE m.memberID = <cfqueryparam value="#arguments.event.getValue('mid',0)#" cfsqltype="CF_SQL_INTEGER">
			AND m.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qryMember.recordcount is 0>
			<cflocation url="#this.link.message#&ec=RFMNF" addtoken="no">
		</cfif>

		<!--- get a site's merchant profiles for which this payment can be refunded --->
		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select p.profileID, g.gatewayID, p.profileName, p.profileCode, g.gatewayClass, p.allowRefundsFromAnyProfile
			from dbo.mp_profiles as p
			inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
			where p.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
			and p.status = 'A'
			and g.isActive = 1
			and p.allowRefunds = 1
			order by p.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- get payments --->
		<cfquery name="local.qryMemberCredit" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpMemberCredit') IS NOT NULL 
				DROP TABLE ##tmpMemberCredit;
			IF OBJECT_ID('tempdb..##tmpProcessingFees') IS NOT NULL 
				DROP TABLE ##tmpProcessingFees;
			CREATE TABLE ##tmpMemberCredit (transactionID int, statusID int, amount decimal(18,2), unallocatedAmount decimal(18,2),
				refundableAmount decimal(18,2), detail varchar(500), dateRecorded datetime, transactionDate datetime, 
				gatewayID int, profileID int, isApplePay bit, isGooglePay bit, paidToday bit);
			CREATE TABLE ##tmpProcessingFees (transactionID int, paymentFeeTypeID tinyint, processingFees decimal(18,2), 
				refundableProcessingFees decimal(18,2), processingFeeLabel varchar(30));

			DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@tr_PaymentFeeTrans int, @tr_SalesTaxTrans int;

			SET @tr_PaymentFeeTrans = dbo.fn_tr_getRelationshipTypeID('PaymentFeeTrans');
			SET @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			INSERT INTO ##tmpMemberCredit (transactionID, statusID, amount, unallocatedAmount, refundableAmount, detail, dateRecorded,
				transactionDate, gatewayID, profileID, isApplePay, isGooglePay, paidToday)
			select pmts.transactionID, t.statusID, t.amount, pmts.unallocatedAmount, pmts.refundableAmount, pmts.detail, pmts.dateRecorded, 
				pmts.transactionDate, ph.gatewayID, ph.profileID, tp.isApplePay, tp.isGooglePay,
				CASE WHEN CONVERT(DATE,pmts.dateRecorded) = CONVERT(DATE,GETDATE()) THEN 1 ELSE 0 END AS paidToday
			from dbo.fn_tr_paymentsWithAvailableAmounts(<cfqueryparam value="#local.qryMember.memberid#" cfsqltype="CF_SQL_INTEGER">,@orgID) as pmts
			inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = pmts.transactionID and t.statusID = 1
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			left outer join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tp.historyID
			where pmts.refundableAmount > 0
			<cfif local.ptid gt 0>
				and pmts.transactionID = <cfqueryparam value="#local.ptid#" cfsqltype="CF_SQL_INTEGER">
			</cfif>;

			INSERT INTO ##tmpProcessingFees (transactionID, paymentFeeTypeID, processingFees)
			SELECT tmp.transactionID, MIN(ts.paymentFeeTypeID), SUM(ts.cache_activePaymentAllocatedAmount + ISNULL(ts2.cache_activePaymentAllocatedAmount,0))
			FROM ##tmpMemberCredit AS tmp
			CROSS APPLY dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,tmp.transactionID) AS atop
			INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID AND ts.transactionID = atop.transactionID
			LEFT OUTER JOIN dbo.tr_relationships AS tr2 ON tr2.orgID = @orgID AND tr2.typeID = @tr_SalesTaxTrans AND tr2.appliedToTransactionID = ts.transactionID
			LEFT OUTER JOIN dbo.tr_transactionSales AS ts2 ON ts2.orgID = @orgID AND ts2.transactionID = tr2.transactionID
			WHERE ts.paymentFeeTypeID IN (1,2)
			GROUP BY tmp.transactionID;

			UPDATE pf
			SET pf.refundableProcessingFees = 
					CAST( (pf.processingFees / (tmp.refundableAmount - pf.processingFees))
								* 
							(CASE WHEN tmp.unallocatedAmount < tmp.refundableAmount THEN tmp.unallocatedAmount ELSE tmp.refundableAmount END)
					AS decimal(18,2)),
				pf.processingFeeLabel = CASE pf.paymentFeeTypeID WHEN 1 THEN mp.processingFeeLabel WHEN 2 THEN 'Surcharge' ELSE NULL END
			FROM ##tmpMemberCredit AS tmp
			INNER JOIN ##tmpProcessingFees AS pf ON pf.transactionID = tmp.transactionID
			INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = tmp.profileID
			WHERE pf.processingFees > 0;

			SELECT tmp.transactionID, tmp.statusID, tmp.amount, tmp.unallocatedAmount, tmp.refundableAmount, tmp.detail, 
				tmp.dateRecorded, tmp.transactionDate, tmp.gatewayID, isApplePay, isGooglePay, tmp.isApplePay, tmp.isGooglePay, tmp.paidToday, 
				ISNULL(pf.processingFees,0) AS processingFees, ISNULL(pf.refundableProcessingFees,0) AS refundableProcessingFees, pf.processingFeeLabel
			FROM ##tmpMemberCredit AS tmp
			LEFT OUTER JOIN ##tmpProcessingFees AS pf ON pf.transactionID = tmp.transactionID
			ORDER BY tmp.transactionID DESC;

			IF OBJECT_ID('tempdb..##tmpMemberCredit') IS NOT NULL 
				DROP TABLE ##tmpMemberCredit;
			IF OBJECT_ID('tempdb..##tmpProcessingFees') IS NOT NULL 
				DROP TABLE ##tmpProcessingFees;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryMemberCredit.recordcount is 0>
			<cfsavecontent variable="local.stReturn">
				<cfoutput>
				<h4>Issue Refund for #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix# (#local.qryMember.membernumber#)</h4>
				Refunds can only be issued against unallocated funds.<br/>
				<cfif local.ptid gt 0>
					This payment does not have refundable funds.
				<cfelse>
					This member does not have payments with refundable funds.
				</cfif>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.stReturn,"echo")>
		</cfif>

		<cfquery name="local.qryMerchantProfiles" dbtype="query">
			select *
			from [local].qryMerchantProfiles
			where allowRefundsFromAnyProfile = 1
			or gatewayID IN (0#valueList(local.qryMemberCredit.gatewayID)#)
			order by profileName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput><cfinclude template="frm_refund.cfm"></cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveRefund" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- init --->
		<cfset arguments.event.paramValue('mid',0)>
		<cfset arguments.event.paramValue('ptid',0)>
		<cfset arguments.event.paramValue('profileid',0)>

		<!--- loop over each payment tid. if gt 0, we are attempting to refund some/all of it --->
		<cfset local.refundTID = "">
		<cfloop list="#arguments.event.getValue('ptid')#" index="local.thisTransactionID">
			<cfset arguments.event.setValue('amount_#local.thisTransactionID#',val(replace(arguments.event.getValue('amount_#local.thisTransactionID#',0),',','','ALL')))>
			<cfif arguments.event.getValue('amount_#local.thisTransactionID#') gt 0>
				<cfset local.refundTID = listAppend(local.refundTID,local.thisTransactionID)>
			</cfif>
		</cfloop>

		<!--- get payment info for the refunds --->
		<cfquery name="local.qryPayments" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

			select t.transactionID, tp.cache_refundableAmountOfPayment as refundableAmount, 
				tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountofPayment as unallocatedAmount
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
				and tp.cache_refundableAmountOfPayment > 0
				and tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountofPayment > 0
			where t.ownedByOrgID = @orgID
			and t.transactionID in (<cfqueryparam value="0#local.refundTID#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			and t.typeID = 2
			and t.statusID = 1
			order by t.transactionDate DESC, t.transactionID;
		</cfquery>
		<cfif local.qryPayments.recordcount is 0>
			<cflocation url="#this.link.message#&ec=SRFPNRF" addtoken="no">
		</cfif>

		<!--- get profile used --->
		<cfquery name="local.qryMerchantProfile" datasource="#application.dsn.membercentral.dsn#">
			select profileID, profileCode, gatewayID
			from dbo.mp_profiles
			where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
			and status = 'A'
			and profileID = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- get payment fields for gateway --->
		<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
		<cfset local.tmpFields = structNew()>
		<cfloop query="local.qryGatewayProfileFields">
			<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
		</cfloop>

		<!--- refund processing fees --->
		<cfif arguments.event.getValue('refundPFD',0) EQ 1>
			<cfquery name="local.qryProcessingFeeSales" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpPaymentTransactions') IS NOT NULL
					DROP TABLE ##tmpPaymentTransactions;
				IF OBJECT_ID('tempdb..##tmpProcessingFees') IS NOT NULL
					DROP TABLE ##tmpProcessingFees;
				IF OBJECT_ID('tempdb..##tmpProcessingFeeTotals') IS NOT NULL
					DROP TABLE ##tmpProcessingFeeTotals;
				CREATE TABLE ##tmpPaymentTransactions (transactionID int PRIMARY KEY, amount decimal(18,2), refundableAmount decimal(18,2), refundAmountRequested decimal(18,2));
				CREATE TABLE ##tmpProcessingFees (paymentTransactionID int, saleTransactionID int, processingFeesExcTax decimal(18,2), processingFees decimal(18,2));
				CREATE TABLE ##tmpProcessingFeeTotals (paymentTransactionID int, totalProcessingFeesExcTax decimal(18,2), totalProcessingFees decimal(18,2), refundableProcessingFees decimal(18,2));

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
					@tr_SalesTaxTrans int;

				SET @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

				<cfloop query="local.qryPayments">
					INSERT INTO ##tmpPaymentTransactions (transactionID, amount, refundableAmount, refundAmountRequested)
					VALUES (#local.qryPayments.transactionID#, #local.qryPayments.refundableAmount#,
						#min(local.qryPayments.unallocatedAmount,local.qryPayments.refundableAmount)#, 
						#val(arguments.event.getValue('amount_#local.qryPayments.transactionID#'))#
					);
				</cfloop>

				INSERT INTO ##tmpProcessingFees (paymentTransactionID, saleTransactionID, processingFeesExcTax, processingFees)
				SELECT tmp.transactionID, ts.transactionID, ts.cache_activePaymentAllocatedAmount, ts.cache_activePaymentAllocatedAmount + ISNULL(ts2.cache_activePaymentAllocatedAmount,0)
				FROM ##tmpPaymentTransactions AS tmp
				CROSS APPLY dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,tmp.transactionID) AS atop
				INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID AND ts.transactionID = atop.transactionID
				LEFT OUTER JOIN dbo.tr_relationships AS tr2 ON tr2.orgID = @orgID AND tr2.typeID = @tr_SalesTaxTrans AND tr2.appliedToTransactionID = ts.transactionID
				LEFT OUTER JOIN dbo.tr_transactionSales AS ts2 ON ts2.orgID = @orgID AND ts2.transactionID = tr2.transactionID
				WHERE ts.paymentFeeTypeID IN (1,2)
				AND ts.cache_activePaymentAllocatedAmount > 0;

				INSERT INTO ##tmpProcessingFeeTotals (paymentTransactionID, totalProcessingFeesExcTax, totalProcessingFees)
				SELECT paymentTransactionID, SUM(processingFeesExcTax), SUM(processingFees)
				FROM ##tmpProcessingFees
				GROUP BY paymentTransactionID;
 
				UPDATE pf
				SET pf.refundableProcessingFees = CAST( ( ( pf.totalProcessingFees / (tmp.amount - pf.totalProcessingFees) ) * tmp.refundAmountRequested ) AS decimal(18,2))
				FROM ##tmpPaymentTransactions AS tmp
				INNER JOIN ##tmpProcessingFeeTotals AS pf ON pf.paymentTransactionID = tmp.transactionID;

				SELECT pf.paymentTransactionID, pf.saleTransactionID, pf.processingFeesExcTax, pf.processingFees, 
					pft.totalProcessingFeesExcTax, pft.totalProcessingFees, pft.refundableProcessingFees
				FROM ##tmpProcessingFees AS pf
				INNER JOIN ##tmpProcessingFeeTotals AS pft ON pft.paymentTransactionID = pf.paymentTransactionID;

				IF OBJECT_ID('tempdb..##tmpPaymentTransactions') IS NOT NULL
					DROP TABLE ##tmpPaymentTransactions;
				IF OBJECT_ID('tempdb..##tmpProcessingFees') IS NOT NULL
					DROP TABLE ##tmpProcessingFees;
				IF OBJECT_ID('tempdb..##tmpProcessingFeeTotals') IS NOT NULL
					DROP TABLE ##tmpProcessingFeeTotals;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<!--- loop over payments to refund and process one by one --->
		<cfloop query="local.qryPayments">
			<cfset local.paymentTransactionID = local.qryPayments.transactionID>
			<cfset local.refundableAmount = min(local.qryPayments.unallocatedAmount,local.qryPayments.refundableAmount)>
			<cfset local.refundAmountRequested = arguments.event.getValue('amount_#local.paymentTransactionID#')>
			
			<!--- amt requested must be less than refundable amt --->
			<!--- amt requested must be gt 0 --->
			<cfif local.refundAmountRequested lte local.refundableAmount and local.refundAmountRequested gt 0>

				<!--- if gateway 1 or 2 (pending offline refunds are not supported) ask for batch. --->
				<!--- else gateway is online and put it on batch automatically --->
				<cfset local.batchID = 0>
				<cfif arguments.event.getValue('mc_siteinfo.useBatches') is 1>
					<cfif listFind("1,2",local.qryMerchantProfile.gatewayID)>
						<cfset local.batchID = int(val(arguments.event.getValue('batchid',0)))>
					</cfif>
				</cfif>

				<!--- refund processing fee donation --->
				<cfif arguments.event.getValue('refundPFD',0) EQ 1>
					<cfquery name="local.qryThisPaymentProcessingFeeSales" dbtype="query">
						SELECT paymentTransactionID, saleTransactionID, processingFeesExcTax, processingFees, 
							totalProcessingFeesExcTax, totalProcessingFees, refundableProcessingFees
						FROM local.qryProcessingFeeSales
						WHERE paymentTransactionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.paymentTransactionID#">
						AND refundableProcessingFees > 0
					</cfquery>

					<cfif local.qryThisPaymentProcessingFeeSales.recordCount>
						<cfset local.refundableProcessingFees = local.qryThisPaymentProcessingFeeSales.refundableProcessingFees>
						<cfset local.refundAmountRequested = precisionEvaluate(local.refundAmountRequested + local.qryThisPaymentProcessingFeeSales.refundableProcessingFees)>

						<cfloop query="local.qryThisPaymentProcessingFeeSales">
							<cfset local.adjustAmt = min(local.refundableProcessingFees,local.qryThisPaymentProcessingFeeSales.processingFees)>
							<cfset local.refundableProcessingFees = precisionEvaluate(local.refundableProcessingFees - local.adjustAmt)>
							<cfset local.taxAmount = local.qryThisPaymentProcessingFeeSales.processingFees - local.qryThisPaymentProcessingFeeSales.processingFeesExcTax>

							<cfif local.taxAmount>
								<cfif local.adjustAmt EQ local.qryThisPaymentProcessingFeeSales.processingFees>
									<cfset local.adjustAmtExcTax = local.qryThisPaymentProcessingFeeSales.processingFeesExcTax>
								<cfelse>
									<cfset local.taxRate = NumberFormat(precisionEvaluate(local.taxAmount / local.qryThisPaymentProcessingFeeSales.processingFeesExcTax),"9.9999")>
									<cfset local.adjustAmtExcTax = NumberFormat(precisionEvaluate(local.adjustAmt / (1 + local.taxRate)),"9.99")>
								</cfif>
							<cfelse>
								<cfset local.adjustAmtExcTax = local.adjustAmt>
							</cfif>

							<!--- adjust revenue --->
							<cfset arguments.event.setValue("tid",local.qryThisPaymentProcessingFeeSales.saleTransactionID)>
							<cfset arguments.event.setValue("amount",local.adjustAmtExcTax * -1)>
							<cfset saveAdjustedTransaction(event=arguments.event)>

							<cfif local.refundableProcessingFees EQ 0>
								<cfbreak>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>
			
				<!--- prepare fields for gateway and send --->
				<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
						profileCode=local.qryMerchantProfile.profileCode, paymentTransactionID=local.paymentTransactionID,
						assignedToMemberID=arguments.event.getValue('mid'), recordedByMemberID=session.cfcuser.memberData.memberID,
						statsSessionID=session.cfcuser.statsSessionID, x_amount=local.refundAmountRequested,
						x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Refund', overrideTransactionDate=now(),
						overrideBatchID=local.batchID, overrideCreatedByMemberID=session.cfcuser.memberData.memberID }>
				<cfset structAppend(local.strTemp,local.tmpFields)>
				<cfinvoke component="#application.objPayments#" method="refundAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.refundResponse">

				<cfif local.refundResponse.errCode NEQ ''>
					<cfset local.redirectLink = "#this.link.message#&ec=#local.refundResponse.errCode#&ecmsg=#URLEncodedFormat(local.refundResponse.errMsg)#">
					<cflocation url="#local.redirectLink#" addtoken="no">
				</cfif>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif listFindNoCase("subscriptions,registrants",arguments.event.getValue('tabMode',''))>
					top.MCModalUtils.hideModal();
				<cfelse>
					if (top.closeRefundPayment) top.closeRefundPayment();
					else top.MCModalUtils.hideModal();
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="refundProcessingFees" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=doRefundProcessingFees&mode=direct";

		arguments.event.paramValue('mid',0);
		arguments.event.paramValue('ptid',0);
		arguments.event.paramValue('profileid',0);
		</cfscript>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.transRefundPayment') is not 1>
			<cflocation url="#this.link.message#&ec=RFNP" addtoken="no">
		</cfif>

		<!--- get member info --->
		<cfset local.qryMember = application.objMember.getMemberInfo(arguments.event.getValue('mid'))>
		<cfif local.qryMember.recordcount is 0>
			<cflocation url="#this.link.message#&ec=RFMNF" addtoken="no">
		</cfif>

		<cfquery name="local.qryTransactionCheck" datasource="#application.dsn.membercentral.dsn#">
			select transactionID
			from dbo.tr_transactions
			where transactionID = <cfqueryparam value="#arguments.event.getValue('ptid')#" cfsqltype="CF_SQL_INTEGER">
			and ownedByOrgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryTransactionCheck.recordcount is 0>
			<cflocation url="#this.link.message#&ec=VTINF" addtoken="no">
		</cfif>

		<!--- get payment info --->
		<cfstoredproc procedure="tr_viewTransaction_payment" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryTransactionCheck.transactionID#">
			<cfprocresult name="local.qryTransaction" resultset="1">
			<cfprocresult name="local.qryTypeInfo" resultset="2">
			<cfprocresult name="local.qryCurrentAllocations" resultset="3">
			<cfprocresult name="local.qryGLActivity" resultset="4">
		</cfstoredproc>

		<cfset local.qryProcessingFeeSales = QueryFilter(local.qryCurrentAllocations, function(thisRow) { return arguments.thisRow.paymentFeeTypeID EQ 1; })>

		<cfquery name="local.qryProcessingFeeTotals" dbtype="query">
			SELECT SUM(allocAmount) AS totalAllocAmount, SUM(amount) AS totalAmount, MIN(detail) AS detail
			FROM [local].qryProcessingFeeSales
		</cfquery>
		
		<cfif local.qryProcessingFeeSales.recordCount>
			<cfset local.additionalPaymentFeeLabel = local.qryTypeInfo.processingFeeLabel>
			<cfif arguments.event.getValue('doRefundPFD',0) is 1>
				<cfset local.refundAmount = val(local.qryProcessingFeeTotals.totalAllocAmount)>

				<cfloop query="local.qryProcessingFeeSales">
					<cfset local.qrySale = getSaleInfo(orgID=arguments.event.getValue('mc_siteInfo.orgID'), saleTID=local.qryProcessingFeeSales.transactionID, paymentTID=local.qryTransaction.transactionID)>

					<!--- adjust revenue to 0, which will deallocate automatically --->
					<cfset arguments.event.setValue("tid",local.qrySale.transactionID)>
					<cfset arguments.event.setValue("amount",local.qrySale.allocAmountExcTax * -1)>
					<cfset saveAdjustedTransaction(event=arguments.event)>
				</cfloop>

				<!--- refund. Same day refunds will void the processing fee, but it is what it is. --->
				<cfset arguments.event.setValue("amount_#local.qryTransaction.transactionID#",local.refundAmount)>
				<cfset saveRefund(event=arguments.event)>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						if (top.reloadTransTab)
							top.reloadTransTab();
					</script>
					<div class="p-3">
						<div class="alert alert-success" role="alert">
							#dollarFormat(local.refundAmount)# has been refunded to credit card #local.qryTypeInfo.ccDetail#.
						</div>
						<div>
							<div class="mb-2">We:</div>
							<div>&bull; Adjusted the <b>#dollarformat(local.qryProcessingFeeTotals.totalAmount)# #local.qryProcessingFeeTotals.detail#</b> revenue to $0.00;</div>
							<div>&bull; Deallocated <b>#dollarformat(local.refundAmount)#</b> of this payment and refunded it back to the card.</div>
						</div>
					</div>
					</cfoutput>
				</cfsavecontent>

			<cfelse>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_refundProcessingFees.cfm">
				</cfsavecontent>
			</cfif>
		<cfelse>
			<cfset local.data = "Invalid Processing Fees.">
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doRefundProcessingFees" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('doRefundPFD',1)>

		<cfreturn refundProcessingFees(arguments.event)>
	</cffunction>

	<cffunction name="getSaleInfo" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="saleTID" type="numeric" required="true">
		<cfargument name="paymentTID" type="numeric" required="true">
	
		<cfset var qrySale = "">
	
		<!--- get sale info (cannot be adj/tax) --->
		<cfquery name="qrySale" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			IF OBJECT_ID('tempdb..##tmpPaymentTransactions') IS NOT NULL
				DROP TABLE ##tmpPaymentTransactions;
			IF OBJECT_ID('tempdb..##tmpSaleTransIDs') IS NOT NULL
				DROP TABLE ##tmpSaleTransIDs;
			IF OBJECT_ID('tempdb..##tmpTaxPayments') IS NOT NULL
				DROP TABLE ##tmpTaxPayments;
			
			CREATE TABLE ##tmpPaymentTransactions (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));
			CREATE TABLE ##tmpSaleTransIDs (transactionID int);
			CREATE TABLE ##tmpTaxPayments (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));
			
			declare @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
			declare @paymentTID int = <cfqueryparam value="#arguments.paymentTID#" cfsqltype="CF_SQL_INTEGER">;
			declare @saleTID int = <cfqueryparam value="#arguments.saleTID#" cfsqltype="CF_SQL_INTEGER">;
			declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
	
			INSERT INTO ##tmpPaymentTransactions (saleTransactionID, paymentTransactionID, allocatedAmount)
			EXEC dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=@saleTID;
	
			INSERT INTO ##tmpSaleTransIDs (transactionID)
			select distinct tTax.transactionID
			from dbo.tr_relationships as tr 
			inner join dbo.tr_transactions as tTax on tTax.transactionID = tr.transactionID 
				and tTax.statusID = 1
			where tr.orgID = @orgID 
			and tr.typeID = @tr_SalesTaxTrans 
			and tr.appliedToTransactionID = @saleTID;
	
			INSERT INTO ##tmpTaxPayments (saleTransactionID, paymentTransactionID, allocatedAmount)
			EXEC dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=NULL;
	
			select top 1 t.transactionID, t.detail, isnull(tax.cache_amountAfterAdjustment,0)+tsFull.cache_amountAfterAdjustment as amount, apos.allocatedAmount as allocAmountExcTax,
				apos.allocatedAmount+isnull(tax.cache_activePaymentAllocatedAmount,0)+isnull(tax.cache_pendingPaymentAllocatedAmount,0) as allocAmount,
				mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
				mAss2.company as assignedToMemberCompany
			from dbo.tr_transactions as t
			cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
			outer apply (
				select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
					sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
					sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
				from dbo.tr_relationships as tr 
				inner join dbo.tr_transactions as tTax on tTax.transactionID = tr.transactionID and tTax.statusID = 1
				inner join ##tmpTaxPayments as aposTax on aposTax.paymentTransactionID = @paymentTID
				cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,tTax.transactionID) as tsTaxFull 
				where tr.orgID = @orgID 
				and tr.typeID = @tr_SalesTaxTrans 
				and tr.appliedToTransactionID = t.transactionID
				) as tax
			inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = t.assignedToMemberID
			inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
			inner join ##tmpPaymentTransactions as apos on apos.paymentTransactionID = @paymentTID
			where t.ownedByOrgID = @orgID
			and t.typeID = 1
			and t.statusID = 1
			and t.transactionID = @saleTID;
	
			IF OBJECT_ID('tempdb..##tmpPaymentTransactions') IS NOT NULL
				DROP TABLE ##tmpPaymentTransactions;
			IF OBJECT_ID('tempdb..##tmpSaleTransIDs') IS NOT NULL
				DROP TABLE ##tmpSaleTransIDs;
			IF OBJECT_ID('tempdb..##tmpTaxPayments') IS NOT NULL
				DROP TABLE ##tmpTaxPayments;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn qrySale>
	</cffunction>
		
	<cffunction name="checkOutstandingBalance" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySales">
			select sum(paymentDueAmount) as paymentDue
			from fn_tr_salesWithAmountDue(<cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.orgid#" cfsqltype="CF_SQL_INTEGER">)
		</cfquery>

		<cfset local.data = { paymentdue=val(local.qrySales.paymentDue), success=true } >
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getOpenInvoicesAndDeferredSchedForAddSale" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.OpenInvoices = getOpenInvoicesForRevenueGLForMember(orgID=arguments.mcproxy_orgID, memberID=arguments.memberID, revenueGLAccountID=arguments.revenueGLAccountID)>

		<cfquery name="local.qryDeferred" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_tr_getDeferredGLAccountID(<cfqueryparam value="#arguments.revenueGLAccountID#" cfsqltype="CF_SQL_INTEGER">) as deferredGLAccountID
		</cfquery>

		<cfif len(local.qryDeferred.deferredGLAccountID)>
			<cfset local.isDeferred = 1>
		<cfelse>
			<cfset local.isDeferred = 0>
		</cfif>

		<cfset local.data = { arropeninvoices=local.OpenInvoices.arropeninvoices, isdeferred=local.isDeferred, success=true } >

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getOpenInvoicesForRevenueGLForMember" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.arrOpenInvoices = arrayNew(1)>
		
		<!--- get open invoices tied to member --->
		<cfquery name="local.qryInvoices" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @memberID int = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">;
			declare @orgID int = <cfqueryparam value="#arguments.orgid#" cfsqltype="CF_SQL_INTEGER">;

			IF OBJECT_ID('tempdb..##tblInvoices') IS NOT NULL
				DROP TABLE ##tblInvoices;
			CREATE TABLE ##tblInvoices (invoiceID int PRIMARY KEY);

			INSERT INTO ##tblInvoices (invoiceID)
			EXEC dbo.tr_getInvoicesAssociatedToMember @memberID=@memberID;

			select tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue, isnull(sum(it.cache_invoiceAmountAfterAdjustment),0) as InvAmt
			from (
				select distinct i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue
				from ##tblInvoices as invoices
				inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = invoices.invoiceID
				inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID and invs.status = 'Open'
				inner join dbo.organizations as o on o.orgID = i.orgID
				inner join dbo.tr_glAccounts as gl on gl.orgID = @orgID and gl.invoiceProfileID = i.invoiceProfileID
				where gl.glAccountID = <cfqueryparam value="#arguments.revenueGLAccountID#" cfsqltype="CF_SQL_INTEGER">
			) as tmp
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
			group by tmp.invoiceID, tmp.invoiceNumber, tmp.dateDue
			order by tmp.invoiceNumber;
		</cfquery>
		
		<cfloop query="local.qryInvoices">
			<cfset local.thisOpenInvoice = {
				invoiceID = local.qryInvoices.invoiceID,
				invoiceNumber = local.qryInvoices.invoicenumber,
				dueDateStr = dateformat(local.qryInvoices.dateDue,"mm/dd/yyyy"),
				invAmtStr = dollarformat(local.qryInvoices.invAmt) }>
			<cfset arrayAppend(local.arrOpenInvoices, local.thisOpenInvoice)>
		</cfloop>

		<cfset local.data = { arropeninvoices=local.arrOpenInvoices, success=true }>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="convertDeferredScheduledArrayToXML" access="public" output="false" returntype="xml">
		<cfargument name="arrSchedule" type="array" required="true">

		<cfset var local = structNew()>

		<cfsetting enablecfoutputonly="yes">
		<cfsavecontent variable="local.xmlSchedule">
			<cfoutput><rows></cfoutput>
			<cfloop array="#arguments.arrSchedule#" index="local.thisRow">
				<cfoutput><row amt="#local.thisRow.amt#" dt="#local.thisRow.dt#" /></cfoutput>
			</cfloop>
			<cfoutput></rows></cfoutput>
		</cfsavecontent>

		<cfreturn local.xmlSchedule>
	</cffunction>

	<cffunction name="downloadRefundStatement" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.strRefundStatement = CreateObject("component","transaction").generateRefundStatement(siteID=arguments.event.getValue('mc_siteinfo.siteid'), transactionID=arguments.event.getValue('tid',0), tmpFolder=local.strFolder.folderPath, encryptFile=true, refundStatementDesc = arguments.event.getValue('refundDesc',''))>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.strRefundStatement.refundStatementPath, displayName=ListLast(local.strRefundStatement.refundStatementPath,"/"), deleteSourceFile=1)>
		<cfsavecontent variable="local.data">
			<cfoutput>No refund statement generated.</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="emailRefundStatement" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objET = CreateObject("component","model.admin.emailTemplates.emailTemplates")>
		<cfset local.qryEmailTemplates = local.objET.getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="ETINVOICES")>
		<cfset local.qryEmailTemplateCategories = local.objET.getCategoriesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="ETINVOICES")>
		<cfset local.formlink = buildCurrentLink(arguments.event,"doSendCustomEmailRefundStatement") & "&mode=stream">

		<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('tid',0)#">
			<cfprocresult name="local.qryTransaction" resultset="1">
		</cfstoredproc>
		
		<cfset local.qryMember = application.objMember.getMemberInfo(memberid=local.qryTransaction.assignedTomemberID, orgid=local.qryTransaction.ownedByOrgID)>
		<cfset local.recipientEmailList = application.objMember.getMainEmail(memberID=local.qryTransaction.assignedToMemberID).email>

		<cfset local.refundFileNameNoExt = "Refund #local.qryMember.membernumber# #local.qryMember.firstname# #local.qryMember.lastname#">
		<cfset local.refundFileNameNoExt = replace(replace(rereplaceNoCase(local.refundFileNameNoExt,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL')>

		<cfset local.strEmailData = { resourceTitle='Refund Statement', templateEditorLabel='Compose your message. <span class="fr" style="display:block;font-size:1.1em;"><i class="fa-regular fa-paperclip fa-lg"></i> We''ll automatically include a PDF attachment of the refund statement.</span>',
										saveTemplateDesc='<b>Before we e-mail this refund statement,</b> should we save this message as a template for future use?',
										attachmentName='#local.refundFileNameNoExt#.pdf' }>

		<cfset local.strFormFields = structNew()>
		<cfset structInsert(local.strFormFields, 'memberID', local.qryTransaction.assignedTomemberID)>
		<cfset structInsert(local.strFormFields, 'transactionID', arguments.event.getValue('tid',0))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_emailRefundStatement.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getPreviewCustomEmailRefundStatementMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="transactionID" type="string" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
			<cfprocresult name="local.qryTransaction" resultset="1">
		</cfstoredproc>

		<cfif local.qryTransaction.recordcount is 0>
			<cfset local.retStruct.success = false>
			<cfreturn local.retStruct>
		</cfif>

		<!--- message prep --->
		<cfset local.parseContent = parseRefundStatementContentWithMergeCodes(memberID=local.qryTransaction.assignedTomemberID, content=arguments.templateContent, subjectLine=arguments.subjectLine, qryTransaction=local.qryTransaction)>
		<cfset local.emailTitle = "#application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).orgName#">

		<cfset local.retStruct.templateDisp = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.parseContent.content, sitecode=arguments.mcproxy_siteCode)>
		<cfset local.retStruct.subjectline = urlEncodedFormat(local.parseContent.subjectLine)>
		<cfset local.retStruct.success = true>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="sendCustomRefundStatementTestEmailMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="transactionID" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="templateContent" type="string" required="true">
		<cfargument name="subjectLine" type="string" required="true">
		<cfargument name="emailFromName" type="string" required="true">
		<cfargument name="emailFrom" type="string" required="true">
		<cfargument name="refundStatementDesc" type="string" required="false" default=''>		

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.transactionID#">
			<cfprocresult name="local.qryTransaction" resultset="1">
		</cfstoredproc>

		<cfif local.qryTransaction.recordcount is 0>
			<cfset local.retStruct.success = false>
			<cfreturn local.retStruct>
		</cfif>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode)>

		<!--- message prep --->
		<cfset local.parseContent = parseRefundStatementContentWithMergeCodes(memberID=arguments.memberID, content=arguments.templateContent, subjectLine=arguments.subjectLine, qryTransaction=local.qryTransaction)>
		<cfset local.emailTitle = local.mc_siteInfo.orgName>

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.mcproxy_siteCode)>
		<cfset local.strRefundStatement = CreateObject("component","transaction").generateRefundStatement(siteID=local.mc_siteInfo.siteID, transactionID=val(arguments.transactionID), 
			tmpFolder=local.strFolder.folderPath, encryptFile=true, refundStatementDesc = arguments.refundStatementDesc)>
		
		<cfif FileExists(local.strRefundStatement.refundStatementPath) and len(session.cfcuser.memberData.email)>
			<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.emailFromName, email=local.mc_siteinfo.networkEmailFrom },
				emailto=[ { name:'', email:session.cfcuser.memberData.email } ],
				emailreplyto=arguments.emailFrom,
				emailsubject="TEST: #local.parseContent.subjectLine#",
				emailtitle=local.emailTitle,
				emailhtmlcontent=local.parseContent.content,
				emailAttachments=[{ file=local.strRefundStatement.fileName, folderpath=local.strRefundStatement.folderPath }],
				siteID=local.mc_siteinfo.siteID,
				memberID=arguments.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EMAILREFUND"),
				sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID,
				isTestMessage=1
			)>
			<cfset local.retStruct.success = true>
		<cfelse>
			<cfset local.retStruct.success = false>
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="parseRefundStatementContentWithMergeCodes" access="private" output="no" returntype="struct">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="content" type="string" required="yes">
		<cfargument name="subjectLine" type="string" required="yes">
		<cfargument name="qryTransaction" type="query" required="yes">

		<cfset var local = StructNew()>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>

		<cfset arguments.content = urlDecode(arguments.content)>
		<cfset arguments.subjectLine = urlDecode(arguments.subjectLine)>

		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID, 
			memberID=val(local.memberInfo.memberID), content="#arguments.content##arguments.subjectLine#")>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=encodeForHTML(local.memberInfo.FirstName), middleName=encodeForHTML(local.memberInfo.MiddleName),
										lastName=encodeForHTML(local.memberInfo.LastName), company=encodeForHTML(local.memberInfo.Company), suffix=encodeForHTML(local.memberInfo.Suffix),
										prefix=encodeForHTML(local.memberInfo.Prefix), membernumber=local.memberInfo.membernumber, professionalSuffix=encodeForHTML(local.memberInfo.professionalSuffix), 
										orgcode=local.memberInfo.orgcode, siteID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID,
										hostname=local.thisHostname, useRemoteLogin=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).useRemoteLogin }>
		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>	
		</cfloop>
		
		<cfset local.strArgs = { content=arguments.content, memberdata=local.tempMemberData, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode, sitecode=session.mcStruct.siteCode }>
		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset local.strArgs = { content=arguments.subjectLine, memberdata=local.tempMemberData, orgcode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgcode, sitecode=session.mcStruct.siteCode }>
		<cfset local.strMergedSubjectContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs)>

		<cfset local.strReturn = { content=local.strMergedContent.content, subjectLine=local.strMergedSubjectContent.content }>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="doSendCustomEmailRefundStatement" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfstoredproc procedure="tr_viewTransaction_refund" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('transactionID',0)#">
			<cfprocresult name="local.qryTransaction" resultset="1">
		</cfstoredproc>

		<cfif local.qryTransaction.recordcount is 0>
			<cfset local.data = '<h4>No Recipients Found</h4>'>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<!--- message prep --->
		<cfset local.templateContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('templateContent',''), siteid=arguments.event.getValue('mc_siteInfo.siteid'))>	
		<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.orgName')#">
		<cfset local.emailSubject = arguments.event.getTrimValue('emailSubject','')>
		<cfset local.recipientEmailList = arguments.event.getTrimValue('recipientEmailList','')>
		<cfset local.emailTemplateCategoryID = arguments.event.getTrimValue('selCategory',0)>
		<cfset local.templateAndSubjectContentToParse = "#local.emailSubject##local.templateContent#">
		<cfset local.emailContentWrapper = application.objEmailWrapper.wrapMessage(emailTitle=local.emailTitle, emailContent=local.templateContent, sitecode=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<cfif len(local.recipientEmailList)>
			<cfloop list="#local.recipientEmailList#" index="local.thisEmail" delimiters=";">
				<cfif NOT (len(local.thisEmail) AND isValid("regex",local.thisEmail,application.regEx.email))>
					<cfthrow message="Invalid recipient email address.">
					<cfbreak>
				</cfif>
			</cfloop>
		<cfelse>
			<cfthrow message="Invalid recipient email address.">
		</cfif>

		<cfif application.MCEnvironment neq "production"> 
			<cfset local.deliveryReportEmail = "<EMAIL>">
		<cfelseif len(session.cfcuser.memberData.email)>
			<cfset local.deliveryReportEmail = session.cfcuser.memberData.email>
		<cfelse>
			<cfset local.deliveryReportEmail = ''>
		</cfif>

		<!--- if including merge codes that require coldfusion, we need to mark recipient as not ready so we can insert the field below --->
		<cfset local.hasExtendedMergeCodes = false>
		<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=arguments.event.getValue('mc_siteinfo.siteid'), rawContent=local.templateAndSubjectContentToParse, extraMergeTagList='')>
		<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
			<cfset local.hasExtendedMergeCodes = true>
		</cfif>

		<!--- generating refund statement --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'))>
		<cfset local.strRefundStatement = CreateObject("component","transaction").generateRefundStatement(siteID=arguments.event.getValue('mc_siteInfo.siteid'), transactionID=val(local.qryTransaction.transactionID), 
										tmpFolder=local.strFolder.folderPath, encryptFile=true, refundStatementDesc = arguments.event.getValue('refundDesc',''))>
		<cfset local.strRefundStatement.folderPath = local.strFolder.folderPath>

		<!--- create message and recipients with merge codes --->
		<cftry>
			<cfif arguments.event.getValue('saveTemplateOption',0) is 1 and local.emailTemplateCategoryID is 0>
				<cfset local.EmailTemplateAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailTemplateAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
			</cfif>

			<cfquery name="local.qryEmailRefundStatementRecipients" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @resourceTypeID int, @parentSiteResourceID int, @emailSubject varchar(200),
						@templateName varchar(300), @rawContent varchar(max), @contentID int, @siteResourceID int, @toolType varchar(200),
						@memberID int, @contentVersionID int, @controllingSiteResourceID int, @categoryTreeID int, @categoryTreeCode varchar(20),
						@categoryTreeName varchar(100), @emailTemplateCategoryID int, @emailTemplateID int, @emailFromName varchar(200), @emailFrom varchar(200),
						@emailContentWrapper varchar(max), @assignedTomemberID int, @transactionID varchar(max), @deliveryReportEmail varchar(200),
						@categoryName varchar(200), @sendOnDate datetime, @recipientEmailList varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					SET @emailSubject = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.emailSubject#">;
					SET @rawContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.templateContent#">;
					SET @emailFromName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailFromName','')#">;
					SET @emailFrom = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('emailReplyTo','')#">;
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @emailContentWrapper = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailContentWrapper#">;
					SET @assignedTomemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryTransaction.assignedTomemberID#">;
					SET @transactionID = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.qryTransaction.transactionID#">;
					SET @recipientEmailList = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.recipientEmailList#">,'');
					SET @toolType = 'TransactionAdmin';

					<cfif len(local.deliveryReportEmail)>
						SET @deliveryReportEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.deliveryReportEmail#">;
					</cfif>

					<cfif arguments.event.getValue('massEmailScheduling','') eq 'later' and len(arguments.event.getValue('emailDateScheduled',''))>
						SET @sendOnDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#parseDateTime(replace(arguments.event.getValue('emailDateScheduled'),' - ',' '))#">;
					<cfelse>
						SET @sendOnDate = getDate();
					</cfif>
				
					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
				
					SELECT @parentSiteResourceID = st.siteResourceID 
					FROM dbo.admin_tooltypes tt
					INNER JOIN dbo.admin_siteTools st ON st.toolTypeID = tt.toolTypeID
						AND st.siteID = @siteID
						AND tt.toolType = @toolType
					INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = st.siteResourceID
						AND sr.siteResourceStatusID = 1;

					BEGIN TRAN;
						<!--- email refund statement without using template --->
						<cfif arguments.event.getValue('saveTemplateOption',0) is 0>
							EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, @parentSiteResourceID=@parentSiteResourceID, 
								@siteResourceStatusID=1, @isHTML=1, @languageID=1, @isActive=1, @contentTitle=@emailSubject, @contentDesc='', 
								@rawContent=@rawContent, @memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_content as c 
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where c.contentID = @contentID;
						
						<!--- create a new email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 1>
							<cfif local.emailTemplateCategoryID is 0>
								set @categoryTreeCode = 'ETINVOICES';
								set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EmailTemplateAdminSRID#">;
								set @categoryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newCategoryName','')#">;

								select @categoryTreeName = categoryTreeName 
								from dbo.cms_categoryTrees 
								where controllingSiteResourceID = @controllingSiteResourceID 
								and categoryTreeCode = @categoryTreeCode;

								select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@controllingSiteResourceID,@categoryTreeName);

								EXEC dbo.cms_createCategory @categoryTreeID=@categoryTreeID, @categoryName=@categoryName, @categoryDesc='', @categoryCode='', 
									@parentCategoryID=NULL, @contributorMemberID=@memberID, @categoryID=@emailTemplateCategoryID OUTPUT;
							<cfelse>
								set @emailTemplateCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.emailTemplateCategoryID#">;
							</cfif>

							SET @templateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('templateName','')#">;
							
							EXEC dbo.et_createEmailTemplate @templateTypeCode='ckeditor', @templateName=@templateName, @templateDescription='', 
								@categoryID=@emailTemplateCategoryID, @rawContent=@rawContent, @subjectLine=@emailSubject, 
								@emailFromName=@emailFromName, @emailFrom=@emailFrom, @createdByMemberID=@memberID, 
								@siteID=@siteID, @templateID=@emailTemplateID OUTPUT;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.et_emailTemplates as et
							inner join dbo.cms_content as c on c.contentID = et.contentID
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where et.templateID = @emailTemplateID;

						<!--- update existing email template --->
						<cfelseif arguments.event.getValue('saveTemplateOption',0) is 2>
							set @emailTemplateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fEmailTemplateID',0)#">;

							update dbo.et_emailTemplates
							set subjectLine = @emailSubject,
								emailFromName = @emailFromName,
								emailFrom = @emailFrom
							where templateID = @emailTemplateID;

							select @contentID = contentID 
							from dbo.et_emailTemplates
							where templateID = @emailTemplateID;

							EXEC dbo.cms_updateContent @contentID=@contentID, @languageID=1, @isHTML=1, @contentTitle=@emailSubject, 
								@contentDesc='', @rawcontent=@rawcontent, @memberID=@memberID;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.cms_content as c 
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where c.contentID = @contentID;
						</cfif>
					COMMIT TRAN;

					-- insert and return email recipients
					EXEC dbo.ams_emailRefundStatement @siteID=@siteID, @memberID=@assignedTomemberID, @transactionID=@transactionID, @messageToParse=@rawContent, 
						@messageWrapper=@emailContentWrapper, @emailTagTypeID=NULL, @emailFromName=@emailFromName, @emailReplyTo=@emailFrom, @emailSubject=@emailSubject, 
						@contentVersionID=@contentVersionID, @recordedByMemberID=@memberID, @deliveryReportEmail=@deliveryReportEmail, @overrideEmailList=@recipientEmailList, 
						@sendOnDate=@sendOnDate, @markRecipientAsReady=0;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients for message", cfcatch.detail)>
				<cfset local.errorCode = 'noemailrecipient'>
			<cfelse>
				<cfset local.errorCode = ''>
			</cfif>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfreturn returnAppStruct(createObject("component","model.admin.transactions.invoiceAdmin").showMessage(errorCode=local.errorCode),"echo")>
		</cfcatch>
		</cftry>

		<!--- insert email attachments --->
		<cfif FileExists(local.strRefundStatement.refundStatementPath)>
			<cftry>

				<cfset local.pathFromS3Uploader = replacenocase(local.strRefundStatement.refundStatementPath,application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader)>
				<cfset local.pathFromS3Uploader = replace(local.pathFromS3Uploader,'/','\','all')>

				<cfquery name="local.qryInsertEmailAttachments" datasource="#application.dsn.platformMail.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @fileName varchar(400), @localDirectory varchar(400), @s3keyMod varchar(4), @messageStatusIDQueued int,
							@objectKey varchar(400), @filePathForS3Upload varchar(400), @attachmentID int, @s3bucketName varchar(100), 
							@s3UploadReadyStatusID int, @nowDate datetime = GETDATE();
						DECLARE @tmpRecipients TABLE (recipientID int);

						SET @fileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strRefundStatement.displayName#">;
						SET @localDirectory = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strRefundStatement.folderPath#">;
						SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;
						SET @s3bucketName = 'platformmail-membercentral-com';

						SELECT @messageStatusIDQueued = statusID 
						FROM dbo.email_statuses 
						WHERE statusCode = 'Q';

						EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='s3Upload', @queueStatus='readyToProcess', @queueStatusID=@s3UploadReadyStatusID OUTPUT;

						INSERT INTO @tmpRecipients (recipientID)
						select listitem
						from memberCentral.dbo.fn_intListToTable('0#valueList(local.qryEmailRefundStatementRecipients.recipientID)#',',');

						BEGIN TRAN;
							EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@localDirectory, @attachmentID=@attachmentID OUTPUT;

							INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
							select recipientID, @attachmentID
							from @tmpRecipients;

							<!--- insert to s3 upload queue --->
							SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
							SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @fileName);

							IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
								INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
								VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);

							<cfif NOT local.hasExtendedMergeCodes>
								UPDATE mrh 
								SET mrh.emailStatusID = @messageStatusIDQueued
								FROM dbo.email_messageRecipientHistory as mrh
								INNER JOIN @tmpRecipients as tmp on tmp.recipientID = mrh.recipientID;
							</cfif>
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn returnAppStruct(createObject("component","model.admin.transactions.invoiceAdmin").showMessage(errorCode=''),"echo")>
			</cfcatch>
			</cftry>
		</cfif>

		<cfif local.hasExtendedMergeCodes>
			<cftry>
				<cfset createObject("component","model.admin.transactions.invoiceAdmin").replaceDetectedExtendedMergeCodes(event=arguments.event, qryRecipients=local.qryEmailRefundStatementRecipients, strRecipientExtMergeTags=local.strRecipientExtMergeTags)>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfreturn returnAppStruct(createObject("component","model.admin.transactions.invoiceAdmin").showMessage(errorCode=''),"echo")>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry.</h4>
				<div>
					<cfswitch expression="#arguments.event.getValue('ec','')#">
						<cfcase value="IMPANNP">You do not have the necessary permissions to import payments from Authorize.Net.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="VTINF">That transaction could not be found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-VTINF</cfcase>
						<cfcase value="ET">That transaction could not be found. Only pending payments can be accepted.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ET</cfcase>
						<cfcase value="SATMAX">You may not apply an adjustment that would result in a negative sale transaction.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SATMAX</cfcase>
						<cfcase value="SATADJ">We were unable to apply an adjustment to this transaction.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SATADJ</cfcase>
						<cfcase value="SATNOSALE">We were unable to apply an adjustment to this transaction. Only sales can be adjusted.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SATNOSALE</cfcase>
						<cfcase value="SATADJPROMOSALE">We were unable to apply an adjustment to this coupon redeemed transaction.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SATADJPROMOSALE</cfcase>
						<cfcase value="SATAMTZ">You may not apply an zero-dollar adjustment.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SATAMTZ</cfcase>
						<cfcase value="SATDEFSCH">Unable to save adjustment. The recognition schedule was not complete.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SATDEFSCH</cfcase>
						<cfcase value="SVTOKVT">We were unable to void or record the void of this transaction.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SVTOKVT</cfcase>
						<cfcase value="SVTOKNO">We were unable to void this transaction.<br/>Hints: credit card/bank draft payments that have settled cannot be voided. Be sure to only void transactions that were data entry errors or mistakes.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVTOKNO</cfcase>
						<cfcase value="SALEMEMNF">The member selected for the sale was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SALEMEMNF</cfcase>
						<cfcase value="MNGCMEMNF">The member selected was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-MNGCMEMNF</cfcase>
						<cfcase value="SVSALEMEMNF">Unable to save sale. The member assigned to the sale was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEMEMNF</cfcase>
						<cfcase value="SVSALEDATE">Unable to save sale. The sale date was invalid.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEDATE</cfcase>
						<cfcase value="SVSALEDETL">Unable to save sale. The sale detail was invalid.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEDETL</cfcase>
						<cfcase value="SVSALEAMT">Unable to save sale. The sale amount was invalid.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEAMT</cfcase>
						<cfcase value="SVSALEGL">Unable to save sale. The revenue GL was invalid.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEGL</cfcase>
						<cfcase value="SVSALETAXST">Unable to save sale. State/Province tax information was not provided.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALETAXST</cfcase>
						<cfcase value="SVSALETAXPC">Unable to save sale. Postal Code tax information was not provided.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALETAXPC</cfcase>
						<cfcase value="SVSALEDEFSCH">Unable to save sale. The recognition schedule was not complete.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVSALEDEFSCH</cfcase>
						<cfcase value="PAYMEMNF">The member selected for the payment was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-PAYMEMNF</cfcase>
						<cfcase value="SVPAYNOMP">Unable to save payment. The selected payment profile is not available.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVPAYNOMP</cfcase>
						<cfcase value="SVPAYMEMNF">Unable to save payment. The member selected for the payment was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SVPAYMEMNF</cfcase>
						<cfcase value="RFMNF">The member selected for this refund was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-RFMNF</cfcase>
						<cfcase value="SRFPNRF">The payment selected for this refund does not have enough refundable funds.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SRFPNRF</cfcase>
						<cfcase value="RAHCB,RAHFAIL,RAHRFNM1,RAHVDNM1,RAHTWOFAIL,RAHVDNM2,RAHVDPAY,RAHVDPAY2,REFPAYALLOC1,RAHVDREF2,RAHFAIL1">We encountered a problem.<br/>#arguments.event.getValue('ecmsg','')#</cfcase>
						<cfcase value="SVSALE">The sale could not be recorded properly.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SVSALE</cfcase>
						<cfcase value="ALLOCPAYBYINF">The invoice selected was not found, is not closed, or has no amount due.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-ALLOCPAYBYINF</cfcase>
						<cfcase value="ALLOCPAYBYINOC">The invoice selected could not be closed.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-ALLOCPAYBYINOC</cfcase>
						<cfcase value="CAAPMNF">The member selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-CAAPMNF</cfcase>
						<cfcase value="DEALLOCPAYBYSMNF">The member selected for this deallocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-DEALLOCPAYBYSMNF</cfcase>
						<cfcase value="DEALLOCPAYBYSNF">The payment selected for this deallocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-DEALLOCPAYBYSNF</cfcase>
						<cfcase value="DEALLOCPAYBYSPNF">The revenue transaction selected was not found or is not currently allocated to the payment selected.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-DEALLOCPAYBYSPNF</cfcase>
						<cfcase value="DEALLOCPAYBYSALL">Deallocation from this sale was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-DEALLOCPAYBYSALL</cfcase>
						<cfcase value="ALLOCPAYBYIMNF">The member selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYIMNF</cfcase>
						<cfcase value="ALLOCPAYBYPMNF">The member selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYPMNF</cfcase>
						<cfcase value="ALLOCPAYBYSMNF">The member selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYSMNF</cfcase>
						<cfcase value="ALLOCPAYBYPPNF">The payment selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYPPNF</cfcase>
						<cfcase value="ALLOCPAYBYSNF">The sales selected for this allocation were not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYSNF</cfcase>
						<cfcase value="ALLOCPAYBYPNFSV">The payment selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYPNFSV</cfcase>
						<cfcase value="ALLOCPAYBYIALL">Allocation to this invoice was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-ALLOCPAYBYIALL</cfcase>
						<cfcase value="ALLOCPAYBYPALL">Allocation to this invoice was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-ALLOCPAYBYPALL</cfcase>
						<cfcase value="ALLOCPAYBYSALL">Allocation to this sale was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-ALLOCPAYBYSALL</cfcase>
						<cfcase value="ALLOCPAYBYPMNFSV">The member selected for this allocation was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYBYPMNFSV</cfcase>
						<cfcase value="ALPAYBYISVTTL">You may not allocate more funds than the specified available amount to allocate.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALPAYBYISVTTL</cfcase>
						<cfcase value="ALPAYBYPSVTTL">You may not allocate more funds than the specified available amount to allocate.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALPAYBYPSVTTL</cfcase>
						<cfcase value="ALPAYBYSSVTTL">You may not allocate more funds than the specified available amount to allocate.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALPAYBYSSVTTL</cfcase>
						<cfcase value="ALPAYBYPSVINVTTL">You may not allocate more funds than the specified available amount to allocate for any particular invoice.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALPAYBYPSVINVTTL</cfcase>
						<cfcase value="VITNF">That invoice could not be found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-VITNF</cfcase>
						<cfcase value="UTCB">Unable to create batch.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-UTCB</cfcase>
						<cfcase value="UTCB2">Unable to create batch.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-UTCB2</cfcase>
						<cfcase value="UTAPT">Unable to accept payment transaction.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-UTAPT</cfcase>
						<cfcase value="CBNF">That transaction was not found and could not be marked with chargeback/NSF.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-CBNF</cfcase>
						<cfcase value="SCBNF">That transaction was not found and could not be marked with chargeback/NSF.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SCBNF</cfcase>
						<cfcase value="SVCBCB">Unable to create necessary batch for chargeback.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SVCBCB</cfcase>
						<cfcase value="DIANP">Unable to create necessary batch for Authorize.Net payment.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-DIANP</cfcase>
						<cfcase value="SCBCTNSF">Unable to record chargeback.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SCBCTNSF</cfcase>
						<cfcase value="WONF">That transaction was not found and could not be written off.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-WONF</cfcase>
						<cfcase value="SWONF">That transaction was not found and could not be written off.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SWONF</cfcase>
						<cfcase value="SWOWOS">Write-off of this transaction was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SWOWOS</cfcase>
						<cfcase value="SWOWOP">Write-off of this transaction was not successful.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SWOWOP</cfcase>
						<cfcase value="VTNF">That transaction was not found and could not be voided.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-VTNF</cfcase>
						<cfcase value="MNGCNP">You do not have the necessary permissions to manage a member's payment methods.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="PAYMEMNP">You do not have the necessary permissions to add a payment.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="ALLOCNP">You do not have the necessary permissions to allocate payments.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="RFNP">You do not have the necessary permissions to refund payments.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="CBNP">You do not have the necessary permissions to record NSF/chargebacks.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="WONP">You do not have the necessary permissions to record Write Offs.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="WONNP">You do not have the necessary permissions to record Negative Write Offs.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="SALENP">You do not have the necessary permissions to record a manual sale.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="ADJUSTNP">You do not have the necessary permissions to record a manual adjustment of revenue.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="ADJUSTNF">That transaction was not found. Only sale transactions can be adjusted.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ADJUSTNF</cfcase>
						<cfcase value="ADJUSTMNF">That transaction was not found. Only sale transactions can be adjusted.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ADJUSTMNF</cfcase>
						<cfcase value="CHSCHNF">That transaction was not found. Only transactions with existing schedules can be changed.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-CHSCHNF</cfcase>
						<cfcase value="CHSCHWO">Only transactions with existing schedules that have not been written off can be changed.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-CHSCHWO</cfcase>
						<cfcase value="CHSCHMNF">That transaction was not found. Only transactions with existing schedules can be changed.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-CHSCHMNF</cfcase>
						<cfcase value="SRSDEFSCH">Unable to save recognition schedule. The recognition schedule was not complete.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-SRSDEFSCH</cfcase>
						<cfcase value="SRSSP">Unable to save recognition schedule.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SRSSP</cfcase>
						<cfcase value="VOIDNP">You do not have the necessary permissions to void a transaction.<br/><br/>Should you need assistance, contact your site administrator.</cfcase>
						<cfcase value="ADDPAYPA">Not enough information was provided to add a payment.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ADDPAYPA</cfcase>
						<cfcase value="ADDPAYNOM">Not enough information was provided to add a payment.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ADDPAYNOM</cfcase>
						<cfcase value="ADDPAYUCNOM">Not enough information was provided to use this credit.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ADDPAYUCNOM</cfcase>
						<cfcase value="PAYMEMUCNF">The member selected for the payment was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-PAYMEMUCNF</cfcase>
						<cfcase value="ALLOCPAYINF">That invoice either was not found or has no amount due.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYINF</cfcase>
						<cfcase value="ALLOCPAYMNF">The member has no associated invoices that are due.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYMNF</cfcase>
						<cfcase value="ALLOCPAYSNF">That sale either was not found or has no amount due.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-ALLOCPAYSNF</cfcase>
						<cfcase value="VCBDMEMNF">The member selected was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-VCBDMEMNF</cfcase>
						<cfcase value="RECLASSNF">That transaction was not found. Only sale transactions can be reclassified.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-RECLASSNF</cfcase>
						<cfcase value="RECLASSMNF">That member was not found.<br/><br/>Should you need assistance, contact MemberCentral Support and note this exact error code: TA-RECLASSMNF</cfcase>
						<cfcase value="SVRECLASS">Unable to split or reclass this sale.<br/><br/>We have just notified MemberCentral Support of this issue.<br/>For reference, note this exact error code: TA-SVRECLASS</cfcase>
						<cfdefaultcase>An error has occurred. Contact MemberCentral for assistance.</cfdefaultcase>
					</cfswitch>
				</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>