// array to hold bucket instances
var bucketarray = new Array();
var isSearchReportReadyIntervalID = null;

//tsDocView Globals
var tsDocumentViewerControl = null;
var tsDocumentViewerAutoShowDocumentOnLoad = null;
var tsDocumentViewerHeight = 500;
var defaultBucketCartItemTypeID = null;
var tsDownloadableDocumentIDs = new Object();
var tsDocumentViewerCurrentDocumentID = null;
var tsActiveDocViaSearchID = null;
var tsActiveDocItemTypeID = null;
var TSDocumentViewerAllowAddToCart = true;
var TSDocumentViewerUseVersion2 = false;

var displayArea_mainscreen;
var displayArea_docviewer;
var displayArea_inlineCart;

var cart_LoadedInDiv = false;
var cart_divToShowWhenCloseCart = null;

var vars_bid = 0;
var vars_siteid = 0;
var vars_sitecode = "";
var vars_sid = 0;
var vars_spiu = '/assets/common/';
var vars_caserefs = [];

window.onbeforeunload = b_cancelPendingRequests;

//load defaults
function loadSearchGlobals(siteid,bid,spiu,caserefs,sitecode) {
	vars_bid = bid;
	vars_siteid = siteid;
	vars_spiu = spiu;
	vars_caserefs = caserefs;
	vars_sitecode = sitecode;
}

function setSearchID(sid) {
	if ($.isNumeric(sid) && sid > 0)
		vars_sid = parseInt(sid);
}

function getSearchID() {
	return vars_sid;
}

// search bucket object
function searchbucket(siteid,bid,btid) {
	this.siteid = siteid;
	this.bid = bid;
	this.btid = btid;
	this.req = null;
	bucketarray[bucketarray.length] = this;
}

// to cancel all pending requests, loop backwards to we dont run into race condition
function b_cancelPendingRequests() {
	for (var i=bucketarray.length-1; i >= 0; i--) {
		if (bucketarray[i].req != null) { bucketarray[i].req.xhr.abort(); bucketarray[i].req = null; }
	}
}

// general bucket functions 
function b_getObjFromBid(bid) {
	for (var i=0; i < bucketarray.length; i++) {
		if (bucketarray[i].bid == bid) {
			return bucketarray[i];
			break;
		}
	}
	return null;
}

// trim all text fields and remove http:// from all text fields (since we ban ips where http:// is submitted in these fields)
function b_trimAllTxt(theForm) {
	for (var i=0; i < theForm.elements.length; i++) {
		if (theForm.elements[i].type == 'text') 
			theForm.elements[i].value = theForm.elements[i].value.replace(/^\s*|\s*$/g,"").replace(/http:\/\//g,"");
	}
}

function b_addCommas(nStr) {
	nStr += '';
	var rgx = /(\d+)(\d{3})/;
	while (rgx.test(nStr)) nStr = nStr.replace(rgx, '$1' + ',' + '$2');
	return nStr;
}

function b_getTopBannerResult(r) {
	if (r.success && r.success == 'true') {
		var tssearchbar = $('#tssearchbar')[0];
		var searchBarMobile = $('#searchBarMobile')[0];
		$(tssearchbar).empty();
		$(searchBarMobile).empty();
		for (var thisEl = 0; thisEl < r.resultarr.length; thisEl++) {
			tssearchbar.innerHTML += '<li id="nav_' + r.resultarr[thisEl].tab + '">' + r.resultarr[thisEl].tablink + '</li>';
			/*mobile search bar*/
			searchBarMobile.innerHTML += '<a id="' + r.resultarr[thisEl].tab + '" class="btn btn-large" href="' + r.resultarr[thisEl].url + '"><i class="' + r.resultarr[thisEl].icon + ' icon-large"></i></a>';
			
			if(r.resultarr[thisEl].tab == 'VC'){
				if(r.resultarr[thisEl].count !=undefined && r.resultarr[thisEl].count != null 
				&& r.resultarr[thisEl].count !='' && r.resultarr[thisEl].count != 0){
					$('.myCartWrap span').html(r.resultarr[thisEl].count);
					$('.myCartWrap span').show();
					$('#searchBarMobile a#'+ r.resultarr[thisEl].tab).append('<span id="docCartCountDisplay" class="badge badge-inverse">' + r.resultarr[thisEl].count + '</span>');
				}else{
					$('.myCartWrap span').html(0);
					$('.myCartWrap span').hide();
				}
			}
		}
		VCIndex = r.resultarr.findIndex(function( obj ) {
			return obj.tab == 'VC';
		});
		if(VCIndex == -1){
			$('.myCartWrap span').html(0);
			$('.myCartWrap span').hide();
		}
	}
}

function b_doSearchPN(sid,start,sorttype,filter,postfilter) {
	document.getElementById('bk_content').innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
	var obj = b_getObjFromBid(vars_bid);
	b_doSearchResults(obj,sid,start,sorttype,filter,postfilter);
}

function b_doSearchFilter(sid,filter,postfilter) {
	document.getElementById('bk_content').innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
	var obj = b_getObjFromBid(vars_bid);
	b_doSearchResults(obj,sid,1,'',filter,postfilter);
}

function b_doSearchSort(sid,sorttype,filter,postfilter) {
	document.getElementById('bk_content').innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
	var obj = b_getObjFromBid(vars_bid);
	if (typeof filter != 'undefined' && typeof postfilter != 'undefined' ) {
		b_doSearchResults(obj,sid,1,sorttype,filter,postfilter);
	}
	if (typeof filter != 'undefined')
		b_doSearchResults(obj,sid,1,sorttype,filter);
	else
		b_doSearchResults(obj,sid,1,sorttype);
}

// trialsmith docs
function downloadDoc(docid) {
	self.name = "popupX";
	remote = open('/?pg=tsDocDownload&da=download&mode=direct&did=' + docid, "docViewer", "scrollbars=no,resizable,width=500,height=400");
}

function showDownload(documentID){
	$.colorbox( {innerWidth:750, innerHeight:400, href:'/?pg=tsDocDownload&da=download&mode=direct&did=' + documentID, iframe:true, overlayClose:false} );
}

// verdicts
function viewVerdict(vid) {
	remote = open('/?pg=myDocuments&tab=VPV&verdictid=' + vid, "docViewer", "scrollbars=yes,resizable,width=600,height=400");
}

// da reports
function viewDAReport(rptid) {
	remote = open('/?pg=myDocuments&tab=VDA&mode=direct&reportid=' + rptid, "docViewer", "scrollbars=yes,resizable,width=725,height=550");
}

// daubert reports
function viewDTReport(rptid) {
	remote = open('/?pg=myDocuments&tab=VDT&mode=direct&reportid=' + rptid, "docViewer", "scrollbars=yes,resizable,width=725,height=650");
}

// daubert challenge study expand
function toggleDCSExpert(xid) {
	const expdetail = $('#row'+xid+'detail');
	const togglebtn = $('#row'+xid+'summary').find('.exptogglebtn');
	const togglebtnchevron = togglebtn.find('i');
	
	$('.bk_expertcard').removeClass('bk-card-selected');
	$('.bk_expertdetail:not(#row'+xid+'detail)').hide();
	$('.bk_expertsummary:not(#row'+xid+'summary)').find('i.icon-chevron-down').toggleClass('icon-chevron-down icon-chevron-right');
	expdetail.slideToggle();
	
	if (togglebtnchevron.hasClass('icon-chevron-right')) {
		togglebtnchevron.removeClass('icon-chevron-right').addClass('icon-chevron-down');
		expdetail.closest('.bk_expertcard').addClass('bk-card-selected');
	} else {
		togglebtnchevron.removeClass('icon-chevron-down').addClass('icon-chevron-right');
	}
}
function showNotLoggedInDCSRpt() {
	$('#bk_expertsList').hide();
	$('#bk_notLoggedIn').show(300);
	$('html, body').animate({
		scrollTop: $('#bk_notLoggedIn').offset().top - 175
	}, 400);
}
function orderDCSReport(expid,rpt) {
	if ($('#bk_notLoggedIn').length) {
		showNotLoggedInDCSRpt();
		return false;
	}

	const orderDCSReport_init = function() {
		if (!$('.mc-modal-overlay').length) $('body').append('<div class="mc-modal-overlay"></div>');
		
		$('.mc-modal,.mc-modal-overlay').addClass('active');
		$('.mc-modal').addClass('bk-bg-secondary');
		$('.mc-modal-title').html('ORDER STUDY');

		let expArea = '';
		if ($('#row'+expid+'summary').find('.bk_expertArea').length) {
			expArea = $('#row'+expid+'summary').find('.bk_expertArea').html().trim().split('<br>').map(str => str.trim()).filter(str => str.length > 0).join(', ');
		}
		
		let initObj = { expertID: expid, expertName: $('#row'+expid+'summary').find('.bk_expertname').text(),
						expertLocation: $('#row'+expid+'summary').find('.bk_expertlocation').text(),
						expertArea: expArea, rptType: rpt, rptOrderNextStep: 'init' };

		$('.mc-modal-body')
			.load('/?pg=search&bid='+vars_bid+'&s_a=oneClickPurchaseBillingInfo&mode=stream', initObj,
				function() {
					orderDCSReport_loadBillingForm();
					
					try {
						window.removeEventListener("message", DCSOrderRptMessageHandler);
					} catch(e) {}
					window.addEventListener("message", DCSOrderRptMessageHandler);
				})
			.html('<div style="text-align:center;margin-top:20px;"><i class="icon-spin icon-spinner"></i> Please wait...</div>');
		
		$('.mc-modal-close').off('click').on('click', function () {
			$('.mc-modal-title,.mc-modal-body').html('');
			$('.mc-modal,.mc-modal-overlay').removeClass('active');
			$('.mc-modal').removeClass('bk-bg-secondary');
		});
	};

	const orderDCSReport_getFormObj = function() {
		let fd = {};
		$.each($('#frmDCSReport').serializeArray(), function(_, field) {
			fd[field.name] = field.value;
		});
		return fd;
	}

	const orderDCSReport_loadBillingForm = function() {
		$('#expert'+expid+'billing')
			.load('/?pg=search&bid='+vars_bid+'&s_a=oneClickPurchaseBillingInfo&mode=stream', orderDCSReport_getFormObj(),
				function() { 
					$('#frmDCSReport input[name="rptOrderNextStep"]').val('payment');
					$('#expName'+expid).html($('#row'+expid+'summary').find('.bk_expertname').text());
					let expLoc = $('#row'+expid+'summary').find('.bk_expertlocation').text();
					if (expLoc && expLoc.length) {
						$('#expLoc'+expid).html(expLoc);
						$('.expLoc'+expid).show();
					}

					let expArea = '';
					if ($('#row'+expid+'summary').find('.bk_expertArea').length) {
						expArea = $('#row'+expid+'summary').find('.bk_expertArea').html().trim().split('<br>').map(str => str.trim()).filter(str => str.length > 0).join(', ');
						if (expArea && expArea.length) {
							$('#expArea'+expid).html(expArea);
							$('.expArea'+expid).show();
						}
					}

					$('#btnSaveDCSOrderBillInfo').off('click').on('click', function () {
						$(this).html('<i class="icon-spin icon-spinner"></i> Please wait...').prop('disabled',true);

						let arrReq = [];
						let emailRegEx = new RegExp(mc_emailregex,"i");
						hideAlert();

						let billingstate = $("#billingstate").val().trim();
						let billingzip = $("#billingzip").val().trim();
						let userMainEmail = $("#userMainEmail").val().trim();

						if(!billingstate.length) arrReq.push('Enter the State/Province.');
						if(!billingzip.length) arrReq.push('Enter the Postal Code.');
						if (billingstate.length && billingzip.length && !mc_isValidBillingZip(billingzip,0,billingstate))
							arrReq.push('Invalid Postal Code.');
						if(!userMainEmail.length) arrReq.push('Enter the E-mail Address.');
						else if (!emailRegEx.test(userMainEmail)) arrReq.push('Enter a valid E-mail Address.');
						
						if (arrReq.length) {
							showAlert(arrReq.join('<br/>'));
							$(this).html('Continue').prop('disabled',false);
							return false;
						}

						let objParams = { billingaddress: $('#BillingAddress').val().trim(), billingcity: $('#BillingCity').val().trim(), billingstate: $('#billingstate').val().trim(), 
											billingzip: $('#billingzip').val().trim(), mainEmail: $('#userMainEmail').val().trim() };

						let memUpdateResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								vars_user_billingstate = objParams.billingstate;
								vars_user_billingzip = objParams.billingzip;

								orderDCSReport_loadPaymentForm();
							} else {
								alert('We were unable to update billing information. Try again!');
								$('#btnSaveDCSOrderBillInfo').html('Continue').prop('disabled',false);
							}
						};
						TS_AJX('MEMBER','updateTSMemberData',objParams,memUpdateResult,memUpdateResult,10000,memUpdateResult);
					});
				})
			.html('<div style="text-align:center;margin-top:20px;"><i class="icon-spin icon-spinner"></i> Please wait...</div>');
	};

	const orderDCSReport_loadPaymentForm = function() {
		$('#DCSOrderBillInfo').hide();

		$('#expert'+expid+'payment')
			.load('/?pg=search&bid='+vars_bid+'&s_a=oneClickPurchaseBillingInfo&mode=stream', orderDCSReport_getFormObj(),
				function() { 
					$('#frmDCSReport input[name="rptOrderNextStep"]').val('processpayment');
					$('#selCaseRefBadges').html('');
					if (vars_caserefs.length) {
						for (var i=0; i < vars_caserefs.length; i++) {
							$('#selCaseRefBadges').append('<a href="#" class="bk-mc-tag" onclick="$(\'#caseref\').val(\''+vars_caserefs[i]+'\');return false;">'+vars_caserefs[i]+'</a>');
						}
						$('#caseref').val(vars_caserefs[vars_caserefs.length-1]);
						$('#selCaseRefBadges').show();
					} else {
						$('#selCaseRefBadges').hide();
					}

					$('#btnOrderDCSRpt').off('click').on('click', function () {
						let thisBtnText = $(this).html();
						$(this).html('<i class="icon-spin icon-spinner"></i> Please wait...').prop('disabled',true);
						let arrReq = [];
						hideAlert();

						let hasPOF = $('#p_TS_DIV').length && $('#p_TS_DIV').attr("pofcount") != 0;
						if (!hasPOF && $('#p_TS_tokenData').length && $('#p_TS_tokenData').val().length) hasPOF = true;

						if (!hasPOF) arrReq.push('Select a Payment Method.');
						if (!$("#caseref").val().trim().length) arrReq.push('Enter the Case Reference.');
						
						if (arrReq.length) {
							showAlert(arrReq.join('<br/>'));
							$(this).html(thisBtnText).prop('disabled',false);
							return false;
						}

						orderDCSReport_processPayment();
					});
				})
			.html('<div style="text-align:center;margin-top:20px;"><i class="icon-spin icon-spinner"></i> Please wait...</div>');
	};

	const orderDCSReport_processPayment = function() {
		$('#expert'+expid+'confirmation')
			.load('/?pg=search&bid='+vars_bid+'&s_a=oneClickPurchaseBillingInfo&mode=stream', orderDCSReport_getFormObj(),
				function() {
					$('#frmDCSReport').hide();
					$(this).show();
				});
	};
	
	orderDCSReport_init();
}
function DCSOrderRptMessageHandler(event) {
	if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
		switch (event.data.messagetype.toLowerCase()) {
			case "tsgatewayevent":
				if (['applepaytokenready','gpaytokenready'].indexOf(event.data.eventname.toLowerCase()) != -1) {
					$('<input>').attr({ type: 'hidden', name: 'p_'+event.data.merchantorgcode+'_tokenData' }).val(JSON.stringify(event.data.tokendata)).appendTo('form#frmDCSReport');
					if (!$("#caseref").val().trim().length) $("#caseref").val('One Click Purchase');
					$('#btnOrderDCSRpt').trigger('click');
					$('.mc-modal-body').scrollTop($('.mc-modal-body')[0].scrollHeight);
				}
			break;
		};

	} else {
		return false;
	}
}
function viewDCSSampleReport() {
	remote = open('https://www.trialsmith.com/docDownload/34194', "_blank");
}
function orderCustomDCSRpt() {
	if ($('#bk_notLoggedIn').length) {
		showNotLoggedInDCSRpt();
		return false;
	}

	let customDCSCard = $('#customDCSRptContainer');
	customDCSCard.addClass('bk-active-card');
	customDCSCard.removeClass('bk-d-none');
	$('#customDCSRptDetail').hide();
	$('#customDCSRptForm').show(300);

	$('html, body').animate({
		scrollTop: customDCSCard.offset().top - 175
	}, 400);

	$('.bk-overlay').removeClass('bk-d-none');
}
function cancelOrderCustomDCSRpt() {
	let customDCSCard = $('#customDCSRptContainer');
	customDCSCard.removeClass('bk-active-card');
	customDCSCard.addClass('bk-d-none');
	$('.bk-overlay').addClass('bk-d-none');
}
function validateDCSExpertInfo() {
	$('.exp_fld_err').html('').hide();

	let arrErrFlds = [];
	let fname = $('#exp_firstName').val().trim();
	let mname = $('#exp_middleName').val().trim();
	let lname = $('#exp_lastName').val().trim();
	let expCity = $('#exp_city').val().trim();
	let expState = $('#exp_state').val().trim();
	let expArea = $('#exp_area').val().trim();

	if (! fname.length) arrErrFlds.push({id: 'exp_firstName_err', msg: 'Enter Expert\'s First Name.'});
	if (! lname.length) arrErrFlds.push({id: 'exp_lastName_err', msg: 'Enter Expert\'s Last Name.'});
	if (! expCity.length) arrErrFlds.push({id: 'exp_city_err', msg: 'Enter Expert\'s City.'});
	if (! expState.length) arrErrFlds.push({id: 'exp_state_err', msg: 'Enter Expert\'s State.'});
	if (! expArea.length) arrErrFlds.push({id: 'exp_area_err', msg: 'Enter Expert\'s Area of Expertise.'});

	if (arrErrFlds.length) {
		$.each(arrErrFlds, function(index, error) {
			$('#' + error.id).html(error.msg).show();
		});
		return false;
	}

	let expName = fname + ' ' + (mname.length ? mname + ' ' : '') + lname;
	let expLoc = expCity + ', ' + expState;
	let customExpRow = $('#rowcustomexp');
	customExpRow.find('.bk_expertname').text(expName);
	customExpRow.find('.bk_expertlocation').text(expLoc);
	customExpRow.find('.bk_expertArea').text(expArea);

	$('.bk-overlay').addClass('bk-d-none');
	$('#customDCSRptContainer').removeClass('bk-active-card');
	$('#exp_middleName,#exp_city,#exp_state,#exp_area').val('');
	$('#customDCSRptForm').hide();
	$('#customDCSRptDetail').show(300);
}

function promptMissingTaxInfo(docid, funcName) {
	var atcLink = document.getElementById('s_addtocart' + docid);
	var arrPOS = findPos(atcLink);
	var atcdiv = atc_get_atc_div();

	// adjust for doc link
	arrPOS[0] -= 364;
	arrPOS[1] += 18;

	var stateOptions = '<option value=""></option>';
	$.each(tsStatesArray, function(key,value) {
		stateOptions += '<option value="' + value[0] + '"' + (vars_user_billingstate == value[0] ? ' selected' : '' ) + '>' + value[1] + '</option>';
	});

	var missingState = vars_user_billingstate.length == 0;
	var missingZip = vars_user_billingzip.length == 0;

	// create dialog
	var dialog = '<div>\
		<span id="modalpopupreq"></span>\
		<div id="modalpopuphead">Billing Info</div>\
		</div>Please provide the following information to proceed. <br/>\
		<table cellspacing="0" cellpadding="0" style="margin-top:6px;position:relative;" id="billingInfoTbl">'+
			'<tr><td class="tsAppBodyText">* State/Province: </td><td><select id="billingstate" name="billingstate" class="tsAppBodyText billingFieldInput" style="width:250px;">'+ stateOptions +'</select></td></tr>' +
			'<tr><td class="tsAppBodyText">* Postal Code: </td><td><input type="text" id="billingzip" name="billingzip" size="12" maxlength="25" class="tsAppBodyText billingFieldInput" value="'+vars_user_billingzip+'" style="width:250px;"></td></tr>' +
			'<tr><td></td><td style="padding-top:3px;"><input type="button" value="Continue" class="tsAppBodyText" name="btnContinue" autocomplete="off" onclick="saveMissingTaxInfo('+docid+', \''+funcName+'\');"> <a href="javascript:hideAddToCart();" style="margin-left:5px">Cancel</a></td></tr>\
		</table>';

	// write dialog
	atcdiv.innerHTML = '<div class="modalpopup tsAppBodyText"><div class="modalpopuparrow"><span>^_^dodo1</span></div><div class="modalpopupcontent">' + dialog + '</div></div>';
	atcdiv.style.left = arrPOS[0]+'px';
	atcdiv.style.top = arrPOS[1]+'px';
	atcdiv.style.visibility = 'visible';
}

function isMissingTaxInfo(){
	var missingState = vars_user_billingstate.length == 0;
	var missingZip = vars_user_billingzip.length == 0;
	var isValidZip = !missingState && !missingZip && mc_isValidBillingZip(vars_user_billingzip,0,vars_user_billingstate);
	return missingState || missingZip || !isValidZip;
}

function saveMissingTaxInfo(docid, funcName){
	var missingState = vars_user_billingstate.length == 0;
	var missingZip = vars_user_billingzip.length == 0;
	
	if(missingState && $('#billingstate').val() == '') { $('#billingstate').focus(); return false; }
	if(missingZip && $('#billingzip').val() == '') { $('#billingzip').focus(); return false; }
	if ($('#billingstate').val().length && $('#billingzip').val().length && !mc_isValidBillingZip($('#billingzip').val(),0,$('#billingstate').val())) {
		alert('Invalid Zip');
		return false;
	}

	if($('#billingstate').length) vars_user_billingstate = $('#billingstate').val();
	if($('#billingzip').length) vars_user_billingzip = $('#billingzip').val();

	hideAddToCart();
	eval(funcName+'('+docid+')');
}

function oneClickPurchaseDAReport(uniqueNameID,sid) {
	if(isMissingTaxInfo()){
		promptMissingTaxInfo(uniqueNameID, 'oneClickPurchaseDAReport');
		return;
	}
	var cr = "One Click Purchase";
	var DAWindow = window.open("", "docViewer", "scrollbars=yes,resizable,width=725,height=550");
	DAWindow.document.write('<h1>Loading Report</h1>');
	onClickPurchaseDo(uniqueNameID,cr,sid,function(returnStruct){
		if (returnStruct.hasOwnProperty('reportid') && returnStruct.reportid > 0) {
			document.getElementById('s_addtocart' + uniqueNameID).innerHTML = '<div class="s_opt"><a href="javascript:viewDAReport('+ returnStruct.reportid +');" title="View Report"><img src="/assets/common/images/search/page_white_put.png" width="16" height="16" align="left" border="0" />View Report</a></div>';
			DAWindow.location.href='/?pg=myDocuments&tab=VDA&mode=direct&reportid=' + returnStruct.reportid;
		} else {
			DAWindow.document.body.innerHTML=''
			DAWindow.document.write('<h1>Error</h1><p>Error loading the disciplinary actions report. Please close this window and try again.</p>');
		}
	});
}

function oneClickPurchaseDTReport(uniqueNameID,sid) {
	if(isMissingTaxInfo()){
		promptMissingTaxInfo(uniqueNameID, 'oneClickPurchaseDTReport');
		return;
	}
	var cr = "One Click Purchase";
	var DAWindow = window.open("", "docViewer", "scrollbars=yes,resizable,width=725,height=550");
	DAWindow.document.write('<h1>Loading Report</h1>');
	onClickPurchaseDo(uniqueNameID,cr,sid,function(returnStruct){
		if (returnStruct.hasOwnProperty('reportid') && returnStruct.reportid > 0) {
			document.getElementById('s_addtocart' + uniqueNameID).innerHTML = '<div class="s_opt"><a href="javascript:viewDTReport('+ returnStruct.reportid +');" title="View Report"><img src="/assets/common/images/search/page_white_put.png" width="16" height="16" align="left" border="0" />View Report</a></div>';
			DAWindow.location.href='/?pg=myDocuments&tab=VDT&mode=direct&reportid=' + returnStruct.reportid;
		} else {
			DAWindow.document.body.innerHTML=''
			DAWindow.document.write('<h1>Error</h1><p>Error loading the expert challenges report. Please close this window and try again.</p>');
		}
	});
}

// aaj litigation packets
function b_showAllAAJPackets() {
    self.location.href = '/?pg=search&bid=' + vars_bid + '&s_a=doSearch&s_category=1&s_key_all=&s_key_phrase=&s_key_one=&s_key_x=&s_frm=1';
}

// court docs
function b_refreshCourtDocs() {
	document.getElementById('bk_cd_ca').innerHTML = document.getElementById('bk_cd_td').innerHTML = b_getloadingicon();
	var obj = b_getObjFromBid(vars_bid);
	var theForm = document.forms["frms_Search"];
	var objParams = { bucketid:vars_bid, bankid:theForm.s_ownership.options[theForm.s_ownership.selectedIndex].value,
		documentTypeId:theForm.s_type.options[theForm.s_type.selectedIndex].value, caseTypeId:theForm.s_cause.options[theForm.s_cause.selectedIndex].value };
	var refreshCourtDocsResult = function(r) {
		document.getElementById('bk_cd_ca').innerHTML = document.getElementById('bk_cd_td').innerHTML = '';
		if (r.success && r.success == 'true') {
			var qrylabels = r.qrylabels.data;
			var qrycasetypes = r.qrycasetypes.data;
			var qrydocumenttypes = r.qrydocumenttypes.data;
			document.getElementById('bk_cd_ca_nm').innerHTML = (qrylabels.casetypelabel[0] && qrylabels.casetypelabel[0].length > 0 ? qrylabels.casetypelabel[0] : 'Cause of Action');
			document.getElementById('bk_cd_td_nm').innerHTML = (qrylabels.doctypelabel[0] && qrylabels.doctypelabel[0].length > 0 ? qrylabels.doctypelabel[0] : 'Type of Document');
			var objCA = document.forms["frms_Search"].s_cause;
			objCA.options.length = 0;
			objCA.options[objCA.options.length] = new Option('All Categories','0');
			for (var i=0; i < qrycasetypes.casetypeid.length; i++) {
				objCA.options[objCA.options.length] = new Option(qrycasetypes.description[i] + (r.showcategorycounts == 'true' ? ' (' + qrycasetypes.doccount[i] + ')' : ''),qrycasetypes.casetypeid[i]);
				if (qrycasetypes.casetypeid[i] == r.casetypeid) objCA.selectedIndex = objCA.options.length - 1;
			}
			var objTD = document.forms["frms_Search"].s_type;
			objTD.options.length = 0;
			objTD.options[objTD.options.length] = new Option('All Document Types','0');
			for (var i=0; i<qrydocumenttypes.documenttypeid.length; i++) {
				objTD.options[objTD.options.length] = new Option(qrydocumenttypes.description[i] + (r.showcategorycounts == 'true' ? ' (' + qrydocumenttypes.doccount[i] + ')' : ''),qrydocumenttypes.documenttypeid[i]);
				if (qrydocumenttypes.documenttypeid[i] == r.documenttypeid) objTD.selectedIndex = objTD.options.length - 1;
			}
		}
		obj.req = null;
	};
	if (obj) obj.req = TS_AJX('SBT' + obj.btid,'getCaseTypesAndDocTypes',objParams,refreshCourtDocsResult);
}

// list messages
var s_lyris_messagewindowWidthSet = false;

function b_loadmsg(mid,amidx,vd) {
	var mw = document.getElementById('s_lyris_messagewindow');
	try {
		if (s_lyris_messagewindowWidthSet != true) {
			mw.style.width = mw.offsetWidth + "px";
			s_lyris_messagewindowWidthSet = true;
		}
	} catch (e){}
	mw.innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
	if(vd == 'responsive') $('html, body').animate({scrollTop:$('#s_lyris_messagewindow').position().top}, 'slow');
	
	var obj = b_getObjFromBid(vars_bid,vd);
	var objParams = { mid:mid, attachedmessage:amidx, vd:vd };
	var loadmsgResult = function(r) {
		if (r.success && r.success == 'true') mw.innerHTML = r.resulthtml;
		else mw.innerHTML = '<div class="s_err">Error loading message. Try again.</div>';
		obj.req = null;
	};
	if (obj) obj.req= TS_AJX('SBT' + obj.btid,'getMessage',objParams,loadmsgResult);
}

function b_loadmsgjson(mid,amidx,vd) {
	var mw = document.getElementById('s_lyris_messagewindow');
	try {
		if (s_lyris_messagewindowWidthSet != true) {
			mw.style.width = mw.offsetWidth + "px";
			s_lyris_messagewindowWidthSet = true;
		}
	} catch (e){}
	mw.innerHTML = '<div align="center">' + b_getlgloadingicon() + '</div>';
	if(vd == 'responsive') $('html, body').animate({scrollTop:$('#s_lyris_messagewindow').position().top}, 'slow');
	var obj = b_getObjFromBid(vars_bid);
	var objParams = { mid:mid, attachedmessage:amidx, vd:vd };
	if (obj) {
		var default_jsonurl = '/?event=proxy.ts_json&c=SBT' +  obj.btid + '&m=getMessage';
		$.getJSON( default_jsonurl, objParams)
			.done(function( data ) {
				if (data.success && data.success == true) mw.innerHTML = data.resulthtml;
				else mw.innerHTML = '<div class="s_err">Error loading message. Try again.</div>';
				obj.req = null;
			})
			.fail(function( jqxhr, textStatus, error ) {
			  var err = textStatus + ', ' + error;
			  $('#loadingIndicator').hide();
			});	
	}
}

function printMessage(){
	var content = document.getElementById("listMessageContent");
	var pri = document.getElementById("MCcontenttoprint").contentWindow;
	pri.document.open();
	pri.document.write(content.innerHTML);
	pri.document.close();
	pri.focus();
	pri.print();
}

// seminarweb
function goSW(caturl,ct,cid) {
	switch (ct) {
		case 'SWL': caturl += '/?d=SWLive-' + cid; break;
		case 'SWOD': caturl += '/?d=SWOD-' + cid; break;
	}
	self.location.href = caturl;
}

/* Cart functions */
function viewCart() {
	var isMobileView = $('#searchBarTopNav div#searchBarMobile').is(':visible');
	
	if(!isMobileView) cart_loadInline();
	else self.location.href = '/?pg=viewCart';
}

function cart_loadInline(callback) {
	if (displayArea_inlineCart)
		$(displayArea_inlineCart).load('/?pg=viewCart #tsCartOuterWrapper', function () {
			if ($(displayArea_docviewer).is(":visible")) {
				cart_divToShowWhenCloseCart = displayArea_docviewer;
				$(displayArea_docviewer).hide();
				$('#tsdocumentviewerbar').hide();
			} else if ($(displayArea_mainscreen).is(":visible")) {
				cart_divToShowWhenCloseCart = displayArea_mainscreen;
				$(displayArea_mainscreen).hide();
				$('#tssearchbar').hide();
				$('#myDocumentsTabs').hide();
				$('#searchMyDocumentsButtonDiv').hide();
			}
			cart_LoadedInDiv = true;
			if ($('#tsEmptyCartActionButton')) $('#tsEmptyCartActionButton').remove();
			c_loadTotals();
			$(displayArea_inlineCart).show();
			$('#tsinlinecartbar').show();
			if (callback) callback();
		});
}

function cart_closeInlineCart() {
	$('#tsinlinecartbar').hide();
	$(displayArea_inlineCart).hide();
	cart_LoadedInDiv = false;
	if (cart_divToShowWhenCloseCart) {
		$(cart_divToShowWhenCloseCart).show();
		if (cart_divToShowWhenCloseCart == displayArea_mainscreen) {
			$('#tssearchbar').show();
			if ($('#myDocumentsTabs')) $('#myDocumentsTabs').show();
			if ($('#searchMyDocumentsButtonDiv')) $('#searchMyDocumentsButtonDiv').show();
		}
		if (cart_divToShowWhenCloseCart == displayArea_docviewer) $('#tsdocumentviewerbar').show();
	} 
}

function atc_get_atc_div() {
	if (! document.getElementById('atcdiv')) {
		var newdiv = document.createElement("div");
			newdiv.setAttribute("id","atcdiv");
			newdiv.setAttribute("style","z-index: 9000;");
		document.body.appendChild(newdiv);
	}
	return document.getElementById('atcdiv');
}

function findPos(obj) {
	var curleft = curtop = 0;
	if (obj.offsetParent) {
		do {
			curleft += obj.offsetLeft;
			curtop += obj.offsetTop;
		} while (obj = obj.offsetParent);
	}
	return [curleft,curtop];
}

function addDepoDocToCartPrompt(docid,sid) {
	$('#btnAddDocToCart').prop('disabled',true);
	let docCaseRefDescTxt = $('#addCart_btn_'+docid).data("doccaserefdesctxt");
	$("#addDocCaseRefDesc").text(docCaseRefDescTxt);
	$('#addDocCaseRefBadges').html('');
	$('#err_addDocCart').html('').hide();

	if (vars_caserefs.length) {
		for (var i=0; i < vars_caserefs.length; i++) {
			$('#addDocCaseRefBadges').append('<a href="#" class="bk-mc-tag" onclick="$(\'#addDocCaseRef\').val(\''+vars_caserefs[i]+'\');return false;">'+vars_caserefs[i]+'</a>');
		}
		$('#addDocCaseRef').val(vars_caserefs[vars_caserefs.length-1]);
		$('#addDocCaseRefBadges').show();
	} else {
		$('#addDocCaseRefBadges').hide();
	}

	$('#addDocToCartModal').modal({ keyboard: false, backdrop: 'static' });

	$('#btnAddDocToCart').off('click');
	$('#btnAddDocToCart').on('click',function() {
		$(this).prop('disabled',true);
		$('#err_addDocCart').html('').hide();
		var caseRef = $("#addDocCaseRef").val().trim();
		
		if (!caseRef.length) {
			$('#err_addDocCart').html('Enter the Case Reference.').show();
			$(this).prop('disabled',false);
			return false;
		} else {
			addToCartDo(docid,caseRef,sid);
			$('#addDocToCartModal').modal('hide');
		}
	});

	$('#addDocToCartModal').on('hidden', function () {
		$('#btnAddDocToCart').prop('disabled',true);
		$('#btnAddDocToCart').off('click');
	});

	$('#btnAddDocToCart').prop('disabled',false);
}

function oneClickPurchasePrompt(docid,sid,fileName,fileFormat,rowid,atype) {
	$('#oneClickPurchaseModal').modal({ keyboard: false, backdrop: 'static' });
	$('#btnContinueOneClickPurchase').prop('disabled',true);
	
	var docCaseRefDescTxt = $('#'+fileFormat+'_btn_'+rowid).data("doccaserefdesctxt");
	var thisScreen = "initScr";

	$('#oneClickPurchaseModal .modal-body')
		.load('/?pg=search&bid='+vars_bid+'&s_a=oneClickPurchaseBillingInfo&mode=stream', 
				function() { 
					$('#btnContinueOneClickPurchase').off('click');
					$('#btnContinueOneClickPurchase').on('click', function() {
						$(this).prop('disabled',true);

						let arrScreens = [];
						if ($('#initial_billInfo').length) arrScreens.push('initScr');
						if ($('#contact_billInfo').length) arrScreens.push('contactScr');
						if ($('#cc_billInfo').length) arrScreens.push('cccScr');
						arrScreens.push('caserefScr');

						/* validation */
						var arrReq = [];
						var emailRegEx = new RegExp(mc_emailregex,"i");
						hideAlert();

						if (thisScreen == "contactScr") {
							var billingstate = $("#billingstate").val().trim();
							var billingzip = $("#billingzip").val().trim();
							var userMainEmail = $("#userMainEmail").val().trim();

							if(!billingstate.length) arrReq.push('Enter the State/Province.');
							if(!billingzip.length) arrReq.push('Enter the Postal Code.');
							if (billingstate.length && billingzip.length && !mc_isValidBillingZip(billingzip,0,billingstate))
								arrReq.push('Invalid Postal Code.');
							if(!userMainEmail.length) arrReq.push('Enter the E-mail Address.');
							else if (!emailRegEx.test(userMainEmail)) arrReq.push('Enter a valid E-mail Address.');
						} else if (thisScreen == 'cccScr' && $('#p_TS_DIV').length && $('#p_TS_DIV').attr("pofcount") == 0) {
							arrReq.push('Enter your credit card information to continue.');
						} else if (thisScreen == 'caserefScr' && !$("#caseref").val().trim().length) {
							arrReq.push('Enter the Case Reference.');
						}

						if (arrReq.length) {
							showAlert(arrReq.join('<br/>'));
							$(this).prop('disabled',false);
							return false;
						}

						/* if no validation errors show next screen or save info */
						if (thisScreen == "initScr" && arrScreens.indexOf('contactScr') != -1) {
							$(".mc_billinginfo_section").hide();
							$("#contact_billInfo").show();
							thisScreen = 'contactScr';
						} else if (["initScr","contactScr"].indexOf(thisScreen) != -1 && arrScreens.indexOf('cccScr') != -1) {
							$(".mc_billinginfo_section").hide();
							$("#cc_billInfo").show();
							thisScreen = 'cccScr';
						} else if (thisScreen != 'caserefScr') {
							$(".mc_billinginfo_section").hide();
							$("#caseref_billInfo").show();
							$("#docCaseRefDesc").text(docCaseRefDescTxt);
							$('#selCaseRefBadges').html('');
							if (vars_caserefs.length) {
								for (var i=0; i < vars_caserefs.length; i++) {
									$('#selCaseRefBadges').append('<a href="#" class="bk-mc-tag" onclick="$(\'#caseref\').val(\''+vars_caserefs[i]+'\');return false;">'+vars_caserefs[i]+'</a>');
								}
								$('#caseref').val(vars_caserefs[vars_caserefs.length-1]);
								$('#selCaseRefBadges').show();
							} else {
								$('#selCaseRefBadges').hide();
							}
							thisScreen = 'caserefScr';
						} else {
							oneClickSaveBillingInfo(docid,sid,fileName,fileFormat,rowid,atype,arrScreens);
						}

						if (thisScreen == 'cccScr') {
							$('#btnContinueOneClickPurchase,#btnCloseOneClickPurchasePrompt').prop('disabled',true);
						} else {
							$('#btnContinueOneClickPurchase,#btnCloseOneClickPurchasePrompt').prop('disabled',false);
						}
					});

					$('#btnContinueOneClickPurchase').prop('disabled',false); 
					if ($(".mc_billinginfo_section").length == 1) $('#btnContinueOneClickPurchase').trigger('click');
				})
		.html('<div style="text-align:center;min-height:300px;"><i class="icon-spin icon-spinner"></i> Please wait...</div>');

	$('#oneClickPurchaseModal').on('hidden', function () {
		$('#btnContinueOneClickPurchase').prop('disabled',true);
		$('#btnContinueOneClickPurchase').off('click');
	});
}

function oneClickSaveBillingInfo(docid,sid,fileName,fileFormat,rowid,atype,arrScreens){
	var obj = b_getObjFromBid(vars_bid);
	
	if (obj) {
		let saveBillInfoPromise = 
			new Promise(function(resolve, reject) {
				let thisCaseref = $("#caseref").val().trim();

				if (arrScreens.indexOf('contactScr') != -1) {
					var objParams = {
						billingaddress: $('#BillingAddress').val().trim(),
						billingcity: $('#BillingCity').val().trim(),
						billingstate: $('#billingstate').val().trim(), 
						billingzip: $('#billingzip').val().trim(),
						mainEmail: $('#userMainEmail').val().trim()
					};

					var memUpdateResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							vars_user_billingstate = objParams.billingstate;
							vars_user_billingzip = objParams.billingzip;

							$('#oneClickPurchaseModal').modal('hide');
							resolve({caseRef:thisCaseref});
						} else {
							alert('We were unable to update billing information. Try again!');
							$('#btnContinueOneClickPurchase').prop('disabled',false);
							reject();
						}
					};
					TS_AJX('MEMBER','updateTSMemberData',objParams,memUpdateResult,memUpdateResult,10000,memUpdateResult);
				} else {
					$('#oneClickPurchaseModal').modal('hide');
					resolve({caseRef:thisCaseref});
				}

			}).then(function(resObj) {
				return new Promise(function(resolve, reject) {
					var oneClickPurchaseResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							vars_caserefs = r.caserefs;
							if (r.removedcartdoc) {
								showDocumentAddCartLink(docid);
								rebuildTSSearchBar();
							}
							resolve(resObj);
						} else {
							alert('Error encountered. Please try again.');
							reject();
						}
					};
					
					$("#s_addtocart"+docid).html('<span style="font-size:12px; margin-bottom: 0;" class="span12 well well-small"><i class="icon-spin icon-spinner"></i> Processing...</span>');

					let objPurchaseParams = { btid:obj.btid, sid:sid, docid:docid, caseref:resObj.caseRef, billingstate:vars_user_billingstate, billingzip:vars_user_billingzip };
					TS_AJX('SBT' + obj.btid,'oneClickPurchase',objPurchaseParams,oneClickPurchaseResult,oneClickPurchaseResult,15000,oneClickPurchaseResult);
				});
			}).then(function(resObj) {
				oneClickPurchaseDepoDownload(docid, resObj.caseRef, fileName, fileFormat, rowid, atype);
			});
	}
}

function addToCart(docid,sid) {
	var atcLink = document.getElementById('s_addtocart' + docid);
	var arrPOS = findPos(atcLink);
	var atcdiv = atc_get_atc_div();

	// adjust for cart link
	arrPOS[0] -= 364;
	arrPOS[1] += 18;


	// create dialog
	var dialog = '<div><span id="modalpopupreq"></span><div id="modalpopuphead">Add Document To Cart</div></div>You must purchase this document in order to print or save. <br/><br/>Keep your accountant happy! A case reference or memo for this document appears on your statement to help track your purchase.<br/><table cellspacing="0" cellpadding="0" style="margin-top:6px;position:relative;"><tr><td class="tsAppBodyText">Memo: </td><td><input type="text" value="" size="30" maxlength="50" class="tsAppBodyText" name="caseref" id="caseref"> <input type="button" value="Add to Cart" class="tsAppBodyText" name="btnATC" autocomplete="off" onclick="addToCartDo(\'' + docid + '\',document.getElementById(\'caseref\').value,' + (sid ?? 0) + ');"></td></tr>';
	if (vars_caserefs.length > 0)
		dialog += '<tr><td></td><td><select id="modalpopupSuggestResults" class="tsAppBodyText" size="4" onclick="atc_setSearch(this)" onchange="atc_setSearch(this)"></select></td></tr><tr><td colspan="2"><br/><br/><br/></td></tr>';
	dialog += '</table><div class="modalpopupinput" align="right"><a href="javascript:hideAddToCart();">Cancel</a></div>';

	// write dialog
	atcdiv.innerHTML = '<div class="modalpopup tsAppBodyText"><div class="modalpopuparrow"><span>^_^dodo1</span></div><div class="modalpopupcontent">' + dialog + '</div></div>';
	atcdiv.style.left = arrPOS[0]+'px';
	atcdiv.style.top = arrPOS[1]+'px';
	atcdiv.style.visibility = 'visible';

	// populate caserefs
	if (vars_caserefs.length > 0) {
		var txtCR = document.getElementById('caseref');
		var selbox = document.getElementById('modalpopupSuggestResults');
		for (var i=0; i < vars_caserefs.length; i++) {
			selbox.options[selbox.options.length] = new Option(vars_caserefs[i],vars_caserefs[i]);
		}
		var iHeight = vars_caserefs.length*15;
		if (vars_caserefs.length > 2) {
			selbox.style.height = (iHeight > 60) ? '60px' : iHeight + 'px';
		}
		selbox.style.width = txtCR.offsetWidth + 'px';
		selbox.style.visibility = 'visible';
	}
}

function atc_setSearch(sel) {
	document.getElementById('caseref').value = sel.options[sel.selectedIndex].value;
}

function hideAddToCart() {
	var atcdiv = atc_get_atc_div();
	if (atcdiv) {
		atcdiv.style.visibility = 'hidden';
		atcdiv.style.left = '-1000px';
	}
}

function showDocumentAddCartLink(docid) {
	var atcLink = document.getElementById('s_addtocart' + docid);
	var inCartLink = document.getElementById('s_incart' + docid);

	if (atcLink) $(atcLink).show();
	if (inCartLink)	$(inCartLink).hide();
}

function hideDocumentAddCartLink(docid) {
	var atcLink = document.getElementById('s_addtocart' + docid);
	var inCartLink = document.getElementById('s_incart' + docid);

	if (atcLink) $(atcLink).hide();
	if (inCartLink)	$(inCartLink).show();
}

function addToCartDo(docid,cr,sid) {
	cr = cr.replace(/^\s\s*/,'').replace(/\s\s*$/,'');
	if (cr.length == 0) {
		document.getElementById('modalpopupreq').innerHTML = 'Required';
		return false;
	}
	
	var obj = b_getObjFromBid(vars_bid);
	if (obj) {
		hideAddToCart();
		// if (atcLink) {
		// 	var cartStatusElement = atcLink.after('');
		// 	atcLink.innerHTML = 'Adding... ' + b_getloadingicon();
		// }
		var objParams = { btid:obj.btid, docid:docid, caseref:cr, sid:sid ?? 0 };
		var addToCartErr = function(r) {
			// atcLink.innerHTML = oldatc;
			obj.req = null;
		};
		var addToCartResult = function(r) {
			if (r.success && r.success == 'true') {
				if (r.loggedin == true) {
					var creditAmtRemaining = $($("#creditAmtRemaining")[0]).data("creditamtremaining") - $("#creditAmtRemaining_"+docid).data("mycostprice");
					if(creditAmtRemaining < 0 ) creditAmtRemaining = 0;
					$($("#creditAmtRemaining")[0]).data("creditamtremaining",creditAmtRemaining)
					$(".creditAmtRemaining").html("$" + creditAmtRemaining);
					hideDocumentAddCartLink(docid);
					vars_caserefs = r.caserefs;
					if (getSearchID() > 0) {
						buildTSSearchBar(vars_bid,getSearchID());
						if(isTsDocumentViewerOpen())
							rebuildViewerToolbar(r.docid, r.documentcartid);
					}
				} else {
					self.location.href = self.location.href;		
				}
			} else addToCartErr(r);	
			obj.req = null;
		};
		obj.req = TS_AJX('SBT' + obj.btid,'addToCart',objParams,addToCartResult,addToCartErr,15000);
	}
}

function onClickPurchaseDo(docid,cr,sid,callback) {
	cr = cr.replace(/^\s\s*/,'').replace(/\s\s*$/,'');
	if (cr.length == 0) {
		cr = "One Click Purchase";
	}
	var obj = b_getObjFromBid(vars_bid);
	if (obj) {
		var objParams = { btid:obj.btid, docid:docid, sid:sid, caseref:cr, billingstate:vars_user_billingstate, billingzip:vars_user_billingzip };

		var addToCartErr = function(r) {
			obj.req = null;
		};

		if (callback && typeof(callback) === "function") {  
	    	var callbackToUse = callback;  
	    } else {
	    	//if no callback passed in, then use this dummy no-op function
	    	var callbackToUse = function(r) {};
	    }
		obj.req = TS_AJX('SBT' + obj.btid,'oneClickPurchase',objParams,callbackToUse,addToCartErr,15000);
	}
}

function c_removeAllDocs(btid,dt) {
	var msg = 'Are you sure that you want to remove all ' + dt.toLowerCase() + ' from your cart?';
	if (confirm(msg)) {
		var objParams = { itemtype:btid };
		var removeAllDocsResult = function(r) { 
			$('.tsCartItemsTable tr').each(function(index, row){
				if ($(row).data('docid'))
					showDocumentAddCartLink($(row).data('docid'));
			});

			rebuildTSSearchBar();
			if(tsDocumentViewerCurrentDocumentID)
				rebuildViewerToolbar(tsDocumentViewerCurrentDocumentID);

			if (cart_LoadedInDiv)
				cart_loadInline();
			else
				self.location.href = self.location.href;
		};
		TS_AJX('CART','removeAllDocs',objParams,removeAllDocsResult,removeAllDocsResult,20000);
	}
}

function c_removeDoc(cartid) {
	var msg = 'Are you sure that you want to remove this document from your cart?';
	if (confirm(msg)) {
		document.getElementById('tr' + cartid).style.background = '#ccc';
		var objParams = { cartid:cartid };
		var removeDocResult = function(r) { 
			if (r.success && r.success == 'true') {
				var removedDocID = $('#tr' + r.cartid).data('docid');
				showDocumentAddCartLink(removedDocID);

				rebuildTSSearchBar();
				if(tsDocumentViewerCurrentDocumentID)
					rebuildViewerToolbar(tsDocumentViewerCurrentDocumentID);

				if (r.itemcount <= 0) {
					if (cart_LoadedInDiv)
						cart_loadInline();
					else
						self.location.href = self.location.href;
				} else {
					document.getElementById('tr' + r.cartid).style.display = 'none';
					var cartTotals = {};
					cartTotals.subtotal = r.st;
					cartTotals.pc = r.pc;
					cartTotals.tax = r.t;
					c_showTotals(cartTotals);
				}
			}
		};
		TS_AJX('CART','removeDoc',objParams,removeDocResult,removeDocResult,20000);
	}
}

function c_loadTotals() {
	var loadTotalsResult = function(r) { 
		if (r.success && r.success == 'true') {
			var cartTotals = {};
			cartTotals.subtotal = r.st;
			cartTotals.pc = r.pc;
			cartTotals.tax = r.t;
			c_showTotals(cartTotals);
		}
	};
	TS_AJX('CART','getCartTotalsStruct',{},loadTotalsResult,loadTotalsResult,20000);
}

function c_showTotals(cartTotals) {
	cartTotals.ordertotal = (cartTotals.pc <= 0 && cartTotals.tax <= 0 ? cartTotals.subtotal : (cartTotals.pc > 0 ? cartTotals.subtotal + cartTotals.tax - cartTotals.pc : cartTotals.subtotal + cartTotals.tax));
	var tbl = '<table border="0" cellpadding="3" cellspacing="0" align="right"><tr bgcolor="#DEDEDE"><td colspan="2" class="tsAppBodyText tsAppBB tsAppBT"><b>Totals</b></td></tr>';
	
	// if we dont have pc or tax just show amount
	if (cartTotals.pc <= 0 && cartTotals.tax <= 0)
		tbl += '<tr><td class="tsAppBodyText" align="right"><b>Order Total:</b></td><td class="tsAppBodyText" align="right" width="70">' + formatCurrency(cartTotals.ordertotal) + '</td></tr>';

	// else show additional line items
	else {
		tbl += '<tr><td class="tsAppBodyText" align="right"><b>Subtotal:</b></td><td class="tsAppBodyText" align="right" width="70">' + formatCurrency(cartTotals.subtotal) + '</td></tr>';
		if (cartTotals.pc > 0)
			tbl += '<tr><td class="tsAppBodyText" align="right"><b>Purchase Credit:</b></td><td class="tsAppBodyText" align="right">(' + formatCurrency(cartTotals.pc) + ')</td></tr>';
		if (cartTotals.tax > 0)
			tbl += '<tr><td class="tsAppBodyText" align="right"><b>Sales Tax:</b></td><td class="tsAppBodyText" align="right">' + formatCurrency(cartTotals.tax) + '</td></tr>';
		tbl += '<tr><td class="tsAppBodyText tsAppBT50" align="right"><b>Order Total:</b></td><td class="tsAppBodyText tsAppBT50" align="right">' + formatCurrency(cartTotals.ordertotal) + '</td></tr>';
	}
	
	tbl += '</table>';
	if(document.getElementById('divTotals') != null)
		document.getElementById('divTotals').innerHTML = tbl;
}

function formatCurrency(num) {
	num = num.toString().replace(/\$|\,/g,'');
	if(isNaN(num)) num = "0";
	num = Math.abs(num);
	sign = (num == (num = Math.abs(num)));
	num = Math.floor(num*100+0.50000000001);
	cents = num%100;
	num = Math.floor(num/100).toString();
	if(cents<10) cents = "0" + cents;
	for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+
	num.substring(num.length-(4*i+3));
	return (((sign)?'':'-') + '$' + num + '.' + cents);
}

function doCheckoutDocumentCart(memberID, hasMissingTaxInfo){
	if(!hasMissingTaxInfo) {
		document.location.href = '/?pg=buyNow&item=DOCCART-' + memberID;
	}
	else {
		let arrReq = new Array();
		let billingState = $('#billingState').val();
		let billingZip = $('#billingZip').val();		

		if (billingState == '') arrReq[arrReq.length] = 'Select your billing state.';
		if (billingZip == '') arrReq[arrReq.length] = 'Enter a valid billing postal code.';
		if (billingState.length && billingZip.length && !mc_isValidBillingZip(billingZip,0,billingState))
			arrReq.push('Invalid Postal Code.');
		
		if (arrReq.length > 0) {
			$('div#viewCartTaxInfoErr').html(arrReq.join('<br/>')).show();
		} 
		else {
			$('div#viewCartTaxInfoErr').html('').hide();
			$('form#frmTaxInfo').submit();
		}
	}
}

// The following functions are used by fileshare2
function fs_clearPostFilters(sid, filter) {
	$('select#s_postfilter').val('');
	b_doSearchFilter(sid,filter,'');
}	

function fs_postfilter(sid, filter) {
	var postfilter = '';
	postfilter = $("select#s_postfilter :selected").map(function(i, el) {
		return $(el).val();
	}).get().join();
	
	b_doSearchFilter(sid,filter,postfilter);
}	

function fs_togglePriorVersions(srid) {
	if (document.getElementById('' + srid))	{
		if (document.getElementById('' + srid).style.display == 'block') {
			document.getElementById('' + srid).style.display = 'none';
		} else {
			document.getElementById('' + srid).style.display = 'block';
		}
	}
}

// used by the Website bucket
function ws_showMore() {
	$.colorbox( { innerWidth:430, innerHeight:350, href:'#winMore', inline:true, overlayClose:false} );
}

// used by the Publications bucket
function pub_showMore(beid) {
	$.colorbox( { innerWidth:430, innerHeight:350, href:'#winMore_'+beid, inline:true, overlayClose:false} );
}

// used for flashing Tip in results
var fadeTime = 1000.0;

function fade() {
  var elem = document.getElementById('divFade');
  if(!elem) return;
  if(elem.FadeState == null) {
    if(elem.style.opacity == null || elem.style.opacity == '' || elem.style.opacity == '1') {
      elem.FadeState = 2;
    } else {
      elem.FadeState = -2;
    }
  }
  if(elem.FadeState == 1 || elem.FadeState == -1) {
    elem.FadeState = elem.FadeState == 1 ? -1 : 1;
    elem.FadeTimeLeft = fadeTime - elem.FadeTimeLeft;
  } else {
    elem.FadeState = elem.FadeState == 2 ? -1 : 1;
    elem.FadeTimeLeft = fadeTime;
    setTimeout("flasherFade(" + new Date().getTime() + ",'divFade')", 33);
  }  
}

function flasherFade(lastTick, elemID) {  
  var curTick = new Date().getTime();
  var elapsedTicks = curTick - lastTick;
 
  var elem = document.getElementById(elemID);
 
  if(elem.FadeTimeLeft <= elapsedTicks)
  {
    elem.style.opacity = elem.FadeState == 1 ? '1' : '0';
    elem.style.filter = 'alpha(opacity = '
        + (elem.FadeState == 1 ? '100' : '0') + ')';
    elem.FadeState = elem.FadeState == 1 ? 2 : -2;
    return;
  }
 
  elem.FadeTimeLeft -= elapsedTicks;
  var newOpVal = elem.FadeTimeLeft/fadeTime;
  if(elem.FadeState == 1)
    newOpVal = 1 - newOpVal;

  elem.style.opacity = newOpVal;
  elem.style.filter = 'alpha(opacity = ' + (newOpVal*100) + ')';
 
  setTimeout("flasherFade(" + curTick + ",'" + elemID + "')", 33);
}

var flashID = 0;
function flashIt() {
	setTimeout("fade('divFade')", 10);
	setTimeout("fade('divFade')", 1000);
	setTimeout("fade('divFade')", 2000);
	setTimeout("fade('divFade')", 3000);
	setTimeout("fade('divFade')", 4000);
	setTimeout("fade('divFade')", 5000);
}
function startFlash() {
	var elem = document.getElementById('divFade'); 
	if (elem) {
		elem.style.display = 'block';
		flashIt();
		flashID = setInterval(flashIt, 20000);
	} else {
		setTimeout(startFlash,1000);
	}
}

// used to preload images for search results
function preloadSearchResultsImages() {
	if (document.images) {
		pic1 = new Image(46,16);
		pic1.src="/assets/common/images/search/testarrow.png";
		pic2 = new Image(450,50);
		pic2.src="/assets/common/images/search/searchLeftArrow.png";
	}
}

//check if search report bucket types are finished
function isSearchReportReady() {
	var isReady = true;
	for (var i=bucketarray.length-1; i >= 0; i--) {
		if ((bucketarray[i].req != null)&&(arrSearchReportBucketTypes.indexOf(bucketarray[i].btid))) { 
			isReady = false;
			break;
		}
	}

	if (isReady) {
		$(".searchReportButton").toggleClass("hiddenButton");
		clearInterval(isSearchReportReadyIntervalID);
	}
}

function TSDocumentViewerIsFullscreen() {
	var viewerElement = $(displayArea_docviewer)[0];
	var viewerElementContainer = $(displayArea_docviewer)[0].parentElement;

	return ($(viewerElementContainer).hasClass('tsAppFullscreen'));
}

function TSDocumentViewerActivateFullscreen() {
	var viewerElement = $(displayArea_docviewer)[0];
	var viewerElementContainer = $(displayArea_docviewer)[0].parentElement;

	$(viewerElementContainer).toggleClass('tsAppFullscreen');
	$(viewerElementContainer).css( "background-color","#ffffff");
	tsDocumentViewerHeight = $(viewerElement).css("height");
	$(viewerElement).height('100%');
}

function TSDocumentViewerDeactivateFullscreen() {
	var viewerElement = $(displayArea_docviewer)[0];
	var viewerElementContainer = $(displayArea_docviewer)[0].parentElement;

	//already fullscreen
	$(viewerElementContainer).toggleClass('tsAppFullscreen');
	$(viewerElementContainer).css( "background-color","");
	$(viewerElement).css("height",tsDocumentViewerHeight);
}

function TSDocumentViewerToggleFullscreen() {
	var viewerElement = $(displayArea_docviewer)[0];
	var viewerElementContainer = $(displayArea_docviewer)[0].parentElement;

	// if (screenfull.enabled) {
	// 	screenfull.request(viewerElementContainer);
	// }

	if (TSDocumentViewerIsFullscreen()) {
		//already fullscreen
		$('#docViewerNav_Fullscreen i').removeClass('fa-compress').addClass('fa-expand');
		TSDocumentViewerDeactivateFullscreen();
	} else {
		$('#docViewerNav_Fullscreen i').removeClass('fa-expand').addClass('fa-compress');
		TSDocumentViewerActivateFullscreen();
	}
}

function TSDocumentViewerScreenfullEventListener() {
	var viewerElement = $(displayArea_docviewer)[0];
	var viewerElementContainer = $(displayArea_docviewer)[0].parentElement;

	if (TSDocumentViewerIsFullscreen()) {
		//already fullscreen
		TSDocumentViewerDeactivateFullscreen();
	} else {
		TSDocumentViewerActivateFullscreen();
	}
}


$(document).ready(function() {

	var overrideDocButtons = function(){
		var currentDocumentID = TSDocumentViewerGetActiveDocument();
		var currentCartActionButton = $('#docViewerNav_addCart')[0] || $('#docViewerNav_removeCart')[0];
		if (tsDownloadableDocumentIDs.hasOwnProperty(currentDocumentID)) {
			showDownload(currentDocumentID);
		} else {
			TSDocumentViewerTriggerOneClickPurchase(currentDocumentID);
		}
	}

	$('body').on('tsPrintButtonPressed',overrideDocButtons);
	$('body').on('tsSaveButtonPressed',overrideDocButtons);

	$('body').on('tsFullscreenButtonPressed', function(){
		TSDocumentViewerToggleFullscreen();
	});

	if ($('#bk_sid_holder') && $.isNumeric($('#bk_sid_holder').text()))
		setSearchID($('#bk_sid_holder').text())

	displayArea_mainscreen = $('#s_listing');
	displayArea_docviewer = $('#tsDocumentViewer');
	displayArea_inlineCart = $('#tsCartViewer');
	$('#viewDocPromptModal').click(function() {	
		$('#viewDocPromptModal').modal('hide');
	});
});

window.addEventListener('hashchange', function(){
	_url = window.location.href;
   if(_url.indexOf("docviewer") == '-1')
   location.reload();
});

// Document viewer 6.3.2
function docViewer(docid,itemtypeid,sid) {
	location.hash = 'docviewer';
	var default_jsonurl = '/?event=proxy.ts_json&c=tsDocDownload&m=html5view';
 	var thisItemtypeid = typeof itemtypeid !== 'undefined' ? itemtypeid : 0;

	var objParams = { did:docid, itemtypeid:thisItemtypeid, sid:sid ?? 0 };
	// capture value of tsDocumentViewerAutoShowDocumentOnLoad when function is called
	var tmpTsDocumentViewerAutoShowDocumentOnLoad = tsDocumentViewerAutoShowDocumentOnLoad;

	$.getJSON( default_jsonurl, objParams)
		.done(function( data ) {
			if (data.RESULT) {
				var isUnhandledResult = false;
				TSDocumentViewerUseVersion2 = false;				
				TSDocumentViewerClearActiveDocument();
				tsDestroyDocumentViewer();
				TSDocumentViewerClearActiveDocument();
				TSDocumentViewerSetActiveDocument(docid,itemtypeid,sid);
				tsDocumentViewerAutoShowDocumentOnLoad = null;
				switch (data.RESULT) {
					case "viewingAllowed":
						var urlParams = new URLSearchParams(window.location.search);
						var docType = urlParams.get('doctype');
						if (docType == null) { docType = 'xod'; }

						if ($(window).width() <= 800) {
							if (!TSDocumentViewerIsFullscreen()) {
								TSDocumentViewerActivateFullscreen();
							}
						};

						if (data.fullrights == 1) tsDownloadableDocumentIDs[docid] = 1;
						$("html, body").animate({ scrollTop: 0 }, "fast");
						if (!tsDocumentViewerControl) {
							const wvElement = document.getElementById('tsDocumentViewer');							
							tsDocumentViewerControl = new PDFTron.WebViewer({
								type: "html5",
								path: "/sitecomponents/COMMON/javascript/webviewer/6.3.2",
								documentId: docid,
								mobileRedirect: false,
								initialDoc: data.theLink,
								documentType: docType,
								enableAnnotations: false,
								ui: 'beta',
								config: "/sitecomponents/COMMON/javascript/webviewer/6.3.2/trialsmithconfig.js",
								encryption: {
									p: data.namep,
									type: "aes",
									error: function(msg) { alert(msg); }
								}
							}, wvElement);
							$(displayArea_docviewer).one('documentLoaded', function(event, data){
								tsDocumentViewerControl.setLayoutMode(tsDocumentViewerControl.instance.LayoutMode.Continuous);
								if (tsDocumentViewerControl && tsDocumentViewerControl.selectedType && tsDocumentViewerControl.selectedType == 'html5Mobile') {
									if (!TSDocumentViewerIsFullscreen()) {
										TSDocumentViewerActivateFullscreen();
									}
								}
							});
						}
					break;
					case "requireLogin":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Login Required (Document ID: ' + data.documentID+ ')</div><div class="tsAppBodyText">You are not currently logged in. Please login and attempt to view this document again.</div>');
						$(displayArea_docviewer).append('<br><button style="width:200px;" class="tsAppHeading" onClick="document.location.href=\'/?pg=login\';">Login</button>');
					break;

					case "documentDisabled":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Document Not Online (' + data.documentID + ' - ' + data.style + ' - ' + data.expertName + ' - ' + data.documentDate + ')</div><div class="tsAppBodyText">This document may not yet be available, or the document may be disabled.<br/><br/>If you need this transcript, call TrialSmith at ************ or <a href="mailto:<EMAIL>?subject=Request%20for%20Missing%20Document%3A%20' + data.documentID + '%20-%20' + data.expertName + '&body=Notes%20for%20TrialSmith%3A%0D%0ARequested%20by%20' + data.subscriberName + '%20(' + data.depomemberdataid + ')%20%0D%0ADocument%20' + data.documentID + '%20is%20disabled%3B%20please%20determine%20if%20this%20document%20is%20available.">click here to email us</a>.</div>');
					break;
					case "documentNotAvailable":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Document Not Online (' + data.documentID + ' - ' + data.style + ' - ' + data.expertName + ' - ' + data.documentDate + ')</div><div class="tsAppBodyText">This document may not yet be available, or the document may be disabled.<br/><br/>If you need this transcript, call TrialSmith at ************ or <a href="mailto:<EMAIL>?subject=Request%20for%20Missing%20Document%3A%20' + data.documentID + '%20-%20' + data.expertName + '&body=Notes%20for%20TrialSmith%3A%0D%0ARequested%20by%20' + data.subscriberName + '%20(' + data.depomemberdataid + ')%20%0D%0ADocument%20' + data.documentID + '%20is%20not%20available%20online%3B%20please%20determine%20if%20this%20document%20is%20available.">click here to email us</a>.</div>');
					break;
					case "documentPurchaseRequired":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Restricted Viewing (' + data.documentID + ' - ' + data.style + ' - ' + data.expertName + ' - ' + data.documentDate + ')</div><div class="tsAppBodyText">This document cannot be viewed before it is purchased.<br/><br/>If you need assistance with this transcript, call TrialSmith at ************ or <a href="mailto:<EMAIL>?subject=Request%20for%Assistance%20with%20Document%3A%20' + data.documentID + '%20-%20' + data.expertName + '&body=Notes%20for%20TrialSmith%3A%0D%0ARequested%20by%20' + data.subscriberName + '%20(' + data.depomemberdataid + ')%20%0D%0ADocument%20' + data.documentID + 'requires%20purchase%20before%20viewing%20online.">click here to email us</a>.</div>');
					break;
					case "upgradeRequired":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Restricted Viewing (' + data.documentID + ' - ' + data.style + ' - ' + data.expertName + ' - ' + data.documentDate + ')</div><div class="tsAppBodyText">You must <a href="/?pg=upgradeTrialSmith">Upgrade your subscription</a> in order to preview documents before purchasing.<br/><br/>Contact TrialSmith at ************ to upgrade your account or <a href="mailto:<EMAIL>?subject=Request%20for%Assistance%20with%20Document%3A%20' + data.documentID + '%20-%20' + data.expertName + '&body=Notes%20for%20TrialSmith%3A%0D%0ARequested%20by%20' + data.subscriberName + '%20(' + data.depomemberdataid + ')%20%0D%0ADocument%20' + data.documentID + 'requires%20upgrade%20before%20viewing%20online.">click here to email us</a>.</div>');
					break;		

					default:
						alert('Error loading message. Try again: ' + data.RESULT);
						isUnhandledResult = true;
				}

				if (isUnhandledResult)
					tsCloseDocumentViewer();
				else {
					$('#nav_documentViewer').show();
					$(displayArea_docviewer).show();
					$('#tsdocumentviewerbar,#documentViewerBarMobile').show();
					$('#tssearchbar,#searchBarMobile').hide();
					$(displayArea_mainscreen).hide();
					$('#myDocumentsTabs').hide();
					$('#searchMyDocumentsButtonDiv').hide();
					rebuildViewerToolbar(data.documentID, data.documentCartID);
				}
			}
		})
		.fail(function( jqxhr, textStatus, error ) {
		  var err = textStatus + ', ' + error;
		  $('#loadingIndicator').hide();
		});
}

function docViewer2(docid,itemtypeid) {
	var default_jsonurl = '/?event=proxy.ts_json&c=orgDocDownload&m=html5view';
 	var thisItemtypeid = typeof itemtypeid !== 'undefined' ? itemtypeid : 0;

	var objParams = { did:docid,itemtypeid:thisItemtypeid};
	// capture value of tsDocumentViewerAutoShowDocumentOnLoad when function is called
	var tmpTsDocumentViewerAutoShowDocumentOnLoad = tsDocumentViewerAutoShowDocumentOnLoad;

	$.getJSON( default_jsonurl, objParams)
		.done(function( data ) {
			if (data.RESULT) {
				var isUnhandledResult = false;
				TSDocumentViewerUseVersion2 = true;
				TSDocumentViewerClearActiveDocument();
				tsDestroyDocumentViewer();
				TSDocumentViewerClearActiveDocument();
				TSDocumentViewerSetActiveDocument(docid,itemtypeid,null);
				tsDocumentViewerAutoShowDocumentOnLoad = null;

				switch (data.RESULT) {
					case "viewingAllowed":
						var urlParams = new URLSearchParams(window.location.search);
						var docType = urlParams.get('doctype');
						if (docType == null) { docType = 'xod'; }

						if (data.fullrights == 1) tsDownloadableDocumentIDs[docid] = 1;

						if ($(window).width() <= 800) {
							if (!TSDocumentViewerIsFullscreen()) {
								TSDocumentViewerActivateFullscreen();
							}
						};

						if (!tsDocumentViewerControl) {
							const wvElement = document.getElementById('tsDocumentViewer');							
							tsDocumentViewerControl = new PDFTron.WebViewer({
								type: "html5",
								path: "/sitecomponents/COMMON/javascript/webviewer/6.3.2",
								documentId: docid,
								mobileRedirect: false,
								initialDoc: data.theLink,
								documentType: docType,
								enableAnnotations: false,
								ui: 'beta',
								config: "/sitecomponents/COMMON/javascript/webviewer/6.3.2/websitedocumentconfig.js"
							}, wvElement);

							$("html, body").animate({ scrollTop: 0 }, "fast");
							$(displayArea_docviewer).one('documentLoaded', function(event, data){
								tsDocumentViewerControl.setLayoutMode(tsDocumentViewerControl.instance.LayoutMode.Continuous);
							});
						}
					break;
					case "documentNotAvailable":
						$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Please Download this Document</div><div class="tsAppBodyText">This document format is not viewable online, but is available for immediate download.<br/><br/><button onclick="window.open(\'' + data.downloadlink + '\')">Download</button>');
					break;

					default:
						alert('Error loading message. Try again: ' + data.RESULT);
						isUnhandledResult = true;
				}

				if (isUnhandledResult)
					tsCloseDocumentViewer();
				else {
					$('#nav_documentViewer').show();
					$(displayArea_docviewer).show();
					$('#tsdocumentviewerbar,#documentViewerBarMobile').show();
					$('#tssearchbar,#searchBarMobile').hide();
					$(displayArea_mainscreen).hide();
					$('#myDocumentsTabs').hide();
					$('#searchMyDocumentsButtonDiv').hide();
					rebuildViewerToolbar(data.documentID, data.documentCartID);
				}
			}
		})
		.fail(function( jqxhr, textStatus, error ) {
		  var err = textStatus + ', ' + error;
		  $('#loadingIndicator').hide();
		});
}


function tsDestroyDocumentViewer() {
	try {tsDocumentViewerControl.instance.clearSidePanelData();} catch(e){}
	try {tsDocumentViewerControl.instance.closeDocument();} catch(e){}
	try {tsDocumentViewerControl.dispose();} catch(e){}
	try {tsDocumentViewerControl = null;} catch(e){}
	try {var iframeWindow = $('#tsDocumentViewer iframe')[0].contentWindow;} catch(e){}
	try {iframeWindow.$(iframeWindow.document).off();} catch(e){}
	try {$(displayArea_docviewer).empty();} catch(e){}
}

function tsCloseDocumentViewer() {
	let tsCloseDocumentID = tsDocumentViewerCurrentDocumentID;
	if (TSDocumentViewerIsFullscreen()) TSDocumentViewerDeactivateFullscreen();
	TSDocumentViewerClearActiveDocument();
	$('#nav_documentViewer').hide();
	$(displayArea_docviewer).hide();
	$('#tsdocumentviewerbar,#documentViewerBarMobile').hide();
	$('#tssearchbar,#searchBarMobile').show();
	$(displayArea_mainscreen).show();
	if ($('#myDocumentsTabs')) $('#myDocumentsTabs').show();
	if ($('#searchMyDocumentsButtonDiv')) $('#searchMyDocumentsButtonDiv').show();
	TSDocumentShowDocumentDiv(tsCloseDocumentID);
}

function isTsDocumentViewerOpen() {
	return ($(displayArea_docviewer).is(":visible"));
}

function TSDocumentViewerGetActiveDocument() {
	return tsDocumentViewerCurrentDocumentID;
}

function TSDocumentViewerSetActiveDocument(docid,itemtypeid,sid) {
	tsDocumentViewerCurrentDocumentID = docid;
	tsActiveDocItemTypeID = itemtypeid;
	tsActiveDocViaSearchID = sid;
	$('.tsDocViewerDocument[data-tsdocumentid='+ docid +']').addClass('tsDocViewerActiveDocument');
}

function TSDocumentViewerClearActiveDocument() {
	tsDocumentViewerCurrentDocumentID = null;
	tsActiveDocItemTypeID = null;
	tsActiveDocViaSearchID = null;
	$('.tsDocViewerActiveDocument').removeClass('tsDocViewerActiveDocument');
}

function TSDocumentShowDocumentDiv(docid) {
	let TSDocContainer = $('.tsDocViewerDocument[data-tsdocumentid='+docid+']');
	if(TSDocContainer.length && TSDocContainer.is(':visible')) {
		$('html, body').animate({
			scrollTop: TSDocContainer.offset().top - 300
		}, 750);
	}
}

function TSDocumentViewerMarkDocumentUnavailable(docid) {
	$('.tsDocViewerDocument[data-tsdocumentid='+ docid +']').addClass('tsDocViewerUnavailableDocument').removeClass('tsDocViewerDocument');
}

function TSDocumentViewerGetPageInfo() {
	var returnObj = {}
	var element;

	if ($('.s_pgtop').length)
		element = $('.s_pgtop');
	else
		element = $('#seachResultsPaginationBar');

	returnObj.currentPage = parseInt($(element).data('currentpage'));
	returnObj.totalPages = parseInt($(element).data('totalpages'));
	return returnObj;
}


function TSDocumentViewerNextDocumentExists() {
	// using nextAll.first to skip siblings marked as unavailable
	var documentID = $('.tsDocViewerActiveDocument').nextAll('.tsDocViewerDocument').first().data('tsdocumentid');
	var pageInfo = TSDocumentViewerGetPageInfo();
	var currentPage = pageInfo.currentPage;
	var totalPages = pageInfo.totalPages;
	
	return (documentID || (currentPage < totalPages));
}

function TSDocumentViewerPreviousDocumentExists() {
	// using prevAll.first to skip siblings marked as unavailable
	var documentID = $('.tsDocViewerActiveDocument').prevAll('.tsDocViewerDocument').first().data('tsdocumentid');
	var pageInfo = TSDocumentViewerGetPageInfo();
	var currentPage = pageInfo.currentPage;


	return (documentID || (currentPage > 1));
}

function TSDocumentViewerNextDocument() {
	// using nextAll.first to skip siblings marked as unavailable
	var documentID = $('.tsDocViewerActiveDocument').nextAll('.tsDocViewerDocument').first().data('tsdocumentid');
	var pageInfo = TSDocumentViewerGetPageInfo();
	var currentPage = pageInfo.currentPage;
	var totalPages = pageInfo.totalPages;
	var nextPageButton = $('#tsSearchTopPagingControls .bucketPagingButtonNextPage');
	
	if (documentID)
		if (TSDocumentViewerUseVersion2) 
			docViewer2(documentID,tsActiveDocItemTypeID);
		else 
			docViewer(documentID,tsActiveDocItemTypeID,tsActiveDocViaSearchID);
	else if (currentPage < totalPages) {
		tsDocumentViewerAutoShowDocumentOnLoad = 'first';
		$(nextPageButton)[0].click();
	}
}

function TSDocumentViewerPreviousDocument() {
	// using prevAll.first to skip siblings marked as unavailable
	var documentID = $('.tsDocViewerActiveDocument').prevAll('.tsDocViewerDocument').first().data('tsdocumentid');
	var pageInfo = TSDocumentViewerGetPageInfo();
	var currentPage = pageInfo.currentPage;
	var totalPages = pageInfo.totalPages;
	var previousPageButton = $('#tsSearchTopPagingControls .bucketPagingButtonPreviousPage');
	
	if (documentID)
		if (TSDocumentViewerUseVersion2) 
			docViewer2(documentID,tsActiveDocItemTypeID);
		else 
			docViewer(documentID,tsActiveDocItemTypeID,tsActiveDocViaSearchID);
	else if (currentPage > 1) {
		tsDocumentViewerAutoShowDocumentOnLoad = 'last';
		$(previousPageButton)[0].click();
	}
}

function TSDocumentViewerFirstDocumentOnPage() {
	var documentID = $('.tsDocViewerDocument').first().data('tsdocumentid');

	if (documentID)
		if (TSDocumentViewerUseVersion2) 
			docViewer2(documentID,tsActiveDocItemTypeID);
		else 
			docViewer(documentID,tsActiveDocItemTypeID,tsActiveDocViaSearchID);
}

function TSDocumentViewerLastDocumentOnPage() {
	var documentID = $('.tsDocViewerDocument').last().data('tsdocumentid');
	
	if (documentID)
		if (TSDocumentViewerUseVersion2) 
			docViewer2(documentID,tsActiveDocItemTypeID);
		else 
			docViewer(documentID,tsActiveDocItemTypeID,tsActiveDocViaSearchID);
}

function TSDocumentHasDownloadOption(documentID) {
	return $('button[data-downloaddocid="'+documentID+'"]').length == 1;
}

function TSDocumentDownloadElement(documentID) {
	return $('button[data-downloaddocid="'+documentID+'"]');
}

function TSDocumentHasDropboxSaveOption(documentID) {
	return $('button[data-dropboxdownloaddocid="'+documentID+'"]').length == 1;
}

function TSDocumentDropboxSaveElement(documentID) {
	return $('button[data-dropboxdownloaddocid="'+documentID+'"]');
}

function buildTSSearchBar(bucketid,searchid) {
	var objParamsBanner = { s_a:'ajx', bucketid:bucketid, searchid:searchid };
	TS_AJX('SBS','getTopBanner',objParamsBanner,b_getTopBannerResult);
}

function rebuildTSSearchBar() {
	buildTSSearchBar(vars_bid,getSearchID());
}

function removeCartDocFromViewerBar(cartid,documentID) {
	var msg = 'Are you sure that you want to remove this document from your cart?';
	if (confirm(msg)) {
		var objParams = { cartid:cartid };
		var atcLink = document.getElementById('s_addtocart' + documentID);
		var inCartLink = document.getElementById('s_incart' + documentID);

		var removeDocResult = function(r) { 
			if (r.success && r.success == 'true') {
				rebuildTSSearchBar();
				rebuildViewerToolbar(documentID);
				if (atcLink) $(atcLink).show();
				if (inCartLink)	$(inCartLink).hide();
			} else self.location.href = self.location.href;
		};
		TS_AJX('CART','removeDoc',objParams,removeDocResult,removeDocResult,20000);
	}
}

function addToCartFromViewer(docid,element,sid) {
	var arrPOS = findPos(element);
	var atcdiv = atc_get_atc_div();

	// adjust for link height
	arrPOS[1] += $(element).height();

	// create dialog
	var dialog = '<div><span id="modalpopupreq"></span><div id="modalpopuphead">Add Document To Cart</div></div>You must purchase this document in order to print or download. <br/><br/>Keep your accountant happy! A case reference or memo for this document appears on your statement to help track your purchase.<br/><table cellspacing="0" cellpadding="0" style="margin-top:6px;position:relative;"><tr><td class="tsAppBodyText">Memo: </td><td><input type="text" value="" size="30" maxlength="50" class="tsAppBodyText" name="caseref" id="caseref"> <input type="button" value="Add to Cart" class="tsAppBodyText" name="btnATC" autocomplete="off" onclick="addToCartDo(\'' + docid + '\',document.getElementById(\'caseref\').value,' + (sid ?? 0) + ');"></td></tr>';
	if (vars_caserefs.length > 0)
		dialog += '<tr><td></td><td><select id="modalpopupSuggestResults" class="tsAppBodyText" size="4" onclick="atc_setSearch(this)" onchange="atc_setSearch(this)"></select></td></tr><tr><td colspan="2"><br/><br/><br/></td></tr>';
	dialog += '</table><div class="modalpopupinput" align="right"><a href="javascript:hideAddToCart();">Cancel</a></div>';

	// write dialog
	atcdiv.innerHTML = '<div class="modalpopup tsAppBodyText"><div class="modalpopupcontent">' + dialog + '</div></div>';
	atcdiv.style.left = arrPOS[0]+'px';
	atcdiv.style.top = arrPOS[1]+'px';
	atcdiv.style.visibility = 'visible';

	// populate caserefs
	if (vars_caserefs.length > 0) {
		var txtCR = document.getElementById('caseref');
		var selbox = document.getElementById('modalpopupSuggestResults');
		for (var i=0; i < vars_caserefs.length; i++) {
			selbox.options[selbox.options.length] = new Option(vars_caserefs[i],vars_caserefs[i]);
		}
		var iHeight = vars_caserefs.length*15;
		if (vars_caserefs.length > 2) {
			selbox.style.height = (iHeight > 60) ? '60px' : iHeight + 'px';
		}
		selbox.style.width = txtCR.offsetWidth + 'px';
		selbox.style.visibility = 'visible';
	}
}

function TSDocumentViewerTriggerOneClickPurchase(documentID) {
	let docDownloadElement = TSDocumentDownloadElement(documentID)

	if (docDownloadElement && docDownloadElement.attr("id"))
		$('#'+ docDownloadElement.attr("id")).trigger('click');
}


function rebuildViewerToolbar(documentID, documentCartID) {
	var default_jsonurl = '/?event=proxy.ts_json&c=CART&m=getDocumentCartCount';
	$.getJSON( default_jsonurl, {})
		.done(function( r ) {
			var tsdocumentviewerbar = $('#tsdocumentviewerbar');
			tsdocumentviewerbar.empty();

			if (TSDocumentViewerPreviousDocumentExists())
				tsdocumentviewerbar.append('<li id="docViewerNav_Previous"><a href="javascript:TSDocumentViewerPreviousDocument();"><i class="fas fa-xl fa-caret-left" style="vertical-align: middle;"></i> Previous Doc</a></li>');
			if (TSDocumentViewerNextDocumentExists())
				tsdocumentviewerbar.append('<li id="docViewerNav_Next"><a href="javascript:TSDocumentViewerNextDocument();"><i class="fas fa-xl fa-caret-right" style="vertical-align: middle;"></i> Next Doc</a></li>');

			if (TSDocumentViewerUseVersion2 == false) {
				if (TSDocumentHasDownloadOption(documentID)) {
					let docDownloadElement = TSDocumentDownloadElement(documentID);
					
					tsdocumentviewerbar.append(`<li id="docViewerNav_downloadDocument"><a href="javascript:void(0);" onclick="TSDocumentViewerTriggerOneClickPurchase(${documentID});"><i class="fas fa-download"></i> ${(docDownloadElement.find("i").attr("title") ?? "Download")}</a></li>`);
					if (TSDocumentHasDropboxSaveOption(documentID)) {
						let docDropboxSaveElement = TSDocumentDropboxSaveElement(documentID);
						tsdocumentviewerbar.append('<li id="docViewerNav_dropboxSaveDocument"><a href="javascript:void(0);" onclick="$(\'#'+docDropboxSaveElement.attr("id")+'\').trigger(\'click\');"><img src="'+docDropboxSaveElement.find("img").attr("src")+'" width="16" height="16" border="0" align="absmiddle"> '+docDropboxSaveElement.find("img").attr("title")+'</a></li>');
					}
				} else if (tsDownloadableDocumentIDs.hasOwnProperty(documentID)) {
					tsdocumentviewerbar.append('<li id="docViewerNav_downloadDocument"><a href="javascript:void(0);" onclick="showDownload(' + documentID + ')"><i class="fas fa-download"></i> Download Doc</a></li>');
				}
				if (TSDocumentViewerAllowAddToCart && !tsDownloadableDocumentIDs.hasOwnProperty(documentID)) {
					if (documentCartID && documentCartID > 0) {
						tsdocumentviewerbar.append('<li id="docViewerNav_removeCart"><a href="javascript:void(0);" onclick="removeCartDocFromViewerBar(' + documentCartID + ',' + documentID + ')"><i class="fa-regular fa-cart-circle-xmark"></i> Remove from Cart</a></li>');
					} else {
						tsdocumentviewerbar.append('<li id="docViewerNav_addCart"><a href="javascript:void(0);" onclick="addToCartFromViewer(' + documentID + ',this.parentElement,' + tsActiveDocViaSearchID ?? 0 + ');"><i class="fa-regular fa-cart-circle-plus"></i> Add to Cart</a></li>');
					}
				}
				if (r.numitems){
					tsdocumentviewerbar.append('<li id="docViewerNav_ViewCart"><a href="javascript:cart_loadInline()"><i class="fa-regular fa-cart-shopping"></i> Doc Cart (' + r.numitems + ')</a></li>');
				}else{
					tsdocumentviewerbar.append('<li id="docViewerNav_ViewCart"><a href="javascript:cart_loadInline()"><i class="fa-regular fa-cart-shopping"></i> Doc Cart</a></li>');
				}
				tsdocumentviewerbar.append('<li id="nav_documentViewer" style="display:none;"><a href="javascript:tsCloseDocumentViewer();">Close Doc</a></li>');
			}
			if (tsDocumentViewerControl && tsDocumentViewerControl.selectedType && tsDocumentViewerControl.selectedType == 'html5Mobile')
				tsdocumentviewerbar.append('<li id="docViewerNav_Fullscreen"><a href="javascript:TSDocumentViewerToggleFullscreen();"><i class="fa-regular fa-expand"></i> Fullscreen</a></li>');

			tsdocumentviewerbar.append('<li id="nav_documentViewer"><a href="javascript:tsCloseDocumentViewer();"><i class="fas fa-xl fa-caret-left" style="vertical-align: middle;"></i> Back to List</a></li>');

			setViewerToolbarOptionsMobile();
		});
}

function setViewerToolbarOptionsMobile(){
	var documentViewerBarMobile = $('#documentViewerBarMobile');
	if(documentViewerBarMobile.length){
		documentViewerBarMobile.empty();
		if (TSDocumentViewerPreviousDocumentExists())
			documentViewerBarMobile.append('<a id="docViewerNavMobile_Previous" class="btn btn-large" href="javascript:TSDocumentViewerPreviousDocument();" title="Previous Doc"><i class="icon-circle-arrow-left icon-large"></i></a>');
		if (TSDocumentViewerNextDocumentExists())
			documentViewerBarMobile.append('<a id="docViewerNavMobile_Next" class="btn btn-large" href="javascript:TSDocumentViewerNextDocument();" title="Next Doc"><i class="icon-circle-arrow-right icon-large"></i></a>');
		documentViewerBarMobile.append('<a id="nav_documentViewerMobile" class="btn btn-large" href="javascript:tsCloseDocumentViewer();" title="Back to List"><i class="icon-level-up icon-large"></i></a>');
	}
}

function TSDocumentViewerCheckAutoShowDocument() {
	var documentArray = $('.tsDocViewerDocument');
	if ($(documentArray).length) {
		switch(tsDocumentViewerAutoShowDocumentOnLoad) {
			case "first":
				TSDocumentViewerFirstDocumentOnPage();
			break;
			case "last":
				TSDocumentViewerLastDocumentOnPage();
			break;
		}
	} else {
		tsDocumentViewerAutoShowDocumentOnLoad = null;
	}
}

function loadSearchSponsorRightSideContainer() {
	MCPromises.BackendPlatformServices.then(function() {
		var MCadlimit = 1;
		var MCadContainer = $("#searchSponsorRightSideContainer");
		var MCadSitecode = vars_sitecode;
		var MCadZoneType = 'Search Bucket Right Side';
		
		if ($(MCadContainer).length) {
			var overrideCallback = function (adsArray) {
				if (adsArray != null && adsArray.length) {
					for (var i=0;i<Math.min(adsArray.length,MCadlimit);i++) {
						$(MCadContainer).append('<a href="' + adsArray[i].ADLINK + '" title="Click for more information" target="_blank"><img style="width:' + adsArray[i].WIDTH + ';height:' + adsArray[i].HEIGHT + ';" src="' + adsArray[i].IMAGEURL + '" /></a>');
					}
				} else {
					$(MCadContainer).remove();
				}
			};												
			MCBackendPlatformServices.SponsorAdsService.MCIncludeAds(MCadContainer, MCadZoneType,MCadSitecode,MCadlimit,'_top',overrideCallback);
		}
	}).catch(error => {
		$("#searchSponsorRightSideContainer").remove();
		let msg = 'Failed to run loadSearchSponsorRightSideContainer';
		if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
			MCJSErrorReporting.promiseRejectionHandler(msg)
		else 
			console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`)
	});
}

// Medline
function loadMedlineAbstractText(pmid,sid) {
	var obj = b_getObjFromBid(vars_bid);
	if (obj) {
		displayArea_docviewer = $('#tsDocumentViewer');
		displayArea_mainscreen = $('#s_listing');

		TSDocumentViewerClearActiveDocument();
		TSDocumentViewerSetActiveDocument(pmid,null,sid);

		$('#nav_documentViewer').show();
		$('#tssearchbar').hide();
		$(displayArea_mainscreen).hide();

		$('#tsdocumentviewerbar').empty().hide();
		$('#tsdocumentviewerbar').append('<li id="docViewerNav_MyDocuments"><a href="/?pg=myDocuments&tab=PD"><i class="fa-regular fa-memo-circle-check"></i> My Purchased Docs</a></li>');
		$('#tsdocumentviewerbar').append('<li id="nav_documentViewer"><a href="javascript:tsCloseDocumentViewer();"><i class="fas fa-xl fa-caret-left" style="vertical-align: middle;"></i> Back to List</a></li>');
		$('#tsdocumentviewerbar').show();

		setMobileToolbarForMedlineAbstractText();

		$(displayArea_docviewer).html('<i class="icon-spin icon-spinner icon-3x"></i> Please wait while we load the abstract.').css("height","auto").css("padding","10px").css("border","1px solid #ccc").show();

		var default_jsonurl = '/?event=proxy.ts_json&c=SBT' + obj.btid + '&m=getAbstract&pmid=' + pmid;
		$.getJSON(default_jsonurl, function(response) { 
			if (!response.success) {
				$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Error retrieving the abstract.</div><div class="tsAppBodyText">If you continue to see this message, contact TrialSmith at ************ or <a href="mailto:<EMAIL>?subject=Missing%20Medline%20Article%20' + pmid + '">click here to email us</a>.</div>');
			} else {
				$(displayArea_docviewer).html(response.resulthtml);
			}
		}).fail(function() {
			$(displayArea_docviewer).html('<br/><div class="tsAppHeading">Error retrieving the abstract.</div><div class="tsAppBodyText">If you continue to see this message, contact TrialSmith at ************ or <a href="mailto:<EMAIL>?subject=Missing%20Medline%20Article%20' + pmid + '">click here to email us</a>.</div>');
		});
	}
}

function setMobileToolbarForMedlineAbstractText(){
	var documentViewerBarMobile = $('#documentViewerBarMobile');
	if(documentViewerBarMobile.length){
		documentViewerBarMobile.empty().hide();
		documentViewerBarMobile.append('<a id="nav_documentViewerMobile" class="btn btn-large" href="javascript:tsCloseDocumentViewer();" title="Back to List"><i class="icon-level-up icon-large"></i></a>');
		documentViewerBarMobile.show();
	}
}

function medlinePriceEstimates() {
	var documentArray = $('.medlineArticles');
	var obj = b_getObjFromBid(vars_bid);

	MCPromises.BackendPlatformServices.then(function() {
		$(documentArray).each(function (index, element) {
			var spanId = '#price' + $(element).data('tsdocumentid');
			$(spanId).html('(<i class="icon-spin icon-spinner"></i>)');
			$(spanId).show();

			var standardCallback = function (response) {
				if (response.success) {
					$(spanId).html('($' + response.estimatedPrice.toFixed(2) + ')');
					MCBackendPlatformServices.MCReprintsService.updatePriceEstimate(vars_sid, vars_bid, $(element).data('tsdocumentid'), response.estimatedPrice);
				}
				else { 
					$(spanId).html(response.error);
				}
			};			
			MCBackendPlatformServices.MCReprintsService.getPriceEstimate($(element).data('tsdocumentid'),  $(element).data('sn'),  $(element).data('year'), standardCallback);
		});
	}).catch(error => {
		let msg = 'Failed to run Medline Price Estimates';
		if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
			MCJSErrorReporting.promiseRejectionHandler(msg)
		else 
			console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);
	});

	tsDocumentViewerAutoShowDocumentOnLoad = null;
}

// Expert Connect
function downloadExpertMatches() {
	var downloadResult = function(r) {
		$('#downloadMatchBtnLabel').html('Download Matches')
		$('#btnDownloadExpMatches').prop('disabled',false);
		if (r.success && r.success.toLowerCase() == 'true'){
			document.location.href = '/tsdd/' + r.u;
		} else {
			alert('We were unable to download the expert matches. Try again!')
		}
	};
	
	$('#downloadMatchBtnLabel').html('<i class="icon-spin icon-spinner"></i> Please wait...');
	$('#btnDownloadExpMatches').prop('disabled',true);

	var obj = b_getObjFromBid(vars_bid);
	var objParams = { searchID:getSearchID(), bucketID:obj.bid };
	TS_AJX('SBT'+obj.btid,'downloadExpertMatches',objParams,downloadResult,downloadResult,120000,downloadResult);
}
function validateExpertInquiryStepChange(event, currentIndex, newIndex){
	var currentStep = currentIndex + 1;
	var arrReq = [];
	var emailRegEx = new RegExp(mc_emailregex,"i");
	$('.err_email_inq').remove();

	if (currentIndex > newIndex) {
		return true;
	}

	let objValues = getEmailInquiryFormValues();

	switch(currentStep) {
		case 1:
			var arrSelection = getSelectedRecipientDepoIDs();
			if(arrSelection.length == 0) arrReq.push('No selected Recipients.');
			break;
		case 2:
			if(!objValues.email.length) arrReq.push('Enter the E-mail Address.');
			else if (!emailRegEx.test(objValues.email)) arrReq.push('Enter a valid E-mail Address.');
			if(!objValues.firm.length) arrReq.push('Enter the Firm.');
			if(!(objValues.address1+objValues.address2+objValues.address3).trim().length) arrReq.push('Enter the Address.');
			if(!objValues.city.length) arrReq.push('Enter the City.');
			if(!objValues.stateCode.length) arrReq.push('Enter the State.');
			if(!objValues.postalCode.length) arrReq.push('Enter the Postal Code.');
			break;
		case 3:
			if(!objValues.topic.length) arrReq.push('Enter the Subject Line.');
			if(!objValues.descriptionOfAsk.length) arrReq.push('Tell lawyers the types of information you are seeking on the expert.');
			break;
	}
	
	if(arrReq.length) {
		var elID = 'section#expertInquiryWizard-p-' + currentIndex;
		$(elID).append('<div class="'+$('#err_email_inq').attr('class')+' err_email_inq" style="margin:10px 0 10px 0;">'+ arrReq.join('<br/>') +'</div>');
		return false;
	}
	else return true;
}
function onExpertInquiryStepChanged(event, currentIndex, priorIndex){
	var currentStep = currentIndex + 1;
	if(currentStep == 4){
		$('#divEmailInquiryPreview').hide();
		toggleInquiryPreviewLoading(true);
		loadEmailInquiryPreview();
	}
}
function toggleEmailInquiryForm(f){
	$('#divEmailInquiryContainer').toggle(f);
	$('#divExpertConnectResults').toggle(!f);
	if(f && !$("#expertInquiryWizard").hasClass('wizard')){
		$("#expertInquiryWizard").steps({
			headerTag: "h3",
			bodyTag: "section",
			autoFocus: false,
			titleTemplate: '<span class="number">#index#</span>',
			stepsOrientation: 1,
			onStepChanging: validateExpertInquiryStepChange,
			onStepChanged: onExpertInquiryStepChanged,
			onFinishing: saveEmailInquiry,
			onInit: function(){
				$('#expertInquiryWizard > .content > h3').each(function(index, elm){
					var elmID = $(elm).attr('id');
					$(".content").find(`[aria-labelledby='${elmID}']`).prepend('<div style="margin-bottom:1em;"><b>'+ $(elm).html() +'</b></div>');
				});
			},
			labels: {
				next: "Continue to Next Step",
				finish: "Send Now",
			}
		});

		$('#tblExpertConnectLawyersToContact').DataTable({
			"language": { 
				"search": "",
				"searchPlaceholder": "Search"
			},
			"order": [],
			"autoWidth": false,
			"columnDefs": [
				{ "width": "35%", "targets": 1 },
				{ "width": "35%", "targets": 2 },
				{ "width": "20%", "targets": 3 },
			]
		});
		
		// workaround to enable sorting on the selection column - with selection from all pages
		// using delegated event to be sure that the checkboxes always is bound to the change handler
		$('#tblExpertConnectLawyersToContact').on('change', ':checkbox.recipientCheckbox', function() {
			var thisRow = $(this).closest('tr');
			var ischecked = thisRow.find(':checkbox:checked').length;
			thisRow.find('p.elmCheckVal').text(parseInt(ischecked));
			thisRow.find('td').toggleClass('dt-cell-exclude', !ischecked);
			$('#tblExpertConnectLawyersToContact').DataTable().row(thisRow).invalidate('dom');
		});
	}
}
function getSelectedRecipientDepoIDs(){
	var arrExclusions = [];
	var dtRecipients = $('#tblExpertConnectLawyersToContact').DataTable();
	dtRecipients.$("input[name='recipientCheckbox']:not(:checked)", {"page": "all"}).each(function(index,elem){
		arrExclusions.push($(elem).val());
	});

	var arrAllDepoMembers = $('#inq_depoMemberDataIDList').val().trim().split(",");
	var arrSelectedDepoMembers = arrAllDepoMembers.filter( function( el ) {
		return !arrExclusions.includes(el);
	});

	return arrSelectedDepoMembers;
}
function toggleInquiryPreviewLoading(f){
	$('#divEmailInquiryPreviewLoading').toggle(f);
}
function loadEmailInquiryPreview(){
	toggleInquiryPreviewLoading(true);
	$("html, body").animate({ scrollTop: $('#divEmailInquiryPreviewLoading').offset().top - 300 }, "fast");

	// fetch preview email content
	var getContentResult = function(r) {
		if (r.success && r.success == 'true') {
			if(!$('#divInquiryEmailBody')[0].shadowRoot)
				$('#divInquiryEmailBody')[0].attachShadow({ mode: "open" });
			$('#divInquiryEmailBody')[0].shadowRoot.innerHTML = r.emailcontenthtml;
			var arrSelection = getSelectedRecipientDepoIDs();
			$('b#selRecipientsCount').text(arrSelection.length);
			toggleInquiryPreviewLoading(false);
			$('#divEmailInquiryPreview').show();
		}
		else {
			alert('some error occured while trying to load the preview.');
			toggleInquiryPreviewLoading(false);
		}
	};
	let objFormValues = getEmailInquiryFormValues();
	let objParams = {
		requestorName: $("#inq_requestorName").val(),
		expertName: $("#inq_expertName").val(),
		descriptionOfAsk: objFormValues.descriptionOfAsk,
		email: objFormValues.email,
		firm: objFormValues.firm,
		address1: objFormValues.address1,
		address2: objFormValues.address2,
		address3: objFormValues.address3,
		city: objFormValues.city,
		stateCode: objFormValues.stateCode,
		postalCode: objFormValues.postalCode,
		website: objFormValues.website
	}
	TS_AJX('ADMTSTOOLS','generateInquiryEmailHTMLFromData',objParams,getContentResult,getContentResult,60000,getContentResult);

	$('#spFromFirm').html(objFormValues.firm.length ? ' - ' + objFormValues.firm : '');
	$('#spReplyTo').html(objFormValues.email);
	$('#spSubject').html(objFormValues.topic);
}
function getEmailInquiryFormValues(){
	return {
		topic: $("#inq_topic").val().trim(),
		descriptionOfAsk: $("#inq_descriptionOfAsk").val().trim().replace(/\r?\n/g, '<br />'),
		email: $("#inq_email").val().trim(),
		firm: $("#inq_firm").val().trim(),
		address1: $("#inq_address1").val().trim(),
		address2: $("#inq_address2").val().trim(),
		address3: $("#inq_address3").val().trim(),
		city: $("#inq_city").val().trim(),
		stateCode: $("#inq_statecode").val().trim(),
		postalCode: $("#inq_postalcode").val().trim(),
		website: $("#inq_website").val().trim(),
		updateTSRecord:$('input[name="updateTSRecord"]:checked').val()
	};
}
function saveEmailInquiry(){
	$('#divEmailInquiryForm').hide();
	$('#divSendingInquiryLoading').show();
	$("html, body").animate({ scrollTop: $('#divSendingInquiryLoading').offset().top - 300 }, "fast");

	let objParams = getEmailInquiryFormValues();
	let arrSelectedDepoMembers = getSelectedRecipientDepoIDs();
	objParams.depoMemberDataIDList =  arrSelectedDepoMembers.join();
	objParams.searchid = getSearchID();

	var saveResults = function(r) {
		if (r.success && r.success == 'true') {
			$('#divEmailInquirySuccess').show();
		}
		else {
			alert('some error occured while trying to submit the email inquiry.');
			$('#divEmailInquiryForm').show();
		}
		$('#divSendingInquiryLoading').hide();
	};
	var objBucket = b_getObjFromBid(vars_bid);
	
	TS_AJX('SBT' + objBucket.btid,'saveEmailInquiry',objParams,saveResults,saveResults,60000,saveResults);
}

function viewDocPrompt(docid) {
	$('#viewDocPromptModal').modal({ keyboard: false, backdrop: 'static' });
}
function viewDocPopUp(docid,num) {
	var atcLink = document.getElementById('docLink'+num+docid);
	var arrPOS = findPos(atcLink);
	var atcdiv = atc_get_atc_div();

	// adjust for doc link
	arrPOS[0] -= 364;
	arrPOS[1] += 18;

	// create dialog
	var dialog = '<div><span id="modalpopupreq"></span><div id="modalpopuphead">Your Basic Plan only allows you to purchase documents a-la-carte.</div></div>Upgrade Your TrialSmith Membership to Read ENTIRE Transcripts Before Purchasing <br/><br/><table cellspacing="0" cellpadding="0" style="margin-top:6px;position:relative;"><tr "> <input type="button" value="Learn more"  class="tsAppBodyText" name="btnLearnMore" autocomplete="off" onclick="location.href='+"'/?pg=joinTrialSmith'"+'"></td></tr>';
	dialog += '</table><div class="modalpopupinput" align="right"><a href="javascript:hideAddToCart();">Close</a></div>';

	// write dialog
	atcdiv.innerHTML = '<div class="modalpopup tsAppBodyText"><div class="modalpopuparrow"><span>^_^dodo1</span></div><div class="modalpopupcontent">' + dialog + '</div></div>';
	atcdiv.style.left = arrPOS[0]+'px';
	atcdiv.style.top = arrPOS[1]+'px';
	atcdiv.style.visibility = 'visible';

	
}
function MCSearchBillingfInfoMessageHandler(event) {
	if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype) {
		
		switch (event.data.messagetype.toLowerCase()) {
			case "tspaymentformloadevent":
			case "tspaypaymentformloadevent":
				let ccFormHeight = 0;
				ccFormHeight = $('#oneClickPurchaseModal .modal-body #iframeBuyNow').height();
					
				$('#BillingCardFormLoading').hide();
				let modalBodyHeight = ccFormHeight < 600 ? ccFormHeight + 100 : 600;
				$('#oneClickPurchaseModal .modal-body').css('max-height',modalBodyHeight);
				break;

			case "tsgatewayevent":
					if (['cardadded','cardupdated'].indexOf(event.data.eventname.toLowerCase()) != -1 && event.data.tspayprofileid) {
						$('#CIMTable')
							.html('<img src="/assets/common/images/progress.gif" width="16" height="16" alt="Please wait..."> Please Wait...')
							.css({'height':'75px', 'padding':'5px'});
						$('#btnContinueOneClickPurchase,#btnCloseOneClickPurchasePrompt').prop('disabled',false);
						$('#btnContinueOneClickPurchase').trigger('click');
					}
					break;
		};

	} else {
		return false;
	}
}