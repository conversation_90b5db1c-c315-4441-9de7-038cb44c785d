function resizeContactIframe(){
	$("#contactIframe").height( $("#contactIframe").contents().find('.contactFrmHolder').outerHeight()+25);
}
function mobfun() {
	if ($(window).width() < 980) {

		$('.dropdown .dropdown-menu').css('height', 'auto');

		$(".dropdown").append("<span class='menu-arrow'></span>");
		$('.mainMenu').each(function() {
			$(this).closest('.megaMenuSection').addClass('dromenulist');
		})

	} else {

		$('.header .navbar .nav>li').children('.dropdown-menu').find('.megaMenuSection:first-child').addClass('show-menu');
		$('.dropdown .dropdown-menu').hide();

		$(".menu-arrow").remove();
		$('.mainMenu').each(function() {
			$(this).closest('.megaMenuSection').removeClass('dromenulist');
		})
		$('.megamenu.open-droupdown').removeClass('open-droupdown');
	}
}

$(window).on('resize', function() {
	mobfun();
})
$(document).ready(function() {
   
    var menuHtml="";
    $('.menuWrapper > ul > li').each(function() {
        var sectionTitle = $(this).children('a').first().text().trim();
        var newGroup = $('<li><a href="javascript:void(0);">' + sectionTitle + '</a><ul></ul></li>');

        $(this).find('> ul > li').each(function() {
            var iconImg = $(this).find('>ul > li li img').attr('src') || '';
            var title = $(this).find('>ul > li li a').first().text().trim() || '';
            var titleHref = $(this).find('> ul > li li a').first().attr("href") || '#';
            var description = $(this).find('>ul > li li').eq(2).text().trim() || '';

            var featureLinks = $(this).find('>ul>li').eq(2).find('a').map(function() {
                var icon = $(this).find('img').attr('src') || '';
                var text = $(this).text().trim() || '';
                var href = $(this).attr('href') || '';
                return `
					<li>
						<a href="${href}">
							<span class="link-icon"><img src="${icon}" alt="" /></span> ${text}
						</a>
					</li>`;
            }).get().join('');

            var headingBox = $(this).find(">ul>li").next();
            var heading = headingBox.find('ul > li').first().text().trim() || '';
            var desc2 = headingBox.find('ul > li').eq(1).text().trim() || '';
            var explore = headingBox.find(">ul").eq(0).find('li a').eq(0).text().trim() || '';
            var exploreHref = headingBox.find(">ul").eq(0).find('li a').eq(0).attr('href') || '#';
            var feature = headingBox.find(">ul").eq(0).find('li a').eq(1).text().trim() || '';
            var featureHref = headingBox.find(">ul").eq(0).find('li a').eq(1).attr('href') || '#';

            var html = `
				<li>
					<a href="${titleHref}">
						<span class="nav_icon"><img src="${iconImg}" alt="" /></span>
						<h4>${title}</h4>
						<p>${description}</p>
					</a>
					<ul>
						<li>
							<div class="menubox">
								<h3><a href="${featureHref}">${feature}</a></h3>
								<ul>${featureLinks}</ul>
							</div>
						</li>
						<li>
							<div class="menuImgBox">
								<h2 class="SubHeading">${heading}</h2>
								<h3 class="HeaderTextSmall">${desc2}</h3>
								<a href="${exploreHref}" class="BlueButton">${explore}</a>
							</div>
						</li>
					</ul>
				</li>
			`;
			
            newGroup.find('>ul').append(html);
        });
		if($.trim(newGroup.find('ul').html()).length == 0){
			newGroup.find('ul').remove();			
		}
        menuHtml += newGroup.prop("outerHTML");
    });
	var sheduleLink = "";
	if($(".menuWrapper > a").length){
		$(".menuWrapper > a").addClass("MCButton");
		sheduleLink = $(".menuWrapper > a").prop("outerHTML");
	}
    $(".menuWrapper").replaceWith('<ul class="nav">'+menuHtml+'</ul>'+sheduleLink);
	
	$('.nav-collapse > ul > li:has(ul)').addClass('dropdown');
    $('.nav-collapse > ul > li.dropdown > a').addClass('dropdown-toggle');
    $('.nav-collapse > ul > li.dropdown > ul').addClass('dropdown-menu row-fluid');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li').addClass('megaMenuSection');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul').addClass('mainMenu');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection > ul.mainMenu > li > ul').addClass('subMenu');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection:has(form)').addClass('formDiv clearfix');
    $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.megaMenuSection.formDiv > div ').addClass('formframe clearfix');
    $('.mainMenu > li:has(ul)').addClass('subMenuParent');
    $('.mainMenu').closest('.dropdown').addClass('megamenu');
	
	mobfun();
	
    var footerQuickLInkHtml = "";

    $('.footerQuickWrapper > ul > li').each(function(i) {
        var title = $(this).contents().get(0).nodeValue.trim();
        var subMenu = $(this).find('ul').first().clone();

        var colClass = 'col' + (i + 1);

        var html = '<div class="' + colClass + ' footer-links">' +
            '<h3>' + title + '</h3>' +
            $('<div>').append(subMenu).html() +
            '</div>';
        footerQuickLInkHtml += html;
    });

    $(".footerQuickWrapper").replaceWith(footerQuickLInkHtml);

    var bannerType = 0;
	var imgCount = $('.zoneK1Wrapper ul li').find('img').length;
	var hasAnchor = $('.zoneK1Wrapper ul li').find('a').length > 0;
	var hasImage = $('.zoneK1Wrapper ul li').find('img').length > 0;

	if (imgCount == 2 && hasAnchor) {
		bannerType = 1;
	} else if (hasAnchor && hasImage) {
		bannerType = 2;
	} else if (hasAnchor && imgCount == 0) {
		bannerType = 3;
	}
    var html = '';
	
    if (bannerType == 1 || bannerType == 3) {
        var imageBlock = "";
        var classDiv = "span6";
        if (bannerType == 3) {
            var classDiv = "span12";
        } else {
			var iconColor = $("li:eq(1) li:eq(1)", $(".zoneK1Wrapper > ul > li:eq(0)")).text() || "#6D9D43";
            imageBlock = `<div class="${classDiv}">
								<div class="img-holder img-style-1">
									<img src="${$("img:eq(0)", $(".zoneK1Wrapper > ul > li:eq(0)")).attr("src")}" alt="" class="mainImg" />
									<span class="is1-icon-1" style="background-color: ${iconColor};">
										<img src="${$("img:eq(1)", $(".zoneK1Wrapper > ul > li:eq(0)")).attr("src")}" alt="" />
									</span>
								</div>
							</div>`;

        }
        html = `
			<div class="slider Herobanner-2">
				<div class="item">
					<div class="container containerCustom w-1200">
						<div class="row d-flex-wrap">
							${imageBlock}
							<div class="${classDiv}">
								<div class="carousel-caption">
									<div class="captionFrame">
										<ul>
											<li>
												<span class="SubHeading">${$("li:eq(0)", $(".zoneK1Wrapper > ul > li:eq(1)")).text() || ''}</span>
											</li>
											<li>
												<span class="TitleText">${$("li:eq(1)", $(".zoneK1Wrapper > ul > li:eq(1)")).text() || ''}</span>
											</li>
											<li>${$("li:eq(2)", $(".zoneK1Wrapper > ul > li:eq(1)")).html() || ''}</li>
											<li>
												<a href="${$("li:eq(3)", $(".zoneK1Wrapper > ul > li:eq(1)")).find('a').attr("href") || ''}" class="FactButtonBlue">${$("li:eq(3)", $(".zoneK1Wrapper > ul > li:eq(1)")).find('a').text() || ''}</a>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;
    } else if (bannerType == 2) {
        var imgBlock = "";
        if ($("li:eq(0)", $(".zoneK1Wrapper > ul > li:eq(0)")).find('img').length) {
			var iconColor = $("li:eq(1)", $(".zoneK1Wrapper > ul > li:eq(0)")).text() || "#6D9D43";
            imgBlock = `<span class="banner-icon-1" style="background-color: ${iconColor};">
					<img src="${$("li:eq(0)", $(".zoneK1Wrapper > ul > li:eq(0)")).find('img').attr('src')}" alt="" />
				</span>`;
        }
        html = `
			<div class="slider home3-banner">
				<div class="item">
					<div class="container containerCustom w-1200">
						<div class="row d-flex-wrap">
							<div class="span12">
								<div class="carousel-caption text-center">
									<div class="captionFrame">
										<ul>
											<li>
												<span class="SubHeading">
													${imgBlock}
													${$("li:eq(0)", $(".zoneK1Wrapper > ul > li:eq(1)")).text() || ''}
												</span>
											</li>
											<li><span class="TitleText">${$("li:eq(1)", $(".zoneK1Wrapper > ul > li:eq(1)")).text() || ''}</span></li>
											<li class="fs21">${$("li:eq(2)", $(".zoneK1Wrapper > ul > li:eq(1)")).html() || ''}</li>
											<li>
												<a href="${$("li:eq(3)", $(".zoneK1Wrapper > ul > li:eq(1)")).find('a').attr("href") || ''}" class="FactButtonBlue">${$("li:eq(3)", $(".zoneK1Wrapper > ul > li:eq(1)")).find('a').text() || ''}</a>
											</li>
										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		`;
    }

    $(".zoneK1Holder").replaceWith(html);

    var $items = $(".zoneC1Wrapper > ul > li");
    if ($items.length) {
        var $section = $('<div class="reduces-costs-sec" id="reducescostssecholder"><div class="container containerCustom w-1200"></div></div>');
        var $container = $section.find(".container");

        var $heading = $items.eq(0).find("ul li");
        var headingTitle = $heading.eq(0).html();
        var headingDesc = $heading.eq(1).html();

        $container.append(`
		  <div class="mb-50">
			<h2 class="HeaderText">${headingTitle}</h2>
			<p class="text-center">${headingDesc}</p>
		  </div>
		`);

        var $row = $('<div class="row d-flex-wrap boxcontent"></div>');
        $items.slice(1).each(function() {
            var $ul = $(this).find(">ul");
            var img = $ul.find(">li li:eq(0) img").attr("src") || "";
			var iconcolor = $ul.find(">li li:eq(1)").text() || "#2c57a0";
            var $link = $ul.find(">li").eq(1).find("a");
            var href = $link.attr("href") || "#";
            var title = $link.text().trim() || '';
            var desc = $ul.find(">li").eq(2).text().trim() || '';

            $row.append(`
			<div class="span4">
			  <a class="left-icon-box d2" href="${href}">
				<span class="lib-icon" style="background-color: ${iconcolor};">
				  <img src="${img}" alt="" />
				</span>
				<div class="lib-content">
				  <h2>${title}</h2>
				  <p>${desc}</p>
				</div>
			  </a>
			</div>
		  `);
        });

        $container.append($row);
		
        $(".zoneC1Holder").replaceWith($section);
    }

    var $ul = $('.zoneD1Wrapper > ul');
    var title = $ul.find('>li').eq(0).text().trim();
    var desc = $ul.find('>li').eq(1).text().trim();
    var exploreAnchor = $ul.find('>li').eq(2).find('a');
    var exploreLink = exploreAnchor.attr('href');
    var exploreText = exploreAnchor.text().trim();

    var iconImg = $ul.find('>li').eq(3).find('li img').attr('src');
	var iconClass = $ul.find('>li').eq(3).find('li:eq(1)').text() || "#2c57a0";
    var mainImg = $ul.find('>li').eq(4).find('img').attr('src');

    var benefitsLi = $ul.find('>li').eq(5);
    var benefitsTitle = benefitsLi.clone().children().remove().end().text().trim();
    var benefits = benefitsLi.find('ul').html();

    var factAnchor = $ul.find('>li').eq(6).find('a');
    var factLink = factAnchor.attr('href');
    var factText = factAnchor.text().trim();

    var finalHtml = '';

    if ($ul.length) {
        finalHtml = `
		<div class="pd_30 info-block-sec" id="infoblocksecholder1">
			<div class="container containerCustom w-1200">
				<div class="row d-flex-wrap">
					<div class="span12">
						<div class="card-img-right-box">
							<div class="cirb-content">
								<h2 class="HeaderTextSmall">${title}</h2>
								<p class="fs21">${desc}</p>
								<a href="${exploreLink}" class="TextButton">${exploreText}</a>
							</div>
							<div class="img-holder">
								<span class="icons-left-centred" style="background-color: ${iconClass};">
									<img src="${iconImg}" alt="" />
								</span>
								<img src="${mainImg}" alt="" />
							</div>
						</div>
					</div>
					<div class="span12">
						<h3 class="SubHeading">${benefitsTitle}</h3>
						<ul class="check-list list-col-4">
							${benefits}
						</ul>
					</div>
					<div class="span12">
						<div class="btn-wrap">
							<a href="${factLink}" class="FactButton">${factText}</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;
    }

    $(".zoneD1Holder").replaceWith(finalHtml);

    var $ul = $('.zoneE1Wrapper > ul');
    var title = $ul.find('>li').eq(0).text().trim();
    var desc = $ul.find('>li').eq(1).text().trim();
    var exploreAnchor = $ul.find('>li').eq(2).find('a');
    var exploreLink = exploreAnchor.attr('href');
    var exploreText = exploreAnchor.text().trim();

    var iconImg = $ul.find('>li').eq(3).find('li img').attr('src');
	var iconClass = $ul.find('>li').eq(3).find('li:eq(1)').text() || "#2c57a0";
    var mainImg = $ul.find('>li').eq(4).find('img').attr('src');
	
    var benefitsLi = $ul.find('>li').eq(5);
    var benefitsTitle = benefitsLi.clone().children().remove().end().text().trim();
    var benefits = benefitsLi.find('ul').html();

    var factAnchor = $ul.find('>li').eq(6).find('a');
    var factLink = factAnchor.attr('href');
    var factText = factAnchor.text().trim();

    var finalHtml = '';
    if ($ul.length) {
        finalHtml = `
		<div class="pd_30 info-block-sec" id="infoblocksecholder2">
			<div class="container containerCustom w-1200">
				<div class="row d-flex-wrap">
					<div class="span12">
						<div class="card-img-right-box">
							<div class="cirb-content">
								<h2 class="HeaderTextSmall">${title}</h2>
								<p class="fs21">${desc}</p>
								<a href="${exploreLink}" class="TextButton">${exploreText}</a>
							</div>
							<div class="img-holder">
								<span class="icons-left-centred" style="background-color: ${iconClass};">
									<img src="${iconImg}" alt="" />
								</span>
								<img src="${mainImg}" alt="" />
							</div>
						</div>
					</div>
					<div class="span12">
						<h3 class="SubHeading">${benefitsTitle}</h3>
						<ul class="check-list list-col-4">
							${benefits}
						</ul>
					</div>
					<div class="span12">
						<div class="btn-wrap">
							<a href="${factLink}" class="FactButton">${factText}</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;
    }

    $(".zoneE1Holder").replaceWith(finalHtml);

    var $ul = $('.zoneF1Wrapper > ul');
    var title = $ul.find('>li').eq(0).text().trim();
    var desc = $ul.find('>li').eq(1).text().trim();
    var exploreAnchor = $ul.find('>li').eq(2).find('a');
    var exploreLink = exploreAnchor.attr('href');
    var exploreText = exploreAnchor.text().trim();

    var iconImg = $ul.find('>li').eq(3).find('li img').attr('src');
	var iconClass = $ul.find('>li').eq(3).find('li:eq(1)').text() || "#2c57a0";
    var mainImg = $ul.find('>li').eq(4).find('img').attr('src');

    var benefitsLi = $ul.find('>li').eq(5);
    var benefitsTitle = benefitsLi.clone().children().remove().end().text().trim();
    var benefits = benefitsLi.find('ul').html();

    var factAnchor = $ul.find('>li').eq(6).find('a');
    var factLink = factAnchor.attr('href');
    var factText = factAnchor.text().trim();

    var finalHtml = '';
    if ($ul.length) {
        finalHtml = `
		<div class="pd_30 info-block-sec" id="infoblocksecholder3">
			<div class="container containerCustom w-1200">
				<div class="row d-flex-wrap">
					<div class="span12">
						<div class="card-img-right-box">
							<div class="cirb-content">
								<h2 class="HeaderTextSmall">${title}</h2>
								<p class="fs21">${desc}</p>
								<a href="${exploreLink}" class="TextButton">${exploreText}</a>
							</div>
							<div class="img-holder">
								<span class="icons-left-centred" style="background-color: ${iconClass};">
									<img src="${iconImg}" alt="" />
								</span>
								<img src="${mainImg}" alt="" />
							</div>
						</div>
					</div>
					<div class="span12">
						<h3 class="SubHeading">${benefitsTitle}</h3>
						<ul class="check-list list-col-4">
							${benefits}
						</ul>
					</div>
					<div class="span12">
						<div class="btn-wrap">
							<a href="${factLink}" class="FactButton">${factText}</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;
    }

    $(".zoneF1Holder").replaceWith(finalHtml);

    var $ul = $('.zoneG1Wrapper > ul');

    var title = $ul.find('li:eq(0)').text();
    var description = $ul.find('li:eq(1)').text();
    var btn1Text = $ul.find('li:eq(2) a').text();
    var btn1Link = $ul.find('li:eq(2) a').attr('href');
    var btn2Text = $ul.find('li:eq(3) a').text();
    var btn2Link = $ul.find('li:eq(3) a').attr('href');
    var imgSrc = $ul.find('li:eq(4) img').attr('src');

    var html = "";

    if ($ul.length) {
        html = `
		<div class="sec_60 sec-topnotch pd_30" id="sectopnotchholder">
			<div class="container containerCustom w-1200">
				<div class="notch-box">
					<img src="/images/notch-bg.png" alt="" class="topnotch-bg" />
					<div class="row d-flex-wrap">
						<div class="span6">
							<h3 class="HeaderTextSmall">${title}</h3>
							<p>${description}</p>
							<div class="btn-wrap">
								<a href="${btn1Link}" class="WhiteBorder">${btn1Text}</a>
								<a href="${btn2Link}" class="TextButtonWhite">${btn2Text}</a>
							</div>
						</div>
						<div class="span6">
							<div class="img-holder">
								<img src="${imgSrc}" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;
    }

    $(".zoneG1Holder").replaceWith(html);

    var $ul = $('.zoneI1Wrapper > ul');

    var heading1 = $ul.find('> li:eq(0)').text();
    var heading2 = $ul.find('> li:eq(1)').text();

    var html = '';

    if ($ul.length) {
        html = `
		<div class="infoicon-sec number-box-sec pd_60" id="numberboxsecholder">
		  <div class="container containerCustom w-1200">
			<div class="row-fluid flex-row">
			  <div class="span12">
				<h2 class="SubHeading text-center">${heading1}</h2>
				<h3 class="HeaderTextSmall text-center">${heading2}</h3>
			  </div>
			</div>
			<div class="row d-flex-wrap">
		`;

        $ul.find('> li:eq(2) > ul > li').each(function() {
            var number = $(this).find('li:eq(0)').text().trim();
            var text = $(this).find('li:eq(1)').text().trim();
            var match = number.match(/^(\d+)([A-Z+]+)?$/i);
            var count = match ? match[1] : '0';
            var suffix = match ? match[2] || '' : '';

            html += `
			  <div class="span3">
				<div class="numberBoxWrapper">
					<div class="numberBox">
					  <h2><span data-count="${count}">0</span>${suffix}</h2>
					  <p>${text}</p>
					</div>
				</div>
			  </div>
		  `;
        });

        html += `
			</div>
		  </div>
		</div>
		`;

        $(".zoneI1Holder").replaceWith(html);
    }

    var $ul = $('.zoneL1Wrapper > ul');
    if ($ul.length) {
        var subHeading = $ul.find("> li:eq(0)").text();
        var header = $ul.find("> li:eq(1)").text();
        var paragraph = $ul.find("> li:eq(2)").text();
        var listItems = $ul.find("> li:eq(3) > ul > li").map(function() {
            return "<li>" + $(this).text() + "</li>";
        }).get().join("");
        var img1 = $ul.find("> li:eq(4) > ul > li:eq(0) img").attr("src");
        var img2 = $ul.find("> li:eq(4) > ul > li:eq(1) img").attr("src");

        var html = `
		<div class="pd_30 sec-row-flex" id="keybenefitholder">
			<div class="container containerCustom w-1200">
				<div class="row d-flex-wrap">
					<div class="span6">
						<div class="details-1">
							<span class="SubHeading mb-10">${subHeading}</span>
							<h2 class="HeaderTextSmall mb-10">${header}</h2>
							<p>${paragraph}</p>
							<ul class="check-list">
								${listItems}
							</ul>
						</div>
					</div>
					<div class="span6">
						<div class="card-icon-top-right">
							<span class="icons-top-right cyanBlue">
								<img src="${img2}" alt="" />
							</span>
							<div class="img-holder">
								<img src="${img1}" alt="" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;

        $(".zoneL1Holder").replaceWith(html);
    }

    var items = $('.zoneM1Wrapper > ul > li').toArray();

    if ($('.zoneM1Wrapper > ul > li').length) {
        var html = `
		<div class="pd_30 sec-shadowbox" id="secshadowboxholder">
		  <div class="container containerCustom w-1200"><div class="row d-flex-wrap">
		`;
        var items = $('.zoneM1Wrapper > ul > li').toArray();
        var parsedItems = items.map(li => {
            var $innerLi = $(li).find('ul > li');
            return {
                title: $innerLi.eq(0).text().trim(),
                desc: $innerLi.eq(1).text().trim(),
                imgSrc: $innerLi.eq(2).find('img').attr('src')
            };
        });

        var i = 0;
        while (i < parsedItems.length) {
            var spanClass = 'span3';
            var perRow = 4;

            var remaining = parsedItems.length - i;

            if (remaining >= 4) {
                spanClass = 'span3';
                perRow = 4;
            } else if (remaining === 3) {
                spanClass = 'span4';
                perRow = 3;
            } else if (remaining === 2) {
                spanClass = 'span6';
                perRow = 2;
            } else {
                spanClass = 'span6';
                perRow = 1;
            }

            for (var j = 0; j < perRow && i < parsedItems.length; j++, i++) {
                var item = parsedItems[i];
                html += `
			  <div class="${spanClass}">
				<div class="shadowbox-1">
				  <div class="sb1-icon">
					<img src="${item.imgSrc}" alt="" />
				  </div>
				  <div class="sb1-details">
					<h3 class="SubHeading mb-10">${item.title}</h3>
					<p>${item.desc}</p>
				  </div>
				</div>
			  </div>
			`;
            }
        }

        html += `</div>
		  </div>
		</div>
		`;
        $(".zoneM1Holder").replaceWith(html);
    }

    var $ul = $(".zoneN1Wrapper ul").first();
    if ($ul.find("ul").length) {
        var heading = $ul.find("li").eq(0).text().trim();
        var subheading = $ul.find("li").eq(1).text().trim();

        var $factLink = $ul.find("li a").first();
        var factLinkHref = $factLink.attr("href") || "#";
        var factLinkText = $factLink.text().trim() || "View More";

        var $features = $ul.find("ul").first().children("li");


        var featureListHtml = $features.map(function() {
            return "<li>" + $(this).html() + "</li>";
        }).get().join('');

        var html = `
		<div class="feature-list-sec pd_30" id="featurelistsecholder">
			<div class="container containerCustom w-1200">
				<div class="mb-50">
					<h2 class="HeaderText">${heading}</h2>
					<p class="text-center">${subheading}</p>
				</div>
				<ul class="check-list list-col-4 shadow-added">
					${featureListHtml}
				</ul>
				<div class="btn-wrap">
					<a href="${factLinkHref}" class="FactButtonBlue">${factLinkText}</a>
				</div>
			</div>
		</div>`;

        $(".zoneN1Holder").replaceWith(html);
    }

    var $ul = $(".zoneO1Wrapper ul").first();
    if ($(".zoneO1Wrapper > ul").length) {
        var heading = $ul.children("li").first().text();
        var $sections = $ul.find("> li > ul > li");

        var html = `
		<div class="bring-toggather-sec bg-gradient-1 pd_30 pb-0" id="accordionholder"> 
		  <div class="container containerCustom w-1200">
			<div class="row flex-row">
			  <div class="span6 left-image-placeholder2"></div>
			  <div class="span6 pl-15">
				<h2 class="HeaderTextSmall">${heading}</h2>
				<div class="accordion-design">
				  <div class="accordion" id="accordionExample">
		`;

        $sections.each(function(index) {
            var $section = $(this);
			
            var $info = $section.find(">ul>li>ul>li");
            var title = $info.eq(0).text();
            var description = $info.eq(1).html();
            if ($info.eq(2).find("a").length) {
                $info.eq(2).find("a").addClass("TextButton");
            }
			var buttonLink = $info.eq(2).find("a").length ? $info.eq(2).html() : '';
            var feature = $section.find(">ul>li").eq(1).find('li:eq(0)').text()
			
            var imgs = $section.find("img").map(function() {
                return $(this).attr("src");
            }).get();
			
            var collapseId = "collapse" + (index + 1);
            var isActive = index === 1 ? "in" : "";
            var isCollapsed = index === 1 ? "" : "collapsed";
			var iconEditClass = $section.find('>ul>li').eq(2).find('li:eq(1)').text() || "#6D9D43";
			var iconStatClass = $section.find(">ul>li").eq(1).find('li:eq(1)').text() || "#2c57a0";
            html += `
		  <div class="accordion-group">
			<div class="accordion-heading">
			  <a class="accordion-toggle ${isCollapsed}" data-toggle="collapse" data-parent="#accordionExample" href="#${collapseId}">
				${title}
			  </a>
			</div>
			<div id="${collapseId}" class="accordion-body collapse ${isActive}">
			  <div class="accordion-left-content">
				<div class="banner-card banner-card-3">
				  <div class="image-section">
					${imgs[0] ? `<img src="${imgs[1]}" alt="" class="w-100" />` : ''}
					${imgs[2] ? `<div class="badge benefit"><img src="${imgs[2]}" alt="" /></div>` : ''}
					${imgs[1] ? `<div class="badge edit-icon" style="background-color: ${iconEditClass};"><img src="${imgs[0]}"/></div>` : ''}
					<div class="badge statistic" style="background-color: ${iconStatClass};"><span>${feature}</span></div>
				  </div>
				</div>
			  </div>
			  <div class="accordion-inner">
				<p>${description}</p>
				${buttonLink}
			  </div>
			</div>
		  </div>
		  `;
        });

        html += `
				  </div>
				</div>
			  </div>
			</div>
		  </div>
		</div>
		`;
        $(".zoneO1Holder").replaceWith(html);
    }


    var $ul = $('.zoneH1Wrapper > ul');
    if ($ul.length) {
        var $lis = $ul.find('li');

        var subHeading = $lis.eq(0).text();
        var heading = $lis.eq(1).text();
        var paragraph = $lis.eq(2).text();
        var button1 = $lis.eq(3).find('a');
        var button2 = $lis.eq(4).find('a');

        var finalHtml = `
		<div class="schedule-sec pd_30" id="scheduleHolder">
			<div class="container containerCustom w-1200">
				<div class="schedule-box">
					<div class="row d-flex-wrap">
						<div class="span6">
							<span class="SubHeading">${subHeading}</span>
							<h2 class="HeaderTextSmall">${heading}</h2>
							<p>${paragraph}</p>
							<div class="btn-wrap mt-40">
								<a href="${button1.attr('href')}" class="MCButton">${button1.text()}</a>
								<a href="${button2.attr('href')}" class="TextButton">${button2.text()}</a>
							</div>
						</div>
						<div class="span6">
							<div style="position: relative;">
								<div id="loadingText" style="position: absolute; top: 0; left: 0; right: 0; text-align: center; padding: 10px; background: white; z-index: 10;">
									Loading, please wait...
								</div>
								<iframe src="?pg=scheduleNow&mode=direct" onload="document.getElementById('loadingText').style.display='none';resizeContactIframe()" id="contactIframe" frameborder="0" height="0px" width="100%" scrolling="no"></iframe>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		`;
        $(".zoneH1Holder").replaceWith(finalHtml);
    }

    var $ul = $(".zoneMainC1Wrapper ul").first();
    if ($ul.length) {
        var $lis = $ul.children("li");
        var heading = $lis.eq(0).text();
        var description = $lis.eq(1).text();
        var $logos = $lis.eq(2).find("li");
        var $desktopCarousel = $("<div class='owl-carousel owl-theme'></div>");
        for (var i = 0; i < $logos.length; i += 6) {
            var $slide = $("<div class='item'><ul></ul></div>");
            $logos.slice(i, i + 6).each(function() {
                $slide.find("ul").append($(this).clone());
            });
            $desktopCarousel.append($slide);
        }

        var $mobileCarousel = $("<div class='owl-carousel owl-theme'></div>");
        $logos.each(function() {
            var $slide = $("<div class='item'><ul></ul></div>");
            $slide.find("ul").append($(this).clone());
            $mobileCarousel.append($slide);
        });

        var $finalHTML = $(`
			<div class="pd_50 trust-member-sec">
				<div class="container containerCustom w-1200">
					<h3 class="title20 mb-10"></h3>
					<p class="text-center fs21"></p>
					<div class="for-desktop">
						<div class="friendsSliderBox"></div>
					</div>
					<div class="for-mobile">
						<div class="friendsSliderBox friendsSliderBox-mobile"></div>
					</div>
				</div>
			</div>
		`);

        $finalHTML.find("h3").text(heading);
        $finalHTML.find("p").text(description);

        $finalHTML.find(".for-desktop .friendsSliderBox").append($desktopCarousel);
        $finalHTML.find(".for-mobile .friendsSliderBox").append($mobileCarousel);
        $(".zoneMainC1Holder").replaceWith($finalHTML);
    }

    var $ul = $(".zoneMainD1Wrapper ul").first();
    if ($ul.length) {
        var title = $ul.children("li").eq(0).text();
        var desc = $ul.children("li").eq(1).html();
        var $sections = $ul.children("li").slice(2);
		
        var html = `
		<div class="pd_30 reduces-costs-sec">
		  <div class="container containerCustom w-1200">
			<div class="mb-50">
			  <h2 class="HeaderText">${title}</h2>
			  <p class="text-center fs21">${desc}</p>
			</div>
			<div class="row d-flex-wrap boxcontent">
		`;

        $sections.each(function() {
            var $list = $(this).find("ul").first();
            var heading = $list.find("li").eq(0).text();
            var para = $list.find("li").eq(1).text();
            var image = $list.find("li").eq(2).find("img").attr("src");
            var iconClass = $list.find("ul").eq(0).find("li").eq(1).text() || '#2c57a0';
            var iconImg = $list.find("ul").eq(0).find("li").eq(0).find("img").attr("src");

            var subheading = $list.find(">li").eq(4).contents().filter(function() {
                return this.nodeType === 3;
            }).text().trim();

            var features = $list.find(">li").eq(4).find("ul li").map(function() {
                return `<li>${$(this).html()}</li>`;
            }).get().join("");

            html += `
			<div class="col-6">
			  <div class="left-icon-box">
				<span class="lib-icon"  style="background-color: ${iconClass};">
				  <img src="${iconImg}" alt="" />
				</span>
				<div class="lib-content">
				  <h2 class="fs22">${heading}</h2>
				  <p>${para}</p>
				</div>
			  </div>
			  <div class="img-holder">
				<img src="${image}" alt="" class="border-radius-1" />
			  </div>
			  <h4 class="SubHeading">${subheading}</h4>
			  <ul class="check-list list-doubleline">
				${features}
			  </ul>
			</div>
		  `;
        });

        html += `
			</div>
		  </div>
		</div>
		`;

        $(".zoneMainD1Holder").replaceWith(html);
    }

    var $ul = $(".zoneMainH1Wrapper > ul");
    if ($ul.length) {

        var $items = $ul.find("> li");
        var html = `
		<div class="infoicon-sec">
		  <div class="container containerCustom w-1200">
			<div class="row-fluid flex-row">
		`;

        $items.each(function(index) {
            var $nestedLis = $(this).find("ul > li");

            if (index === 0) {
                // First block (title + button)
                var headingText = $nestedLis.eq(0).text();
                var $buttonLink = $nestedLis.eq(1).find("a");
                var buttonText = $buttonLink.text();
                var buttonHref = $buttonLink.attr("href");

                html += `
				<div class="span3">
				  <h2 class="HeaderTextSmall">${headingText}</h2>
				  <a href="${buttonHref}" class="BlueButton">${buttonText}</a>
				</div>`;
            } else {
                // Icon box blocks
                var number = $nestedLis.eq(0).text();
                var $link = $nestedLis.eq(1).find("a");
                var linkText = $link.text();
                var linkHref = $link.attr("href");
                var description = $nestedLis.eq(2).text();

                html += `
				<div class="span3">
					<div class="info-iconbox-wrapper">
						<div class="info-iconbox">
							<a href="${linkHref}">
								<span class="cyanBlue">${number}</span>
								<h2>${linkText}</h2>
								<p>${description}</p>
							</a>
						</div>
					</div>
				</div>`;
            }
        });

        html += `
			</div>
		  </div>
		</div>
		`;

        $(".zoneMainH1Holder").replaceWith(html);
    }

    var $ul = $('.zoneMainE1Wrapper ul').first();
    if ($ul.length) {
        var headings = $ul.find('> li > ul').first().find('li');
        var testimonials = $ul.find('> li > ul').last().find('> li');

        var html = `
		<div class="testimonials-sec pd_60">
		  <div class="container containerCustom w-1200">
			<div class="mb-30">
			  <h3 class="SubHeading text-center mb-10">${headings.eq(0).text()}</h3>
			  <h2 class="HeaderText">${headings.eq(1).text()}</h2>
			</div>
			<div class="row d-flex-wrap">
			  <div class="left-wrap testimonial-slider">
				<div class="owl-carousel owl-theme">
		`;

        var rightLinks = '';
		
        testimonials.each(function(index) {
            var $lis = $(this).find('ul > li');
            var quote = $lis.eq(0).html();
            var name = $lis.eq(1).text();
            var org = $lis.eq(2).text();
            var shortQuote = $lis.eq(3).text();
            var assoc = $lis.eq(4).text();
            var logoImg = $lis.eq(5).find('img').attr('src');
            var userImg = $lis.eq(6).find('img').attr('src');

            html += `
			<div class="item" data-index="slide${index + 1}">
			  <div class="testimonial-block">
				<ul>
				  <li><p>${quote}</p></li>
				  <li>${name} <span>${org}</span></li>
				</ul>
				<div class="testimonial-user">
				  <a href="javascript:void(0)">
					<span class="left-logo">
					  <img alt="" src="${logoImg}" />
					</span>
					<img alt="" src="${userImg}" />
				  </a>
				</div>
			  </div>
			</div>
		  `;

            rightLinks += `
			<a href="javascript:void(0)" data-index="slide${index + 1}" ${index === 0 ? 'class="active"' : ''}>
			  <h2>${shortQuote}</h2>
			  <p>${assoc}</p>
			</a>
		  `;
        });

        html += `
				</div>
			  </div>
			  <div class="right-wrap">
				<div class="testimonial-links">
				  ${rightLinks}
				</div>
				<div class="text-right">
				  <a href="${headings.eq(2).find('a').attr('href')}" class="TextButton">${headings.eq(2).text()}</a>
				</div>
			  </div>
			</div>
		  </div>
		</div>
		`;

        $(".zoneMainE1Holder").replaceWith(html);
    }

    var $ul = $(".zoneMainF1Wrapper ul").first();
    if ($ul.length) {
        var intro = $ul.children("li").slice(0, 3).map(function() {
            return $(this).text().trim();
        }).get();
		$ul.children("li").eq(3).find("a").addClass("MCButton");
        var globalDemoLink = $ul.children("li").eq(3).find("a").clone();   

        var $sections = $ul.children("li").eq(4).find("> ul > li");

        var accordionHtml = "";

        $sections.each(function(index) {
            var $section = $(this);
            var $blocks = $section.find(">ul>li");

            var heading = $blocks.eq(0).find("li").eq(0).text().trim();
            var description = $blocks.eq(0).find("li").eq(1).text().trim();
            var linkTag = $blocks.eq(0).find("li").eq(2).find("a");
            var link = linkTag.length ? `<a href="${linkTag.attr("href")}" class="TextButton">${linkTag.text()}</a>` : "";
			
            var starCount = parseInt($blocks.eq(1).text().trim()) || 0;
            var starHtml = "";
            for (var i = 0; i < starCount; i++) {
                starHtml += `<img src="/images/star.png" alt="star" />`;
            }

            var stat = $blocks.eq(2).find("li");
            var statNumber = stat.eq(0).text().trim();
            var statText = stat.eq(1).text().trim();
            var statColor = stat.eq(2).text().trim() || "#2c57a0";

            var benefit = $blocks.eq(3).find("li");
            var benefitText = benefit.eq(0).text().trim();
            var benefitImg = benefit.eq(1).find("img").attr("src");
            var benefitColor = benefit.eq(2).text().trim() || "#66C8D8";

            var mainImage = $blocks.eq(4).find("img").attr("src") || "";
            var editIcon = $blocks.eq(5).find("img").attr("src") || "";
			var editIconcolor = $blocks.eq(5).find("li").eq(1).text() || "#ffffff";
            var membershipImg = $blocks.eq(6).find("li").eq(0).find("img").attr("src") || "";
			var membershipImgIcon = $blocks.eq(6).find("li").eq(1).text() || "#6D9D43";

            var collapseId = "collapse" + (index + 1);
            var accordionGroup = `
			<div class="accordion-group">
				<div class="accordion-heading">
					<a class="accordion-toggle collapsed" data-toggle="collapse" data-parent="#accordionExample" href="#${collapseId}">
						${heading}
					</a>
				</div>
				<div id="${collapseId}" class="accordion-body collapse" data-accordion="img__${index + 1}">
					<div class="accordion-left-content">
						<div class="banner-card banner-card-2">
							<div class="image-section">
								<img src="${mainImage}" alt="" class="w-100" />
								<div class="badge benefit" style="background-color: ${benefitColor};">
									<span>${benefitText}</span>
									<img src="${benefitImg}" alt="" />
								</div>
								<div class="badge edit-icon" style="background-color: ${editIconcolor};">
									<img src="${editIcon}" alt="" />
								</div>
								<div class="badge statistic" style="background-color: ${statColor};">
									<span>${statNumber}</span>
									<span>${statText}</span>
								</div>
								<div class="badge membership" style="background-color: ${membershipImgIcon};">
									<img src="${membershipImg}" alt="" />
								</div>
								<div class="rating">
									${starHtml}
								</div>
							</div>
						</div>
						<div class="text-center btn-wrap">
							${globalDemoLink.prop("outerHTML")}
						</div>
					</div>
					<div class="accordion-inner">
						<p>${description}</p>
						${link}
					</div>
				</div>
			</div>`;
            accordionHtml += accordionGroup;
        });

        var finalHtml = `
		<div class="bring-toggather-sec pd_30">
			<div class="container containerCustom w-1200">
				<div class="row flex-row">
					<div class="span6 left-image-placeholder"></div>
					<div class="span6 pl-15">
						<p class="SubHeading">${intro[0]}</p>
						<h2 class="HeaderTextSmall">${intro[1]}</h2>
						<p>${intro[2]}</p>
						<div class="accordion-design">
							<div class="accordion" id="accordionExample">
								${accordionHtml}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>`;

        $(".zoneMainF1Holder").replaceWith(finalHtml);
    }

    var $ul = $(".zoneMainWrapper ul").first();
    if ($ul.length) {
        var $li = $ul.children("li");

        var title = $li.eq(0).text().trim();
        var subtitle = $li.eq(1).text().trim();
        var description = $li.eq(2).text().trim();

        var $demoLinkA = $li.eq(3).find("a");
        var demoLink = $demoLinkA.attr("href") || "#";
        var demoText = $demoLinkA.text().trim();
        var $featureLinkA = $li.eq(4).find("a");
        var featureLink = $featureLinkA.attr("href") || "#";
        var featureText = $featureLinkA.text().trim();

        var benefitText = $li.eq(5).find("ul > li").eq(0).text().trim();
        var benefitIcon = $li.eq(5).find("ul > li").eq(1).find("img").attr("src");
		var benefitIconcolor = $li.eq(5).find("ul > li").eq(2).text() || "#66C8D8";

        var statisticNumber = $li.eq(6).find("ul > li").eq(0).text().trim();
        var statisticText = $li.eq(6).find("ul > li").eq(1).text().trim();
		var statisticTextColor = $li.eq(6).find("ul > li").eq(2).text().trim() || "#2c57a0";

        var mainImage = $li.eq(7).find("img").attr("src");
        var editIcon = $li.eq(8).find("img").attr("src");
		var editIconcolor = $li.eq(8).find("li:eq(1)").text() || "#f3a81c";
        var membershipIcon = $li.eq(9).find("img").attr("src");
		var membershipIconcolor = $li.eq(9).find("li:eq(1)").text() || "#6D9D43";

        var starCount = parseInt($li.last().text().trim()) || 0;
        var starHtml = "";
        for (var i = 0; i < starCount; i++) {
            starHtml += `<img src="/images/star.png" alt="" />\n`;
        }

        var html = `
	<!-- slider start -->
	<div class="slider">
		<div class="item">
			<div class="container containerCustom w-1200">
				<div class="row d-flex-wrap">
					<div class="span6">
						<div class="carousel-caption">
							<div class="captionFrame">
								<ul>
									<li>${title}</li>
									<li>
										<span class="TitleText">${subtitle}</span>
									</li>
									<li>${description}</li>
									<li>
										<a href="${demoLink}" class="MCButton">${demoText}</a>
										<a href="${featureLink}" class="TextButton">${featureText}</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div class="span6">
						<div class="img-holder">
							<div class="banner-card">
								<div class="image-section">
									<img src="${mainImage}" alt="" />
									<div class="badge benefit" style="background-color: ${benefitIconcolor};">
										<span>${benefitText}</span>
										<img src="${benefitIcon}" width="40">
									</div>
									<div class="badge edit-icon" style="background-color: ${editIconcolor};">
										<img src="${editIcon}" width="40">
									</div>
									<div class="badge statistic" style="background-color: ${statisticTextColor};">
										<span>${statisticNumber}</span>
										<span>${statisticText}</span>
									</div>
									<div class="badge membership" style="background-color: ${membershipIconcolor};">
										<img src="${membershipIcon}" alt="" />
									</div>
									<div class="rating">
										${starHtml}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- slider End -->
	`;

        $(".zoneMainHolder").replaceWith(html);
    }
    $(".footerContact > a").addClass("MCButton btn-sm");	
	$(".foot-logo-wrap img").addClass("footlogo");

    $(document).on('click', ".btn-navbar", function() {
        $("body").toggleClass("overlay");
    });

    $(document).on('click', '.menu-arrow', function() {
        $(this).parents(".dropdown").toggleClass('open-droupdown');
        $(this).parents(".dropdown").children(".dropdown-menu").slideToggle();
    });
    $(".btn-navbar").click(function() {
        $(".dropdown").removeClass('open-droupdown');
        $(".dropdown-menu").hide();
    });


    $(document).on('click', ".mainMenuMobBtn", function() {
        $(this).toggleClass('textUnderline');
        $(this).parents(".megaMenuSection").toggleClass('closeBox');
        $(this).parents(".megaMenuSection").children(".mainMenuMob").slideToggle();
        $(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").slideToggle();
        $(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").toggleClass('openBoxInner');
    });


    $(".mainMenuOnclickBtn").click(function() {
        $(this).parents(".megaMenuSection").children(".mainMenuOnclick").slideToggle();
    });



    $(".mainMenuOnclickBtn").click(function() {
        if ($(window).width() > 979) {
            $(this).parents(".megaMenuSection").children(".mainMenuOnclick").slideToggle();
        }
    });

    /*****************/
    $(".quicklink-mobile .DiamondBullets").hide();
    $(".quicklink-mobile h3").click(function() {
        $(".quicklink-mobile .DiamondBullets").slideToggle();
        $(this).toggleClass("quicklink-open");
    });

    $(".event-mobile .event-list").hide();
    $(".event-mobile h3").click(function() {
        $(this).closest(".event-mobile").find(".event-list").slideToggle();
        $(this).toggleClass("event-open");
    });

    $(document).on('click', '.searchBtnFn  .dropdown-toggle', function(e) {
        e.preventDefault();
        $(this).closest('li').addClass('show-search-bar');
    });

    $(document).on('click', '.searchclose', function(e) {
        e.preventDefault();
        $(this).closest('li.show-search-bar').removeClass('show-search-bar');
    });

    $(document).on('click', '.toggle-form', function(e) {
        e.preventDefault();
        $(this).closest('li').toggleClass('show-form');
    });

    $(document).on('click', '#myTab a', function(e) {
        e.preventDefault();
        $(this).tab('show');
        $(".friendsSliderBox .owl-carousel").trigger('refresh.owl.carousel');
    })
	
	$(".slider .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        autoplay: true,
        autoplayTimeout: 7000,
        autoplayHoverPause: true,
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false
    });

    $(".friendsSliderBox .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        nav: true,
        dots: false,
        autoplay: true,
        autoplayTimeout: 7000,
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false
    });

    $(".friendsSliderBox.friendsSliderBox-mobile .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        nav: true,
        dots: false,
        autoplay: true,
        autoplayTimeout: 7000,
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false
    });


    $(".testimonial-slider .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        nav: false,
        dots: false,
        autoplay: true,
        autoplayTimeout: 7000,
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false
    });

    $(".testimonial-slider .owl-carousel").on('changed.owl.carousel', function(event) {
        var current = event.item.index;
        var index = $(event.target).find(".owl-item").eq(current).find(".item").attr('data-index');

        $('.testimonial-links a').removeClass('active');

        $('.testimonial-links a[data-index="' + index + '"]').addClass('active');
    });
	
	$('.accordion-design .accordion-body').on('shown.bs.collapse', function() {
        $(this).closest('.accordion').find('.accordion-toggle').addClass('collapsed');
        $(this).closest('.accordion-group').find('.accordion-toggle').removeClass('collapsed');
    });

    if ($('#stickyHeader').length > 0) {
        window.addEventListener('scroll', function() {
            var header = document.getElementById('stickyHeader');

            var sectionOffset = header.offsetTop - 62;

            if (window.scrollY > sectionOffset) {
                header.classList.add('sticky');
            } else {
                header.classList.remove('sticky');
            }
        });
    }
	
	$(".accordion-toggle").trigger("click");
	
	$(".header .navbar .nav > li").hover(
		function () {
			if ($(window).width() > 979) {
				var $dropdown = $(this).children('.dropdown-menu');

				if (!$dropdown.is(':visible')) {
					$dropdown.show(); // Instantly show dropdown
				}

				$dropdown.css('height', 'auto');

				var allHeights = [];
				$dropdown.find('.mainMenu').each(function () {
					allHeights.push($(this).outerHeight());
				});

				var maxHeight = Math.max(...allHeights);
				$dropdown.css('height', maxHeight); // Instantly set height

				$(this).find('.megaMenuSection.show-menu').removeClass('show-menu');
				$dropdown.find('.megaMenuSection:first-child').addClass('show-menu');
			}
		},
		function () {
			if ($(window).width() > 979) {
				$(this).children('.dropdown-menu').hide(); // Instantly hide dropdown
			}
		}
	);


	$('.megaMenuSection > a').hover(function () {
		if ($(window).width() > 979) {
			var _this = $(this);
			_this.closest('.dropdown-menu').find('.show-menu').removeClass('show-menu');
			_this.parent('.megaMenuSection').addClass('show-menu');

			if (_this.closest('.megamenu').length === 0) {
				_this.children('.dropdown-menu').show(); // Instantly show dropdown
			}
		}
	});


	$(document).on('click', '.dropdown .dropdown-menu li.megaMenuSection>a', function() {
		if ($(window).width() < 979) {
			if ($(this).closest('li').hasClass('menushow')) {
				$(this).closest('li').removeClass('menushow');
			} else {
				$(this).closest('.dropdown-menu').find('.menushow').removeClass('menushow')
				$(this).closest('li').addClass('menushow');
			}
		}
	});
	
});

document.addEventListener("DOMContentLoaded", function() {
    var counters = document.querySelectorAll(".numberBox span");
    var speed = 200; // Adjust speed as needed

    var countUp = (counter) => {
        var target = +counter.getAttribute("data-count");
        var increment = target / speed;
        var updateCount = () => {
            var current = +counter.innerText;
            if (current < target) {
                counter.innerText = Math.ceil(current + increment);
                setTimeout(updateCount, 10);
            } else {
                counter.innerText = target;
            }
        };
        updateCount();
    };

    var observer = new IntersectionObserver(
        (entries, observer) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    countUp(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.5
        }
    );

    counters.forEach((counter) => observer.observe(counter));
});



$(document).on('click', '.explore-links-wrapper a', function(e) {
    e.preventDefault();
    var targetId = $(this).attr('href');
	targetId = (targetId === '#' || targetId === '##') ? '' : targetId;        
	if (targetId && targetId.length && $(this).length) {
		// Remove active class from all and add to the clicked anchor
		$('.explore-links-wrapper a').removeClass('active');
		$(this).addClass('active');

		if ($(window).width() < 979) {
			$('.explore-links-sec').removeClass('active-class');

			if (targetId && $(targetId).length) {
				$('html, body').animate({
					scrollTop: $(targetId).offset().top - 100
				}, 800);
			}

		} else {

			if (targetId && $(targetId).length) {
				$('html, body').animate({
					scrollTop: $(targetId).offset().top - 100 // Adjust the offset as needed
				}, 800);
			}

		}
	}
});

// Highlight the active link while scrolling
$(window).on('scroll', function() {
    $('.explore-links-wrapper a').each(function() {
        var targetId = $(this).attr('href');
		targetId = (targetId === '#' || targetId === '##') ? '' : targetId;
        if (targetId && targetId.length && $(this).length) {
            var sectionOffset = $(targetId).offset().top - 120; // Adjust offset as needed
            var sectionHeight = $(targetId).outerHeight();
            var scrollPosition = $(window).scrollTop();

            if (scrollPosition >= sectionOffset && scrollPosition < sectionOffset + sectionHeight) {
                $('.explore-links-wrapper a').removeClass('active');
                $(this).addClass('active');
            }
        }
    });
});

$(document).on('click', '.explore-links-sec ul li:first-child', function() {
    if ($(window).width() < 979) {
        $('.explore-links-sec').toggleClass('active-class');
    }
});

$(document).on('click', '.testimonial-links a', function(e) {
    e.preventDefault();

    var clickedIndex = $(this).index(); // Get index of clicked link

    $('.testimonial-slider .owl-carousel').trigger('to.owl.carousel', [clickedIndex, 300]);

    $('.testimonial-links a').removeClass('active');
    $(this).addClass('active');
});