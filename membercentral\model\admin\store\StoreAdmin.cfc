<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Load Objects for the page ---------------------------------------------------------------- ::
			this.objAdminStore = CreateObject("component", "model.admin.store.store");	
			this.storeInfo = this.objAdminStore.getStoreInfo(arguments.event);
			this.objCart = CreateObject("component", "model.admin.store.shoppingCart");
			arguments.event.setValue('storeApplicationInstanceID', this.storeInfo.applicationInstanceID);
			arguments.event.setValue('storeID',this.storeInfo.storeID);
			
			// override the siteResourceID. ------------------------------------------------------------- ::
			// for this tool, it needs to be the siteResourceID of the store, not the storeAdmin tool. -- ::
			this.siteResourceID = this.storeInfo.siteResourceID;

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Build Quick Links for SectionAdmin ------------------------------------------------------- ::
			this.link.home = buildCurrentLink(arguments.event,"home");
			// Settings --------------------------------------------------------------------------------- ::
			this.link.saveSettings = buildCurrentLink(arguments.event,"saveSettings");

			this.link.editCategory = buildCurrentLink(arguments.event,"editCategory") & '&mode=direct';
			
			this.link.addShipping = buildCurrentLink(arguments.event,"addShipping");
			this.link.editShipping = buildCurrentLink(arguments.event,"editShipping");
			
			this.link.editStreamProfile = buildCurrentLink(arguments.event,"editStreamProfile")& "&mode=direct";

			this.link.addProduct = buildCurrentLink(arguments.event,"addProduct");
			this.link.editProduct = buildCurrentLink(arguments.event,"editProduct");
			this.link.copyProduct = buildCurrentLink(arguments.event,"copyProduct");
			this.link.saveProduct = buildCurrentLink(arguments.event,"saveProduct");
			this.link.insertProduct = buildCurrentLink(arguments.event,"insertProduct");
			
			this.link.manageOrder = buildCurrentLink(arguments.event,"manageOrder");
			this.link.addOrder = buildCurrentLink(arguments.event,"addOrder");
			this.link.saveOrder = buildCurrentLink(arguments.event,"saveOrder") & "&mode=stream";
			this.link.addOrderItem = buildCurrentLink(arguments.event,"addOrderItem") & "&mode=direct";
			this.link.editOrderItems = buildCurrentLink(arguments.event,"editOrderItems");
			this.link.updateOrderItems = buildCurrentLink(arguments.event,"updateOrderItems");
			this.link.editOrderFormat = buildCurrentLink(arguments.event,"editOrderFormat") & "&mode=stream";
			this.link.editOrderRate = buildCurrentLink(arguments.event,"editOrderRate") & "&mode=direct";
			this.link.editOrderStatus = buildCurrentLink(arguments.event,"editOrderStatus") & "&mode=direct";
			this.link.saveOrderStatus = buildCurrentLink(arguments.event,"saveOrderStatus") & "&mode=stream";
			this.link.editOrderNotes = buildCurrentLink(arguments.event,"editOrderNotes") & "&mode=direct";
			this.link.saveOrderNotes = buildCurrentLink(arguments.event,"saveOrderNotes") & "&mode=stream";
			this.link.editOrderShipping = buildCurrentLink(arguments.event,"editOrderShipping") & "&mode=direct";
			this.link.saveOrderShipping = buildCurrentLink(arguments.event,"saveOrderShipping") & "&mode=stream";
			this.link.sendOrderDetail = buildCurrentLink(arguments.event,"sendOrderDetail") & "&mode=direct";
			this.link.generatePackingSlip = buildCurrentLink(arguments.event,"generatePackingSlip") & "&mode=direct";
			this.link.exportOrders = buildCurrentLink(arguments.event,"exportOrders") & "&mode=stream";

			this.link.addStream = buildCurrentLink(arguments.event,"addStream") & "&mode=direct";
			this.link.editStream = buildCurrentLink(arguments.event,"editStream") & "&mode=direct";
			this.link.saveStream = buildCurrentLink(arguments.event,"saveStream") & "&mode=stream";
			this.link.viewStreamLogs = buildCurrentLink(arguments.event,"viewStreamLogs") & "&mode=direct";
			this.link.manageStreams = buildCurrentLink(arguments.event,"manageStreams") & "&mode=direct";
			this.link.manageStreamCredit = buildCurrentLink(arguments.event,"manageStreamCredit") & "&mode=direct";
			this.link.saveStreamDates = buildCurrentLink(arguments.event,"saveStreamDates") & "&mode=stream";
			this.link.saveStreamCredits = buildCurrentLink(arguments.event,"saveStreamCredits") & "&mode=stream";

			this.link.addDocument = buildCurrentLink(arguments.event,"addDocument") & "&mode=direct";
			this.link.editDocument = buildCurrentLink(arguments.event,"editDocument") & "&mode=direct";
			this.link.viewDocument = buildCurrentLink(arguments.event,"viewDocument") & "&mode=direct";
			this.link.saveDocument = buildCurrentLink(arguments.event,"saveDocument") & "&mode=stream";
			
			// transaction payments ------------------------------------------------------ ::
			local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
			this.link.addPayment = local.transAdminTool & "&mca_ta=addPayment&mode=direct";
			this.link.allocatePayment = local.transAdminTool & "&mca_ta=allocatePayment&mode=direct";
			
			// Rates code ---------------------------------------------------------------- ::
			this.link.addFormat = buildCurrentLink(arguments.event,"addFormat") & "&mode=direct";
			this.link.saveFormat = buildCurrentLink(arguments.event,"saveFormat") & "&mode=stream";
			this.link.editFormat = buildCurrentLink(arguments.event,"editFormat") & "&mode=direct";	
			this.link.addRate = buildCurrentLink(arguments.event,"addRate") & "&storeID=#this.storeInfo.storeID#&mode=direct";
			this.link.saveRate = buildCurrentLink(arguments.event,"saveRate") & "&mode=stream";
			this.link.editRate	= buildCurrentLink(arguments.event,"editRate") & "&storeID=#this.storeInfo.storeID#&mode=direct";
			this.link.permissionsGridAdd = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
			
			this.link.addRateOverride = buildCurrentLink(arguments.event,"addRateOverride") & "&mode=direct";
			this.link.editRateOverride = buildCurrentLink(arguments.event,"editRateOverride") & "&mode=direct";
			this.link.saveRateOverride = buildCurrentLink(arguments.event,"saveRateOverride") & "&mode=stream";
			
			this.link.linkAffirmationToMember = buildCurrentLink(arguments.event,"linkAffirmationToMember") & "&mode=direct";	
			this.link.savelinkAffirmationToMember = buildCurrentLink(arguments.event,"savelinkAffirmationToMember") & "&mode=direct";
			this.link.generateAffirmationCertificate = buildCurrentLink(arguments.event,"generateAffirmationCertificate") & "&mode=direct";	
			this.link.exportAffirmationGrid = buildCurrentLink(arguments.event,"exportAffirmationGrid") & "&mode=stream";
			this.link.removeShippingRenderLink = buildCurrentLink(arguments.event,"removeShippingRender") & "&mode=direct";

			// View certificate 
			this.link.emailCertificates = buildCurrentLink(arguments.event,"emailCertificates") & "&mode=direct";
			this.link.viewCertificate = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=direct";
			this.link.viewCertificatePDF = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=stream";

			this.link.message = buildCurrentLink(arguments.event,"message");	
			// local page params ------------------------------------------------------------------------ ::			
			this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');	
			this.languageID = this.objAdminStore.getDefaultLanguageID(arguments.event.getValue('mc_siteInfo.siteID'));
			// Run Assigned Method ---------------------------------------------------------------------- ::			
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<cffunction name="home" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="importData" type="string" required="no" default="0">
		<cfscript>
			var local = structNew();
			
			/* Page Security ---------------------------------------------------------------------------- */
			local.security.add = checkRights(arguments.event,'AddProducts');
			local.security.delete = checkRights(arguments.event,'DeleteProducts');
			local.security.edit = checkRights(arguments.event,'EditProducts');
			local.security.viewOrders = checkRights(arguments.event,'ViewOrders');
			local.security.editSettings	= checkRights(arguments.event,'EditSettings');
			local.sampleAffirmationsImportTemplate = buildCurrentLink(arguments.event,"sampleAffirmationsImportTemplate") & "&mode=stream";
			local.affirmationsImport = buildCurrentLink(arguments.event,"affirmationsImport");

			local.storeDTRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=storeJSON&storeSRID=#this.siteResourceID#&storeID=#arguments.event.getValue('storeID')#&mode=stream&meth=";

			/* settings tab */
			local.qrySelectedMP = this.objAdminStore.getActiveMerchantProfiles(storeID=arguments.event.getValue('storeID'));
			local.selectedMPList = valuelist(local.qrySelectedMP.profileID);
			if (this.storeInfo.OfferStreams) {
				local.streamProfilesLink = '#local.storeDTRootLink#getStreamProfiles';
			}

			/* categories tab */
			local.storeCategoriesLink = "#local.storeDTRootLink#getCategoryList";

			/* products tab */
			local.productsListLink = "#local.storeDTRootLink#getProductList";
			local.qryCategories = this.objAdminStore.getStoreCategories(storeID=arguments.event.getValue('storeID'));

			/* shipping tab */
			local.shippingListLink = "#local.storeDTRootLink#getShippingList";
			local.shippingMethods = this.objAdminStore.getShippingMethods(arguments.event);
			
			/* order tab */
			local.storeOrderLink = "#local.storeDTRootLink#getStoreOrderList";

			/* unclaimed & claimed affirmations tab */
			if (this.storeInfo.OfferAffirmations) {
				local.unclaimedResultsLink = '#local.storeDTRootLink#getUnclaimedAffirmations';
				local.claimedResultsLink = '#local.storeDTRootLink#getClaimedAffirmations';
			}

			arguments.event.setValue('storeApplicationInstanceID', this.storeInfo.applicationInstanceID);
			arguments.event.setValue('storeID',this.storeInfo.storeID);
			
			local.formLink = this.link.saveSettings & '&storeID=' & arguments.event.getValue('storeID');
			local.storeDetailLink = this.link.sendOrderDetail;
			local.removeShippingRenderLink = this.link.removeShippingRenderLink & '&storeID=' & arguments.event.getValue('storeID');
			local.formAction = "Update Store Settings";
			local.showImpTemplate = false;
			local.exportOrdersLink = buildCurrentLink(arguments.event,"exportOrdersPrompt")& "&mode=direct";

			// Initialize GL Account Widgets for store settings
			if (val(this.storeInfo.GLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=this.storeInfo.GLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.GLAccountPath = "";
			}
			local.strProductRevenueGLAcctWidgetData = {
				label="Default Product Revenue Account",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=val(this.storeInfo.GLAccountID),
				pathFldValue=local.GLAccountPath,
				pathNoneTxt="(No account selected)"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			if (val(this.storeInfo.ShippingGLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=this.storeInfo.ShippingGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.ShippingGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.ShippingGLAccountPath = "";
			}
			local.strShippingRevenueGLAcctWidgetData = {
				label="Default Shipping Revenue Account",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="ShippingGLAccountID",
				idFldValue=val(this.storeInfo.ShippingGLAccountID),
				pathFldValue=local.ShippingGLAccountPath,
				pathNoneTxt="(No account selected)"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);

			// Initialize Org Identity Selector for store settings
			local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(
				orgID=arguments.event.getValue('mc_siteinfo.orgID'),
				selectorID="orgIdentityID",
				selectedValueID=val(this.storeInfo.orgIdentityID),
				allowBlankOption=false
			);

			local.baseTestLink = getAppBaseLink(applicationInstanceID=this.storeInfo.applicationInstanceID);
		</cfscript>
		<cfif val(arguments.event.getValue('hidReload',0)) eq 1>
			<cfset local.showImpTemplate = true>
		</cfif>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfquery name="local.qryStreamProviders" datasource="#application.dsn.memberCentral.dsn#">
			SELECT p.providerID, p.providerName
			FROM dbo.stream_providers as p
			WHERE p.isActive = 1
			ORDER BY p.providerName
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_store.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfif arguments.importData eq 0>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cfset local.returnData = structNew()>
			<cfset local.returnData.data = local.data>
			<cfreturn local.returnData>
		</cfif>
	</cffunction>
	
	<cffunction name="linkAffirmationToMember" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.qryAffirmation = this.objAdminStore.getAffirmationByID(arguments.event)>

		<cfif local.qryAffirmation.recordcount>
			<cfset local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & '&mode=stream'>
			<cfset local.objStore = CreateObject("component","model.store.store")>
			<cfset local.qryFormats = local.objStore.getFormats(arguments.event,arguments.event.getValue('itemid'))>
			<cfset local.objCredit = CreateObject("component","model.admin.credit.credit")>
			<cfset local.qryCreditsGrid = local.objCredit.getCreditOfferedGrid('Store',local.qryAffirmation.productFormatID)>

			<cfquery name="local.qryFormats" dbtype="query">
				select * from [local].qryFormats where isAffirmation = 0 and offerAffirmations = 1
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfif local.qryAffirmation.recordcount>
				<cfinclude template="frm_linkAffirmation.cfm">
			<cfelse>
				<cfoutput>
				<div class="alert alert-danger mb-4">This affirmation has already been linked to a member.</div>
				<h4>Link Affirmation to Member</h4>
				<div class="alert alert-info mt-4">This affirmation is no longer an unclaimed affirmation. Close this box and select a different affirmation.</div>
				</cfoutput>
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveLinkAffirmationToMember" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();

			if( val(arguments.event.getValue('affirmationID')) and val(arguments.event.getValue('assignToMemberID')) and val(arguments.event.getValue('assignToFormatID')) ){
				this.objAdminStore.linkAffirmationToMember(arguments.event);
			}
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadUnclaimedAffirmationsTab();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
		
	<cffunction name="generateAffirmationCertificate" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.returnStruct = CreateObject("component","certificate").generateCertificate(arguments.event.getValue('affirmationID'));	
		</cfscript>
		
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnStruct.certificatePath, displayName=ListLast(local.returnStruct.certificatePath,"/"), deleteSourceFile=1)>
	</cffunction>

	<cffunction name="editCategory" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.categoryID = arguments.event.getValue('categoryID',0);
			local.qryCategory = this.objAdminStore.getCategoryByID(storeID=arguments.event.getValue('storeID'), categoryID=local.categoryID);
			local.qryStoreCategories = this.objAdminStore.getStoreCategories(storeID=arguments.event.getValue('storeID'));
			local.storeApplicationInstanceID = arguments.event.getValue('storeApplicationInstanceID');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_category.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewCertificate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		// security 
		if (val(arguments.event.getValue('aID',0)) lte 0)
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>

		<cfswitch expression="#arguments.event.getValue('certaction','')#">
			<cfcase value="loadCert">
				<cftry>
					<cfset local.strCertificate = CreateObject("component","certificate").generateCertificate(affirmationID=arguments.event.getValue('aid'))>
					<cfif len(local.strCertificate.certificateURL)>
						<cflocation url="#local.strCertificate.certificateURL#" addtoken="No">
					</cfif>
					<cfsavecontent variable="local.data">
						<cfoutput>No certificate generated.</cfoutput>
					</cfsavecontent>
					
					<cfcatch>
						<cfset application.objError.extendRequestTimeout() />
						<cfset application.objError.sendError(cfcatch=cfcatch) />
					</cfcatch>
				</cftry>
			</cfcase>
			<cfcase value="emailCert">
 				<cfif isValid("regex",arguments.event.getValue('_email',''),application.regEx.email)>
					<cfset doEmailCert(event=arguments.event,affirmationID=arguments.event.getValue('aid'),emailOverride=arguments.event.getValue('_email',''))>
				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">top.MCModalUtils.hideModal();</script>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>

				<cfquery name="local.qryAffirmationMember" datasource="#application.dsn.memberCentral.dsn#">
					select me.email
					from dbo.crd_affirmations ca
					inner join dbo.ams_members m on m.memberid = ca.assignToMemberID
					inner join dbo.ams_members m2 on m2.memberid = m.activememberid	
					inner join dbo.ams_memberEmails as me on me.orgID = m.orgID and me.memberID = m2.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
					where ca.affirmationID = <cfqueryparam value="#arguments.event.getValue('aID',0)#" cfsqltype="cf_sql_integer">
				</cfquery>

				<cfset local.purchaserEmail = local.qryAffirmationMember.email>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfinclude template="frm_creditCertificate.cfm">
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doEmailCert" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="affirmationID" type="numeric" required="true">
		<cfargument name="emailOverride" type="string" required="true" default="">
		<cfargument name="customText" type="string" required="no" default="">
		
		<cfset var local = structNew()>
		<cfset local.objCertificate = CreateObject("component","certificate")>
		<cfset local.strCertificate = local.objCertificate.generateCertificate(affirmationID=arguments.affirmationID)>
		<cfif StructKeyExists(local.strCertificate,"certificatePath")>
			<!--- wait one second for the pdf to appear on the server --->
			<cfloop from="1" to="5" index="local.count">
				<cfif NOT FileExists("#local.strCertificate.certificatePath#")>
					<cfset createObject("java","java.lang.Thread").sleep(1000)>
				<cfelse>
					<cfbreak>
				</cfif>
			</cfloop>
				
			<cfif FileExists("#local.strCertificate.certificatePath#")>
				<cfscript>
				local.strEmailContent = local.objCertificate.generateCertificateEmail(affirmationID=arguments.affirmationID, customText=arguments.customtext);

				local.strReturn = application.objEmailWrapper.sendMailESQ(
										emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
										emailto=[{ name=local.strEmailContent.affirmationMemberName, email=arguments.emailOverride }],
										emailreplyto=this.storeInfo.emailRecipient,
										emailsubject=local.strEmailContent.subject,
										emailtitle=local.strEmailContent.subject,
										emailhtmlcontent=local.strEmailContent.htmlemail,
										emailAttachments=[{ file="certificate.pdf", folderpath=local.strCertificate.certificateFolderPath }],
										siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										memberID=local.strEmailContent.memberID,
										messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="STORECERT"),
										sendingSiteResourceID=this.siteResourceID
									);
				</cfscript>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="editShipping" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		<cfsavecontent variable="local.data">
			<cfscript>
				local.contentData 	= this.objAdminStore.getShippingByID(arguments.event, url.shippingID);
				local.shippingName 	= local.contentData.shippingName;
				local.Visible = local.contentData.Visible;
				local.storeApplicationInstanceID = arguments.event.getValue('storeApplicationInstanceID');
				local.buttonValue	= 'Update Shipping Method';
				
				// Build breadCrumb Trail ----------------------------------------------------------------- ::
				appendBreadCrumbs(arguments.event,{ link='#this.link.home#&tab=shipping', text="Shipping Methods" });
				appendBreadCrumbs(arguments.event,{ link='', text="Edit Shipping Method" });
			</cfscript>
			<cfoutput>
				<cfinclude template="frm_shipping.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editProduct" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			
			local.itemID = int(val(arguments.event.getValue('itemID',0)));
			local.storeID = arguments.event.getValue('storeID');
			local.product = this.objAdminStore.getProductByID(arguments.event, local.itemID);
			local.urlString = this.link.saveProduct & "&mode=stream" & "&itemID=" & local.itemID;
			local.storeApplicationInstanceID = arguments.event.getValue('storeApplicationInstanceID');	

			local.storeProductFormatsRatesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=storeJSON&meth=getStoreProductFormatsRates&mode=stream&storeSRID=#this.siteResourceID#&storeID=#local.storeID#&itemID=#local.itemID#";

			local.buttonValue	= 'Update Product';
			local.qryCategories = this.objAdminStore.getStoreCategories(storeID=local.storeID);

			// Initialize GL Account Widgets for product form
			local.strProductRevenueGLAcctWidgetData = {
				label="Product Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="productGLAccountID",
				idFldValue=0,
				pathFldValue=len(local.product.productGLAccountPath) ? #local.product.productGLAccountPath# : "",
				pathNoneTxt="(No override; uses store's designated GL Account.)",
				clearBtnTxt="Clear Selected GL Account"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);
			local.strShippingRevenueGLAcctWidgetData = {
				label="Shipping Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="productShippingGLAccountID",
				idFldValue=0,
				pathFldValue=len(local.product.productShippingGLAccountPath) ? #local.product.productShippingGLAccountPath# : "",
				pathNoneTxt="(No override; uses store's designated GL Account.)",
				clearBtnTxt="Clear Selected GL Account"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);
	
			// Build breadCrumb Trail ----------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.home#&tab=products', text="Products" });
			appendBreadCrumbs(arguments.event,{ link='', text="Edit Product" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_product.cfm">
		</cfsavecontent>
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addProduct" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.security.add = checkRights(arguments.event,'AddProducts')>
		<cfif NOT local.security.add>
			<cflocation url="#this.link.message#&message=1" addtoken="no">
		</cfif>
		<cfsavecontent variable="local.data">
			<cfscript>
				local.product 	= this.objAdminStore.getProductByID(arguments.event, "0");
				local.urlString 	= this.link.insertProduct & "&mode=stream" & "&itemID=0";
				local.storeApplicationInstanceID = arguments.event.getValue('storeApplicationInstanceID');
				local.buttonValue	= 'Add Product';
				local.itemID = "0";
				local.storeID = arguments.event.getValue('storeID');
				local.qryCategories = this.objAdminStore.getStoreCategories(storeID=local.storeID);

				// Initialize GL Account Widgets for product form
				if (val(local.product.productGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.product.productGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.GLAccountPath = "";
				}
				local.strProductRevenueGLAcctWidgetData = {
					label="Product Revenue Override",
					btnTxt="Choose GL Account",
					glatid=3,
					widgetMode='GLSelector',
					idFldName="productGLAccountID",
					idFldValue=val(local.product.productGLAccountID),
					pathFldValue=local.GLAccountPath,
					pathNoneTxt="(No override; uses store's designated GL Account.)",
					clearBtnTxt="Clear Selected GL Account"
				};
				local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

				if (val(local.product.productShippingGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.product.productShippingGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.ShippingGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.ShippingGLAccountPath = "";
				}
				local.strShippingRevenueGLAcctWidgetData = {
					label="Shipping Revenue Override",
					btnTxt="Choose GL Account",
					glatid=3,
					widgetMode='GLSelector',
					idFldName="productShippingGLAccountID",
					idFldValue=val(local.product.productShippingGLAccountID),
					pathFldValue=local.ShippingGLAccountPath,
					pathNoneTxt="(No override; uses store's designated GL Account.)",
					clearBtnTxt="Clear Selected GL Account"
				};
				local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);

				// Build breadCrumb Trail
				appendBreadCrumbs(arguments.event,{ link='', text="Add Product" });
			</cfscript>
			
			<cfoutput>
				<cfinclude template="frm_product.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="copyProduct" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsavecontent variable="local.data">
			<cfscript>
				local.copyFromitemID = arguments.event.getValue('copyFromitemID',0);
				local.product 	= this.objAdminStore.getProductByID(arguments.event, local.copyFromitemID);
				local.urlString 	= this.link.insertProduct & "&mode=stream" & "&copyFromitemID=" & local.copyFromitemID;
				local.storeApplicationInstanceID = arguments.event.getValue('storeApplicationInstanceID');
				local.buttonValue	= 'Copy Product';
				local.itemID = "0";
				local.storeID = arguments.event.getValue('storeID');
				local.qryCategories = this.objAdminStore.getStoreCategories(storeID=local.storeID);
				local.product["contentTitle"][1] = "Copy of " & local.product.contentTitle;
				local.product["productID"][1] = "";
				local.product["showAvailable"][1] = 0;

				// Initialize GL Account Widgets for product form
				if (val(local.product.productGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.product.productGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.GLAccountPath = "";
				}
				local.strProductRevenueGLAcctWidgetData = {
					label="Product Revenue Override",
					btnTxt="Choose GL Account",
					glatid=3,
					widgetMode='GLSelector',
					idFldName="productGLAccountID",
					idFldValue=val(local.product.productGLAccountID),
					pathFldValue=local.GLAccountPath,
					pathNoneTxt="(No override; uses store's designated GL Account.)",
					clearBtnTxt="Clear Selected GL Account"
				};
				local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

				if (val(local.product.productShippingGLAccountID) gt 0) {
					local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.product.productShippingGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
					local.ShippingGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
				} else {
					local.ShippingGLAccountPath = "";
				}
				local.strShippingRevenueGLAcctWidgetData = {
					label="Shipping Revenue Override",
					btnTxt="Choose GL Account",
					glatid=3,
					widgetMode='GLSelector',
					idFldName="productShippingGLAccountID",
					idFldValue=val(local.product.productShippingGLAccountID),
					pathFldValue=local.ShippingGLAccountPath,
					pathNoneTxt="(No override; uses store's designated GL Account.)",
					clearBtnTxt="Clear Selected GL Account"
				};
				local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);
				
				appendBreadCrumbs(arguments.event,{ link='', text="Copy Product" });
			</cfscript>
			<cfoutput>
				<cfinclude template="frm_product.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProduct" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = "";
			/* ------------------------------------------------------------------------------------------ */
			/* PAGE SECURITY ---------------------------------------------------------------------------- */
			local.security.edit		= checkRights(arguments.event,'EditProducts');

			if( NOT local.security.edit ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}
			/* ------------------------------------------------------------------------------------------ */
			local.updateProduct = this.objAdminStore.updateProduct(arguments.event);
			
			// product contentObject - Product Description
			local.updateProductContent = this.objAdminStore.updateContent(
				arguments.event.getValue('productContentID'),
				this.languageID,
				1,
				arguments.event.getValue('contentTitle'),
				'',
				replace(arguments.event.getValue('productContent'),"&quot;",chr(34),"ALL")
			);	
			
			// product contentObject - Product Summary
			local.updateProductContent = this.objAdminStore.updateContent(
				arguments.event.getValue('summaryContentID'),
				this.languageID,
				0,
				arguments.event.getValue('contentTitle'),
				'',
				replace(arguments.event.getValue('productSummary'),"&quot;",chr(34),"ALL")
			);
			
			// Add product format
			if( arguments.event.valueExists('btnAddProductFormat') AND arguments.event.getValue('productFormat') NEQ ""){
				local.loadCategories	= this.objAdminStore.insertProductFormat(arguments.event);
				local.itemValue 		= "&itemID=" & arguments.event.getValue('itemID');
				local.tabValue 			= "&tab=productRates";
				local.msg				= "3";
				
				local.redirectURL = this.link.editProduct & local.itemValue & local.tabValue; 
				application.objCommon.redirect('#local.redirectURL#&msg=#local.msg#');
			}
			application.objCommon.redirect('#this.link.editProduct#&storeID=#arguments.event.getValue('storeID')#&itemID=#arguments.event.getValue('itemID')#');
		</cfscript>
	</cffunction>

	<cffunction name="insertProduct" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = "";
			/* ------------------------------------------------------------------------------------------ */
			/* PAGE SECURITY ---------------------------------------------------------------------------- */
			local.security.edit		= checkRights(arguments.event,'EditProducts');

			if( NOT local.security.edit ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}
		</cfscript>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="store_createProduct">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('productID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#this.languageID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('contentTitle')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getValue('productContent'),"&quot;",chr(34),"ALL")#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('showQuantity')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('showAvailable')#">
			<cfif val(arguments.event.getValue('productGLAccountID'))>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('productGLAccountID')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="NULL" null="true">
			</cfif>
			<cfif val(arguments.event.getValue('productShippingGLAccountID'))>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('productShippingGLAccountID')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="NULL" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getValue('productSummary'),"&quot;",chr(34),"ALL")#">
			<cfif len(trim((arguments.event.getValue('productDate'))))>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('productDate')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="NULL" null="true">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('categoryID','')#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.itemID">
			<cfprocparam type="Out" cfsqltype="CF_SQL_VARCHAR" variable="local.errorMessage">
		</cfstoredproc>
		
		<cfscript>
			if (local.errorMessage EQ "ProductIDExists"){
				application.objCommon.redirect('#this.link.home#&msg=18&tab=products');
			}
			else {
				if (arguments.event.valueExists('copyFromitemID') and arguments.event.getValue('copyFromitemID')){
					this.objAdminStore.saveCopyProduct(storeID=arguments.event.getValue('storeID'), copyFromItemID=arguments.event.getValue('copyFromitemID'), itemID=local.itemID);
				}
				application.objCommon.redirect('#this.link.home#&msg=12&tab=products');
			}
		</cfscript>
	</cffunction>

	<cffunction name="getDocument" access="public" returntype="query">
		<cfargument name="documentID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.memberDocument">
			SELECT md.documentID, dl.docTitle, dv.fileName, dv.fileExt, dv.dateModified
				, d.siteResourceID, d.sectionID, dl.documentLanguageID, dl.languageID, dl.docTitle
				, dl.docDesc, dv.dateCreated, sr.siteResourceStatusID, dv.documentVersionID 
			FROM dbo.store_ProductFormatsDocuments md
			INNER JOIN dbo.cms_documents d on md.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages dl on d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions dv on dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = d.siteResourceID
			WHERE md.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
		</cfquery>

		<cfreturn local.memberDocument>
	</cffunction>
	
	<cffunction name="addDocument" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.usageID = 0>
		<cfset local.itemID = arguments.event.getValue('itemID')>
		<cfset local.formatID = arguments.event.getValue('formatID')>

		<cfset local.docTitle = "">
		<cfset local.docDesc = "">
		<cfset local.expireDays = "">
		<cfset local.existingFileName = "">
		<cfset local.documentLanguageID = 0>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_document.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editDocument" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.usageID = arguments.event.getValue('usageID')>
		<cfset local.itemID = arguments.event.getValue('itemID')>
		<cfset local.formatID = arguments.event.getValue('formatID')>

		<cfset local.qryDocument = CreateObject("component","store").getStoreDocuments(formatID=local.formatID, usageID=local.usageID)>
		<cfset local.docTitle = local.qryDocument.docTitle>
		<cfset local.docDesc = local.qryDocument.docDesc>
		<cfset local.expireDays = local.qryDocument.accessExpireInDays>
		<cfset local.existingFileName = local.qryDocument.fileName>
		<cfset local.documentLanguageID = local.qryDocument.documentLanguageID>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_document.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveDocument" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
		
		<cfif arguments.event.getValue('usageID') gt 0>

			<cfif len(arguments.event.getTrimValue('newFile'))>
				<cfset local.fileUploaded = true>
				<cftry>
					<cfset local.newFile = local.objDocument.uploadFile("form.newFile")>
					<cfset local.fileUploaded = local.newFile.uploadComplete>
				<cfcatch type="Any">
					<cfset local.fileUploaded = false>
				</cfcatch>
				</cftry>

				<cfif local.fileUploaded>
					<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
					<cfset local.documentVersionID = local.objDocument.insertVersion(orgcode=arguments.event.getValue('mc_siteInfo.orgcode'), 
							siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), documentLanguageID=arguments.event.getValue('documentLanguageID'),
							fileData=local.newFile, author='', contributorMemberID=session.cfcuser.memberdata.memberID, recordedByMemberID=session.cfcuser.memberdata.memberID,
							oldFileExt=local.newFile.clientFileExt)>
				</cfif>
			</cfif>

			<cfquery name="local.qrySaveStoreDoc" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @docLanguageID int, @docVersionID int;
					SET @docLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('documentLanguageID')#">;
					SELECT @docVersionID = max(documentVersionID) from dbo.cms_documentVersions where documentLanguageID = @docLanguageID;

					BEGIN TRAN;
						UPDATE dbo.cms_documentLanguages
						SET docTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('docTitle')#">, 
							docDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('docDesc')#">, 
							dateModified = getdate()
						WHERE documentLanguageID = @docLanguageID;

						UPDATE dbo.cms_documentVersions
						SET	dateModified = getDate()
						WHERE documentVersionID = @docVersionID;

						UPDATE dbo.store_ProductFormatsDocuments
						SET accessExpireInDays = nullIf(nullIf(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('expireDays')#">,''),0)
						WHERE uid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('usageID')#">;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

		<cfelse>

			<cfset local.fileUploaded = true>
			<cftry>
				<cfset local.newFile = local.objDocument.uploadFile("form.newFile")>
				<cfset local.fileUploaded = local.newFile.uploadComplete>
			<cfcatch type="Any">
				<cfset local.fileUploaded = false>
			</cfcatch>
			</cftry>

			<cfif local.fileUploaded>
				<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
				<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
							resourceType='ApplicationCreatedDocument', parentSiteResourceID=this.storeInfo.siteResourceID,
							sectionID=this.storeInfo.rootSectionID, docTitle=arguments.event.getTrimValue('docTitle'),
							docDesc=arguments.event.getTrimValue('docDesc'), author='', fileData=local.newFile, isActive=1, isVisible=true, 
							contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid, 
							oldFileExt=local.newFile.serverFileExt)>

				<cfquery name="local.qrySaveStoreDoc" datasource="#application.dsn.membercentral.dsn#">
					INSERT INTO dbo.store_ProductFormatsDocuments (formatID, ItemID, documentID, accessExpireInDays)
					VALUES (
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('formatID')#">,
						<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('itemID')#">,
						<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertResults.documentID#">,
						nullIf(nullIf(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('expireDays')#">,''),0)
					)
				</cfquery>
			</cfif>
		</cfif>
			
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadProductFormatsTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="addStream" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.usageID = 0>
		<cfset local.profileID = int(val(arguments.event.getValue('profileID',0)))>
		<cfset local.formatID = arguments.event.getValue('formatID')>

		<cfif local.profileID is 0>
			<cfquery name="local.qryProfiles" datasource="#application.dsn.membercentral.dsn#">
				SELECT profileID, profileName
				FROM dbo.stream_profiles
				WHERE siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
				AND isActive = 1
			</cfquery>
			<cfif local.qryProfiles.recordcount is 1>
				<cfset local.profileID = local.qryProfiles.profileID>
			</cfif>
		</cfif>

		<cfif local.profileID gt 0>
			<cfquery name="local.qryProfileName" datasource="#application.dsn.membercentral.dsn#">
				SELECT profileName
				FROM dbo.stream_profiles
				WHERE profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
			</cfquery>
			<cfset local.profileName = local.qryProfileName.profileName>

			<cfquery name="local.qryStreamFields" datasource="#application.dsn.membercentral.dsn#">
				SELECT pf.providerFieldID, pf.fieldName
				FROM dbo.stream_profiles as p
				INNER JOIN dbo.stream_providerFields as pf on pf.providerID = p.providerID
				INNER JOIN dbo.stream_providerFieldLevels as pfl on pfl.fieldLevelID = pf.fieldLevelID
				WHERE p.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
				AND pfl.fieldLevel = 'usage'
				ORDER BY pf.fieldName
			</cfquery>
			<cfset local.streamUsageFields = QueryNew("providerFieldID,fieldvalue","integer,varchar")>
		</cfif>

		<cfset local.product = this.objAdminStore.getProductByID(arguments.event, arguments.event.getValue('itemID'))>
		<cfset local.streamName = local.product.contentTitle>
		<cfset local.expireDays = "">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_stream.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editStream" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.usageID = arguments.event.getValue('usageID')>
		<cfset local.formatID = arguments.event.getValue('formatID')>

		<cfset local.qryStream = CreateObject("component","store").getStoreStreams(formatID=local.formatID, usageID=local.usageID)>
		<cfset local.profileID = local.qryStream.profileID>
		<cfset local.profileName = local.qryStream.profileName>
		<cfset local.streamName = local.qryStream.streamName>
		<cfset local.expireDays = local.qryStream.accessExpireInDays>

		<cfquery name="local.qryStreamFields" datasource="#application.dsn.membercentral.dsn#">
			SELECT pf.providerFieldID, pf.fieldName
			FROM dbo.stream_profiles as p
			INNER JOIN dbo.stream_providerFields as pf on pf.providerID = p.providerID
			INNER JOIN dbo.stream_providerFieldLevels as pfl on pfl.fieldLevelID = pf.fieldLevelID
			WHERE p.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
			AND pfl.fieldLevel = 'usage'
			ORDER BY pf.fieldName
		</cfquery>

		<cfquery name="local.streamUsageFields" datasource="#application.dsn.membercentral.dsn#">
			select puf.providerFieldID, puf.value as fieldvalue
			FROM dbo.stream_profileUsageFields as puf
			WHERE puf.usageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.usageID#">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_stream.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveStream" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qrySaveStream" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @usageID int, @profileID int, @formatID int, @streamName varchar(400), @expireDays int;
				SET @usageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('usageID')#">;
				SET @profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">;
				SET @formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('formatID')#">;
				SET @streamName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('streamName')#">;
				SET @expireDays = nullIf(nullIf(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('expireDays')#">,''),0);

				BEGIN TRAN;	
					IF @usageID = 0 BEGIN
						INSERT INTO dbo.stream_profileUsages (profileID, streamName)
						VALUES (@profileID, @streamName);
							SELECT @usageID = SCOPE_IDENTITY();

						INSERT INTO dbo.store_ProductFormatsStreamUsages (formatID, usageID, accessExpireInDays)
						VALUES (@formatID, @usageID, @expireDays);
					END ELSE BEGIN
						UPDATE dbo.stream_profileUsages
						SET streamName = @streamName
						WHERE usageID = @usageID;

						UPDATE dbo.store_ProductFormatsStreamUsages
						SET accessExpireInDays = @expireDays
						WHERE formatID = @formatID
						AND usageID = @usageID;
					END

					DELETE FROM dbo.stream_profileUsageFields
					WHERE usageID = @usageID;

					<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
						<cfif left(local.thisField,4) eq "use_">
							insert into dbo.stream_profileUsageFields (usageID, providerFieldID, value)
							values (@usageID, #GetToken(local.thisField,2,"_")#, <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue(local.thisField,'')#">);
						</cfif>
					</cfloop>
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadProductFormatsTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewStreamLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrLogs = ArrayNew(1)>
		<cfset local.orderDetailID = arguments.event.getValue('orderDetailID',0)>

		<cfquery name="local.qryLogs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID',0)#">;

			select o.DateOfOrder, ods.logID, 
				m2.memberid, m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as membername, 
				m2.company,
				pc.contentTitle as productTitle, pf.name as formatTitle, pu.streamName
			FROM dbo.store_orders as o
			INNER JOIN dbo.store_orderDetails AS od on od.orderID = o.orderID
			inner join dbo.store_orderDetailStreams as ods on ods.orderDetailID = od.orderDetailID
			inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
			inner join dbo.store_ProductFormatsStreamUsages as pfsu on pfsu.uid = ods.streamUsageUID
			inner join dbo.store_ProductFormats as pf on pf.formatID = pfsu.formatID
			INNER JOIN dbo.stream_profileUsages pu on pu.usageID = pfsu.usageID
			inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
			INNER JOIN dbo.ams_members as m on m.memberid = ods.assignedToMemberID
			INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
			WHERE od.orderDetailID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orderDetailID#">
			ORDER BY ods.logID;
		</cfquery>

		<cfloop query="local.qryLogs">
			<cfset local.logkey = "#getTickCount()#|#local.qryLogs.logid#|#local.qryLogs.memberid#|#randrange(1,1000000)#">
			<cfset local.encryptString = encrypt(local.logkey ,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>
			<cfhttp url="#application.paths.backendPlatform.internalUrl#?event=logging.download&lid=#local.encryptString#" method="get" result="local.log"></cfhttp>

			<cfif listFind("404,500",local.log.responseHeader.Status_Code)>
				<cfset local.logfileContent = "">
			<cfelse>
				<cfset local.logfileContent = local.log.fileContent>
			</cfif>

			<cfset local.tmpStr = { dateOfOrder=local.qryLogs.dateOfOrder, memberid=local.qryLogs.memberID, membername=local.qryLogs.membername, 
									company=local.qryLogs.company, productTitle=local.qryLogs.productTitle, formatTitle=local.qryLogs.formatTitle,
									streamName=local.qryLogs.streamName, logcontent=trim(local.logfileContent) }>
			<cfset arrayAppend(local.arrLogs,local.tmpStr)>
		</cfloop>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_streamLogs.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageStreams" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrLogs = ArrayNew(1)>
		<cfset local.orderDetailID = arguments.event.getValue('orderDetailID',0)>

		<cfquery name="local.qryStreams" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID',0)#">;

			select o.DateOfOrder, ods.orderDetailStreamID, ods.dateExpires, ods.dateCompleted, 
				m2.memberid, m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as membername, 
				m2.company,
				pc.contentTitle as productTitle, pf.name as formatTitle, pu.streamName
			FROM dbo.store_orders as o
			INNER JOIN dbo.store_orderDetails AS od on od.orderID = o.orderID
			inner join dbo.store_orderDetailStreams as ods on ods.orderDetailID = od.orderDetailID
			inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
			inner join dbo.store_ProductFormatsStreamUsages as pfsu on pfsu.uid = ods.streamUsageUID
			inner join dbo.store_ProductFormats as pf on pf.formatID = pfsu.formatID
			inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
			INNER JOIN dbo.stream_profileUsages pu on pu.usageID = pfsu.usageID
			INNER JOIN dbo.ams_members as m on m.memberid = ods.assignedToMemberID
			INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
			WHERE od.orderDetailID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orderDetailID#">
			ORDER BY ods.logID;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_streamManage.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveStreamDates" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qrySaveStream" datasource="#application.dsn.membercentral.dsn#">
			UPDATE dbo.store_orderDetailStreams
			SET dateCompleted = <cfif len(arguments.event.getTrimValue('dc'))><cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('dc')#"><cfelse>null</cfif>,
				dateExpires = <cfif len(arguments.event.getTrimValue('de'))><cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('de')#"><cfelse>null</cfif>
			WHERE orderDetailStreamID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('odsid')#">
		</cfquery>
		<cfset local.returnStruct = { success=true }>

		<cfreturn returnAppStruct(SerializeJSON(local.returnStruct),"echo")>
	</cffunction>

	<cffunction name="manageStreamCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.arrLogs = ArrayNew(1)>
		<cfset local.orderDetailID = arguments.event.getValue('orderDetailID',0)>

		<cfquery name="local.qryProductFormat" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID',0)#">;

			select o.DateOfOrder, aff.affirmationID, 
				m2.memberid, m2.lastname + ', ' + m2.firstname + ' ' + isnull(m2.middlename,'') + ' (' + m2.membernumber + ')' as membername, 
				m2.company,
				pc.contentTitle as productTitle, pf.name as formatTitle, aff.dateClaimed,

				cast(isnull((
					select credit.offeringTypeID, credit.creditValueAwarded
					from dbo.crd_requests as credit
					inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = credit.offeringTypeID
					inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
					inner join dbo.crd_authorities as ca on ca.authorityId = cat.authorityID
					where credit.affirmationID = aff.affirmationID
					and credit.creditAwarded = 1
					for XML AUTO, ROOT('credits')
					),'<credits/>') as xml) as awardedCreditsXML,

				cast(isnull((
					select ect.offeringTypeID, ect.creditValue, 
						isnull(ast.ovTypeName,cat.typeName) as creditType,
						isnull(ca.authorityName,'') as authority
					from dbo.crd_offeringTypes as ect 
					inner join dbo.crd_offerings as eo on eo.offeringID = ect.offeringID and eo.productFormatID = pf.formatID
					inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
					inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
					inner join dbo.crd_authorities as ca on ca.authorityId = cat.authorityID
					for XML AUTO, ROOT('credits')
					),'') as xml) as formatCreditsXML
			FROM dbo.store_orders as o
			INNER JOIN dbo.store_orderDetails AS od on od.orderID = o.orderID
			inner join dbo.store_Products as p on p.storeID = @storeID and p.itemID = od.productItemID
			inner join dbo.store_ProductFormats as pf on pf.formatID = od.formatID
			inner join dbo.cms_contentLanguages as pc on pc.contentID = p.productContentID and pc.languageID = 1
			inner join dbo.crd_affirmations as aff on aff.orderID = o.orderID and aff.productFormatID = pf.formatID
			inner join dbo.crd_affirmationTypes as afft on afft.affirmationTypeID = aff.affirmationTypeID and afft.affirmationType = 'staff-approved'
			INNER JOIN dbo.ams_members as m on m.memberid = o.memberID
			INNER JOIN dbo.ams_members as m2 on m2.memberid = m.activememberid
			WHERE od.orderDetailID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orderDetailID#">;
		</cfquery>

		<cfif local.qryProductFormat.recordCount gt 0>
			<cfset local.arrFormatCredits = XMLSearch(local.qryProductFormat.formatCreditsXML,"/credits/ect")>
		<cfelse>
			<cfset local.arrFormatCredits = arrayNew(1)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_streamManageCredit.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveStreamCredits" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @siteID int, @affirmationID int, @orderDetailID int, @offeringTypeID int, @creditValueAwarded decimal(6,2), 
						@memberID int, @nowDate datetime = getdate(), @requestID int;

					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					set @affirmationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('affirmationID',0)#">;
					set @orderDetailID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('orderDetailID',0)#">;

					select @memberID = m.activeMemberID
					from dbo.store_orderDetails as od
					inner join dbo.store_orders as o on o.orderID = od.orderID
					inner join dbo.ams_members as m on m.memberID = o.memberID
					where od.orderDetailID = @orderDetailID;

					BEGIN TRAN;
						delete from dbo.crd_requests
						where affirmationID = @affirmationID;

						update dbo.crd_affirmations 
						set assignToMemberID = @memberID,
							dateClaimed = <cfif len(arguments.event.getValue('dateClaimed',''))><cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('dateClaimed')#"><cfelse>getdate()</cfif>
						where affirmationID = @affirmationID;

						<cfloop collection="#arguments.event.getCollection()#" item="local.thisEV">
							<cfif left(local.thisEv,4) eq "crd_" and arguments.event.getValue(local.thisEV,0) gt 0>
								set @offeringTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#GetToken(local.thisEV,2,'_')#">;
								set @creditValueAwarded = <cfqueryparam cfsqltype="cf_sql_double" value="#arguments.event.getValue(local.thisEV,0)#">;

								EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Store', @itemID=@affirmationID, @offeringTypeID=@offeringTypeID,
									@IDnumber='', @lastDateToComplete=@nowDate, @creditAwarded=1, @creditValueAwarded=@creditValueAwarded,
									@addedViaAward=1, @requestID=@requestID OUTPUT;
							</cfif>
						</cfloop>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshStreamsData();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageOrder" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfscript>
		local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
		if( NOT local.security.viewSettings)
			application.objCommon.redirect('#this.link.message#&message=1');

		local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID',0));
		local.strOrderDetails = this.objAdminStore.getOrderDetails(arguments.event.getValue('storeid'),local.qryOrder.orderID);
		local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo);
		local.editMode = 0;
		
		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		appendBreadCrumbs(arguments.event,{ link='#this.link.home#&tab=orders', text="Orders" });
		appendBreadCrumbs(arguments.event,{ link='', text='Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryOrder.orderID,"0000")#' });
		</cfscript>
		
		<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
			<cfset local.qryStates = application.objCommon.getStates() />
			<cfquery name="local.getStateCode" dbtype="query">
				<cfif isNumeric(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
					select stateCode from [local].qryStates where stateid = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_integer" />
				<cfelse>
					select stateCode from [local].qryStates where stateCode = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_varchar" />
				</cfif>
			</cfquery>
		</cfif>

		<cfset local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
		
		<cfquery name="local.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			SELECT ga.gatewayID, mpContent.rawContent as paymentInstructions
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			WHERE ga.gatewayID = <cfqueryparam value="#val(local.qryOrder.gatewayid)#" cfsqltype="cf_sql_integer">
			AND pr.profileID = <cfqueryparam value="#val(local.qryOrder.merchantProfileID)#" cfsqltype="cf_sql_integer">
			AND pr.[status] = 'A';
		</cfquery>

		<cfquery name="local.qryStatusLog" datasource="#application.dsn.membercentral.dsn#">
			select statusLogID, orderID, m2.memberID, previousStatusID, currentStatusID, osl.dateLastUpdated, 
				m2.firstname + ' ' + m2.lastname + ' (' + m2.membernumber + ')' as adminName,
				prev.statusName as previousStatusName, curr.statusName as currentStatusName
			from dbo.store_OrderStatusLog as osl
			inner join dbo.ams_members as m on m.memberID = osl.memberID
			inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
			left outer join dbo.store_OrderStatus as prev on prev.orderStatusID = osl.previousStatusID
			left outer join dbo.store_OrderStatus as curr on curr.orderStatusID = osl.currentStatusID
			where osl.orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrder.orderID#">
			order by osl.dateLastUpdated desc
		</cfquery>
		
		<cfquery name="local.qryGetStatusInfo" datasource="#application.dsn.membercentral.dsn#">
			select statusName, canEditAddress, canEditItems
			from dbo.store_OrderStatus
			where orderStatusID = <cfqueryparam value="#val(local.qryOrder.orderStatusID)#" cfsqltype="cf_sql_integer" />
		</cfquery>
		
		<cfquery name="local.qryAffirmations" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID',0)#">;

			select co.ASID, aff.productFormatID, aff.affirmationCode, cas.affirmationFileName, pCon.contentTitle
			from dbo.crd_affirmations as aff
			inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = aff.affirmationTypeID and cat.affirmationType = 'paper'
			inner join dbo.crd_offerings as co on co.offeringID = aff.offeringID
			inner join dbo.crd_authoritySponsors as cas on cas.ASID = co.ASID
			inner join dbo.store_ProductFormats as pf on pf.Formatid = aff.productFormatID
			inner join dbo.store_products as p on p.storeID = @storeID and p.itemID = pf.itemID
			inner join dbo.cms_contentLanguages pCon on pCon.contentid = p.productContentID and pCon.languageID = 1
			where aff.orderid = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrder.orderID#">
			and aff.status = 'A'
			order by co.ASID, aff.productFormatID, aff.affirmationCode;
		</cfquery>
		<cfset local.showDownloadAffirmationLink = local.qryAffirmations.recordCount>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_order.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSettings	= checkRights(arguments.event,'EditSettings');
			if( NOT local.security.editSettings){ application.objCommon.redirect('#this.link.message#&message=1'); }

			// update Store Settings -------------------------------------------------------------------- ::
			this.objAdminStore.updateStoreSettings(event=arguments.event);
			this.objAdminStore.updateMerchantProfiles(storeID=arguments.event.getValue('storeID'), profileIDList=arguments.event.getValue('merchantProfiles'));

			// store contentObject ---------------------------------------------------------------------- ::
			local.updateMainContent = this.objAdminStore.updateContent(arguments.event.getValue('mainContentID'), this.languageID, 1, 'StoreMain', '', arguments.event.getValue('mainContent'));
			local.updateMainContent = this.objAdminStore.updateContent(arguments.event.getValue('orderReceiptFooterContentID'), this.languageID, 0, 'StoreMailFooter', '', arguments.event.getValue('orderReceiptFooterContent'));
		</cfscript>
		
		<cflocation url="#this.link.home#&tab=settings&msg=1" addtoken="no">
	</cffunction>

	<cffunction name="addShipping" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset this.objAdminStore.insertShipping(storeID=this.storeInfo.storeID, shippingName=arguments.event.getValue('shippingName',''))>
		<cflocation url="#this.link.home#&tab=shipping" addtoken="no">
	</cffunction>
	
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();	
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this section.</b></cfcase>
							<cfcase value="2"><b>That Quick Link was not found.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportOrders" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "StoreOrders.csv">

		<cfstoredproc procedure="store_exportOrderTransactions" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('storeID')#">
			<cfif len(arguments.event.getTrimValue('o_prdkey',''))>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('o_prdkey','')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="Yes">
			</cfif>
			<cfif len(arguments.event.getTrimValue('o_pur',''))>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('o_pur','')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="Yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('o_st','')#">
			<cfif len(arguments.event.getValue('o_ds',''))>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('o_ds','')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
			</cfif>
			<cfif len(arguments.event.getValue('o_de',''))>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('o_de','')#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="Yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\#local.reportFileName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fsid',0)#">
		</cfstoredproc>
		
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addOrder" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.formAction = this.link.saveOrder;
			local.objMemberAdmin = CreateObject("component","model.admin.members.memberAdmin");
			local.objLocator = CreateObject("component","model.system.user.accountLocater");	
			local.qryMember = CreateObject("component","model.admin.members.members").getMember_demo(arguments.event.getValue('mid',0));
			local.qryStates = application.objCommon.getStates();
			local.qryCurrentRegMember = getPurchaserInfo(mid=arguments.event.getValue('mid',0));

			appendBreadCrumbs(arguments.event,{ link='', text="Create Order" });
		</cfscript>
		
		<cfset arguments.event.paramValue('regAction','') />
		<cfset arguments.event.setValue('mainurl',this.link.addOrder) />
		<cfset arguments.event.setValue('mainregurl',this.link.addOrder & '&sa=regStoreUser') />
		<cfset arguments.event.setValue('locatorurl',this.link.addOrder & '&sa=regStoreUser&regaction=locator&mode=stream')>
		<cfset local.stepNum = 1 />
		
		<cfswitch expression="#arguments.event.getValue('regAction')#">
			<cfcase value="locator">
				<cfset local.resultsData = local.objLocator.locateMemberAdmin(event=arguments.event)>
				<cfset local.xmlResultFields = local.resultsData.qryMemberLocator.mc_outputFieldsXML[1]>
				<cfsavecontent variable="local.data">
					<cfinclude template="addOrder_step1_results.cfm">
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo") />
			</cfcase>
			<!--- usemid (selected a member from step 1) --->
			<cfcase value="usemid">
				<cfset arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0)))) />
				<cfset local.qryMemberAddress = application.objMember.getMemberAddressByFirstAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=arguments.event.getValue('mid'))>
				<cfset local.stateid = 0 />
				<cfif len(local.qryMemberAddress.stateCode)>
					<cfquery name="local.getStateID" dbtype="query">
					select stateid from [local].qryStates where stateCode = <cfqueryparam value="#local.qryMemberAddress.stateCode#" cfsqltype="cf_sql_varchar" />
					</cfquery>
					<cfset local.stateid = val(local.getStateID.stateid) />
				</cfif>
				<cfif arguments.event.getValue('mid') gt 0>
					<cfset local.memberid = arguments.event.getValue('mid') />
					<cfset local.stepNum = 2 />
				</cfif>
			</cfcase>
			<cfcase value="newacct">
				<cfset local.newMemID = local.objLocator.createAccount(event=arguments.event) />
				<cfset arguments.event.paramValue('mid',local.newMemID) />
				<cfif local.newMemID gt 0>
					<cfsavecontent variable="local.data">
						<cfoutput>
						Please wait... we will refresh this page automatically.
						<script language="javascript">useMember(#local.newMemID#);</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn returnAppStruct(local.data,"echo") />
				</cfif>
			</cfcase>
			<cfcase value="createOrder">
				<cfscript>
				local.DateOfOrder = now();
				if (len(arguments.event.getValue('DateOfOrder')))
					local.DateOfOrder = arguments.event.getValue('DateOfOrder') & ' ' & timeFormat(now(),'h:mm tt');	
				
				arguments.event.setValue('DateOfOrder',local.DateOfOrder);
														
				</cfscript>
				<!--- Create order --->
				<cfset local.newOrderID = createOrder(event=arguments.event) />
				
				<cfif val(local.newOrderID)>
					<!--- Update order status --->
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrderStatusUpdate">
						insert into dbo.store_OrderStatusLog (orderID, memberID, previousStatusID, currentStatusID)
						values(
							<cfqueryparam value="#local.newOrderID#" cfsqltype="cf_sql_integer" />,
							<cfqueryparam value="#arguments.event.getValue('memberID')#" cfsqltype="cf_sql_integer" />,
							null,
							1
						)
					</cfquery>
					<cflocation url="#this.link.editOrderItems#&storeID=#arguments.event.getValue('storeID')#&orderID=#local.newOrderID#" addtoken="false" />
				</cfif>
			</cfcase>
		</cfswitch>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_addOrder.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="createOrder" access="public" output="false" returntype="numeric">
		<cfargument name="orderNumber" type="string" required="false" default="#createUUID()#" />
		<cfargument name="Event" type="any" required="true" />
		
		<cfscript>
		var local = structNew();

		// billing fields
		local.stateIDForTax = val(arguments.event.getValue('fldship_state',0));
		local.zipForTax = arguments.event.getTrimValue('fldship_zip','');
		if (NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip) {
			throw(message="Invalid State/Zip.");
		}
		
		// shipping info for storage
		local.xmlShippingInfo = '<shipping>';
		for (local.fld in arguments.event.getCollection()) {
			if (findNoCase("fldship_",local.fld))
				local.xmlShippingInfo = local.xmlShippingInfo & "<#lcase(local.fld)#>#xmlformat(arguments.event.getValue(local.fld))#</#lcase(local.fld)#>";
		}
		local.xmlShippingInfo = local.xmlShippingInfo & '</shipping>';
		</cfscript>
		
		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="local.qryInsert">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				INSERT INTO dbo.store_orders (storeID, orderNumber, DateOfOrder, totalProduct, shippingKey, xmlShippingInfo, totalTax, memberID, OrderCompleted, orderStatusID)
				VALUES(
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('storeID')#" />,
					<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#" />,
					<cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('DateOfOrder')#" />,
					<cfqueryparam cfsqltype="cf_sql_double" value="0.00" />,
					<cfqueryparam cfsqltype="cf_sql_varchar" value="PID0" />,
					<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.xmlShippingInfo#" />,
					<cfqueryparam cfsqltype="cf_sql_double" value="0.00" />,
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('memberID')#" />,
					<cfqueryparam cfsqltype="cf_sql_bit" value="1" />,
					<cfqueryparam cfsqltype="cf_sql_integer" value="1" />
				);
			
				select SCOPE_IDENTITY() as orderID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryInsert.orderID>
	</cffunction>

	<cffunction name="editOrderItems" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();

			local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
			if( NOT local.security.viewSettings)
				application.objCommon.redirect('#this.link.message#&message=1');
	
			local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeid'), orderID=arguments.event.getValue('orderID',0));
			local.strOrderDetails = this.objAdminStore.getOrderDetails(storeID=arguments.event.getValue('storeid'), orderID=local.qryOrder.orderID);
			local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo);

			local.couponData = { offerCoupon=false, hasQualifiedCoupons=false, hasAppliedCoupon=false, isValid=false, couponResponse='', totalItemDiscountExcTax=0, qryDiscountItems=QueryNew(""), qualifiedRateIDList="" };

			// no promo code applied
			if (local.strOrderDetails.qryOrderCouponDiscounts.couponID eq 0) {
				local.couponData.hasQualifiedCoupons = this.objAdminStore.hasValidCouponsForStore(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.qryOrder.memberID, rateIDList=valueList(local.strOrderDetails.qryOrderProductSaleDetails.rateID));

				if (local.couponData.hasQualifiedCoupons)
					local.couponData.offerCoupon = true;
			} else {
				local.qryStoreOrderCouponAppliedItems = this.objAdminStore.getStoreOrderCouponAppliedItems(orgID=arguments.event.getValue('mc_siteinfo.orgID'), orderID=local.qryOrder.orderID);
				local.couponData.qryDiscountItems = queryNew("cartItemID,quantity,itemAmount,itemDiscountExcTax","integer,integer,decimal,decimal");
			}
			
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.home#&tab=orders', text="Orders" });
			appendBreadCrumbs(arguments.event,{ link='', text='Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryOrder.orderID,"0000")#' });
		</cfscript>
		
		<!--- Get tax fields --->
		<cfset local.stateidForTax = local.shippingInfoXML.xmlRoot.fldship_state.xmlText>
		<cfset local.zipForTax = local.shippingInfoXML.xmlRoot.fldship_zip.xmlText>
		<cfif len(local.stateidForTax)>
			<cfset local.qryStates = application.objCommon.getStates()>
			<cfif not isNumeric(local.stateidForTax)>
				<cfquery name="local.getStateCode" dbtype="query">
				select stateid from [local].qryStates where stateCode = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_varchar">
				</cfquery>
				<cfset local.stateidForTax = local.getStateCode.stateid>
			</cfif>
		</cfif>

		<cfset local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
		
		<cfquery name="local.qryGetStatusInfo" datasource="#application.dsn.membercentral.dsn#">
			select statusName, canEditAddress, canEditItems
			from dbo.store_OrderStatus
			where orderStatusID = <cfqueryparam value="#val(local.qryOrder.orderStatusID)#" cfsqltype="cf_sql_integer">
		</cfquery>
		
		<!--- Get Shipping Info --->
		<cfset local.qryGetShippingInfo = this.objCart.getShippingInfo(orderID=local.qryOrder.orderID, rateIDList=valueList(local.strOrderDetails.qryOrderDetails.rateID))>
		
		<cfset arguments.event.setValue("shippingKey", replace(local.qryOrder.shippingKey, 'PID','')) />
		<cfset arguments.event.setValue("orderID", local.qryOrder.orderID) />
		<cfset arguments.event.setValue("orderNumber", local.qryOrder.orderNumber) />
		<cfset arguments.event.setValue('memberID',local.qryOrder.memberID) />
		<cfset arguments.event.setValue('stateid', local.stateidForTax) />
		<cfset arguments.event.setValue('zipForTax', local.zipForTax) />
		<cfset arguments.event.setValue("statusName", local.qryGetStatusInfo.statusName) />
		<cfset arguments.event.setValue('removeAffirmationList','') />
		
		<!--- Clear pre-existing Cart for this order number, if any --->
		<cfset this.objCart.deleteCartData(orderNumber=local.qryOrder.orderNumber)>
		
		<!--- Create shopping Cart --->
		<cfoutput query="local.strOrderDetails.qryOrderProductSaleDetails" group="orderDetailID">
			<cfset local.thisCartItemID = this.objCart.insertCartItem(storeID = arguments.event.getValue('storeid'),				
																		orderNumber = local.strOrderDetails.qryOrderProductSaleDetails.orderNumber, 
																		memberID = session.cfcUser.memberData.memberID, 
																		statSessionID = session.cfcUser.statsSessionID, 
																		itemID = local.strOrderDetails.qryOrderProductSaleDetails.itemID, 
																		FormatID = local.strOrderDetails.qryOrderProductSaleDetails.formatID, 
																		quantity = local.strOrderDetails.qryOrderProductSaleDetails.quantity, 
																		rateID = local.strOrderDetails.qryOrderProductSaleDetails.rateID)>

			<cfset local.thisItemAmount = 0>
			<cfoutput>
				<cfset this.objCart.insertCartItemSaleDetails(cartItemID = local.thisCartItemID,				
																orderNumber = local.strOrderDetails.qryOrderProductSaleDetails.orderNumber, 
																saleID = local.strOrderDetails.qryOrderProductSaleDetails.saleID,
																saleAmountChanged = local.strOrderDetails.qryOrderProductSaleDetails.saleAmountChanged)>

				<cfset local.thisItemAmount = local.thisItemAmount + local.strOrderDetails.qryOrderProductSaleDetails.cache_amountAfterAdjustment>
			</cfoutput>

			<cfif local.strOrderDetails.qryOrderCouponDiscounts.couponID gt 0>
				<cfquery name="local.qryThisItemDiscount" dbtype="query">
					select sum(amount) as itemDiscount
					from [local].qryStoreOrderCouponAppliedItems
					where itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strOrderDetails.qryOrderProductSaleDetails.orderDetailID#">
				</cfquery>

				<cfif val(local.qryThisItemDiscount.itemDiscount) gt 0>
					<cfif QueryAddRow(local.couponData.qryDiscountItems)>
						<cfset QuerySetCell(local.couponData.qryDiscountItems,"cartItemID",local.thisCartItemID)>
						<cfset QuerySetCell(local.couponData.qryDiscountItems,"quantity",local.strOrderDetails.qryOrderProductSaleDetails.quantity)>
						<cfset QuerySetCell(local.couponData.qryDiscountItems,"itemAmount",local.thisItemAmount)>
						<cfset QuerySetCell(local.couponData.qryDiscountItems,"itemDiscountExcTax",local.qryThisItemDiscount.itemDiscount)>
					</cfif>

					<cfset local.couponData.qualifiedRateIDList = listAppend(local.couponData.qualifiedRateIDList,local.strOrderDetails.qryOrderProductSaleDetails.rateID)>
				</cfif>
			</cfif>
		</cfoutput>
		
		<!--- Get Shopping Cart data--->
		<cfset local.qryCart = this.objCart.getCartData(storeid=arguments.event.getValue('storeid'), 
														orderNumber=local.qryOrder.orderNumber, 
														shippingid=arguments.event.getValue("shippingKey"))>
														
		<cfset local.qryItemsTotal = this.objCart.totalCartItems(arguments.event.getValue('storeid'),local.qryCart)>

		<cfset local.shippingOptionsTotal = 0.00>
		<cfif local.qryItemsTotal.totalRate>
			<cfset local.shippingOptionsTotal = local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingKey")].PerShipment + local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingKey")].totalItems>
		</cfif>

		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteInfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteInfo.defaultCurrencyType')#">
		</cfif>

		<!--- promo code applied --->
		<cfif local.strOrderDetails.qryOrderCouponDiscounts.couponID gt 0 and local.couponData.qryDiscountItems.recordCount>
			<cfset local.couponData.hasAppliedCoupon = true>
			<cfset local.couponData.isValid = true>
			<cfset local.couponData.couponResponse = local.strOrderDetails.qryOrderCouponDiscounts.redeemDetail>
			<cfset local.couponData.qualifiedRateIDList = listRemoveDuplicates(local.couponData.qualifiedRateIDList)>
			<cfset local.couponData.totalItemDiscountExcTax = local.strOrderDetails.qryOrderCouponDiscounts.totalDiscount>
		
			<cfset arguments.event.setValue("appliedCouponCode", local.strOrderDetails.qryOrderCouponDiscounts.couponCode)>
			<cfset arguments.event.setValue("couponID", local.strOrderDetails.qryOrderCouponDiscounts.couponID)>
			<cfset arguments.event.setValue("qualifiedRateIDList", local.couponData.qualifiedRateIDList)>

			<cfset this.objAdminStore.addDiscountToOrderQuery(qryOrder=local.qryCart, qryDiscountItems=local.couponData.qryDiscountItems)>

			<cfset local.orderSubTotal = val(local.strOrderDetails.qryOrderTotals.totalProducts) + local.strOrderDetails.qryOrderCouponDiscounts.totalDiscount>
			<cfset local.orderTotal = local.orderSubTotal + val(local.strOrderDetails.qryOrderTotals.totalShipping)>
		<cfelse>
			<cfset local.orderSubTotal = val(local.strOrderDetails.qryOrderTotals.totalProducts)>
			<cfset local.orderTotal = val(local.strOrderDetails.qryOrderTotals.totalProducts) + val(local.strOrderDetails.qryOrderTotals.totalShipping)>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_orderEdit.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateOrderItems" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();

			local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
			if( NOT local.security.viewSettings)
				application.objCommon.redirect('#this.link.message#&message=1');

			local.storeAction = arguments.event.getValue('sa');
			local.couponData = { offerCoupon=true, hasQualifiedCoupons=false, hasAppliedCoupon=false, isValid=false, couponResponse='', totalItemDiscountExcTax=0, qryDiscountItems=QueryNew("") };	
			
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='#this.link.home#&tab=orders', text="Orders" });
			appendBreadCrumbs(arguments.event,{ link='', text='Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),"0000")#' });
		</cfscript>
		
		<cfif not arguments.event.valueExists('closeInvoice')>
			<cfset arguments.event.setValue('closeInvoice',0) />
		</cfif>
		
		<cfswitch expression="#local.storeAction#">
			<cfcase value="update">
				<cfloop list="#form.fieldnames#" index="local.thisField">
					<cfif findNoCase("quantity_", local.thisField)>
						<cfset local.cartItemID = listGetAt(local.thisField,2,"_") />
						<cfset local.quantity = evaluate("form." & local.thisField) />
						<!--- Delete record if quentity entered is zero or null --->
						<cfif not val(local.quantity)>
							<cfset this.objCart.removeCartItem(local.cartItemID) />
							<cfset local.isAffirmation = evaluate("form.isAffirmation_" & local.cartItemID) />
							<cfset local.formatID = evaluate("form.format_" & arguments.event.getValue("cartItemID")) />
							<cfset local.orderID = arguments.event.getValue('orderID') />
							<cfset local.affirmationCount = this.objAdminStore.getAffirmationCount(orderID=local.orderID,formatID=local.formatID)>
							<cfif local.isAffirmation or local.affirmationCount>
								<cfset local.removeAffirmationList = arguments.event.getValue('removeAffirmationList') />
								<cfset arguments.event.setValue('removeAffirmationList',listAppend(local.removeAffirmationList,"#local.orderID#|#local.formatID#")) />
							</cfif>
						<cfelse>
							<cfset this.objCart.updateQuantity(cartItemID=local.cartItemID, quantity=val(local.quantity)) />
						</cfif>
					</cfif>
				</cfloop>
			</cfcase>
			<cfcase value="remove">
				<cfif arguments.event.valueExists("cartItemID")>
					<cfset this.objCart.removeCartItem(arguments.event.getValue("cartItemID")) />
					<cfset local.isAffirmation = evaluate("form.isAffirmation_" & arguments.event.getValue("cartItemID")) />
					<cfset local.formatID = evaluate("form.format_" & arguments.event.getValue("cartItemID")) />
					<cfset local.orderID = arguments.event.getValue('orderID') />
					<cfset local.affirmationCount = this.objAdminStore.getAffirmationCount(orderID=local.orderID,formatID=local.formatID)>
					<cfif local.isAffirmation or local.affirmationCount>
						<cfset local.removeAffirmationList = arguments.event.getValue('removeAffirmationList','') />
						<cfset arguments.event.setValue('removeAffirmationList',listAppend(local.removeAffirmationList,"#local.orderID#|#local.formatID#")) />
					</cfif>
				</cfif>
			</cfcase> 
			<cfcase value="updateRate">
				<cfif arguments.event.valueExists("cartItemID") and arguments.event.valueExists("newRateID")>
					<cfset this.objCart.updateRate(arguments.event.getValue("cartItemID"), arguments.event.getValue("newRateID")) />
				</cfif>
			</cfcase>
			<cfcase value="insertItem">
				<cfif arguments.event.valueExists("itemID") 
						and arguments.event.valueExists("formatID") 
						and arguments.event.valueExists("rateID") 
						and arguments.event.valueExists("orderNumber")>
					<cfset this.objCart.insertCartItem(	storeID = arguments.event.getValue('storeID'),				
														orderNumber = arguments.event.getValue("orderNumber"), 
														memberID = session.cfcUser.memberData.memberID, 
														statSessionID = session.cfcUser.statsSessionID, 
														itemID = arguments.event.getValue('itemID'), 
														formatID = arguments.event.getValue('formatID'), 
														quantity = 1, 
														rateID = arguments.event.getValue('rateID')) />
					
					<cfif arguments.event.valueExists("shippingID") and not arguments.event.getValue("shippingID")>
						<!--- if we don't have one, take the first one --->
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetShippingID">
							select shippingID, shippingName, Visible
							from dbo.store_ShippingMethods sm
							where sm.storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="cf_sql_integer" /> 
							and sm.Visible = 1
						</cfquery>
						<cfset arguments.event.setValue("shippingOption", val(local.qryGetShippingID.shippingID))>
					</cfif>
				</cfif>
			</cfcase>
			<cfcase value="insertItemFormat">
				<cfif arguments.event.valueExists("itemID") and arguments.event.valueExists("orderNumber")>
					<cfloop collection="#arguments.event.getCollection()#" item="local.key">
						<cfif left(local.key,10) eq "FQuantity_">
							<cfset local.formatID = GetToken(local.key,2,'_')>
							<cfset local.rateID = GetToken(local.key,3,'_')>
							<cfif arguments.event.getValue(local.key) gt 0>
							
								<cfset local.cartItem = this.objCart.getCartItem(storeID = arguments.event.getValue('storeID'),				
																				orderNumber = arguments.event.getValue("orderNumber"),
																				ProductItemID = arguments.event.getValue('itemID'), 
																				formatID = local.formatID)>
								
								<cfif local.cartItem.recordCount>
									<cfset local.newQuantity = arguments.event.getValue(local.key) + local.cartItem.quantity>
									<cfset this.objCart.updateQuantity(local.cartItem.cartItemID,local.newQuantity)>
								<cfelse>
									<cfset this.objCart.insertCartItem(	storeID = arguments.event.getValue('storeID'),				
																		orderNumber = arguments.event.getValue("orderNumber"), 
																		memberID = session.cfcUser.memberData.memberID, 
																		statSessionID = session.cfcUser.statsSessionID, 
																		itemID = arguments.event.getValue('itemID'), 
																		formatID = local.formatID, 
																		quantity = arguments.event.getValue(local.key), 
																		rateID = local.rateID) />
									
									<cfif arguments.event.valueExists("shippingID") and not arguments.event.getValue("shippingID")>
										<!--- if we don't have one, take the first one --->
										<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetShippingID">
											select shippingID, shippingName, Visible
											from dbo.store_ShippingMethods sm
											where sm.storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="cf_sql_integer" />
											and sm.Visible = 1
										</cfquery>
										<cfset arguments.event.setValue("shippingOption", val(local.qryGetShippingID.shippingID))>
									</cfif>
								</cfif>
							</cfif>
						</cfif>
					</cfloop>
				</cfif>
			</cfcase>
			<cfcase value="removeAppliedCoupon">
				<cfset arguments.event.setValue("appliedCouponCode", '')>
				<cfset arguments.event.setValue("couponID", 0)>
				<cfset arguments.event.setValue("qualifiedRateIDList", '')>
			</cfcase>
			<cfcase value="finalize">
				<cfset local.returnStruct = this.objCart.buynow_buy(arguments.event)>
				<cfif listLen(arguments.event.getValue('removeAffirmationList',''))>
					<cfset this.objAdminStore.removeAffirmation(removeAffirmationList=arguments.event.getValue('removeAffirmationList'))>
				</cfif>
				<cfif local.returnStruct.success>
					<cflocation url="#this.link.home#&tab=orders" addtoken="false">
				</cfif>
			</cfcase>	
		</cfswitch>

		<cfset local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'))>
		<cfset local.strOrderDetails = this.objAdminStore.getOrderDetails(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'))>
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(arguments.event.getValue('memberID'))>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
		
		<!--- Get Shipping Info --->
		<cfset local.qryGetShippingInfo = this.objCart.getShippingInfo(orderID = arguments.event.getValue('orderID'), rateIDList = valueList(local.strOrderDetails.qryOrderDetails.rateID)) />
		
		<cfset arguments.event.setValue("shippingKey", arguments.event.getValue("shippingOption")) />
		
		<!--- Get Shopping Cart data--->
		<cfset local.qryCart = this.objCart.getCartData(storeid = arguments.event.getValue('storeID'), 
														orderNumber = arguments.event.getValue('orderNumber'), 
														shippingid = arguments.event.getValue("shippingKey")) />
		
		<cfset local.rateIDList = listRemoveDuplicates(valueList(local.qryCart.rateID))>
		
		<!--- check for valid coupons availability --->
		<cfif len(local.rateIDList)>
			<cfset local.couponData.hasQualifiedCoupons = this.objAdminStore.hasValidCouponsForStore(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=arguments.event.getValue('memberID'), rateIDList=local.rateIDList)>
		</cfif>
		
		<!--- apply/re-apply promo code --->
		<cfif local.couponData.hasQualifiedCoupons and (local.storeAction eq "validateCouponCode" or len(arguments.event.getTrimValue("appliedCouponCode", '')) gt 0)>
			<cfset local.couponCode = "">
			<cfif local.storeAction eq "validateCouponCode">
				<cfset local.couponCode = arguments.event.getTrimValue("couponCode",'')>
			<cfelse>
				<cfset local.couponCode = arguments.event.getTrimValue("appliedCouponCode",'')>
			</cfif>

			<cfif len(local.couponCode) and len(local.rateIDList)>
				<cfset local.validateResult = this.objAdminStore.validateCouponCode(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
					couponCode=local.couponCode, memberID=arguments.event.getValue('memberID'), rateIDList=local.rateIDList, 
					storeID=arguments.event.getValue('storeID'), orderNumber=arguments.event.getValue('orderNumber'), 
					shippingID=arguments.event.getValue("shippingOption"))>
				
				<cfset local.couponData.hasAppliedCoupon = true>
				<cfset local.couponData.isValid = local.validateResult.isValidCoupon>
				<cfset local.couponData.couponResponse = local.validateResult.couponResponse>
				<cfset local.couponData.qualifiedRateIDList = local.validateResult.qualifiedRateIDList>
				<cfset local.couponData.qryDiscountItems = local.validateResult.qryDiscountItems>
				<cfset local.couponData.totalItemDiscountExcTax = local.validateResult.totalItemDiscountExcTax>
				
				<cfif local.couponData.isValid>
					<cfset arguments.event.setValue("appliedCouponCode", local.couponCode)>
					<cfset arguments.event.setValue("couponID", local.validateResult.couponID)>
					<cfset arguments.event.setValue("qualifiedRateIDList", local.couponData.qualifiedRateIDList)>
				</cfif>
			</cfif>
		</cfif>

		<cfif local.couponData.isValid>
			<cfset this.objAdminStore.addDiscountToOrderQuery(qryOrder=local.qryCart, qryDiscountItems=local.couponData.qryDiscountItems)>
		<cfelse>
			<cfset arguments.event.setValue("couponID", 0)>
			<cfset arguments.event.setValue("qualifiedRateIDList", '')>
		</cfif>

		<cfif local.strOrderDetails.qryOrderCouponDiscounts.couponID gt 0>
			<cfif arguments.event.getValue("couponID",0) is 0>
				<cfset local.couponData.couponResponse = "The coupon #local.strOrderDetails.qryOrderCouponDiscounts.couponCode# will be removed upon finalizing changes to this store order.">
			</cfif>

			<cfset local.couponData.offerCoupon = false>
		</cfif>
											
		<cfset local.qryItemsTotal = this.objCart.totalCartItems(arguments.event.getValue('storeID'),local.qryCart)>
		
		<cfset local.shippingOptionsTotal = 0.00>
		<cfif local.qryItemsTotal.totalRate>
			<cfset local.shippingOptionsTotal = local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingKey")].PerShipment + local.qryItemsTotal.shippingOptions["PID" & arguments.event.getValue("shippingKey")].totalItems>
		</cfif>
		
		<cfset local.orderSubTotal = val(local.qryItemsTotal.totalRate)>
		<cfset local.orderTotal = val(local.qryItemsTotal.totalRate) + val(local.shippingOptionsTotal)>

		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteInfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteInfo.defaultCurrencyType')#">
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_orderEdit.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addOrderItem" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.formatID = arguments.event.getValue('formatID',0);
			local.rateID = arguments.event.getValue('rateID',0);
			local.orderID = arguments.event.getValue('orderID',0);
			local.orderNumber = arguments.event.getValue('orderNumber',0);
			local.memberID = arguments.event.getValue('memberID',0);
			local.cartItemID = arguments.event.getValue('cartItemID',0);
			local.shippingid = arguments.event.getValue('shippingid',0);
			local.urlString = "";

			local.listProductsLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=storeJSON&meth=getProductList&storeSRID=#this.siteResourceID#&storeID=#arguments.event.getValue('storeID')#&listMode=addOrderItem&mode=stream';

			local.qryPurchaser = application.objMember.getMemberInfo(local.memberID);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_orderAddItem.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrderFormat" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.objStore = CreateObject("component","model.store.store");
			local.returnQryStruct = structNew();
			local.itemID = arguments.event.getValue('itemID',0);
			local.orderID = arguments.event.getValue('orderID',0);
			local.orderNumber = arguments.event.getValue('orderNumber',0);
			local.storeID = arguments.event.getValue('storeid',0);
			local.rateID = arguments.event.getValue('rateID',0);
			local.memberID = arguments.event.getValue('memberID',0);
			local.cartItemID = arguments.event.getValue('cartItemID',0);	
			local.shippingid = arguments.event.getValue('shippingid',0);
			local.qryPurchaser = application.objMember.getMemberInfo(local.memberID);

			local.qryProducts = local.objStore.getProducts(storeID=local.storeID, event=arguments.event);
			local.qryAvailableProductFormats = local.objStore.getAvailableProductFormats(storeID=local.storeID, itemID=local.itemID, memberID=local.memberID);

			local.title = local.cartItemID GT 0 
							? "Change Product Format for Item in Order #arguments.event.getValue('mc_siteinfo.sitecode') & Numberformat(local.orderID,"0000")#"
							: "Select Product Format for Item in Order #arguments.event.getValue('mc_siteinfo.sitecode') & Numberformat(local.orderID,"0000")#";

			local.stopReg = NOT local.qryAvailableProductFormats.recordCount;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfif local.stopReg>
				<cfoutput>
					<div class="alert alert-warning">
						<div class="font-weight-bold mt-2 mb-2">We're Sorry...</div>
						<div class="font-weight-bold">
							<i>#htmlEditFormat(local.qryProducts.contenttitle)#</i> is not available for you to purchase at this time.<br/>
							There are no available formats for you to select from.
						</div>
					</div>
				</cfoutput>
			<cfelse>
				<cfinclude template="frm_orderEditFormat.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrderRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.returnQryStruct = structNew();
			local.storeID = arguments.event.getValue('storeID',0);
			local.orderID = arguments.event.getValue('orderID',0);
			local.itemID = arguments.event.getValue('itemID',0);
			local.formatID = arguments.event.getValue('formatID',0);
			local.rateID = arguments.event.getValue('rateID',0);
			local.memberID = arguments.event.getValue('memberID',0);
			local.cartItemID = arguments.event.getValue('cartItemID',0);	
			local.shippingid = arguments.event.getValue('shippingid',0);
			local.qryPurchaser = application.objMember.getMemberInfo(local.memberID);
		</cfscript>
		
		<!--- Get Rate Options --->
		<cfset local.returnQryStruct = this.objCart.getRateOptions(storeID=local.storeID, formatID=local.formatID, memberID=local.memberID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_orderEditRate.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrderStatus" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.formAction	= this.link.saveOrderStatus;
			local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'));
			local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID);
			
			arguments.event.setValue('orderID',arguments.event.getValue('orderID'));
		</cfscript>

		<cfquery name="local.qryStatus" datasource="#application.dsn.membercentral.dsn#">
			select orderStatusID, statusName
			from dbo.store_OrderStatus
			order by orderStatusID
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_status.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="saveOrderStatus" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
	
			this.objAdminStore.updateOrderStatus(
				arguments.event.getValue('orderID',0),
				arguments.event.getValue('statusID',0),
				session.cfcuser.memberdata.memberID
			);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.manageOrder(#arguments.event.getValue('orderID',0)#);
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrderNotes" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.formAction	= this.link.saveOrderNotes;
			arguments.event.paramValue('orderID',0);
			local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'));
			local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_notes.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveOrderNotes" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
	
			this.objAdminStore.updateOrderNotes(
				arguments.event.getValue('orderID',0),
				arguments.event.getTrimValue('orderNotes','')
			);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.manageOrder(#arguments.event.getValue('orderID',0)#);
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editOrderShipping" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.formAction	= this.link.saveOrderShipping;
			arguments.event.paramValue('orderID',0);
			local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'));
			local.qryStates = application.objCommon.getStates();
			local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID);
		</cfscript>

		<cfset local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo)>
		<cfset local.ship_attn = trim(local.shippingInfoXML.xmlRoot.fldship_attn.xmlText)>
		<cfif StructKeyExists(local.shippingInfoXML.xmlRoot, "fldship_firm")>
			<cfset local.ship_firm = trim(local.shippingInfoXML.xmlRoot.fldship_firm.xmlText)>
		<cfelse>
			<cfset local.ship_firm = ''>
		</cfif>
		<cfset local.ship_address1 = trim(local.shippingInfoXML.xmlRoot.fldship_address1.xmlText)>
		<cfset local.ship_city = trim(local.shippingInfoXML.xmlRoot.fldship_city.xmlText)>
		<cfset local.ship_state = trim(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
		<cfset local.ship_zip = trim(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_ordershipping.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveOrderShipping" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
	
			this.objAdminStore.updateOrderShipping(
				arguments.event.getValue('orderID',0),
				arguments.event.getTrimValue('fldship_attn',''),
				arguments.event.getTrimValue('fldship_address1',''),
				arguments.event.getTrimValue('fldship_city',''),
				arguments.event.getTrimValue('fldship_state',''),
				arguments.event.getTrimValue('fldship_zip',''),
				arguments.event.getTrimValue('fldship_firm','')
			);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.manageOrder(#arguments.event.getValue('orderID',0)#);
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- RATE FUNCTIONS --->
	<cffunction name="addFormat" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryGetFormat = this.objAdminStore.getRateByItemID(arguments.event.getValue('itemID'));
		
			arguments.event.setValue('formatid',0);
			arguments.event.setValue('formatName','');
			arguments.event.setValue('status','');
			arguments.event.setValue('GLAccountID',0);
			arguments.event.setValue('GLAccountPath','');
			arguments.event.setValue('ShippingGLAccountID',0);
			arguments.event.setValue('ShippingGLAccountPath','');
			arguments.event.setValue('offerAffirmations',0);
			arguments.event.setValue('quantity',1);
			
			local.formlink = this.link.saveFormat & "&itemID=" & arguments.event.getValue('itemID');
			local.itemTypeName = "Format";
			if (arguments.event.getValue('isAffirmation'))
				local.itemTypeName = "Affirmation";	

			// Initialize GL Account Widgets for rate form
			local.strProductRevenueGLAcctWidgetData = {
				label="Product Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			local.strShippingRevenueGLAcctWidgetData = {
				label="Shipping Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="ShippingGLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);

		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_formats.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editFormat" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryGetFormat = this.objAdminStore.getRateByFormatID(arguments.event.getValue('formatID',0));
			
			arguments.event.setValue('formatid',val(local.qryGetFormat.formatid));
			arguments.event.setValue('formatName',local.qryGetFormat.formatName);
			arguments.event.setValue('status',local.qryGetFormat.status);	
			arguments.event.setValue('offerAffirmations',local.qryGetFormat.offerAffirmations);
			arguments.event.setValue('isAffirmation',local.qryGetFormat.isAffirmation);	
			arguments.event.setValue('quantity',local.qryGetFormat.quantity);

			if (val(local.qryGetFormat.formatGLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryGetFormat.formatGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.GLAccountPath = "";
			}
			arguments.event.setValue('GLAccountPath',local.GLAccountPath);

			arguments.event.setValue('ShippingGLAccountID',val(local.qryGetFormat.formatShippingGLAccountID));
			if (val(local.qryGetFormat.formatShippingGLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryGetFormat.formatShippingGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.ShippingGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.ShippingGLAccountPath = "";
			}
			arguments.event.setValue('ShippingGLAccountPath',local.ShippingGLAccountPath);

			// Initialize GL Account Widgets for format form
			local.strProductRevenueGLAcctWidgetData = {
				label="Product Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=val(local.qryGetFormat.formatGLAccountID),
				pathFldValue=local.GLAccountPath,
				pathNoneTxt="(No override; uses product's designated GL Account.)",
				clearBtnTxt="Clear Selected GL Account"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			local.strShippingRevenueGLAcctWidgetData = {
				label="Shipping Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="ShippingGLAccountID",
				idFldValue=val(local.qryGetFormat.formatShippingGLAccountID),
				pathFldValue=local.ShippingGLAccountPath,
				pathNoneTxt="(No override; uses product's designated GL Account.)",
				clearBtnTxt="Clear Selected GL Account"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);

			local.formlink = this.link.saveFormat & "&itemID=" & arguments.event.getValue('itemID');
			local.itemTypeName = "Format";
			if (arguments.event.getValue('isAffirmation'))
				local.itemTypeName = "Affirmation";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_formats.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveFormat" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
	
			if (arguments.event.getValue('formatid',0) eq 0) {
				local.createFormat = this.objAdminStore.insertProductFormat(event=arguments.event);
			} else {
				this.objAdminStore.updateFormat(event=arguments.event);
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadProductFormatsTable();
					<cfif arguments.event.getValue('formatid',0) gt 0 OR arguments.event.getValue('offerAffirmations',0) is 0>
						top.MCModalUtils.hideModal();
					<cfelse>
						top.MCModalUtils.hideModal();
						top.editFormatWindow(#val(local.createFormat)#)
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryGetRates = this.objAdminStore.getRateByFormatID(arguments.event.getValue('formatID'));
		
			arguments.event.setValue('rateid',0);
			arguments.event.setValue('rateName','');
			arguments.event.setValue('reportCode','');
			arguments.event.setValue('rateGLAccountID',0);
			arguments.event.setValue('GLAccountPath','');
			arguments.event.setValue('rateShippingGLAccountID',0);
			arguments.event.setValue('ShippingGLAccountPath','');
			arguments.event.setValue('rate','');
			
			arguments.event.setValue('startDate',now());
			arguments.event.setValue('endDate',now());
			
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			
			if (local.regTimeZone neq "US/Central") {
				arguments.event.setValue('startDate',local.objTZ.convertTimeZone(dateToConvert=arguments.event.getValue('startDate'), fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
				arguments.event.setValue('endDate',local.objTZ.convertTimeZone(dateToConvert=arguments.event.getValue('endDate'), fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
			}
			
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			
			local.formlink = this.link.saveRate & "&formatID=" & arguments.event.getValue('formatID');

			// Initialize GL Account Widgets for rate form
			local.strProductRevenueGLAcctWidgetData = {
				label="Product Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			local.strShippingRevenueGLAcctWidgetData = {
				label="Shipping Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="ShippingGLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);

			
			local.qryShipping = this.objAdminStore.getShippingRates(storeID=arguments.event.getValue('storeID'), rateID=arguments.event.getValue('rateid'));
			local.shippingMethods = this.objAdminStore.getShippingMethods(arguments.event);
		</cfscript>
		<cfset local.shipPerShipmentArr = arrayNew(1) />
		<cfset local.shipPerItemArr = arrayNew(1) />
		<cfset local.rateIDArr = arrayNew(1) />
		<cfif (not local.qryShipping.recordCount and local.shippingMethods.recordCount)>
			<cfset local.shippingMethodName = local.shippingMethods.shippingName />
			<cfset local.nColumnNumber4 = queryAddColumn(local.shippingMethods, "PerShipment", "Decimal", local.shipPerShipmentArr) />
			<cfset local.nColumnNumber5 = queryAddColumn(local.shippingMethods, "PerItem", "Decimal", local.shipPerItemArr) />
			<cfset local.temp = arraySet(local.rateIDArr, 1, local.shippingMethods.recordCount, "0") />
			<cfset local.nColumnNumber6 = queryAddColumn(local.shippingMethods, "rateID", "Integer", local.rateIDArr) />
			<cfset local.qryShipping = local.shippingMethods />
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryRate = this.objAdminStore.getRateByRateID(arguments.event.getValue('rateID',0));
	
			arguments.event.setValue('rateid',val(local.qryRate.rateid));
			arguments.event.setValue('rateName',local.qryRate.rateName);
			arguments.event.setValue('reportCode',local.qryRate.reportCode);
			arguments.event.setValue('rate',local.qryRate.rate);

			arguments.event.setValue('rateGLAccountID',val(local.qryRate.GLAccountID));
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryRate.GLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

			arguments.event.setValue('rateShippingGLAccountID',val(local.qryRate.ShippingGLAccountID));
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryRate.ShippingGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			arguments.event.setValue('ShippingGLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

			arguments.event.setValue('startDate',local.qryRate.startDate);
			arguments.event.setValue('endDate',local.qryRate.endDate);
				// convert times from central (how stored in db) to default timezone of site
				local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
				local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
				local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
				if (local.regTimeZone neq "US/Central") {
					arguments.event.setValue('startDate',local.objTZ.convertTimeZone(dateToConvert=local.qryRate.startDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
					if (isDate(local.qryRate.endDate))
						arguments.event.setValue('endDate',local.objTZ.convertTimeZone(dateToConvert=local.qryRate.endDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
				}

			local.formlink = this.link.saveRate;

			// Initialize GL Account Widgets for rate form
			if (val(local.qryRate.GLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryRate.GLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.GLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.GLAccountPath = "";
			}
			local.strProductRevenueGLAcctWidgetData = {
				label="Product Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=val(local.qryRate.GLAccountID),
				pathFldValue=local.GLAccountPath,
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			if (val(local.qryRate.ShippingGLAccountID) gt 0) {
				local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.qryRate.ShippingGLAccountID, orgID=arguments.event.getValue('mc_siteInfo.orgID'));
				local.ShippingGLAccountPath = local.tmpStrAccount.qryAccount.thePathExpanded;
			} else {
				local.ShippingGLAccountPath = "";
			}
			local.strShippingRevenueGLAcctWidgetData = {
				label="Shipping Revenue Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="ShippingGLAccountID",
				idFldValue=val(local.qryRate.ShippingGLAccountID),
				pathFldValue=local.ShippingGLAccountPath,
				pathNoneTxt="(No override; uses format's designated GL Account.)",
				clearBtnTxt="Remove Override"
			};
			local.strShippingRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strShippingRevenueGLAcctWidgetData);


			local.qryShipping = this.objAdminStore.getShippingRates(storeID=arguments.event.getValue('storeID'), rateID=arguments.event.getValue('rateid'));
			local.shippingMethods = this.objAdminStore.getShippingMethods(arguments.event);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.returnStruct = structNew();
			local.rateid = 0;
	
			local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('startDate'),' - ',' ')#");
			
			local.endDateTime = "";
			if (len(arguments.event.getValue('endDate')))
				local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('endDate'),' - ',' ')#");	
			
			// convert times from default timezone to central to store in db as central
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			if (local.regTimeZone neq "US/Central") {
				local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
				if(len(local.endDateTime))
					local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
			}

			if (arguments.event.getValue('rateid',0) is 0) {
				local.returnStruct = this.objAdminStore.insertRate(
					arguments.event.getValue('formatID',0),
					arguments.event.getTrimValue('GLAccountID',0),
					arguments.event.getTrimValue('ShippingGLAccountID',0),
					arguments.event.getTrimValue('rateName',''),
					arguments.event.getTrimValue('reportCode',''),
					replace(arguments.event.getValue('rate',0),'$','','ALL'),
					local.startDateTime,
					local.endDateTime
				);
				local.rateid = local.returnStruct.rateID;
			} else {
				this.objAdminStore.updateRate(
					arguments.event.getValue('rateID',0),
					arguments.event.getTrimValue('GLAccountID',0),
					arguments.event.getTrimValue('ShippingGLAccountID',0),
					arguments.event.getTrimValue('rateName',''),
					arguments.event.getTrimValue('reportCode',''),
					replace(arguments.event.getValue('rate',0),'$','','ALL'),
					local.startDateTime,
					local.endDateTime
				);
				local.rateid = arguments.event.getValue('rateID',0);
			}

			local.shippingMethods = this.objAdminStore.getShippingMethods(arguments.event);
		</cfscript>
		
		<cfif local.rateid>
			<cfquery name="local.updateShippinRate" datasource="#application.dsn.membercentral.dsn#" result="local.updateShippinRateResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					declare @currProductShippingID int
					
					BEGIN TRAN;
						<cfloop query="local.shippingMethods">
							<cfset local.tmpShipPerShipment = "0.00">
							<cfset local.tmpshipPerItem = "0.00">
							set @currProductShippingID = 0;
							
							<cfif (isDefined("form.shipPerShipment_#local.shippingMethods.shippingID#_#local.rateid#")
									and isDefined("form.shipPerItem_#local.shippingMethods.shippingID#_#local.rateid#"))
									OR
								(isDefined("form.shipPerShipment_#local.shippingMethods.shippingID#_0") 	
									and isDefined("form.shipPerItem_#local.shippingMethods.shippingID#_0"))
									OR
								(isDefined("form.shipPerShipment_0_0") 	
									and isDefined("form.shipPerItem_0_0"))>
										
								<cfif isDefined("form.shipPerShipment_#local.shippingMethods.shippingID#_#local.rateid#")
										and isDefined("form.shipPerItem_#local.shippingMethods.shippingID#_#local.rateid#")>
									<cfset local.tmpShipPerShipment = evaluate("form.shipPerShipment_#local.shippingMethods.shippingID#_#local.rateID#") />
									<cfset local.tmpshipPerItem = evaluate("form.shipPerItem_#local.shippingMethods.shippingID#_#local.rateid#") />
								</cfif>
								
								<cfif isDefined("form.shipPerShipment_#local.shippingMethods.shippingID#_0") and isDefined("form.shipPerItem_#local.shippingMethods.shippingID#_0")>
									<cfset local.tmpShipPerShipment = evaluate("form.shipPerShipment_#local.shippingMethods.shippingID#_0") />
									<cfset local.tmpshipPerItem = evaluate("form.shipPerItem_#local.shippingMethods.shippingID#_0") />
								</cfif>
									
								<cfif isDefined("form.shipPerShipment_0_0") and isDefined("form.shipPerItem_0_0")>
									<cfset local.tmpShipPerShipment = evaluate("form.shipPerShipment_0_0") />
									<cfset local.tmpshipPerItem = evaluate("form.shipPerItem_0_0") />
								</cfif>
								
								<cfset local.tmpShipPerShipment = replace(local.tmpShipPerShipment,"$","","all") />
								<cfset local.tmpshipPerItem = replace(local.tmpshipPerItem,"$","","all") />

								select @currProductShippingID = isNULL(ProductShippingID,0)
								from dbo.store_ProductShipping
								where shippingID = <cfqueryparam value="#val(local.shippingMethods.shippingID)#" cfsqltype="cf_sql_integer" />
								and rateID = <cfqueryparam value="#local.rateID#" cfsqltype="cf_sql_integer" />;
									
								IF @currProductShippingID > 0
									update dbo.store_ProductShipping
									set PerShipment = <cfqueryparam value="#val(local.tmpShipPerShipment)#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
										PerItem = <cfqueryparam value="#val(local.tmpshipPerItem)#" cfsqltype="CF_SQL_DECIMAL" scale="2" />
									where ProductShippingID = @currProductShippingID;
								ELSE 
									insert into store_ProductShipping(ShippingID, RateID, PerShipment, PerItem, PriceID)
									values (
										<cfqueryparam value="#val(local.shippingMethods.shippingID)#" cfsqltype="cf_sql_integer" />, 
										<cfqueryparam value="#local.rateID#" cfsqltype="cf_sql_integer" />,
										<cfqueryparam value="#val(local.tmpShipPerShipment)#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
										<cfqueryparam value="#val(local.tmpshipPerItem)#" cfsqltype="CF_SQL_DECIMAL" scale="2" />,
										<cfqueryparam value="0" cfsqltype="cf_sql_integer" />
									);
							</cfif>
						</cfloop>
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif><!--- //fif local.rateid --->
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadProductFormatsTable();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addRateOverride" access="public" output="false" returntype="struct" hint="add rate override">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew() />
		<!--- Load Objects --->
		<cfset local.objStore = CreateObject("component","model.admin.store.store") />
		
		<!--- Get Data --->
		<cfset local.formAction	= this.link.saveRateOverride>
		
		<cfscript>
			arguments.event.setValue('rateOverrideID',0);
			arguments.event.setValue('rateOverrideName','');
			arguments.event.setValue('rateOverride',0);
			arguments.event.setValue('startDate','#dateFormat(now(), "m/d/yyyy")#');
			arguments.event.setValue('endDate','#dateFormat(now() + 1, "m/d/yyyy")#');
			arguments.event.setValue('rateID',arguments.event.getValue('rateID'));
			arguments.event.setValue('itemID',arguments.event.getValue('itemID'));
			
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			if (local.regTimeZone neq "US/Central") {
				arguments.event.setValue('startDate',local.objTZ.convertTimeZone(dateToConvert=arguments.event.getValue('startDate'), fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
				arguments.event.setValue('endDate',local.objTZ.convertTimeZone(dateToConvert=arguments.event.getValue('endDate'), fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
			}
	
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));	
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rateOverride.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="editRateOverride" access="public" output="false" returntype="struct" hint="edit rate override">
		<cfargument name="Event" type="any" />
		
		<cfscript>
			var local = structNew();
			local.qryRateOverride = this.objAdminStore.getRateOverrideByID(arguments.event.getValue('rateOverrideID',0));
			local.formAction = this.link.saveRateOverride;
			
			arguments.event.setValue('rateOverrideName',local.qryRateOverride.rateOverrideName);
			arguments.event.setValue('rateOverride',local.qryRateOverride.rateOverride);
			arguments.event.setValue('rateID',local.qryRateOverride.rateID);
			arguments.event.setValue('itemID',arguments.event.getValue('itemID'));
			
			// convert times from central (how stored in db) to default timezone of site
			arguments.event.setValue('startDate',local.qryRateOverride.startDate);
			arguments.event.setValue('endDate',local.qryRateOverride.endDate);
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			if (local.regTimeZone neq "US/Central") {
				arguments.event.setValue('startDate',local.objTZ.convertTimeZone(dateToConvert=local.qryRateOverride.startDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
				arguments.event.setValue('endDate',local.objTZ.convertTimeZone(dateToConvert=local.qryRateOverride.endDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone));
			}			
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rateOverride.cfm" />
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="saveRateOverride" access="public" output="false" returntype="struct" hint="saves a rate override">
		<cfargument name="event" type="any" />
		
		<cfscript>
			var local = structNew();
	
			local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('startDate'),' - ',' ')#");
			local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('endDate'),' - ',' ')#");

			// convert times from default timezone to central to store in db as central
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			if (local.regTimeZone neq "US/Central") {
				local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime, fromTimeZone=local.regTimeZone, toTimeZone='US/Central');
				local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime, fromTimeZone=local.regTimeZone, toTimeZone='US/Central');
			}

			if (arguments.event.getValue('rateOverrideID',0) is 0) {
				this.objAdminStore.insertRateOverride(
					arguments.event.getValue('rateID',0),
					arguments.event.getTrimValue('rateOverrideName',''),
					replace(arguments.event.getValue('rateOverride',0),'$','','ALL'),
					local.startDateTime,
					local.endDateTime
				);
			} else {
				this.objAdminStore.updateRateOverride(
					arguments.event.getValue('rateOverrideID',0),
					arguments.event.getValue('rateID',0),
					arguments.event.getTrimValue('rateOverrideName',''),
					replace(arguments.event.getValue('rateOverride',0),'$','','ALL'),
					local.startDateTime,
					local.endDateTime
				);
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.refreshRateTab();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="sendOrderDetail" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew() />
			
		<cfscript>
		local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
		if( NOT local.security.viewSettings)
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>

		<cfset local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID',0))>

		<cfswitch expression="#arguments.event.getValue('ordaction','')#">
			<cfcase value="emailOrd">
				<cfif local.qryOrder.recordcount and isValid("regex",arguments.event.getValue('_email',''),application.regEx.email)>
					<cfset doEmailOrder(event=arguments.event)>
				</cfif>
				<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.$("##MCModalFooter").toggleClass('d-flex d-none');
				window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
			</script>
			<div class="p-3">
				<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
					<span class="font-size-lg d-block d-40 mr-2 text-center">
						<i class="fa-solid fa-circle-check"></i>
					</span>
					<span>E-mail sent successfully.</span>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfif local.qryOrder.recordcount is 0>
					<cfreturn returnAppStruct("That order was not found.","echo")>
				</cfif>

				<cfsavecontent variable="local.data">
					<cfoutput>
						<cfinclude template="frm_emailOrder.cfm">
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="doEmailOrder" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objStoreReg = CreateObject("component","model.store.storeReg")>
		
		<cfscript>
		local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
		if( NOT local.security.viewSettings)
			application.objCommon.redirect('#this.link.message#&message=1');

		local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'));
		</cfscript>

		<cfset local.emailContent = local.objStoreReg.orderReceiptForEmail(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), 
										storeID=arguments.event.getValue('storeID'), orderID=arguments.event.getValue('orderID'),
										onlineReceipt=0, notes=trim(arguments.event.getValue('_notes')))>
		<cfset local.emailTitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Order Confirmation and Receipt">

		<cfif local.qryOrder.recordCount>
			<cftry>
				<cfif find(";",this.storeInfo.emailRecipient)>
					<cfset local.sendFrom = trim(listFirst(this.storeInfo.emailRecipient,";"))>
				<cfelse>
					<cfset local.sendFrom = trim(this.storeInfo.emailRecipient)>
				</cfif>
				<cfif NOT len(local.sendFrom) or NOT isValid("regex",local.sendFrom,application.regEx.email)>
					<cfthrow>
				</cfif>
			<cfcatch type="any">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfcatch>
			</cftry>

			<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
										emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
										emailto=[{ name="#local.qryOrder.firstName# #local.qryOrder.lastName#", email=arguments.event.getValue('_email') }],
										emailreplyto=local.sendFrom,
										emailsubject="#arguments.event.getValue('mc_siteinfo.siteName')# Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),"0000")# Confirmation and Receipt",
										emailtitle=local.emailTitle,
										emailhtmlcontent=local.emailContent,
										emailAttachments=[],
										siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										memberID=local.qryOrder.memberID,
										messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="STOREORDER"),
										sendingSiteResourceID=this.siteResourceID
									)>

			<!--- Update order notes. Append email notes to Order notes --->
			<cfif len(trim(arguments.event.getValue('_notes')))>
				<cfset local.orderNotes = trim(arguments.event.getValue('_notes'))>
				<cfif len(trim(local.qryOrder.orderNotes))>
					<cfset local.orderNotes = local.qryOrder.orderNotes & chr(13) & chr(10) & local.orderNotes>
				</cfif>
				
				<cfquery name="local.qryUpdateNotes" datasource="#application.dsn.membercentral.dsn#">
					update store_orders
					set orderNotes = <cfqueryparam value="#local.orderNotes#" cfsqltype="cf_sql_varchar">
					where orderid = <cfqueryparam value="#local.qryOrder.orderid#" cfsqltype="cf_sql_integer">
				</cfquery>
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="generatePackingSlip" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew() />
		<cfset local.returnStruct = structNew() />
			
		<cfscript>
		local.security.viewSettings = checkRights(arguments.event,'ViewOrders');
		if( NOT local.security.viewSettings)
			application.objCommon.redirect('#this.link.message#&message=1');	
		</cfscript>
		
		<cfset local.returnStruct = doGeneratePackingSlip(event=arguments.event)>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath=local.returnStruct.packingSlipPath, displayName=ListLast(local.returnStruct.packingSlipPath,"/"), deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
				top.manageOrder(#arguments.event.getValue('orderid')#);	
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="doGeneratePackingSlip" access="public" returntype="struct" output="no">
		<cfargument name="Event" type="any" required="true" />

		<cfset var local = structNew() />
		<cfset local.returnStruct = { packingSlipPath='' } />
				
		<cfscript>
		local.qryOrder = this.objAdminStore.getOrder(storeID=arguments.event.getValue('storeid'), orderID=arguments.event.getValue('orderID'));	
		local.strOrderDetails = this.objAdminStore.getOrderDetails(storeID=arguments.event.getValue('storeid'), orderID=local.qryOrder.orderID);
		local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo);
		</cfscript>
		
		<cfif not local.qryOrder.recordCount>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
			<cfset local.qryStates = application.objCommon.getStates()>
			<cfquery name="local.getStateCode" dbtype="query">
				<cfif isNumeric(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
					select stateCode from [local].qryStates where stateid = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_integer">
				<cfelse>
					select stateCode from [local].qryStates where stateCode = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_varchar">
				</cfif>
			</cfquery>
		</cfif>
		
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(local.qryOrder.memberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
		
		<cfquery name="local.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			select ga.gatewayID, mpContent.rawContent as paymentInstructions
			from dbo.mp_profiles as pr
			inner join dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			outer apply dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			where ga.gatewayID = <cfqueryparam value="#val(local.qryOrder.gatewayid)#" cfsqltype="cf_sql_integer">
			and pr.profileID = <cfqueryparam value="#val(local.qryOrder.merchantProfileID)#" cfsqltype="cf_sql_integer">
			and pr.[status] = 'A'
		</cfquery>
		
		<cfquery name="local.qryGetStatusName" datasource="#application.dsn.membercentral.dsn#">
			select statusName 
			from dbo.store_OrderStatus 
			where orderStatusID = <cfqueryparam value="#val(local.qryOrder.orderStatusID)#" cfsqltype="cf_sql_integer">
		</cfquery>
		
		<cfset local.margintop = "3.5">
		<cfset local.orderTotal = val(local.strOrderDetails.qryOrderTotals.totalProducts) + val(local.strOrderDetails.qryOrderTotals.totalShipping) + val(local.strOrderDetails.qryOrderTotals.totalTax)>
		<cfset local.orderTotalIncProcessingFees = NumberFormat((local.orderTotal + val(local.strOrderDetails.qryOrderTotals.totalProcessingFees)),'0.00')>
		
		<cfsavecontent variable="local.ordHeader">
			<cfoutput>
			<html>
			<head>
				<style type="text/css">
				.c { text-align:center; }
				.l { text-align:left; }
				.r { text-align:right; }
				.bt { border-top:1px solid ##000; }
				.bb { border-bottom:1px solid ##000; }
				.bl { border-left:1px solid ##000; }
				.br { border-right:1px solid ##000; }
				.ord1 { font-family:verdana;font-size:12pt;line-height:16pt;font-weight:bold;letter-spacing:2pt; }
				.ord2 { font-family:verdana;font-size:8.5pt;line-height:12pt;padding:2px 0; }
				.ord3 { font-family:verdana;font-size:8.5pt;padding-top:8px; }
				.address { font-size:9pt;font-family:verdana;font-weight:bold;line-height:12pt;margin-left:20px; }
				.status { font-size:9pt;font-family:verdana;font-weight:normal;line-height:12pt;margin-left:20px; }
				.logo { height:180px;overflow:hidden; }
				##infotbl { margin-top:6px; }
				</style>
			</head>
			<body>
			<div id="header">
				<table cellspacing="0" cellpadding="0" width="100%">
				<tr>
					<td width="60%" valign="top">
						&nbsp;
					</td>
					<td width="40%" valign="top">
						<div id="infotbl">
							<table cellspacing="0" cellpadding="2" width="100%">
							<tr>
								<td colspan="2" class="c bt bl br ord1">PACKING SLIP</td>
							</tr>
							<tr>
								<td width="50%" class="c bt bl br bb ord2"><b><i>Order Number</i></b><br/>#arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryOrder.orderID,"0000")#</td>
								<td width="50%" class="c bt br bb ord2"><b><i>Order Date</i></b><br/>#dateformat(local.qryOrder.DateOfOrder,"m/d/yyyy")#</td>
							</tr>
							<tr>
								<td colspan="2" class="c ord3">&nbsp;<!-- cpp -->&nbsp;</td>
							</tr>
							</table>
						</div>
					</td>
				</tr>
				</table>
				<div class="address">
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_attn.xmlText)>ATTN: #local.shippingInfoXML.xmlRoot.fldship_attn.xmlText#<br/></cfif>
					#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#<br/>
					<cfif StructKeyExists(local.shippingInfoXML.xmlRoot, "fldship_firm") AND len(local.shippingInfoXML.xmlRoot.fldship_firm.xmlText)>
						#local.shippingInfoXML.xmlRoot.fldship_firm.xmlText#<br/>
					<cfelseif len(local.qryPurchaser.company)>
						#local.qryPurchaser.company#<br />
					</cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_address1.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_address1.xmlText#<br/></cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_city.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_city.xmlText#,</cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>#local.getStateCode.stateCode#</cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_zip.xmlText#</cfif>
					<br /><br/>
				</div>
			</div>
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>
		
		<!--- local.ordBody styles --->
		<cfset local.pageStyle = "width:607px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##000000;" />
		<cfset local.sectionTitleStyle = "line-height:1.5em; font-family:Verdana, Arial, Helvetica, sans-serif; color:##0E568D; font-weight:bold; font-size:13px;" />
		<cfset local.dataHeaderStyle = "font-weight:bold;margin-left:10px;" />
		<cfset local.dataStyle = "margin-left:30px; padding-bottom:10px;" />
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##000000;" />
		<cfset local.mc_border_bottom = "border-bottom:1px solid ##000000;" />
		<cfset local.mc_border_top = "border-top:1px solid ##000000;" />
		
		<cfsavecontent variable="local.ordBody">
			<cfoutput>
			<html>
			<head>
			</head>
			<body>
			<table cellpadding="4" cellspacing="0" width="100%" border="0">
			<tr> 
				<td width="*" style="#local.tdStyle# #local.mc_border_bottom#"><b>Item</b></td>
				<td width="50" style="#local.tdStyle# #local.mc_border_bottom#" align="right"><b>Quantity</b></td>
				<td width="80" style="#local.tdStyle# #local.mc_border_bottom#" align="right"><b>Price</b></td>
			</tr>
			</cfoutput>
			
			<cfset local.showDocumentArea = false>
			<cfoutput query="local.strOrderDetails.qryOrderDetails" group="formatID">
				<cfset local.qryDocuments = this.objAdminStore.getStoreDocuments(formatID=local.strOrderDetails.qryOrderDetails.formatID)>
				<cfset local.itemQuantity = 0>
				<cfset local.itemTotal = 0 >
				<cfoutput>
					<cfset local.itemQuantity =local.itemQuantity + local.strOrderDetails.qryOrderDetails.Quantity>
					<cfset local.itemTotal = local.itemTotal + local.strOrderDetails.qryOrderDetails.itemTotal>
				</cfoutput>
				<tr valign="top">
					<td style="#local.tdStyle#">
						<div>#local.strOrderDetails.qryOrderDetails.contenttitle#</div>
						<div style="margin-top:4px;">#local.strOrderDetails.qryOrderDetails.formatname#</div>
						<cfif local.qryDocuments.recordcount>
							<cfset local.showDocumentArea = true>
							<cfloop query="local.qryDocuments">
								<cfset local.expireMessage = "">
								<cfset local.accessDoc = true>
								<cfif local.qryDocuments.accessExpireInDays gt 0>
									<cfset local.expireDate = DateAdd("d",local.qryDocuments.accessExpireInDays,local.qryOrder.dateOfOrder)>
									<cfif dateCompare(local.expireDate, now(), "d") gte 0>
										<cfset local.expireMessage = "(access expires on #dateFormat(local.expireDate,"m/d/yyyy")#)">
										<cfset local.accessDoc = true>
									<cfelse>
										<cfset local.expireMessage = "(access expired on #dateFormat(local.expireDate,"m/d/yyyy")#)">
										<cfset local.accessDoc = false>
									</cfif>
								</cfif>
								<div style="margin:4px 0 0 15px;">
									<i>#local.qryDocuments.docTitle#</i> #local.expireMessage#
								</div>
							</cfloop>
						</cfif>
						<cfif local.strOrderDetails.qryOrderStreamsDetails.recordcount>
							<cfset local.showDocumentArea = true>
							<cfloop query="local.strOrderDetails.qryOrderStreamsDetails">
								<cfset local.expireMessage = "">
								<cfif len(local.strOrderDetails.qryOrderStreamsDetails.dateExpires)>
									<cfif dateCompare(local.strOrderDetails.qryOrderStreamsDetails.dateExpires,now(),"d") gte 0>
										<cfset local.expireMessage = "(access expires on #dateFormat(local.strOrderDetails.qryOrderStreamsDetails.dateExpires,"m/d/yyyy")#)">
									<cfelse>
										<cfset local.expireMessage = "(access expired on #dateFormat(local.strOrderDetails.qryOrderStreamsDetails.dateExpires,"m/d/yyyy")#)">
									</cfif>
								</cfif>
								<div style="margin:4px 0 0 15px;">
									<i class="fa-light fa-download fa-lg"></i> #local.strOrderDetails.qryOrderStreamsDetails.streamName# #local.expireMessage#
								</div>
							</cfloop>
						</cfif>
						<cfif local.strOrderDetails.qryOrderDetails.numAffirmationsIncluded gt 0>
							<div style="margin:4px 0 0 15px;">
								<i>Affirmation Forms</i>
							</div>
						</cfif>
					</td>
					<td style="#local.tdStyle#" align="right">#local.itemQuantity#</td>
					<td style="#local.tdStyle#" align="right" nowrap><cfif val(local.itemTotal) gte 0>#dollarFormat(local.itemTotal)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif><cfelse>N/A</cfif></td>
				</tr>
			</cfoutput>
			<cfoutput>
			<tr>
				<td colspan="3">&nbsp;</td>
			</tr>
			<cfif local.strOrderDetails.qryOrderTotals.totalProducts is not local.orderTotalIncProcessingFees>
				<tr> 
					<td></td>
					<td align="right" style="#local.tdStyle# #local.mc_border_top#" nowrap><b>Sub Total:</b></td>
					<td align="right" style="#local.tdStyle# #local.mc_border_top#" nowrap><b><cfif val(local.strOrderDetails.qryOrderTotals.totalProducts) gte 0>#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProducts)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif><cfelse>N/A</cfif></b></td>
				</tr>
			</cfif>
			<cfif local.strOrderDetails.qryOrderTotals.totalShipping gt 0>
				<tr> 
					<td></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>Shipping & Handling:</b></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>#dollarFormat(local.strOrderDetails.qryOrderTotals.totalShipping)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></b></td>
				</tr>
			</cfif>
			<cfif local.strOrderDetails.qryOrderTotals.totalTax gt 0>
				<tr> 
					<td></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>Tax:</b></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>#dollarFormat(local.strOrderDetails.qryOrderTotals.totalTax)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></b></td>
				</tr>
			</cfif>
			<cfif val(local.strOrderDetails.qryOrderTotals.totalProcessingFees) gt 0>
				<tr> 
					<td></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>#local.strOrderDetails.qryOrderTotals.processingFeeLabel#:</b></td>
					<td style="#local.tdStyle#" align="right" nowrap><b>#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProcessingFees)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></b></td>
				</tr>
			</cfif>
			<tr> 
				<td></td>
				<td style="#local.tdStyle# #local.mc_border_top#" align="right"><b>Total:</b></td>
				<td style="#local.tdStyle# #local.mc_border_top#" align="right" nowrap><b><cfif val(local.orderTotalIncProcessingFees) gte 0>#dollarFormat(local.orderTotalIncProcessingFees)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif><cfelse>N/A</cfif></b></td>
			</tr>
			</table>
			<br/>
			</cfoutput>
			<cfoutput>
			<cfif local.strOrderDetails.qryOrderDetails.recordcount and local.showDocumentArea or local.strOrderDetails.qryOrderTotals.totalAffirmationsIncluded gt 0>
				<div style="#local.tdStyle# border:1px solid ##B2B38F;">
					<div style="margin:10px;">
						<div style="font-size:12px;"><b>How to view Electronically delivered items</b></div>
						<div style="margin-left:20px;">
							<br/>
							Access this order by logging in and clicking the "My purchases" link in the store application.
						</div>
					</div>
				</div>
				<br/>
			</cfif>
		
			<table cellpadding="4" cellspacing="0" width="100%" border="0">
			<tr> 
				<td style="#local.tdStyle# #local.mc_border_bottom#"><b>Billing</b></td>
				<td style="#local.tdStyle# #local.mc_border_bottom#"><b>Purchaser / Shipping</b></td>
			</tr>
			<tr valign="top"> 
				<td style="#local.tdStyle#">
					<cfif local.strOrderDetails.qryOrderPayments.recordcount>
						<cfloop query="local.strOrderDetails.qryOrderPayments">
							<!--- sensitive info hidden unless pending transaction --->
							<cfset local.maskSensitive = true>
							<cfif local.strOrderDetails.qryOrderPayments.statusID is 3>
								<cfset local.maskSensitive = false>
							</cfif>
							<b>Payment #local.strOrderDetails.qryOrderPayments.currentrow# <cfif local.strOrderDetails.qryOrderPayments.statusID is 3><span class="red">(Pending Payment)</span></cfif></b> - #dollarformat(local.strOrderDetails.qryOrderPayments.allocSum + val(local.strOrderDetails.qryOrderPayments.processingFees))#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif> applied to order<br/>
							#local.strOrderDetails.qryOrderPayments.detail#<br/>
							Payment date: #DateFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"m/d/yyyy")# #TimeFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"h:mm tt")#<br/>
							<br/>
						</cfloop>
					<cfelseif local.orderTotal gt 0 and local.qryPaymentGateway.gatewayID is 11>
						No payment was made.<br/><br/>
						<b>Payment instructions:</b><br/>
						<cfif len(local.qryPaymentGateway.paymentInstructions)>
							<cfoutput>#local.qryPaymentGateway.paymentInstructions#</cfoutput>
						<cfelse>
							No instructions have been provided. Contact the association for payment instructions.
						</cfif>
						<br/><br/>
					<cfelseif local.orderTotal gt 0>
						<b>No payment was made.</b>
						<br/><br/>
					<cfelse>
						<b>No payment was due.</b>
						<br/><br/>
					</cfif>
				</td>
				<td style="#local.tdStyle#">
					<cfif len(local.qryPurchaser.company)>#local.qryPurchaser.company#<br/></cfif>
					<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
					<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
					<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
					#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#<br /><br />
					<br/>
					<b>Ship To:</b><br/>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_attn.xmlText)>ATTN: #local.shippingInfoXML.xmlRoot.fldship_attn.xmlText#<br/></cfif>
					<cfif StructKeyExists(local.shippingInfoXML.xmlRoot, "fldship_firm") AND len(local.shippingInfoXML.xmlRoot.fldship_firm.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_firm.xmlText#<br/></cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_address1.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_address1.xmlText#<br/></cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_city.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_city.xmlText#,</cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>#local.getStateCode.stateCode# </cfif>
					<cfif len(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_zip.xmlText#</cfif>
				</td>
			</tr>
			</table>
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>
			
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>

		<!--- create a PDF --->
		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.ordHeader } >
		<cftry>
			<cfdocument filename="#local.strFolder.folderPath#/un_#local.qryOrder.orderid#_order.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom="0.5" marginright="0.25" marginleft="0.25" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
					<cfdocumentsection margintop="0.25" marginbottom="0.5" marginright="0.25" marginleft="0.25">
						<cfdocumentitem attributeCollection="#local.headercol#">
							<cfif int(val(cfdocument.totalsectionpagecount)) gt 1>
								<cfoutput>#replace(local.ordHeader,'<!-- cpp -->','Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#</cfoutput>
							<cfelse>
								<cfoutput>#local.ordHeader#</cfoutput>
							</cfif>
						</cfdocumentitem>
						#local.ordBody#
					</cfdocumentsection>
				</cfoutput>
			</cfdocument> 
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<!--- File Name of Invoice --->
		<cfset local.fullname = rereplaceNoCase('#local.qryPurchaser.firstname# #local.qryPurchaser.middlename# #local.qryPurchaser.lastname# #local.qryPurchaser.suffix#','[^A-Z0-9]','','ALL')>
		<cfset local.ordFileNameNoExt = "Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryOrder.orderID,"0000")# #local.fullname#">
		<cfset local.ordFileNameNoExt = replace(local.ordFileNameNoExt,' ','_','ALL')>

		<cfset application.objCommon.encryptPDF("#local.strFolder.folderPath#/un_#local.qryOrder.orderid#_order.pdf","#local.strFolder.folderPath#/#local.ordFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>

		<cfset local.returnStruct.packingSlipPath = "#local.strFolder.folderPath#/#local.ordFileNameNoExt#.pdf">
			
		<cfreturn local.returnStruct>
	</cffunction>
		
	<cffunction name="getPurchaserInfo" access="package" output="no" returntype="query">
		<cfargument name="mid" type="numeric" required="true" />

		<cfset var local = structNew()>

		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int, @memberID int;
			SET @memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="cf_sql_integer">;
			SELECT @orgID = orgID from dbo.ams_members where memberID = @memberID;

			select top 1 o.hasPrefix, o.hasMiddleName, o.hasSuffix, o.hasProfessionalSuffix, 
				mActive.firstname, mActive.lastname, me.email, mActive.memberNumber, 
				case when o.hasPrefix = 1 then mActive.prefix else '' end as prefix, 
				case when o.hasMiddleName = 1 then mActive.middlename else '' end as middleName, 
				case when o.hasSuffix = 1 then mActive.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then mActive.professionalsuffix else '' end as professionalsuffix, 
				mActive.company
			from dbo.ams_members as m
			inner join dbo.organizations as o on o.orgID = @orgID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = mActive.memberID
			inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
			inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
			where m.memberID = @memberID
			and m.status = 'A';
		</cfquery>
		
		<cfreturn local.qryMember>
	</cffunction>
		
	<cffunction name="exportAffirmationGrid" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfif arguments.event.getValue('grid','u') eq "u">
			<cfset local.reportFileName = "UnclaimedAffirmations.csv">
		<cfelse>
			<cfset local.reportFileName = "ClaimedAffirmations.csv">
		</cfif>
		<cfset local.searchValue = arguments.event.getValue('searchkey','')>

		<!--- export data --->
		<cfquery name="local.qryData" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			
			IF OBJECT_ID('tempdb..##tmpStoreExport') IS NOT NULL 
				DROP TABLE ##tmpStoreExport;

			DECLARE @searchValue varchar(300), @storeID int;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			SET @storeID = <cfqueryparam value="#arguments.event.getValue('storeID')#" cfsqltype="cf_sql_integer">;
			
			<cfif arguments.event.getValue('grid','u') eq "u">
				select 
					case when ca.status = 'D' then 'Deleted' else 'Active' end as [Status],
					ca.affirmationCode as [Code],
					ca.dateIssued as [Issued],
					m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' as [Purchaser],
					isnull(m2.company,'') as [Purchaser Company],
					cl.contentTitle as [Product],
					spf.name as [Product Format],
					ROW_NUMBER() OVER (order by ca.status, ca.affirmationCode) as row
				INTO ##tmpStoreExport
				from dbo.store as s
				inner join dbo.store_orders as so on so.storeid = s.storeid
				inner join dbo.crd_affirmations as ca on ca.orderid = so.orderid 
				inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = ca.affirmationTypeID and cat.affirmationType = 'paper'
				inner join dbo.store_productFormats as spf on spf.formatID = ca.productFormatID
				inner join dbo.store_products as sp on sp.storeID = @storeID and sp.itemid = spf.itemid 
				inner join dbo.cms_contentLanguages as cl on cl.contentid = sp.productContentID and cl.languageID = 1
				inner join dbo.crd_offerings as co on co.offeringid = ca.offeringid
				inner join dbo.ams_members as m on m.memberid = ca.issuedByMemberID
				inner join dbo.ams_members as m2 on m2.memberid = m.activememberid	
				where s.storeid = @storeID
				and ca.assignToMemberID is null
				<cfif len(local.searchValue)>
					and (ca.affirmationCode like  @searchValue or cl.contentTitle like  @searchValue)
				</cfif>;

				DECLARE @selectsql varchar(max) = '
					SELECT [Status], [Code], [Issued], [Purchaser], [Purchaser Company], [Product], [Product Format], 
						row as mcCSVorder 
					*FROM* ##tmpStoreExport';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;
			<cfelse>
				select 
					case when ca.status = 'D' then 'Deleted' else 'Active' end as [Status],
					ca.affirmationCode as [Code],
					ca.dateClaimed as [Claimed],
					m2.lastname + ', ' + m2.firstname + isnull(' ' + m2.middlename,'') + ' (' + m2.membernumber + ')' as [Assignee],
					isnull(m2.company,'') as [Assignee Company],
					m3.lastname + ', ' + m3.firstname + isnull(' ' + m3.middlename,'') + ' (' + m3.membernumber + ')' as [Purchaser],
					isnull(m3.company,'') as [Purchaser Company],
					cl.contentTitle as [Product],
					spf.name as [Product Format],
					ROW_NUMBER() OVER (order by ca.status, ca.dateClaimed) as row
				INTO ##tmpStoreExport
				from dbo.store as s
				inner join dbo.store_orders as so on so.storeid = s.storeid 
				inner join dbo.crd_affirmations ca on ca.orderid = so.orderid
				inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = ca.affirmationTypeID and cat.affirmationType = 'paper'
				inner join dbo.store_productFormats spf on spf.formatID = ca.productFormatID
				inner join dbo.store_products sp on sp.storeID = @storeID and sp.itemid = spf.itemid 
				inner join dbo.cms_contentLanguages cl on cl.contentid = sp.productContentID and cl.languageID = 1
				inner join dbo.ams_members m on m.memberid = ca.assignToMemberID
				inner join dbo.ams_members m2 on m2.memberid = m.activememberid	
				inner join dbo.ams_members as m3 on m3.memberid = ca.issuedByMemberID
				inner join dbo.ams_members as m4 on m4.memberid = m3.activememberid	
				where s.storeid = @storeID
				<cfif len(local.searchValue)>
					and (ca.affirmationCode like  @searchValue or cl.contentTitle like  @searchValue)
				</cfif>;
				DECLARE @selectsql varchar(max) = '
					SELECT [Status], [Code], [Claimed], [Assignee], [Assignee Company], [Purchaser], [Purchaser Company], [Product], [Product Format], 
						row as mcCSVorder 
					*FROM* ##tmpStoreExport';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\#local.reportFileName#', @returnColumns=0;
			</cfif>
			
			IF OBJECT_ID('tempdb..##tmpStoreExport') IS NOT NULL 
				DROP TABLE ##tmpStoreExport;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cfif arguments.event.getValue('grid','u') eq "u">
				<cflocation url="#this.link.home#&tab=creditunclaimed" addtoken="no">
			<cfelse>
				<cflocation url="#this.link.home#&tab=creditclaimed" addtoken="no">
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="editStreamProfile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		arguments.event.paramValue('profileID',0);

		// Build breadCrumb Trail ------------------------------------------------------------------- ::
		if (arguments.event.getValue('profileID')) { local.pageTitle = 'Edit Stream Profile'; }
		else { local.pageTitle = 'Add Stream Profile'; }
		appendBreadCrumbs(arguments.event,{ link='', text=local.pageTitle });
		</cfscript>

		<cfif arguments.event.getValue('profileID') is 0 and val(arguments.event.getValue('providerID')) is 0>
			<cflocation url="#this.link.home#&tab=settings" addtoken="false">
		</cfif>

		<cfquery name="local.streamProfile" datasource="#application.dsn.memberCentral.dsn#">
			SELECT p.profileID, p.providerID, p.profileName, p.profileCode, p.isActive
			FROM dbo.stream_profiles as p
			WHERE p.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>
		<cfif arguments.event.getValue('profileID') is 0>
			<cfset local.providerID = val(arguments.event.getValue('providerID'))>
		<cfelse>
			<cfset local.providerID = local.streamProfile.providerID>
		</cfif>

		<cfquery name="local.streamProvider" datasource="#application.dsn.memberCentral.dsn#">
			SELECT p.providerName
			FROM dbo.stream_providers as p
			WHERE p.providerID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.providerID#">
		</cfquery>
		<cfquery name="local.streamProviderFields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT pf.providerFieldID, pf.fieldName
			FROM dbo.stream_providerFields pf
			INNER JOIN dbo.stream_providerFieldLevels as pfl on pfl.fieldLevelID = pf.fieldLevelID
			WHERE pf.providerID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.providerID#">
			AND pfl.fieldLevel = 'provider'
		</cfquery>
		<cfquery name="local.streamProfileFields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT pf.providerFieldID, pf.value as fieldvalue
			FROM dbo.stream_profileFields pf
			WHERE pf.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getTrimValue('profileID')#">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_streamProfile.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleAffirmationsImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = generateAffirmationsImportTemplate(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateAffirmationsImportTemplate" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='ev')>
		<cfset local.reportFileName = "AffirmationImportTemplate.csv">

		<cfset local.fieldNames = "MemberNumber,ProductID,ProductTitle,ProductFormat,DateClaimed">

		<cfquery name="local.qryCreditColumns" datasource="#application.dsn.memberCentral.dsn#">
			select crdA.authorityCode + '_' + crdAT.typeCode as colName, crdA.authorityCode
			from dbo.crd_sponsors as crdS
			inner join dbo.crd_authoritySponsors as crdAS on crdAS.sponsorID = crdS.sponsorID
			inner join dbo.crd_authorities as crdA on crdA.authorityID = crdAS.authorityID
			inner join dbo.crd_authorityTypes as crdAT on crdAT.authorityID = crdA.authorityID
			inner join dbo.crd_authoritySponsorTypes as crdAST on crdAST.ASID = crdAS.ASID and crdAST.typeID = crdAT.typeID
			where crdS.orgID = #arguments.orgID#
			order by authorityCode, colName
		</cfquery>
		<cfif local.qryCreditColumns.recordcount>
			<cfset local.fieldNames = listAppend(local.fieldNames, valueList(local.qryCreditColumns.colName))>
			<cfset local.arrCreditFields = listToArray(valueList(local.qryCreditColumns.colName))>
		<cfelse>
			<cfset local.arrCreditFields = arrayNew(1)>
		</cfif>
		<cfset local.arrFields = listToArray(local.fieldNames)>

		<cfquery name="local.qryExport" datasource="#application.dsn.memberCentral.dsn#" result="local.qryExportResult">
			set nocount on;

			-- create temp table
			IF OBJECT_ID('tempdb..##tmpAffirmationImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpAffirmationImportTemplate;
			CREATE TABLE ##tmpAffirmationImportTemplate (
				autoID int
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, #local.thisCol# varchar(30)
				</cfloop>
			);
			insert into ##tmpAffirmationImportTemplate (MemberNumber,ProductID,ProductTitle,ProductFormat,DateClaimed
				<cfloop array="#local.arrCreditFields#" index="local.thisCol">,#local.thisCol#</cfloop>
				)
			values ('Req', 'Req', 'Req', 'Def to Blank', 'Def to Today' 
				<cfloop array="#local.arrCreditFields#" index="local.thisCol">,'Req if Credit Awarded'</cfloop>
				);

			DECLARE @selectsql varchar(max) = '
				SELECT #local.fieldNames#, ROW_NUMBER() OVER(order by MemberNumber) as mcCSVorder 
				*FROM* ##tmpAffirmationImportTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpAffirmationImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpAffirmationImportTemplate;
		</cfquery>
				
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="affirmationsImport" access="package" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.common.modules.import.import");

		local.ovAction = arguments.event.getValue('frmOvAction2','s');
		</cfscript>
		<cfset local.storeID = arguments.event.getValue('storeID')>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>
		<cfset local.affirmationsImportHome	= buildCurrentLink(arguments.event,"home")>
		<!--- upload file --->
		<cfset local.uploadResult = local.objImport.uploadFile(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), formFieldName='importFileName1')>
		<cfif local.uploadResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.uploadResult.errMsg)>
			<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
			<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
			<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
		</cfif>
		
		<cfif local.uploadResult.ext eq "xls">
			<cfset local.parseResult = local.objImport.parseXLS(strFilePath=local.uploadResult.uploadedFile)>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfset local.csvFile = replaceNoCase(local.uploadResult.uploadedFile, ".xls", ".csv")>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
				<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
				<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
			</cfif>

			<!--- if only one sheet --->
			<cfif arrayLen(local.parseResult.arrSheets) is 1>
				<cfset local.lstDateColumns = 'DateClaimed'>
				<cfset local.parseResult = local.objImport.parseXLSSheet(strFilePath=local.uploadResult.uploadedFile,strFilePathCSV=local.csvFile,sheetIndex=0,lstDateColumns=local.lstDateColumns)>
				<cfset local.returnStruct.success = local.parseResult.success>
				<cfif NOT local.returnStruct.success>
					<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
					<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
					<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
					<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
					<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
				</cfif>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 7>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
				<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
				<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
			</cfif>
			<!--- Files was successfully read in --->
			<cfset local.uploadResult.uploadedFile = local.csvFile>
		</cfif>
		
		<!--- parse CSVs --->
		<cfset local.parseResult = local.objImport.parseCSV(stFilePath=local.uploadResult.uploadedFile, stFilePathTmp="#local.uploadResult.uploadedFilePath#/#local.uploadResult.uploadFilenameWithoutExt#Parsed.csv")>
		<cfif local.parseResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 2>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
			<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
			<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
		<cfelse>
			<cfset local.uploadResult.columnNames = ToString(local.parseResult.strTableColumnNames)>
		</cfif>

		<!--- import files --->
		<cfset local.importToSQLResult = importAffirmationsToSQL(strImportFile=local.uploadResult, columnNames=local.uploadResult.columnNames, 
			siteID=arguments.event.getValue('mc_siteinfo.siteID'), ovAction=local.ovAction,storeID=local.storeID)>
		<cfif local.importToSQLResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfif isDefined("local.importToSQLResult.importResultXML")>
				<cfset local.returnStruct.errorCode = 5>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.importResultXML)>
			<cfelse>
				<cfset local.returnStruct.errorCode = 4>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.errMsg)>
			</cfif>
		</cfif>
		<cfset local.importeddata = showImportResults(strResult=local.returnStruct, doAgainURL=local.affirmationsImportHome, data="affirmations")>
		
		<cfset local.returnStruct.data = home(event= arguments.event,importData = local.importeddata)>
		<cfreturn returnAppStruct(local.returnStruct.data.data,"echo")>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="data" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Import Results</h4>

				<cfif NOT arguments.strResult.success>
					<div class="alert alert-danger">
						<div><b>The import was stopped and requires your attention.</b></div>
						<br/>
						<cfif arguments.strResult.errorCode is 5>
							<cfset local.arrErrors = XMLSearch(arguments.strResult.errorInfo[arguments.strResult.errorCode],"/import/errors/error")>
							<div>
							<cfif arrayLen(local.arrErrors) gt 200>
								<b>Only the first 200 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 200>
									<cfbreak>
								</cfif>
							</cfloop>
							</div>
						<cfelse>
							<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
						</cfif>

						<br/>
						<cfform name="frmUploadEvents" id="frmUploadEvents" action="#arguments.doAgainURL#" method="post">
							<input type="hidden" name="hidReload" value="1">
							<button name="btnDoOver" type="submit" class="btn btn-sm btn-secondary">Try upload again</button>
						</cfform>
					</div>
				<cfelseif arguments.data eq "affirmations">
					<p><b>Import Has Been Completed</b></p>
					<div>
						The import of the uploaded file has been completed.<br/>
					</div>
					<br/>
					<cfform name="frmUploadEvents" id="frmUploadEvents" action="#arguments.doAgainURL#" method="post">
						<input type="hidden" name="hidReload" value="1">
						<button name="btnDoOver" type="submit" class="btn btn-sm btn-secondary">Try upload again</button>
					</cfform>
				</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="importAffirmationsToSQL" access="private" output="false" returntype="struct">
		<cfargument name="strImportFile" type="struct" required="yes">
		<cfargument name="columnNames" type="string" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="ovAction" type="string" required="yes">
		<cfargument name="storeID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { isErr=0, errMsg='', errCount=0 } >
		<cftry>
			<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					declare @siteID int, @storeID int, @importResult xml, @issuedByMemberID int, @ovAction char(1);
					set @issuedByMemberID = #session.cfcUser.memberData.memberID#;
					set @siteID = #arguments.siteID#;
					set @ovAction = '#arguments.ovAction#';
					set @storeID = '#arguments.storeID#';
				
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_AffirmationsImport') IS NOT NULL 
							DROP TABLE ##mc_AffirmationsImport;
						CREATE TABLE ##mc_AffirmationsImport (
							<cfloop list="#arguments.columnnames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max),
							</cfloop>
						);
						BULK INSERT ##mc_AffirmationsImport 
							FROM '#arguments.strImportFile.uploadedFilePathUNC#\#arguments.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
							
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.store_affirmationsImport @siteID=@siteID,@storeID=@storeID, @issuedByMemberID=@issuedByMemberID, @importResult=@importResult OUTPUT;
						
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					
					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;
					
					IF OBJECT_ID('tempdb..##mc_AffirmationsImport') IS NOT NULL 
						DROP TABLE ##mc_AffirmationsImport;
					
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.rs.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.rs.errCount = local.qryImport.errCount>
			<cfif local.rs.errCount gt 0>
				<cfset local.rs.isErr = 1>
			</cfif>
			<cfcatch type="Any">
				<cfset local.rs.isErr = 1>
				<cfset local.rs.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>
		<cfreturn local.rs>
	</cffunction>

	<cffunction name="removeShippingRender" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.shippingJS">
			<cfoutput>
			<style>
			##strDelete { width: 30%; line-height: 1.6 !important; border-radius: 4px; border: 1px solid; text-transform: uppercase; margin-left: 5px; }
			</style>
			<script type="text/javascript">
				function _validateShippingForm() {
					mca_hideAlert('err_batchfrm');
					if ($('##strDelete').length && $('##strDelete').val().trim().toUpperCase() == 'DELETE'){
						doRemoveShipping();
					} else {
						mca_showAlert('err_batchfrm', 'Enter the required text.');
						return false;
					}
				}
				function doRemoveShipping() {
					var removeData = function(r) {
						if (r.success && r.success.toLowerCase() == 'true'){
							top.shippingTable.draw();
							top.$('##MCModal').modal('hide');
						} else {
							mca_showAlert('err_batchfrm', 'We were unable to remove this shipping method.');
						}
					};
					var objParams = { said:#arguments.event.getValue('storeApplicationInstanceID')#, sid:#arguments.event.getValue('sid')#, storeSRID:#this.siteResourceID# };
					TS_AJX('ADMINSTORE','deleteShipping',objParams,removeData,removeData,10000,removeData);
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.shippingJS#">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="err_batchfrm" class="alert alert-danger my-2 d-none"></div>
			<div class="p-3">
				<div><b>Are you sure you want to delete this shipping method?</b></div>
				<div class="mt-2">If you continue, we will permanently delete the shipping method and any related shipping pricing. Some products may become unavailable.</div>
				<div class="mt-2">This action cannot be undone.</div>
				<div class="mt-4">
					<form name="frmDelShip" id="frmDelShip" action="" method="POST">
					<input type="hidden" name="sid" id="sid" value="#arguments.event.getValue('sid',0)#">
					To continue, type <span class="text-danger"><b>DELETE</b></span> in the box below:
					<input type="text" autocomplete="off" name="strDelete" id="strDelete" onKeyPress="mca_hideAlert('err_batchfrm');" class="form-control" maxlength="6">
					</form>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="exportOrdersPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="divExportOrders")>

		<cfset local.doExportSWProgramRegLink = buildCurrentLink(arguments.event,"exportSWODRegistrants") & "&pID=1&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportOrder.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>