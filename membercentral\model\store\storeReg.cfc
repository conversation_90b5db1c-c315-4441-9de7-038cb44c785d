<cfcomponent extends="model.store.store" output="no">

	<cffunction name="doStoreReg" access="package" output="false" returntype="string">
		<cfargument name="event" type="any">
		<cfargument name="appInstanceID" required="true" type="numeric">
		
		<cfset var local = structNew()>
		<cfset arguments.event.paramValue('regAction','')>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfset local.store[arguments.appInstanceID].CrtItemID = arguments.event.getValue('itemid',0)>
		<cfset local.objLocator = CreateObject("component","model.system.user.accountLocater")>

		<!--- show currency types --->
		<cfset local.displayedCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1>
			<cfset local.displayedCurrencyType = arguments.event.getValue('mc_siteinfo.showCurrencyType')>
		</cfif>
		
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.store[arguments.appInstanceID].CrtMemberID = session.cfcUser.memberData.IdentifiedAsMemberID>
			<cfset local.store[arguments.appInstanceID].currentStep = 2>
		</cfif>
		
		<cfset local.qryProducts = getProducts(arguments.event.getValue('storeID',0),arguments.event) />
		<cfset local.productFormats = getFormats(arguments.event,int(val(arguments.event.getValue('itemid',0)))) />
		<cfset local.formatRates = getFormatRates(arguments.event,valuelist(local.productformats.formatID)) />
		<cfset local.qryRateOverrides = getBestRateOverride(valuelist(local.productformats.formatID)) />

		<!--- switch depending on regAction --->
		<cfswitch expression="#arguments.event.getValue('regAction')#">
			<!--- locator --->
			<cfcase value="locator">
				<cfset local.resultsData = local.objLocator.locateMember(event=arguments.event,numResults=30)>
				<cfscript>
					// Get Store support information from org identity or fallback to network info
					local.objWebsite = CreateObject("component","model.admin.website.website");
					local.storeSupportInfo = local.objWebsite.getApplicationSupportInfo(ID=arguments.event.getValue('storeID'),siteID=arguments.event.getValue('mc_siteinfo.siteid'),type="store");
				</cfscript>
				<cfsavecontent variable="local.data">
					<cfoutput><cfinclude template="/views/store/#arguments.event.getValue('viewDirectory','default')#/storeReg_step1_results.cfm"></cfoutput>
				</cfsavecontent>
				<cfreturn local.data>
			</cfcase>
			<!--- usemid (selected a member from step 1) --->
			<cfcase value="usemid">
				<cfset arguments.event.paramValue('mid',int(val(arguments.event.getValue('mid',0))))>
				<cfif arguments.event.getValue('mid') gt 0>
					<cfif application.objMember.getMemberInfo(arguments.event.getValue('mid')).recordCount>
						<cfset local.store[arguments.appInstanceID].CrtMemberID = arguments.event.getValue('mid')>
						<cfset local.store[arguments.appInstanceID].currentStep = 2>
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainregurl')#" addtoken="no">
					</cfif>
				</cfif>
			</cfcase>			
			<!--- newacct (new account from step 1) --->
			<cfcase value="newacct">
				<cfset local.newMemID = local.objLocator.createAccount(event=arguments.event)>
				<cfif local.newMemID gt 0>
					<cfset local.store[arguments.appInstanceID].CrtMemberID = local.newMemID>
					<cfset local.store[arguments.appInstanceID].currentStep = 2>
					<cfsavecontent variable="local.data">
						<cfoutput>
						Please wait...
						<script language="javascript">useMember(#local.newMemID#);</script>
						</cfoutput>
					</cfsavecontent>
					<cfreturn local.data>
				</cfif>
			</cfcase>
		</cfswitch>
		
		<cfswitch expression="#local.store[arguments.appInstanceID].currentStep#">
			<cfcase value="2">
				<cfset local.qryCurrentRegMember = getRegistrantInfo(mid=local.store[arguments.appInstanceID].CrtMemberID)>
				<cfset local.qryAvailableProductFormats = getAvailableProductFormats(arguments.event.getValue('storeID',0),arguments.event.getValue('itemid',0),local.store[arguments.appInstanceID].CrtMemberID )>
			</cfcase>
		</cfswitch>
		<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>
		<cfsavecontent variable="local.data">
			<cfoutput><cfinclude template="/views/store/#arguments.event.getValue('viewDirectory','default')#/storeReg.cfm"></cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getRegistrantInfo" access="public" output="no" returntype="query">
		<cfargument name="mid" type="numeric" required="true" />
		
		<cfset var qryMember = "">

		<cfquery name="qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT TOP 1 
				o.hasPrefix, 
				o.hasMiddleName, 
				o.hasSuffix, 
				o.hasProfessionalSuffix, 
				case when o.hasPrefix = 1 then m3.prefix else '' end as prefix, 
				m3.firstname, 
				case when o.hasMiddleName = 1 then m3.middlename else '' end as middlename, 
				m3.lastname, 
				case when o.hasSuffix = 1 then m3.suffix else '' end as suffix, 
				case when o.hasProfessionalSuffix = 1 then m3.professionalsuffix else '' end as professionalsuffix, 
				m3.company
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members m3 on m.activeMemberid = m3.memberID
			INNER JOIN dbo.organizations as o on o.orgID = m.orgID
			WHERE m.memberID = <cfqueryparam value="#arguments.mid#" cfsqltype="cf_sql_integer" />
			AND m.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMember>
	</cffunction>	

	<cffunction name="orderReceiptForEmail" access="public" output="no" returntype="string">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderID" type="numeric" required="true">
		<cfargument name="onlineReceipt" type="boolean" required="true">
		<cfargument name="notes" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.objStoreAdminStore = CreateObject("component", "model.admin.store.store")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrder">
			SELECT so.OrderID, so.storeID, so.OrderNumber, so.DateOfOrder, so.totalProduct, so.totalTax,  
				so.merchantProfileID, mp.gatewayID, so.xmlShippingInfo, m.activeMemberID as memberID, so.OrderCompleted, so.shippingKey, 
				so.orderStatusID, so.orderNotes, me.email
			FROM dbo.store_orders as so
			INNER JOIN dbo.ams_members as m on m.memberID = so.memberID
			LEFT OUTER JOIN dbo.mp_profiles as mp on mp.profileID = so.merchantProfileID
			INNER JOIN dbo.ams_memberEmails as me on me.memberID = m.activeMemberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
			WHERE so.storeid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			AND so.orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">			
		</cfquery>
		<cfset local.strOrderDetails = local.objStoreAdminStore.getOrderDetails(arguments.storeID,arguments.orderID)>
		<cfset local.orderReceiptFooterContent = local.objStoreAdminStore.getOrderReceiptFooterContent(arguments.storeID)>
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(memberid=local.qryOrder.memberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryPurchaser.orgID, memberid=local.qryPurchaser.memberID)>
		<cfset local.orderTotal = val(local.strOrderDetails.qryOrderTotals.totalProductsShippingTax)>
		<cfset local.orderTotalIncProcessingFees = NumberFormat((local.orderTotal + val(local.strOrderDetails.qryOrderTotals.totalProcessingFees)),'0.00')>

		<cfset local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo)>
		<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
			<cfset local.qryStates = application.objCommon.getStates()>
			<cfquery name="local.getStateCode" dbtype="query">
				<cfif isNumeric(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
					select stateCode from [local].qryStates where stateid = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_integer">
				<cfelse>
					select stateCode from [local].qryStates where stateCode = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_varchar">
				</cfif>				
			</cfquery>
		</cfif>	
		<cfsavecontent variable="local.orderShippingHTML">
			<cfoutput>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_attn.xmlText)>ATTN: #local.shippingInfoXML.xmlRoot.fldship_attn.xmlText#<br/></cfif>
			<cfif StructKeyExists(local.shippingInfoXML.xmlRoot, "fldship_firm") AND len(local.shippingInfoXML.xmlRoot.fldship_firm.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_firm.xmlText#<br/></cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_address1.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_address1.xmlText#<br/></cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_city.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_city.xmlText#,</cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>#local.getStateCode.stateCode# </cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_zip.xmlText#</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfquery name="local.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			select ga.gatewayID, mpContent.rawContent as paymentInstructions
			from dbo.mp_profiles as pr
			inner join dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			outer apply dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			where ga.gatewayID = <cfqueryparam value="#val(local.qryOrder.gatewayid)#" cfsqltype="cf_sql_integer">
			and pr.profileID = <cfqueryparam value="#val(local.qryOrder.merchantProfileID)#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.orgName = local.mc_siteInfo.orgName>
		<cfset local.siteID = local.mc_siteInfo.siteID>
		<cfset local.siteHostName = local.mc_siteInfo.mainHostName>
		<cfset local.showCurrencyType = local.mc_siteInfo.showCurrencyType>
		<cfset local.defaultCurrencyType = local.mc_siteInfo.defaultCurrencyType>

		<cfsavecontent variable="local.orderReceiptHTML">
			<cfoutput>
			#local.qryPurchaser.firstname# #local.qryPurchaser.middlename# #local.qryPurchaser.lastname#:<br/><br/>
			Thank you for your order with #local.orgName#.<br/><br/>

			<cfif len(arguments.notes)>
				<div>#paragraphFormat(arguments.notes)#</div>
				<br/>
			</cfif>

			Here are the details for order <b>#arguments.sitecode##Numberformat(arguments.orderID,"0000")#</b>:<br/><br/><br/>

			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Billing Information</td>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;text-align:right;">&nbsp;</td>
			</tr>
			<tr valign="top">
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					<cfif local.strOrderDetails.qryOrderPayments.recordcount>
						<cfloop query="local.strOrderDetails.qryOrderPayments">
							<b>Payment #local.strOrderDetails.qryOrderPayments.currentrow# <cfif local.strOrderDetails.qryOrderPayments.statusID is 3><span class="red">(Pending Payment)</span></cfif></b> - #dollarformat(local.strOrderDetails.qryOrderPayments.allocSum + val(local.strOrderDetails.qryOrderPayments.processingFees))#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif> applied to order<br/>
							#local.strOrderDetails.qryOrderPayments.detail#<br/>
							Payment date: #DateFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"m/d/yyyy")# #TimeFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"h:mm tt")# CT<br/>
							<br/>
						</cfloop>
					<cfelseif local.orderTotal gt 0 and local.qryPaymentGateway.gatewayID is 11>
						No payment was made.<br/><br/>
						<b>Payment instructions:</b><br/>
						<cfif len(local.qryPaymentGateway.paymentInstructions)>
							#local.qryPaymentGateway.paymentInstructions#
						<cfelse>
							No instructions have been provided. Contact the association for payment instructions.
						</cfif>
						<br/><br/>
					<cfelseif local.orderTotal gt 0>
						<b>No payment was made.</b>
						<br/><br/>
					<cfelse>
						<b>No payment was due.</b>
						<br/><br/>
					</cfif>
					#local.qryPurchaser.firstname# #local.qryPurchaser.middlename# #local.qryPurchaser.lastname#<br/>
					<cfif len(local.qryPurchaser.company)>#local.qryPurchaser.company#<br/></cfif>
					<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
					<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
					<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
					#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
				</td>
				<td style="padding:6px;" align="right" nowrap>
					<table cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">Items Total:</td>
						<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">
							#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProducts)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
						</td>
					</tr>
					<cfif local.strOrderDetails.qryOrderTotals.totalShipping gt 0>
						<tr>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">Shipping:</td>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalShipping)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<cfif local.strOrderDetails.qryOrderTotals.totalTax gt 0>
						<tr>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">Tax:</td>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalTax)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<cfif val(local.strOrderDetails.qryOrderTotals.totalProcessingFees)>
						<tr>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">#local.strOrderDetails.qryOrderTotals.processingFeeLabel#:</td>
							<td nowrap style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProcessingFees)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<tr>
						<td nowrap style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">Order Total:</td>
						<td nowrap style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding-bottom:6px;text-align:right;">
							#dollarFormat(local.orderTotalIncProcessingFees)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
						</td>
					</tr>
					<cfif local.orderTotal gt 0 and local.orderTotal-val(local.strOrderDetails.qryOrderTotals.totalPaid) gt 0>
						<tr>
							<td nowrap style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##f00;padding-bottom:6px;text-align:right;">Amount Due:</td>
							<td nowrap style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;color:##f00;padding-bottom:6px;text-align:right;">
								#dollarFormat(local.orderTotal-val(local.strOrderDetails.qryOrderTotals.totalPaid))#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					</table>
				</td>
			</tr>
			</table>

			<cfif len(trim(local.orderShippingHTML)) gt 10>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Shipping Information</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						#local.orderShippingHTML#
					</td>
				</tr>
				</table>
			</cfif>

			<br/>
			<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Order Details</td>
			</tr>
			<tr>
				<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
					
					<cfset local.showDocumentArea = false>
					</cfoutput>
					<cfoutput query="local.strOrderDetails.qryOrderDetails" group="formatID">
						<cfset local.qryDocuments = local.objStoreAdminStore.getStoreDocuments(formatID=local.strOrderDetails.qryOrderDetails.formatID)>
						<cfset local.itemQuantity = 0>
						<cfset local.itemTotal = 0 >
						<cfoutput>
							<cfset local.itemQuantity =local.itemQuantity + local.strOrderDetails.qryOrderDetails.Quantity>
							<cfset local.itemTotal = local.itemTotal + local.strOrderDetails.qryOrderDetails.itemTotal  >
						</cfoutput>
						<cfquery name="local.productFormatStreams" dbtype="query">
							select providerCode, DateExpires, streamName, orderDetailID, orderDetailStreamID, formatID
							from [local].strOrderDetails.qryOrderStreamsDetails
							where formatID = #local.strOrderDetails.qryOrderDetails.formatID#
							order by quantityGroup, orderDetailID
						</cfquery>

						<cfif local.qryDocuments.recordcount or local.productFormatStreams.recordCount>
							<cfset local.showDocumentArea = true>
						</cfif>
						<div>
							<b>#local.strOrderDetails.qryOrderDetails.contenttitle#</b><br/>
							<div style="margin-left:20px;margin-top:6px;">
								Quantity: #local.itemQuantity#<br/>
								Format: #local.strOrderDetails.qryOrderDetails.formatname#<br/>
								Price: <cfif val(local.itemTotal) gte 0>#dollarFormat(local.itemTotal)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif><cfelse>N/A</cfif><br/>
								<cfif local.strOrderDetails.qryOrderDetails.numAffirmationsIncluded gt 0>
									<br />
									<u>Affirmations:</u>
									<cfif arguments.onlineReceipt is 1 and dollarformat(val(local.strOrderDetails.qryOrderTotals.totalPaid)) eq dollarformat(local.orderTotal)>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/?pg=store&sa=downloadAffirmations&ordernumber=#local.qryOrder.orderNumber#&fid=#local.strOrderDetails.qryOrderDetails.formatID#')">Download Affirmation Forms</button>
										</div>
									<cfelseif arguments.onlineReceipt is 1>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											You may download affirmation forms once this order is paid in full.<br/>
										</div>
									<cfelse>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											See below for instructions to download affirmation forms.<br/>
										</div>
									</cfif>
								</cfif>
								<cfif local.productFormatStreams.recordCount>
									<cfset local.streamProviders = application.objCommon.listRemoveDuplicates(valuelist(local.productFormatStreams.providerCode))>
									<br/>
									<u>Streaming Media:</u> 

									<cfloop query="local.productFormatStreams">
										<cfset local.expireMessage = "">
										<cfset local.accessStream = true>
										<cfif len(local.productFormatStreams.dateExpires)>
											<cfif dateCompare(local.productFormatStreams.dateExpires,now(),"d") gte 0>
												<cfset local.expireMessage = "(access expires on #dateFormat(local.productFormatStreams.dateExpires,"m/d/yyyy")#)">
												<cfset local.accessStream = true>
											<cfelse>
												<cfset local.expireMessage = "(access expired on #dateFormat(local.productFormatStreams.dateExpires,"m/d/yyyy")#)">
												<cfset local.accessStream = false>
											</cfif>
										</cfif>
										<cfif arguments.onlineReceipt is 1 and dollarformat(val(local.strOrderDetails.qryOrderTotals.totalPaid)) is dollarformat(local.orderTotal)>
											<cfif local.accessStream>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/?pg=store&mode=direct&sa=playStream&odetailID=#local.productFormatStreams.orderDetailID#&odetailStreamID=#local.productFormatStreams.orderDetailstreamID#&fid=#local.productFormatStreams.formatID#')">#local.productFormatStreams.streamName#</button> #local.expireMessage#
												</div>
											</cfif>
										<cfelse>
											<div style="padding-top: 5px;padding-bottom: 5px;">
												#local.productFormatStreams.streamName# #local.expireMessage#
											</div>
										</cfif>
									</cfloop>
								</cfif>
								<cfif local.qryDocuments.recordcount>
									<br/>
									<u>Downloadable Documents:</u> 
									<cfloop query="local.qryDocuments">
										<cfset local.expireMessage = "">
										<cfset local.accessDoc = true>
										<cfif local.qryDocuments.accessExpireInDays gt 0>
											<cfset local.expireDate = DateAdd("d",local.qryDocuments.accessExpireInDays,local.qryOrder.dateOfOrder)>
											<cfif dateCompare(local.expireDate, now(), "d") gte 0>
												<cfset local.expireMessage = "(access expires on #dateFormat(local.expireDate,"m/d/yyyy")#)">
												<cfset local.accessDoc = true>
											<cfelse>
												<cfset local.expireMessage = "(access expired on #dateFormat(local.expireDate,"m/d/yyyy")#)">
												<cfset local.accessDoc = false>
											</cfif>
										</cfif>
										<cfif arguments.onlineReceipt is 1 and dollarformat(val(local.strOrderDetails.qryOrderTotals.totalPaid)) eq dollarformat(local.orderTotal)>
											<cfif local.accessDoc>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/storeDocDownload/#local.qryDocuments.documentID#&vid=#local.qryDocuments.documentVersionID#&lang=#local.qryDocuments.languageCode#')">#local.qryDocuments.docTitle#</button> #local.expireMessage#
												</div>
											<cfelse>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													#local.qryDocuments.docTitle# #local.expireMessage#
												</div>
											</cfif>
										<cfelse>
											<div style="padding-top: 5px;padding-bottom: 5px;">
												#local.qryDocuments.docTitle# #local.expireMessage#
											</div>
										</cfif>
									</cfloop>
								</cfif>
							</div>
						</div>
						<br/>
					</cfoutput>
				<cfoutput>
				</td>
			</tr>
			</table>

			<cfif arguments.onlineReceipt is 0 and (local.showDocumentArea or local.strOrderDetails.qryOrderTotals.totalAffirmationsIncluded gt 0)>
				<br/>
				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">How to View Electronically Delivered Items</td>
				</tr>
				<tr>
					<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
						<a href="#local.mc_siteInfo.scheme#://#local.siteHostName#/?pg=store&sa=viewReceipt&ordernumber=#local.qryOrder.orderNumber#" target="_blank">Access this order</a> and click the "Download" link on the order screen or by logging in and clicking the "My Purchases" link in the store.
					</td>
				</tr>
				</table>
			</cfif>

			<br/>
			#local.orderReceiptFooterContent#
			</cfoutput>			
		</cfsavecontent>

		<cfset local.orderReceiptHTML = ReplaceNoCase(ReplaceNoCase(ReplaceNoCase(local.orderReceiptHTML,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>

		<cfreturn local.orderReceiptHTML>
	</cffunction>	

	<cffunction name="orderReceiptForMobile" access="public" output="no" returntype="string">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderID" type="numeric" required="true">
		<cfargument name="onlineReceipt" type="boolean" required="true">
		<cfargument name="notes" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.objStoreAdminStore = CreateObject("component", "model.admin.store.store")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrder">
			SELECT so.OrderID, so.storeID, so.OrderNumber, so.DateOfOrder, so.totalProduct, so.totalTax,  
				so.merchantProfileID, mp.gatewayID, so.xmlShippingInfo, m.activeMemberID as memberID, so.OrderCompleted, so.shippingKey, 
				so.orderStatusID, so.orderNotes, me.email
			FROM dbo.store_orders as so
			INNER JOIN dbo.ams_members as m on m.memberID = so.memberID
			LEFT OUTER JOIN dbo.mp_profiles as mp on mp.profileID = so.merchantProfileID
			INNER JOIN dbo.ams_memberEmails as me on me.memberID = m.activeMemberID
			INNER JOIN dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
			INNER JOIN dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
			WHERE so.storeid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			AND so.orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">			
		</cfquery>
		<cfset local.strOrderDetails = local.objStoreAdminStore.getOrderDetails(arguments.storeID,arguments.orderID)>
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(memberid=local.qryOrder.memberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryPurchaser.orgID, memberid=local.qryPurchaser.memberID)>
		<cfset local.orderTotal = val(local.strOrderDetails.qryOrderTotals.totalProductsShippingTax)>
		<cfset local.orderTotalIncProcessingFees = NumberFormat((local.orderTotal + val(local.strOrderDetails.qryOrderTotals.totalProcessingFees)),'0.00')>

		<cfset local.shippingInfoXML = XMLParse(local.qryOrder.xmlShippingInfo)>
		<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
			<cfset local.qryStates = application.objCommon.getStates()>
			<cfquery name="local.getStateCode" dbtype="query">
				<cfif isNumeric(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>
					select stateCode from [local].qryStates where stateid = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_integer">
				<cfelse>
					select stateCode from [local].qryStates where stateCode = <cfqueryparam value="#local.shippingInfoXML.xmlRoot.fldship_state.xmlText#" cfsqltype="cf_sql_varchar">
				</cfif>				
			</cfquery>
		</cfif>	
		<cfsavecontent variable="local.orderShippingHTML">
			<cfoutput>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_attn.xmlText)>ATTN: #local.shippingInfoXML.xmlRoot.fldship_attn.xmlText#<br/></cfif>
			<cfif StructKeyExists(local.shippingInfoXML.xmlRoot, "fldship_firm") AND len(local.shippingInfoXML.xmlRoot.fldship_firm.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_firm.xmlText#<br/></cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_address1.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_address1.xmlText#<br/></cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_city.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_city.xmlText#,</cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_state.xmlText)>#local.getStateCode.stateCode# </cfif>
			<cfif len(local.shippingInfoXML.xmlRoot.fldship_zip.xmlText)>#local.shippingInfoXML.xmlRoot.fldship_zip.xmlText#</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfquery name="local.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			select ga.gatewayID, mpContent.rawContent as paymentInstructions
			from dbo.mp_profiles as pr
			inner join dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			outer apply dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			where ga.gatewayID = <cfqueryparam value="#val(local.qryOrder.gatewayid)#" cfsqltype="cf_sql_integer">
			and pr.profileID = <cfqueryparam value="#val(local.qryOrder.merchantProfileID)#" cfsqltype="cf_sql_integer">
		</cfquery>
		<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.orgName = local.mc_siteinfo.orgName>
		<cfset local.siteID = local.mc_siteinfo.siteID>
		<cfset local.siteName = local.mc_siteinfo.siteName>
		<cfset local.siteHostName = local.mc_siteinfo.mainHostName>
		<cfset local.showCurrencyType = local.mc_siteinfo.showCurrencyType>
		<cfset local.defaultCurrencyType = local.mc_siteinfo.defaultCurrencyType>

		<cfsavecontent variable="local.orderReceiptHTML">
			<cfoutput>
			<div class="row-fluid">
				<div class="lead">#local.siteName# Order Confirmation and Receipt</div>
				<div>
					#local.qryPurchaser.firstname# #local.qryPurchaser.middlename# #local.qryPurchaser.lastname#:<br/><br/>
					Thank you for your order with #local.orgName#.<br/><br/>

					<cfif len(arguments.notes)>
						<div>#paragraphFormat(arguments.notes)#</div>
						<br/>
					</cfif>

					Here are the details for order <b>#arguments.sitecode##Numberformat(arguments.orderID,"0000")#</b>:
				</div>
			</div>

			<br/>
			<div class="row-fluid" style="border:1px solid ##ccc; padding:10px;">
				<div class="span6">
					<div class="lead">Billing Information</div>
					<div class="row-fluid">
						<cfif local.strOrderDetails.qryOrderPayments.recordcount>
							<cfloop query="local.strOrderDetails.qryOrderPayments">
								<b>Payment #local.strOrderDetails.qryOrderPayments.currentrow# <cfif local.strOrderDetails.qryOrderPayments.statusID is 3><span class="red">(Pending Payment)</span></cfif></b> - #dollarformat(local.strOrderDetails.qryOrderPayments.allocSum + val(local.strOrderDetails.qryOrderPayments.processingFees))#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif> applied to order<br/>
								#local.strOrderDetails.qryOrderPayments.detail#<br/>
								Payment date: #DateFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"m/d/yyyy")# #TimeFormat(local.strOrderDetails.qryOrderPayments.transactionDate,"h:mm tt")# CT<br/>
								<br/>
							</cfloop>
						<cfelseif local.orderTotal gt 0 and local.qryPaymentGateway.gatewayID is 11>
							No payment was made.<br/><br/>
							<b>Payment instructions:</b><br/>
							<cfif len(local.qryPaymentGateway.paymentInstructions)>
								#local.qryPaymentGateway.paymentInstructions#
							<cfelse>
								No instructions have been provided. Contact the association for payment instructions.
							</cfif>
							<br/><br/>
						<cfelseif local.orderTotal gt 0>
							<b>No payment was made.</b>
							<br/><br/>
						<cfelse>
							<b>No payment was due.</b>
							<br/><br/>
						</cfif>
						#local.qryPurchaser.firstname# #local.qryPurchaser.middlename# #local.qryPurchaser.lastname#<br/>
						<cfif len(local.qryPurchaser.company)>#local.qryPurchaser.company#<br/></cfif>
						<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
						<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
						<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
						#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
					</div>
				</div>
				<div class="span6">
					<div class="lead">&nbsp;</div>
					<table cellpadding="4" cellspacing="0" width="100%" border="0" class="table table-hover" id="tblTotals">
					<tr>
						<td align="right" nowrap>Items Total:</td>
						<td align="right" nowrap>
							#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProducts)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
						</td>
					</tr>
					<cfif local.strOrderDetails.qryOrderTotals.totalShipping gt 0>
						<tr>
							<td align="right" nowrap>Shipping:</td>
							<td align="right" nowrap>
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalShipping)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<cfif local.strOrderDetails.qryOrderTotals.totalTax gt 0>
						<tr>
							<td align="right" nowrap>Tax:</td>
							<td align="right" nowrap>
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalTax)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<cfif val(local.strOrderDetails.qryOrderTotals.totalProcessingFees)>
						<tr>
							<td align="right" nowrap>#local.strOrderDetails.qryOrderTotals.processingFeeLabel#:</td>
							<td align="right" nowrap>
								#dollarFormat(local.strOrderDetails.qryOrderTotals.totalProcessingFees)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif>
							</td>
						</tr>
					</cfif>
					<tr>
						<td align="right" nowrap><b>Order Total:</b></td>
						<td align="right" nowrap>
							<b>#dollarFormat(local.orderTotalIncProcessingFees)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif></b>
						</td>
					</tr>
					<cfif local.orderTotal gt 0 and local.orderTotal-val(local.strOrderDetails.qryOrderTotals.totalPaid) gt 0>
						<tr>
							<td align="right" nowrap><span class="red">Amount Due:</span></td>
							<td align="right" nowrap>
								<span class="red">#dollarFormat(local.orderTotal-val(local.strOrderDetails.qryOrderTotals.totalPaid))#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif></span>
							</td>
						</tr>
					</cfif>						
					</table>
				</div>
			</div>

			<cfif len(trim(local.orderShippingHTML)) gt 10>
				<br/>
				<div class="row-fluid" style="border:1px solid ##ccc; padding:10px;">
					<div class="lead">Shipping Information</div>
					<div>#local.orderShippingHTML#</div>
				</div>
			</cfif>

			<br/>
			<div class="row-fluid" style="border:1px solid ##ccc; padding:10px;">
				<div class="lead">Order Details</div>
				<div>
					<cfset local.showDocumentArea = false>
					<cfloop query="local.strOrderDetails.qryOrderDetails">
						<cfset local.qryDocuments = local.objStoreAdminStore.getStoreDocuments(formatID=local.strOrderDetails.qryOrderDetails.formatID)>	

						<cfquery name="local.productFormatStreams" dbtype="query">
							select providerCode, dateExpires, streamName, orderDetailID, orderDetailstreamID, formatID
							from [local].strOrderDetails.qryOrderStreamsDetails
							where formatID = #local.strOrderDetails.qryOrderDetails.formatID#
							order by quantityGroup, orderDetailID
						</cfquery>

						<cfif local.qryDocuments.recordcount>
							<cfset local.showDocumentArea = true>
						</cfif>
						<div>
							<b>#local.strOrderDetails.qryOrderDetails.contenttitle#</b><br/>
							<div style="margin-left:20px;margin-top:6px;">
								Quantity: #local.strOrderDetails.qryOrderDetails.Quantity#<br/>
								Format: #local.strOrderDetails.qryOrderDetails.formatname#<br/>
								Price: <cfif val(local.strOrderDetails.qryOrderDetails.itemTotal) gte 0>#dollarFormat(local.strOrderDetails.qryOrderDetails.itemTotal)#<cfif local.showCurrencyType is 1> #local.defaultCurrencyType#</cfif><cfelse>N/A</cfif><br/>
								<cfif local.strOrderDetails.qryOrderDetails.numAffirmationsIncluded gt 0>
									<br />
									<u>Affirmations:</u>
									<cfif arguments.onlineReceipt is 1 and val(local.strOrderDetails.qryOrderTotals.totalPaid) is local.orderTotal>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/?pg=store&sa=downloadAffirmations&ordernumber=#local.qryOrder.orderNumber#&fid=#local.strOrderDetails.qryOrderDetails.formatID#')">Download Affirmation Forms</button>
										</div>
									<cfelseif arguments.onlineReceipt is 1>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											You may download affirmation forms once this order is paid in full.<br/>
										</div>
									<cfelse>
										<div style="padding-top: 5px;padding-bottom: 5px;">
											See below for instructions to download affirmation forms.<br/>
										</div>
									</cfif>
								</cfif>
								<cfif local.productFormatStreams.recordCount>
									<cfset local.streamProviders = application.objCommon.listRemoveDuplicates(valuelist(local.productFormatStreams.providerCode))>
									<br/>
									<u>Streaming Media:</u> 

									<cfloop query="local.productFormatStreams">
										<cfset local.expireMessage = "">
										<cfset local.accessStream = true>
										<cfif len(local.productFormatStreams.dateExpires)>
											<cfif dateCompare(local.productFormatStreams.dateExpires,now(),"d") gte 0>
												<cfset local.expireMessage = "(access expires on #dateFormat(local.productFormatStreams.dateExpires,"m/d/yyyy")#)">
												<cfset local.accessStream = true>
											<cfelse>
												<cfset local.expireMessage = "(access expired on #dateFormat(local.productFormatStreams.dateExpires,"m/d/yyyy")#)">
												<cfset local.accessStream = false>
											</cfif>
										</cfif>
										<cfif arguments.onlineReceipt is 1 and val(local.strOrderDetails.qryOrderTotals.totalPaid) is local.orderTotal>
											<cfif local.accessStream>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/?pg=store&mode=direct&sa=playStream&odetailID=#local.productFormatStreams.orderDetailID#&odetailStreamID=#local.productFormatStreams.orderDetailstreamID#&fid=#local.productFormatStreams.formatID#')">#local.productFormatStreams.streamName#</button> #local.expireMessage#
												</div>
											</cfif>
										<cfelse>
											<div style="padding-top: 5px;padding-bottom: 5px;">
												#local.productFormatStreams.streamName# #local.expireMessage#
											</div>
										</cfif>
									</cfloop>
								</cfif>

								<cfif local.qryDocuments.recordcount>
									<br/>
									<u>Downloadable Documents:</u> 
									<cfloop query="local.qryDocuments">
										<cfset local.expireMessage = "">
										<cfset local.accessDoc = true>
										<cfif local.qryDocuments.accessExpireInDays gt 0>
											<cfset local.expireDate = DateAdd("d",local.qryDocuments.accessExpireInDays,local.qryOrder.dateOfOrder)>
											<cfif dateCompare(local.expireDate, now(), "d") gte 0>
												<cfset local.expireMessage = "(access expires on #dateFormat(local.expireDate,"m/d/yyyy")#)">
												<cfset local.accessDoc = true>
											<cfelse>
												<cfset local.expireMessage = "(access expired on #dateFormat(local.expireDate,"m/d/yyyy")#)">
												<cfset local.accessDoc = false>
											</cfif>
										</cfif>
										<cfif arguments.onlineReceipt is 1 and dollarFormat(val(local.strOrderDetails.qryOrderTotals.totalPaid)) eq dollarFormat(local.orderTotal)>
											<cfif local.accessDoc>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													<button style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;" onclick="window.open('/storeDocDownload/#local.qryDocuments.documentID#&vid=#local.qryDocuments.documentVersionID#&lang=#local.qryDocuments.languageCode#')">#local.qryDocuments.docTitle#</button> #local.expireMessage#
												</div>
											<cfelse>
												<div style="padding-top: 5px;padding-bottom: 5px;">
													#local.qryDocuments.docTitle# #local.expireMessage#
												</div>
											</cfif>
										<cfelse>
											<div style="padding-top: 5px;padding-bottom: 5px;">
												#local.qryDocuments.docTitle# #local.expireMessage#
											</div>
										</cfif>
									</cfloop>
								</cfif>

							</div>
						</div>

						<br/>
					</cfloop>
				</div>
			</div>

			<br/>
			<div class="row-fluid">
				<div style="padding-top:20px">
					If you have any questions regarding your order, contact us at <a href="#local.mc_siteInfo.scheme#://#local.siteHostName#">#local.siteHostName#</a>.
					<br/><br/>
					#local.orgName#
				</div>
			</div>
			</cfoutput>			
		</cfsavecontent>
		<cfset local.orderReceiptHTML = ReplaceNoCase(ReplaceNoCase(ReplaceNoCase(local.orderReceiptHTML,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL")>

		<cfreturn local.orderReceiptHTML>
	</cffunction>

	<cffunction name="cartQualifiedForCoupon" access="public" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "applicationType" = 'Store', "isQualified" = false, "cartItemsXML" = '<cart />' }>

		<cfset local.objStore = CreateObject("component", "store")>		
		<cfset local.objCart = CreateObject('component', 'shoppingCart')>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>

		<!--- empty cart --->
		<cfif StructIsEmpty(local.store)>
			<cfreturn local.returnStruct>
		</cfif>

		<cfxml variable="local.returnStruct.cartItemsXML">
			<cfoutput>
				<cart>
					<cfloop collection="#local.store#" item="local.thisStoreAppInstanceID">
						<cfset local.storeInfo = local.objStore.getStoreInfo(appInstanceID=local.thisStoreAppInstanceID)>
						<cfset local.qryStoreCart = local.objCart.getCartData(storeid=local.storeInfo.storeid, orderNumber=local.store[local.thisStoreAppInstanceID].orderNumber, appInstanceID=local.thisStoreAppInstanceID)>
						<cfloop query="local.qryStoreCart">
							<item mid="#local.store[local.thisStoreAppInstanceID].CrtMemberID#" rateid="#local.qryStoreCart.rateID#" itemtype="store" />
						</cfloop>
					</cfloop>
				</cart>
			</cfoutput>
		</cfxml>

		<!--- remove the <xml> tag, specifically the encoding. --->
		<cfset local.returnStruct.cartItemsXML = replaceNoCase(toString(local.returnStruct.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

		<cfif arrayLen(XMLSearch(local.returnStruct.cartItemsXML,"/cart/item"))>
			<cfset local.returnStruct.isQualified = true>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="applyCouponToStoreCart" access="public" output="false" returntype="struct">
		<cfargument name="qryCoupon" type="query" required="true">
		<cfargument name="qualifiedCartItemsXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { totalAmount=0, discount=0, discountAppliedTotal=0 }>

		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.objCart = CreateObject("component","shoppingCart")>
		<cfset local.objStore = CreateObject("component","store")>

		<cfset local.grandTotal = 0>
		<cfset local.totalDiscount = 0>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>

		<!--- empty cart --->
		<cfif StructIsEmpty(local.store) OR NOT arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item"))>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset local.strCoupon = {  couponID = arguments.qryCoupon.couponID,
									couponCode = arguments.qryCoupon.couponCode,
									pctOff = arguments.qryCoupon.pctOff,
									pctOffMaxOff = arguments.qryCoupon.pctOffMaxOff,
									amtOff = arguments.qryCoupon.amtOff,
									redeemDetail = arguments.qryCoupon.redeemDetail,
									invoiceDetail = arguments.qryCoupon.invoiceDetail }>	

		
		<cfloop collection="#local.store#" item="local.storeAppInstanceID">
			<cfset local.thisStoreCart = local.store[local.storeAppInstanceID]>
			
			<cfset local.qryStore = local.objStore.getStoreInfo(appInstanceID=local.storeAppInstanceID)>
			<cfset local.qryStoreCart = local.objCart.getCartData(storeID=local.qryStore.storeID, orderNumber=local.thisStoreCart.orderNumber, appInstanceID=local.storeAppInstanceID)>

			<cfset local.qualifiedRateIDList = "">
			<cfloop array="#XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item[@mid='#local.thisStoreCart.CrtMemberID#']")#" index="local.thisXMLElem">
				<cfset local.qualifiedRateIDList = listAppend(local.qualifiedRateIDList,local.thisXMLElem.XmlAttributes.rateid)>
			</cfloop>

			<!--- coupon qualified cart items --->
			<cfquery name="local.qryQualifiedStoreCart" dbtype="query">
				select cartItemID, quantity, rateID, rate, rateOverride, rateGLAccountID
				from [local].qryStoreCart
				where rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.qualifiedRateIDList#">)
			</cfquery>

			<cfif local.qryQualifiedStoreCart.recordCount>
				<cfset local.thisStoreCart.CrtCoupon = duplicate(local.strCoupon)>
				<cfset local.thisStoreCart.CrtCoupon.qryDiscountItems = queryNew("cartItemID,quantity,itemAmount,itemTax,itemDiscount,itemDiscountExcTax,gl","integer,integer,decimal,decimal,decimal,decimal,integer")>
			<cfelse>
				<cfset local.thisStoreCart.CrtCoupon = structNew()>
				<cfbreak>
			</cfif>

			<cfset local.itemKey = "#local.qryStore.storeID#|#local.thisStoreCart.orderNumber#">

			<cfset local.stateIDForTax = 0>
			<cfset local.zipForTax = "">
			<cfif structKeyExists(session.strStoreTax,local.itemKey)>
				<cfset local.stateIDForTax = session.strStoreTax[local.itemKey].stateIDForTax>
				<cfset local.zipForTax = session.strStoreTax[local.itemKey].zipForTax>
			</cfif>
			
			<!--- insert qualified items and calculate discounts later --->
			<cfloop query="local.qryQualifiedStoreCart">
				<cfif structKeyExists(session.strStoreTax,local.itemKey) and session.strStoreTax[local.itemKey].totalTax gt 0>
					<cfset local.totalItemTax = val(session.strStoreTax[local.itemKey][local.qryQualifiedStoreCart.cartItemID]) * local.qryQualifiedStoreCart.quantity>
				<cfelse>
					<cfset local.totalItemTax = 0>
				</cfif>

				<cfif len(local.qryQualifiedStoreCart.rateOverride)>
					<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rateOverride) * local.qryQualifiedStoreCart.quantity>
				<cfelse>
					<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rate) * local.qryQualifiedStoreCart.quantity>
				</cfif>

				<cfif QueryAddRow(local.thisStoreCart.CrtCoupon.qryDiscountItems)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"cartItemID",local.qryQualifiedStoreCart.cartItemID)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"quantity",local.qryQualifiedStoreCart.quantity)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemAmount",local.totalItemAmount)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemTax",local.totalItemTax)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemDiscount",0)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemDiscountExcTax",0)>
					<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"gl",local.qryQualifiedStoreCart.rateGLAccountID)>
				</cfif>
			</cfloop>

			<cfquery name="local.qryTotals" dbtype="query">
				select sum(itemAmount) as totalItemAmount
				from [local].thisStoreCart.CrtCoupon.qryDiscountItems
			</cfquery>

			<cfset local.totalDiscountExcTax = getCouponDiscount(strCoupon=local.strCoupon, totalItemAmount=local.qryTotals.totalItemAmount)>
			<cfset local.allocateDiscountAmountExcTax = local.totalDiscountExcTax>

			<cfloop query="local.thisStoreCart.CrtCoupon.qryDiscountItems">
				<cfset local.thisItemDiscountExcTax = min(local.thisStoreCart.CrtCoupon.qryDiscountItems.itemAmount,local.allocateDiscountAmountExcTax)>
				<cfset local.thisItemDiscountIncTax = 0>

				<!--- discount eq total product amt --->
				<cfif local.thisItemDiscountExcTax eq local.thisStoreCart.CrtCoupon.qryDiscountItems.itemAmount>
					<cfset local.thisItemDiscountIncTax = NumberFormat((local.thisItemDiscountExcTax + local.thisStoreCart.CrtCoupon.qryDiscountItems.itemTax),"9.99")>

				<!--- item taxed --->
				<cfelseif local.thisStoreCart.CrtCoupon.qryDiscountItems.itemTax gt 0>
					<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.thisStoreCart.CrtCoupon.qryDiscountItems.gl, 
											saleAmount=local.thisItemDiscountExcTax, transactionDate=now(), stateIDForTax=local.stateIDForTax, zipForTax=local.zipForTax)>

					<cfset local.thisItemDiscountIncTax = NumberFormat((local.thisItemDiscountExcTax + local.strTax.totalTaxAmt),"9.99")>
				
				<!--- no tax --->
				<cfelse>
					<cfset local.thisItemDiscountIncTax = local.thisItemDiscountExcTax>
				</cfif>

				<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemDiscount",local.thisItemDiscountIncTax,local.thisStoreCart.CrtCoupon.qryDiscountItems.currentrow)>
				<cfset QuerySetCell(local.thisStoreCart.CrtCoupon.qryDiscountItems,"itemDiscountExcTax",local.thisItemDiscountExcTax,local.thisStoreCart.CrtCoupon.qryDiscountItems.currentrow)>

				<cfset local.allocateDiscountAmountExcTax = local.allocateDiscountAmountExcTax - local.thisItemDiscountExcTax>

				<cfif local.allocateDiscountAmountExcTax eq 0>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfquery name="local.qryDiscountTotals" dbtype="query">
				select sum(itemDiscount) as totalItemDiscount, sum(itemDiscountExcTax) as totalItemDiscountExcTax
				from [local].thisStoreCart.CrtCoupon.qryDiscountItems
			</cfquery>

			<!--- update discount --->
			<cfset local.qryAppInstanceID = local.objCart.getAppInstanceIDFromOrderNumber(orderNumber=local.thisStoreCart.orderNumber)>
			<cfif local.qryAppInstanceID.recordCount>
				<cfset local.objCart.updateOrderDiscounts(orderID=local.qryAppInstanceID.orderID, totalDiscount=local.qryDiscountTotals.totalItemDiscount, totalDiscountExcTax=local.qryDiscountTotals.totalItemDiscountExcTax)>
			</cfif>

			<!--- get order totals --->
			<cfset local.qryItemsTotal = local.objCart.getOrderTotals(orderNumber=local.thisStoreCart.orderNumber)>

			<cfset local.grandTotal = local.grandTotal + local.qryItemsTotal.grandTotal>
			<cfset local.totalDiscount = local.totalDiscount + local.qryItemsTotal.totalDiscount>
		</cfloop>

		<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>

		<cfset local.returnStruct = { totalAmount = NumberFormat(local.grandTotal,"9.99"), discount = NumberFormat(local.totalDiscount,"9.99"), 
									  discountAppliedTotal = NumberFormat(local.grandTotal - local.totalDiscount,"9.99") }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getCouponDiscount" access="private" output="false" returntype="numeric">
		<cfargument name="strCoupon" type="struct" required="true">
		<cfargument name="totalItemAmount" type="numeric" required="true" hint="total amount excluding tax">

		<cfset var local = structNew()>
		<cfset local.totalDiscountExcTax = 0>

		<cfif arguments.totalItemAmount eq 0>
			<cfreturn local.totalDiscountExcTax>
		</cfif>

		<cfif val(arguments.strCoupon.pctOff) gt 0>
			<cfset local.totalDiscountExcTax = NumberFormat((arguments.totalItemAmount * (arguments.strCoupon.pctOff / 100)),"9.99")>

			<cfif val(arguments.strCoupon.pctOffMaxOff) gt 0 and max(arguments.strCoupon.pctOffMaxOff,local.totalDiscountExcTax) eq local.totalDiscountExcTax>
				<cfset local.totalDiscountExcTax = arguments.strCoupon.pctOffMaxOff>
			</cfif>
		<cfelseif val(arguments.strCoupon.amtOff) gt 0>
			<cfset local.totalDiscountExcTax = arguments.strCoupon.amtOff>
		</cfif>

		<cfset local.grandTotalItemAmountExcTax = NumberFormat(arguments.totalItemAmount - local.totalDiscountExcTax,"9.99")>

		<cfif local.grandTotalItemAmountExcTax lte 0>
			<cfset local.totalDiscountExcTax = arguments.totalItemAmount>
		</cfif>

		<cfreturn local.totalDiscountExcTax>
	</cffunction>

	<cffunction name="prepStoreCartForCheckout" access="public" output="false" returntype="void">
		<cfset var local = StructNew()>

		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.objCart = CreateObject("component","shoppingCart")>
		<cfset local.objStore = CreateObject("component","store")>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>

		<cfif local.store.count()>
			<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
			<cfset local.redeemedCouponID = 0>
			<cfset local.strCoupon = StructNew()>
			
			<cfloop collection="#local.store#" item="local.storeAppInstanceID">
				<cfset local.storeCart = local.store[local.storeAppInstanceID]>
				<cfset local.orderNumber = local.storeCart.orderNumber>								

				<cfif local.redeemedCouponID eq 0 and NOT structIsEmpty(local.storeCart.CrtCoupon)>
					<cfset local.redeemedCouponID = local.storeCart.CrtCoupon.couponID>
					<cfset local.strCoupon = duplicate(local.storeCart.CrtCoupon)>
				</cfif>
				
				<!--- check for qualified cart items  --->
				<cfif local.redeemedCouponID gt 0>
					<cfset local.storeInfo = local.objStore.getStoreInfo(appInstanceID=local.storeAppInstanceID)>
					<cfset local.qryStoreCart = local.objCart.getCartData(storeID=local.storeInfo.storeid, orderNumber=local.orderNumber, appInstanceID=local.storeAppInstanceID)>

					<cfxml variable="local.cartItemsXML">
						<cfoutput>
							<cart>
								<cfloop query="local.qryStoreCart">
									<item mid="#local.storeCart.crtMemberID#" rateid="#local.qryStoreCart.rateID#" itemtype="store" />
								</cfloop>
							</cart>
						</cfoutput>
					</cfxml>
					<!--- remove the <xml> tag, specifically the encoding. --->
					<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

					<cfif arrayLen(XMLSearch(local.cartItemsXML,"/cart/item"))>
						<cfscript>
						var sqlParams = {
							siteID = { value=local.mc_siteinfo.siteID, cfsqltype="CF_SQL_INTEGER" },
							applicationType = { value="Store", cfsqltype="CF_SQL_VARCHAR" },
							cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
							couponCode = { value=local.strCoupon.couponCode, cfsqltype="CF_SQL_VARCHAR" }
						};

						// using query to exec proc here due to xml output and luceeV5.2 not supporting xml queryparam
						var qryValidCoupon = queryExecute("
							SET NOCOUNT ON;
							
							DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
								@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml;

							EXEC dbo.tr_isValidCouponCode @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
								@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
								@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT;

							SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
							", 
							sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
						);

						local.couponID = val(qryValidCoupon.couponID);
						local.couponResponse = qryValidCoupon.couponMessage;
						local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;
						</cfscript>

						<cfif local.couponID gt 0 AND arrayLen(XMLSearch(local.qualifiedCartItemsXML,"/cart/item"))>
							<cfset local.storeCart.CrtCoupon.qryDiscountItems = queryNew("cartItemID,quantity,itemAmount,itemTax,itemDiscount,itemDiscountExcTax,gl","integer,integer,decimal,decimal,decimal,decimal,integer")>
						<cfelse>
							<cfset local.storeCart.CrtCoupon = structNew()>
						</cfif>

						<!--- promo code applied --->
						<cfif NOT structIsEmpty(local.storeCart.CrtCoupon)>
							<cfset local.itemKey = "#local.storeInfo.storeid#|#local.orderNumber#">
							
							<cfset local.stateIDForTax = 0>
							<cfset local.zipForTax = "">
							<cfif structKeyExists(session,"strStoreTax") AND structKeyExists(session.strStoreTax,local.itemKey)>
								<cfset local.stateIDForTax = session.strStoreTax[local.itemKey].stateIDForTax>
								<cfset local.zipForTax = session.strStoreTax[local.itemKey].zipForTax>
							</cfif>

							<cfset local.qualifiedRateIDList = "">
							<cfloop array="#XMLSearch(local.qualifiedCartItemsXML,"/cart/item[@mid='#local.storeCart.crtMemberID#']")#" index="local.thisXMLElem">
								<cfset local.qualifiedRateIDList = listAppend(local.qualifiedRateIDList,local.thisXMLElem.XmlAttributes.rateid)>
							</cfloop>

							<!--- coupon qualified cart items --->
							<cfquery name="local.qryQualifiedStoreCart" dbtype="query">
								select cartItemID, quantity, rateID, rate, rateOverride, rateGLAccountID
								from [local].qryStoreCart
								where rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.qualifiedRateIDList#">)
							</cfquery>
							
							<!--- insert qualified items and calculate discounts later --->
							<cfloop query="local.qryQualifiedStoreCart">
								<cfif structKeyExists(session,"strStoreTax") AND structKeyExists(session.strStoreTax,local.itemKey) and session.strStoreTax[local.itemKey].totalTax gt 0>
									<cfset local.totalItemTax = val(session.strStoreTax[local.itemKey][local.qryQualifiedStoreCart.cartItemID]) * local.qryQualifiedStoreCart.quantity>
								<cfelse>
									<cfset local.totalItemTax = 0>
								</cfif>

								<cfif len(local.qryQualifiedStoreCart.rateOverride)>
									<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rateOverride) * local.qryQualifiedStoreCart.quantity>
								<cfelse>
									<cfset local.totalItemAmount = val(local.qryQualifiedStoreCart.rate) * local.qryQualifiedStoreCart.quantity>
								</cfif>

								<cfif QueryAddRow(local.storeCart.CrtCoupon.qryDiscountItems)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"cartItemID",local.qryQualifiedStoreCart.cartItemID)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"quantity",local.qryQualifiedStoreCart.quantity)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemAmount",local.totalItemAmount)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemTax",local.totalItemTax)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemDiscount",0)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemDiscountExcTax",0)>
									<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"gl",local.qryQualifiedStoreCart.rateGLAccountID)>
								</cfif>
							</cfloop>

							<cfquery name="local.qryTotals" dbtype="query">
								select sum(itemAmount) as totalItemAmount
								from [local].storeCart.CrtCoupon.qryDiscountItems
							</cfquery>

							<cfset local.totalDiscountExcTax = getCouponDiscount(strCoupon=local.strCoupon, totalItemAmount=local.qryTotals.totalItemAmount)>
							<cfset local.allocateDiscountAmountExcTax = local.totalDiscountExcTax>

							<cfloop query="local.storeCart.CrtCoupon.qryDiscountItems">
								<cfset local.thisItemDiscountExcTax = min(local.storeCart.CrtCoupon.qryDiscountItems.itemAmount,local.allocateDiscountAmountExcTax)>
								<cfset local.thisItemDiscountIncTax = 0>

								<!--- discount eq total product amt --->
								<cfif local.thisItemDiscountExcTax eq local.storeCart.CrtCoupon.qryDiscountItems.itemAmount>
									<cfset local.thisItemDiscountIncTax = NumberFormat((local.thisItemDiscountExcTax + local.storeCart.CrtCoupon.qryDiscountItems.itemTax),"9.99")>

								<!--- item taxed --->
								<cfelseif local.storeCart.CrtCoupon.qryDiscountItems.itemTax gt 0>
									<cfset local.strTax = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.storeCart.CrtCoupon.qryDiscountItems.gl, 
															saleAmount=local.thisItemDiscountExcTax, transactionDate=now(), stateIDForTax=local.stateIDForTax, zipForTax=local.zipForTax)>

									<cfset local.thisItemDiscountIncTax = NumberFormat((local.thisItemDiscountExcTax + local.strTax.totalTaxAmt),"9.99")>
								
								<!--- no tax --->
								<cfelse>
									<cfset local.thisItemDiscountIncTax = local.thisItemDiscountExcTax>
								</cfif>

								<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemDiscount",local.thisItemDiscountIncTax,local.storeCart.CrtCoupon.qryDiscountItems.currentrow)>
								<cfset QuerySetCell(local.storeCart.CrtCoupon.qryDiscountItems,"itemDiscountExcTax",local.thisItemDiscountExcTax,local.storeCart.CrtCoupon.qryDiscountItems.currentrow)>

								<cfset local.allocateDiscountAmountExcTax = local.allocateDiscountAmountExcTax - local.thisItemDiscountExcTax>

								<cfif local.allocateDiscountAmountExcTax eq 0>
									<cfbreak>
								</cfif>
							</cfloop>

							<cfquery name="local.qryDiscountTotals" dbtype="query">
								select sum(itemDiscount) as totalItemDiscount, sum(itemDiscountExcTax) as totalItemDiscountExcTax
								from [local].storeCart.CrtCoupon.qryDiscountItems
							</cfquery>

							<!--- update discount --->
							<cfset local.qryAppInstanceID = local.objCart.getAppInstanceIDFromOrderNumber(orderNumber=local.orderNumber)>
							<cfif local.qryAppInstanceID.recordCount>
								<cfset local.objCart.updateOrderDiscounts(orderID=local.qryAppInstanceID.orderID, totalDiscount=local.qryDiscountTotals.totalItemDiscount, totalDiscountExcTax=local.qryDiscountTotals.totalItemDiscountExcTax)>
							</cfif>

						</cfif>
					</cfif>
				<cfelse>
					<cfset local.storeCart.CrtCoupon = structNew()>
					<cfset local.qryAppInstanceID = local.objCart.getAppInstanceIDFromOrderNumber(orderNumber=local.orderNumber)>
					<cfif local.qryAppInstanceID.recordCount>
						<cfset local.objCart.updateOrderDiscounts(orderID=local.qryAppInstanceID.orderID, totalDiscount=0, totalDiscountExcTax=0)>
					</cfif>
				</cfif>
			</cfloop>

			<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>
		</cfif>
	</cffunction>

	<cffunction name="removeAppliedCouponFromStoreCart" access="public" output="false" returntype="boolean">
		<cfset var local = structNew()>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		
		<!--- empty cart --->
		<cfif StructIsEmpty(local.store)>
			<cfreturn false>
		</cfif>

		<cfloop collection="#local.store#" item="local.storeAppInstanceID">
			<cfset local.thisStoreCart = local.store[local.storeAppInstanceID]>
			<cfset local.thisStoreCart.CrtCoupon = structNew()>
		</cfloop>

		<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>

		<!--- re-calcualte discounts again and prep cart --->
		<cfset prepStoreCartForCheckout()>

		<cfreturn true>
	</cffunction>

</cfcomponent>