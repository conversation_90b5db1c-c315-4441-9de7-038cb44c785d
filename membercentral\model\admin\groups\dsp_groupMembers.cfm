<cfsavecontent variable="local.groupJS">
	<cfoutput>
	<script language="javascript">
		function initGroupMembers() {
			getGroupMembers(1);
			mca_setupSelect2($('##divFilterGroupMembers'));
		}
		function refreshGroupMembersView() {
			$('.grpdiv').hide();
			$('##mc_viewGroupMembers').show();
			getGroupMembers(1);
		}
		function filterGroupMembers() {
			if (!$('##divFilterGroupMembers').is(':visible'))
				$('##divFilterGroupMembers').show();
		}
		function clearFilterGroupMembers() {
			$('##frmFilter')[0].reset();
			clearAssocType();
			$('select').trigger('change');
			getGroupMembers(1);
		}
		function getGroupMembers(n) {
			$('##groupMembersContainer a.btnGrpLink').addClass('disabled');
			$('##mc_viewGroupMembers').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...</div>');
			var objParams = getFilterParams();
			objParams.memPageNum = n;
			$.getJSON('/?event=proxy.ts_json&c=GRPADM&m=getGroupMembers', objParams)
				.done(populateGroupMembers)
				.fail(populateGroupMembers);
		}
		function populateGroupMembers(objResult) {
			var source = $('##mc_groupMembers_template').html();
			var template = Handlebars.compile(source);
			objResult.editMemberLink = top.mca_mgtl_link;
			$('##mc_viewGroupMembers').html(template(objResult));
			mca_scrollTo('mc_viewGroupMembers');

			<!--- Group Populate --->
			if (objResult.strpage.totalcount == 0) {
				$('input[name="grpPopulateOpt"]').off('click').on('click',function() {
					onSelectPopulateGrpMembersOpt($(this).val());
				});
			} else {
				$('##groupMembersContainer a.btnGrpLink').removeClass('disabled');
			}
		}
		function getFilterParams() {
			var groupAssignment = $('##grpAssignment').val() || [];
			var recordTypeIDList = $('##grpMemberRecordType').val() || [];
			var objParams = { groupID:#local.strGroup.qryGroup.groupid#, 
				memPageNum:1, perPageResults:#local.numPerPageInSearchResults#, canEditProtected:#local.canEditProtected#, 
				showMemberPhoto:#local.showMemberPhotosInSearchResults#,  groupAssignment:groupAssignment.join(','), hideInactive:$('##grpHideInactive').val(), 
				recordTypeIDList:recordTypeIDList.join(','), fLastName:$('##fLastName').val(), fCompany:$('##fCompany').val(), 
				fAssociatedMemberID:$('##fAssociatedMemberID').val(), fAssociatedGroupID:$('##fAssociatedGroupID').val() };
			return objParams;
		}
		function returnToGrpMembers() {
			$('##groupMembersContainer').removeClass('d-none');
			$('##divGroupActionContainer').addClass('d-none');
		}
		function selectMemberFilter() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm')
				.html(mca_getLoadingHTML())
				.load('#local.memSelectGotoLink#&closeModal=0&autoClose=0&dispTitle=&retFunction=onSelectFilterMember&fldName=fAssociatedMemberID&mode=stream');
			$('##groupActionTitle').html('Filter Group Members by Member');
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function selectGroupFilter() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm')
				.html(mca_getLoadingHTML())
				.load('#local.grpSelectGotoLink#&fldName=fAssociatedGroupID&autoClose=0&dispTitle=&retFunction=onSelectFilterGroup&mode=stream');
			$('##groupActionTitle').html('Filter Group Members by Group');
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function onSelectFilterMember(fldID, mID, mNum, mName) {
			var fld = $('##'+fldID);
			var fldName = $('##fAssociatedVal');
			fld.val(mID);
			if ((mName.length > 0) && (mNum.length > 0)) {
				fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
				$('##fAssociatedGroupID').val(0);
				$('##divAssociatedVal').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal').hide();
			}
			returnToGrpMembers();
		}
		function onSelectFilterGroup(fldID,gID,gPath) {
			var fld = $('##'+fldID);
			var fldName = $('##fAssociatedVal');	
			fld.val(gID);
			if (gPath.length > 0) {
				var newgPath = gPath.split("\\");
					newgPath.shift();
					newgPath = newgPath.join(" \\ ");
				fldName.html('<b>' + newgPath + '</b>&nbsp;');
				$('##fAssociatedMemberID').val(0);
				$('##divAssociatedVal').show();
			} else {
				fldName.html('');
				$('##divAssociatedVal').hide();
			}
			returnToGrpMembers();
		}
		function changeAssocType() {
			var assocType = $('input:radio[name=fAssocType]:checked').val();
			if ( assocType != undefined) {
				if (assocType == "group") selectGroupFilter();
				else selectMemberFilter();
			}
		}
		function clearAssocType() {
			$(".fAssocType").each(function(){
				$(this).attr("checked",false);
			});
			$('##fAssociatedVal').html("");
			$('##fAssociatedMemberID').val(0);
			$('##fAssociatedGroupID').val(0);
			$('##divAssociatedVal').hide();
		}
		function addGroupMember() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm')
				.html(mca_getLoadingHTML())
				.load('#local.memSelectGotoLink#&closeModal=0&autoClose=0&dispTitle=&retFunction=doAddGroupMember&fldName=mmMemberID&notInGrp=#local.strGroup.qryGroup.groupID#&mode=stream');
			$('##groupActionTitle').html('Add Member to Group');
			<cfif local.strGroup.qryGroup.isSiteAdminGroup>
				$('##groupActionTitle').append('<div class="alert alert-warning p-2" style="font-size: 1.1875rem;">
								Adding members to the Site Administrators group may result in an increase in hosting fees for your organization.
							</div>');
			</cfif> 
			
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function doAddGroupMember(fldID, mID, mNum, mName) {
			var addMemberResult = function(r) {
				returnToGrpMembers();
				if (r.success && r.success.toLowerCase() == 'true') {
					refreshGroupMembersView();
					if (top.dofilterGroups) top.dofilterGroups();
				} else {
					alert('We were unable to add member to this group.\nTry again or contact support for assistance.');
				}
			};
			
			$('##divAddMember').show();
			$('##divAddMemberArea').html('<h5>Add Member to Group</h5>' + mca_getLoadingHTML());

			var objParams = { memberid:mID, groupid:#local.strGroup.qryGroup.groupid# };
			TS_AJX('GRPADM','addGroupMember',objParams,addMemberResult,addMemberResult,20000,addMemberResult);
		}
		function removeMember(mid,mtxt) {
			var removeMemberResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					setTimeout(function() {
						getGroupMembers(1);
						if (top.dofilterGroups) top.dofilterGroups();
					},2000); 	
				}
			};

			$('##mc_viewGroupMembers').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...</div>');

			var objParams = { memberid:mid, groupid:#local.strGroup.qryGroup.groupid# };
			TS_AJX('GRPADM','removeGroupMember',objParams,removeMemberResult,removeMemberResult,20000,removeMemberResult);
		}
		function emailGroupMembers() {
			if(Number($('##mcgrpfilter_memcount').val()) == 0) {
				alert('There are no filtered group members to act upon.');
				return false;
			}

			let emailGroupMembersLink = '#local.emailGroupMembersLink#&groupid=#local.strGroup.qryGroup.groupid#&' + $('##frmFilter').serialize();
			top.MCModalUtils.showLoading('E-mail Filtered Group Members');
			self.location.href = emailGroupMembersLink;
		}
		function exportGroup() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm').html(mca_getLoadingHTML()).load('#local.exportMembersPromptLink#');
			$('##groupActionTitle').html('Download Members');
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function doExportGroupMembers(fsid) {
			window.setTimeout(returnToGrpMembers,2000);
			self.location.href = '#local.exportMembersLink#&fsid=' + fsid + '&' + $('##frmFilter').serialize();
		}
		function exportGroupChanges() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm').html(mca_getLoadingHTML()).load('#local.exportMemberChangesPromptLink#');
			$('##groupActionTitle').html('Download Recent Member Changes');
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function doExportGroupChanges(fsid) {
			window.setTimeout(returnToGrpMembers,2000);
			self.location.href = '#local.exportMembershipChangesLink#&fsid=' + fsid + '&' + $('##frmFilter').serialize();
		}
		function exportMemPhotos() {
			$('##groupMembersContainer').addClass('d-none');
			$('##divGroupActionForm')
				.html('<div><i class="fa-light fa-circle-notch fa-spin fa-3x"></i> Please Wait...</div><div class="mt-4">This may take several minutes to generate a ZIP file of your photos.</div>')
				.load('#local.exportMemberPhotosLink#' + '&' + $('##frmFilter').serialize());
			$('##groupActionTitle').html('Download Member Photos');
			$('##divGroupActionContainer').removeClass('d-none');
		}
		function doExportMemPhotos(u) {
			self.location.href = '/tsdd/' + u;
			window.setTimeout(returnToGrpMembers,2000);
		}
		<!--- Group Populate Functions --->
		function onSelectPopulateGrpMembersOpt(opt) {
			top.MCModalUtils.setTitle('Populate Group');
			switch(opt) {
				case 'manual':
					addGroupMemberManually();
					break;
				case 'newRule':
					createRuleForThisGroup();
					break;
				case 'copyRule':
					copyRuleFromAnotherGroup();
					break;
			};

			$('input[name="grpPopulateOpt"]').prop('checked',false);
		}
		function addGroupMemberManually() {
			loadGrpForm({
				showreturngrplink:true, 
				title:'Manually Add Member to Group', 
				instructiontext:'',
				frmLink:'#local.memSelectGotoLink#&closeModal=0&autoClose=0&dispTitle=&retFunction=onSelectNewGrpMember&fldName=mmMemberID&notInGrp=#local.strGroup.qryGroup.groupID#&mode=stream'
			});
		}
		function onSelectNewGrpMember(fldID, mID, mNum, mName) {
			var addMemberResult = function(r) {
				returnToManageGrp();
				if (r.success && r.success.toLowerCase() == 'true') {
					refreshGroupMembersView();
					if (top.dofilterGroups) top.dofilterGroups();
				} else {
					alert('We were unable to add member to this group.\nTry again or contact support for assistance.');
				}
			};
			
			$('##grpFormArea').html(mca_getLoadingHTML());

			var objParams = { memberid:mID, groupid:#local.strGroup.qryGroup.groupid# };
			TS_AJX('GRPADM','addGroupMember',objParams,addMemberResult,addMemberResult,20000,addMemberResult);
		}
		function createRuleForThisGroup() {
			var createRuleResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					top.MCModalUtils.showLoading('Populate Group');
					top.MCModalUtils.buildFooter({});
					self.location.href = '#local.linkEditRule#&grpID=#local.strGroup.qryGroup.groupid#&ruleID='+r.ruleid+'&editRuleMode=addGroupRule&retScr=viewGrp';
				} else {
					alert('We were unable to create data filters for populating this group. Try again.');
					getGroupMembers(1);
				}
			};

			$('##mc_viewGroupMembers').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...</div>');

			var objParams = { ruleName:'#ReReplaceNoCase(local.strGroup.qryGroup.groupName,'[^A-Z0-9]','','ALL')#', appendDateTimeStamp:true };
			TS_AJX('VIRTUALGROUPS','createVirtualGroupRule',objParams,createRuleResult,createRuleResult,10000,createRuleResult);
		}
		function copyRuleFromAnotherGroup() {
			loadGrpForm({
				showreturngrplink:true, 
				title:'Copy Rule from Another Group', 
				instructiontext:'Select a group to copy a rule from. Please note that only groups with rules directly assigned are available to copy.',
				frmLink:'#local.grpSelectGotoLink#&gridmode=copyRuleFromGroup&fldName=fCopyRuleGroupID&autoClose=0&dispTitle=&retFunction=onSelectCopyRuleGroup&mode=stream'
			});
		}
		function onSelectCopyRuleGroup(fldID,gID,gPath) {
			loadGrpForm({
				showreturngrplink:false, 
				title:'Copy Rule from Another Group', 
				instructiontext:'Select which rule to copy:',
				frmLink:'#local.copyRuleFromAnotherGrpLink#&fromGrpID=' + gID
			});
		}
		function triggerGroupRuleConditions() {
			var refreshGrpResult = function(r) {
				self.location.href = '#this.link.manageGroup#&groupid=#local.strGroup.qryGroup.groupid#';
			};

			$('##mc_viewGroupMembers').html('<div><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Please Wait...</div>');

			var objParams = { groupID:#local.strGroup.qryGroup.groupid# };
			TS_AJX('GRPADM','triggerGroupRuleConditions',objParams,refreshGrpResult,refreshGrpResult,240000,refreshGrpResult);
		}
		function viewGroupRules() {
			$('##viewGrpRulesTabRadio').prop('checked',false);
			$('##groupSettingsTab').tab('show');
			$('##viewGroupRulesTab').tab('show');
		}
		
		$(function() {
			initGroupMembers();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.groupJS)#">

<cfoutput>
<div id="groupMembersContainer" class="px-1 pt-1">
	<div class="toolButtonBar pb-0" style="font-size:92%;">
		<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter group members." onclick="filterGroupMembers();return false;"><i class="fa-solid fa-filter"></i> Filter Members</a></div>
		<cfif local.canAddMember>
			<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" onclick="addGroupMember();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add member to group."><i class="fa-solid fa-user-plus"></i> Add Member</a></div>
		<cfelse>
			<div><a href="##" class="btn btn-link btn-xs disabled p-0" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Manual assignments are not allowed for this group."><i class="fa-solid fa-user-plus"></i> Add Member</a></div>
		</cfif>
		<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" onclick="emailGroupMembers();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered group members."><i class="fa-solid fa-envelope"></i> Email Members</a></div>
		<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" onclick="exportGroup();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download group members."><i class="fa-solid fa-download"></i> Download Members</a></div>
		<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" onclick="exportMemPhotos();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download member photos."><i class="fa-solid fa-download"></i> Download Member Photos</a></div>
		<cfif local.canExportChanges>
			<div><a href="##" class="btn btn-link btn-xs btnGrpLink p-0" onclick="exportGroupChanges();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download recent group membership changes."><i class="fa-solid fa-download"></i> Download Recent Member Changes</a></div>
		</cfif>
	</div>
	<div id="divFilterGroupMembers" class="my-3 grpdiv" style="display:none;">
		<form name="frmFilter" id="frmFilter" onsubmit="getGroupMembers(1);return false;">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold">
						Filter Members
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="form-row">
						<div class="col-sm-3">
							<div class="form-label-group mb-2">
								<input type="text" name="fLastName" id="fLastName" class="form-control" value="">
								<label for="fLastName">Last Name Contains...</label>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-label-group mb-2">
								<input type="text" name="fCompany" id="fCompany" class="form-control" value="">
								<label for="fCompany">Company Contains...</label>
							</div>
						</div>
						<div class="col-sm-3">
							<div class="form-label-group mb-2">
								<select name="grpAssignment" id="grpAssignment" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
									<option value="manual">Manually Assigned</option>
									<option value="virtual">Virtually Assigned</option>
									<option value="indirect">Indirectly Assigned</option>
								</select>
								<label for="grpAssignment">Assignment Method</label>
							</div>
						</div>
						<cfif local.qryOrgRecordTypes.recordcount>
							<div class="col-sm-3">
								<div class="form-label-group mb-2">
									<select name="grpMemberRecordType" id="grpMemberRecordType" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
										<cfloop query="local.qryOrgRecordTypes">
											<option value="#local.qryOrgRecordTypes.recordTypeID#">#local.qryOrgRecordTypes.recordTypeName#</option>
										</cfloop>
									</select>
									<label for="grpMemberRecordType">Record Type</label>
								</div>
							</div>
						<cfelse>
							<input type="hidden" name="grpMemberRecordType" id="grpMemberRecordType" value="">
						</cfif>
					</div>
					<div class="form-row">
						<div class="col-sm-6">
							<div class="row pt-2">
								<div class="col-sm-auto pr-0">
									Associated With:
								</div>
								<div class="col-sm-auto px-1">
									<input type="radio" name="fAssocType" id="fAssocTypeMember" class="fAssocType" value="member" onclick="changeAssocType();"> <label for="fAssocTypeMember">A Specific Member</label> &nbsp; 
									<input type="radio" name="fAssocType" id="fAssocTypeGroup" class="fAssocType" value="group" onclick="changeAssocType();"> <label for="fAssocTypeGroup">A Specific Group</label> &nbsp;
									<input type="hidden" name="fAssociatedMemberID" id="fAssociatedMemberID" value="0">
									<input type="hidden" name="fAssociatedGroupID" id="fAssociatedGroupID" value="0">
								</div>
							</div>
							<div class="row">
								<div id="divAssociatedVal" class="col-sm-12 offset-sm-3" style="display:none;">
									<span id="fAssociatedVal"></span>
									&nbsp; <a href="javascript:clearAssocType();" id="aClearAssocType">clear</a>
								</div>
							</div>
						</div>
						<div class="col-sm-6">
							<div class="form-label-group mb-0">
								<select name="grpHideInactive" id="grpHideInactive" class="custom-select">
									<option value="0">Include Inactive Members</option>
									<option value="1">Hide Inactive Members</option>
								</select>
								<label for="grpHideInactive">Member Status</label>
							</div>
						</div>
					</div>
				</div>
				<div class="card-footer p-2 text-right">
					<button type="button" name="btnResetFilterGroupMembers" class="btn btn-sm btn-secondary" onclick="clearFilterGroupMembers();">Clear Filters</button>
					<button type="submit" name="btnFilterGroupMembers" class="btn btn-sm btn-primary">
						<i class="fa-light fa-filter"></i> Filter Members
					</button>
				</div>
			</div>
		</form>
	</div>
	<cfif local.qryGroupRulesHavingPendingRuleVersions.recordCount>
		<div class="alert alert-warning">
			This group has a rule with a pending rule version that was created, but not activated. 
			You may want to review this rule version to determine if it should be activated or discarded in <a href="##" onclick="viewGroupRules();return false;">View Rules</a>.
		</div>
	</cfif>
	<div id="mc_viewGroupMembers" class="grpdiv"></div>
	<div id="divAddMember" class="grpdiv"><div id="divAddMemberArea"></div></div>
</div>

<div id="divGroupActionContainer" class="d-none p-3">
	<div class="mb-3"><a href="javascript:returnToGrpMembers();"><i class="fa-solid fa-chevrons-left"></i> Return to View Group</a></div>
	<h4 id="groupActionTitle"></h4>
	<div id="divGroupActionForm"></div>
</div>

<script id="mc_groupMembers_template" type="text/x-handlebars-template">
	{{##if success}}
		{{##compare strpage.totalcount '>' 0}}
			{{##compare strpage.totalcount '<=' strpage.rowsize}}
				<div class="card card-box shadow-none mb-2 bg-light">
					<div class="card-body p-2">
						<div><b>{{strpage.totalcount}} member{{##compare strpage.totalcount '>' 1}}s{{/compare}}</b></div>
					</div>
				</div>
			{{/compare}}
			{{##compare strpage.totalcount '>' strpage.rowsize}}
				<div class="card card-box shadow-none mb-2 bg-light">
					<div class="card-body d-flex p-2">
						<div class="col align-self-center px-0"><b>Page {{strpage.currpage}}</b>&nbsp;&nbsp;(Showing {{strpage.currcountstart}} - {{strpage.currcountstop}} of {{strpage.totalcount}} members)</div>
						<div class="ml-auto">
						{{##compare strpage.prevpage '!=' 0}}
							<button type="button" id="btnPrevResults" name="btnPrevResults" class="btn btn-sm btn-outline-primary" onClick="getGroupMembers({{strpage.prevpage}});" title="Previous Results"><i class="fa-solid fa-xl fa-caret-left" style="vertical-align: middle;"></i> Previous</button>
						{{/compare}}
						{{##compare strpage.prevpage '==' 0}}
							<button type="button" id="btnPrevResults" name="btnPrevResults" class="btn btn-sm btn-outline-primary" disabled title="No Previous Results"><i class="fa-solid fa-xl fa-caret-left" style="vertical-align: middle;"></i> Previous</button>
						{{/compare}}
						{{##compare strpage.nextcountstart '<=' strpage.totalcount}}
							<button type="button" id="btnNextResults" name="btnNextResults" class="btn btn-sm btn-outline-primary" onClick="getGroupMembers({{math strpage.currpage "+" 1}});" title="Next Results"><i class="fa-solid fa-xl fa-caret-right" style="vertical-align: middle;"></i> Next</button>
						{{/compare}}
						{{##compare strpage.nextcountstart '>' strpage.totalcount}}
							<button type="button" id="btnNextResults" name="btnNextResults" class="btn btn-sm btn-outline-primary" disabled title="No Next Results"><i class="fa-solid fa-xl fa-caret-right" style="vertical-align: middle;"></i> Next</button>
						{{/compare}}
						</div>
					</div>
				</div>
			{{/compare}}
		{{/compare}}
		<input type="hidden" name="mcgrpfilter_memcount" id="mcgrpfilter_memcount" value="{{strpage.totalcount}}">
		{{##compare strpage.totalcount '>' 0}}
			{{##each arrgroupmembers}}
				<div class="card card-box shadow-none mb-2">
					<div class="card-body px-2">
						<div class="col d-flex">
							<div class="pl-0 pr-2 col-auto">
								{{##if ../strpage.showmemberphoto}}
									<div class="avatar-icon-wrapper">
										<div class="avatar-icon">
											{{##if hasPhoto}}
												<img class="mc_memthumb" src="/memberphotosth/{{memberphoto}}">
											{{else}}
												<img src="/assets/common/images/directory/default.jpg">
											{{/if}}
										</div>
									</div>
								{{/if}}
							</div>
							<div class="col pl-0">
								<a href="{{../editMemberLink}}&memberID={{memberid}}" class="mb-1" target="_blank">{{mc_combinedName}}</a>
								{{##compare company.length '>' 0}}
									<div class="mb-2 text-dim">{{company}}</div>
								{{/compare}}
								{{{mc_combinedAddresses}}}
								{{{mc_extraInfo}}}
							</div>
							<div class="w-25">
								{{##compare mc_recordType.length '>' 0}}<div class="mb-1"><span class="badge badge-dark">{{mc_recordType}}</div>{{/compare}}
								{{##compare mc_memberType.length '>' 0}}<div class="mb-1"><span class="badge badge-dark">{{mc_memberType}}</div>{{/compare}}
								{{##compare mcaccountstatus '==' 'I'}}<div class="mb-1"><span class="badge badge-danger">ACCOUNT INACTIVE</div>{{/compare}}
								{{##compare mcaccountstatus '!=' 'I'}}
									{{##compare mc_memberStatus.length '>' 0}}<div class="mb-1"><span class="badge badge-info">{{mc_memberStatus}}</div>{{/compare}}
								{{/compare}}
								{{##compare mc_lastlogin.length '>' 0}}<div class="mb-1"><span class="badge badge-secondary">{{{mc_lastlogin}}}</span></div>{{/compare}}
								{{##if isManualDirect}}
									<div class="mb-1">
										<i class="fa-solid fa-check-circle text-success" title="This member is directly assigned to this group." style="vertical-align:middle;"></i> Manually Assigned
										{{##compare manualAddedBy.length '>' 0}} by {{{manualAddedBy}}} on {{{manualDateAdded}}}{{/compare}}
										{{##if canRemove}}
											<a href="##" onclick="removeMember({{memberid}},{{../strpage.currpage}});return false;" class="text-danger small">[Remove]</a>
										{{/if}}
									</div>
								{{/if}}
								{{##if isVirtualDirect}}
									<div class="mb-1"><i class="fa-solid fa-check-circle text-info" title="This member is virtually assigned to this group via Group Assignment Rules." style="vertical-align:middle;"></i> Virtually Assigned</div>
								{{/if}}
								{{##if isManualIndirect}}
									{{##if isVirtualIndirect}}
										<div class="mb-1"><i class="fa-solid fa-check-circle text-muted" title="This member is indirectly assigned to this group via both a lower manual assignment and a lower virtual assignment." style="vertical-align:middle;"></i> Indirectly Assigned</div>
									{{else}}
										<div class="mb-1"><i class="fa-solid fa-check-circle text-muted" title="This member is indirectly assigned to this group via a lower manual assignment." style="vertical-align:middle;"></i> Indirectly Assigned</div>
									{{/if}}
								{{else if isVirtualIndirect}}
									<div class="mb-1"><i class="fa-solid fa-check-circle text-muted" title="This member is indirectly assigned to this group via a lower virtual assignment." style="vertical-align:middle;"></i> Indirectly Assigned</div>
								{{/if}}
								{{##compare classifications.length '>' 0}}<div>{{{classifications}}}</div>{{/compare}}
							</div>
						</div>
					</div>
				</div>
			{{/each}}
		{{/compare}}
		{{##compare strpage.totalcount '==' 0}}
			<cfif arguments.event.valueExists('savedGrpRule')>
				<div class="alert alert-info">
					This group's members have changed and will refresh shortly. You can leave this group and return later to review its members.
				</div>
			<cfelse>
				<div class="card card-box p-3 mb-3 shadow-none">
					<cfif local.manageRuleRights AND local.strGroup.qryGroup.allowManualAssignment EQ 0 AND local.strGroup.qryGroup.isProtected EQ 0>
						<div class="alert alert-info mb-2">
							<cfif local.qryGroupRules.recordcount>
								<h5 class="mb-0">There are no results based on your search criteria, but it does have one or more rules assigned.</h5>
							<cfelse>
								<h5 class="mb-0">This group has no members because it has no rules assigned.</h5>
							</cfif>
						</div>
						<div class="mt-2 mb-4">
							<b>Choose an option below to populate this group automatically.</b>
							If you want to manually assign members to this group, enable Manual Assignments on the Settings tab.
						</div>

						<h6 class="mb-2 font-weight-bold">Choose a population option:</h6>
						<cfif local.qryGroupRules.recordcount>
							<div class="form-input">
								<input type="radio" class="form-input-control" id="viewGrpRulesTabRadio" onclick="viewGroupRules();">
								<label for="viewGrpRulesTabRadio" class="form-input-label">Review rules currently assigned to this group</label>
							</div>
						</cfif>
						<div class="form-input">
							<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-newRule" value="newRule" class="form-input-control">
							<label for="grpPopulateOpt-newRule" class="form-input-label">Create a rule for automated population</label>
						</div>
						<div class="form-input">
							<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-copyRule" value="copyRule" class="form-input-control">
							<label for="grpPopulateOpt-copyRule" class="form-input-label">Copy a rule from another group for automated population</label>
						</div>
					<cfelseif local.manageRuleRights AND local.strGroup.qryGroup.allowManualAssignment EQ 1 AND local.strGroup.qryGroup.isProtected EQ 0>
						<div class="alert alert-info mb-2">
							<cfif local.qryGroupRules.recordcount>
								<h5 class="mb-0">This group has no members, but it does have one or more rules assigned and it allows members to be assigned manually.</h5>
							<cfelse>
								<h5 class="mb-0">This group has no members because it has no rules assigned and has no members manually assigned.</h5>
							</cfif>
						</div>
						<div class="mt-2 mb-4">
							<cfif local.canAddMember>
								This group is set to allow Manual Assignments. You can add members manually or create rules for automated population using the options below.
								Rules assigned to this group will not affect members manually assigned to this group.
							<cfelse>
								<b>Choose an option below to populate this group automatically.</b>
							</cfif>
						</div>

						<h6 class="mb-2 font-weight-bold">Choose a population option:</h6>
						<cfif local.qryGroupRules.recordcount>
							<div class="form-input">
								<input type="radio" class="form-input-control" id="viewGrpRulesTabRadio" onclick="viewGroupRules();">
								<label for="viewGrpRulesTabRadio" class="form-input-label">Review rules currently assigned to this group</label>
							</div>
						</cfif>
						<div class="form-input">
							<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-newRule" value="newRule" class="form-input-control">
							<label for="grpPopulateOpt-newRule" class="form-input-label">Create a rule for automated population</label>
						</div>
						<div class="form-input">
							<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-copyRule" value="copyRule" class="form-input-control">
							<label for="grpPopulateOpt-copyRule" class="form-input-label">Copy a rule from another group for automated population</label>
						</div>
						<cfif local.canAddMember>
							<div class="form-input">
								<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-manual" value="manual" class="form-input-control">
								<label for="grpPopulateOpt-manual" class="form-input-label">Manually add members to this group</label>
							</div>
						</cfif>
					<cfelseif local.canAddMember>
						<div class="alert alert-info mb-2">
							<cfif local.strGroup.qryGroup.isProtected EQ 1>
								<h5 class="mb-0">This group is <b>protected</b> and has no members.</h5>
							<cfelse>
								<h5 class="mb-0">This group has no members.</h5>
							</cfif>
						</div>
						<div class="my-2">
							<cfif local.strGroup.qryGroup.isProtected EQ 1>
								Members must be manually added to protected groups by your site's Client Administrator(s). Group Rules cannot be assigned to protected groups.
							</cfif>
							<h6 class="font-weight-bold mt-1">Choose a population option:</h6>
						</div>
						<div class="form-input">
							<input type="radio" name="grpPopulateOpt" id="grpPopulateOpt-manual" value="manual" class="form-input-control">
							<label for="grpPopulateOpt-manual" class="form-input-label">Manually add members to this group</label>
						</div>
					</cfif>
				</div>
			</cfif>
		{{/compare}}
		
		{{##compare strpage.totalcount '>' 0}}
			{{##compare strpage.totalcount '>' strpage.rowsize}}
				<div class="card card-box shadow-none mt-2 bg-light">
					<div class="card-body text-right p-2">
						{{##compare strpage.prevpage '!=' 0}}
							<button type="button" id="btnPrevResultsBottom" name="btnPrevResultsBottom" class="btn btn-sm btn-outline-primary" onClick="getGroupMembers({{strpage.prevpage}});" title="Previous Results"><i class="fa-solid fa-xl fa-caret-left" style="vertical-align: middle;"></i> Previous</button>
						{{/compare}}
						{{##compare strpage.prevpage '==' 0}}
							<button type="button" id="btnPrevResultsBottom" name="btnPrevResultsBottom" class="btn btn-sm btn-outline-primary" disabled title="No Previous Results"><i class="fa-solid fa-xl fa-caret-left" style="vertical-align: middle;"></i> Previous</button>
						{{/compare}}
						{{##compare strpage.nextcountstart '<=' strpage.totalcount}}
							<button type="button" id="btnNextResultsBottom" name="btnNextResultsBottom" class="btn btn-sm btn-outline-primary" onClick="getGroupMembers({{math strpage.currpage "+" 1}});" title="Next Results"><i class="fa-solid fa-xl fa-caret-right" style="vertical-align: middle;"></i> Next</button>
						{{/compare}}
						{{##compare strpage.nextcountstart '>' strpage.totalcount}}
							<button type="button" id="btnNextResultsBottom" name="btnNextResultsBottom" class="btn btn-sm btn-outline-primary" disabled title="No Next Results"><i class="fa-solid fa-xl fa-caret-right" style="vertical-align: middle;"></i> Next</button>
						{{/compare}}
					</div>
				</div>
			{{/compare}}
			<div class="text-dark mt-2">Changes to group memberships are queued and refreshed as soon as possible.</div>
		{{/compare}}
	{{else}}
	 	<div class="text-danger">There was a problem displaying the data.</div>
	{{/if}}
</script>
</cfoutput>