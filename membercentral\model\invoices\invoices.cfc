<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<!--- check for bots --->
	<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
		<cfset variables.isBot = 1>
	<cfelse>
		<cfset variables.isBot = 0>
	</cfif>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			arguments.event.paramValue('va','list');
			if (NOT listFindNoCase("list,show,lookupInvoice,listPayProfiles,message", arguments.event.getValue('va')))
				location('/?pg=invoices', false);

			// bots
			if (variables.isBot && arguments.event.getValue('va') neq "message")
				application.objCommon.redirect('/?pg=invoices&va=message&message=1');

			// allow memberkey to bypass lookup
			arguments.event.setValue('memberid',application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgid')));

			// method to run
			local.methodToRun = this[arguments.event.getValue('va')];
		</cfscript>

		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>	

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.formlink = "/?pg=invoices&va=lookupInvoice">

		<cfset local.invsFound = 0>
		<cfif NOT variables.isBot and arguments.event.valueExists('item')>
			<cftry>
				<cfset local.lstInvoice = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('item'),"xPcmKx","%","ALL")))),"M3mbeR_CenTR@l")>

				<cfset local.arrInvoiceList = arrayNew(1)>
				<cfloop list="#local.lstInvoice#" index="local.thisInvoice">
					<cfif listLen(local.thisInvoice,"|") eq 3>
						<cfset local.strInvoice = { invoicenumber=getToken(local.thisInvoice,1,'|'), invoiceCode=getToken(local.thisInvoice,3,'|') } >
						<cfset arrayAppend(local.arrInvoiceList, local.strInvoice)>
					</cfif>
				</cfloop>

				<cfif not arrayLen(local.arrInvoiceList)>
					<cfthrow type="MCSilent" message="Invoice list not found">
				</cfif>
	
				<cfquery name="local.qryMyInvoices" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;
					
					IF OBJECT_ID('tempdb..##tmpInvSearchResult') is not null
						drop table ##tmpInvSearchResult;
					
					declare @orgID int, @invCountInProfile int;
					declare @tmpInvCount TABLE (invoiceID int, invRowNum int);
					set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
					
					select i.invoiceID, m2.memberID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue,
						i.invoiceCode, dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) as showLink, ip.profileName as invoiceProfile, 
						sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvCanPay,
						ip.profileID, ip.enforcePayOldest, ip.allowPartialPayment, 1 as invCount, 0 as isOldestInv
					into ##tmpInvSearchResult
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
					inner join dbo.organizations as o on o.orgID = @orgID
					left outer join dbo.tr_invoiceTransactions as it 
						inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
						on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where istat.status in ('Closed','Delinquent','Paid')
					and i.orgID = @orgID
					and (
						<cfloop from="1" to="#arrayLen(local.arrInvoiceList)#" index="local.thisNum">
							( o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) = <cfqueryparam value="#local.arrInvoiceList[local.thisNum].invoicenumber#" cfsqltype="CF_SQL_VARCHAR">
							and i.invoiceCode = <cfqueryparam value="#local.arrInvoiceList[local.thisNum].invoiceCode#" cfsqltype="CF_SQL_CHAR"> )
							<cfif local.thisNum neq arrayLen(local.arrInvoiceList)> OR </cfif>
						</cfloop> 
					)
					group by i.invoiceID, m2.memberID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateDue, 
						i.invoiceCode, ip.profileName, ip.profileID, ip.enforcePayOldest, ip.allowPartialPayment;
					
					IF EXISTS (select 1 from ##tmpInvSearchResult where enforcePayOldest = 1) BEGIN
						insert into @tmpInvCount (invoiceID, invRowNum)
						select i.invoiceID, ROW_NUMBER() over (partition by m.activeMemberID, ip.profileID order by i.dateDue, sum(it.cache_invoiceAmountAfterAdjustment)) as invRowNum
						from dbo.tr_invoices as i
						inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
						inner join ##tmpInvSearchResult as tmp on tmp.profileID = i.invoiceProfileID
						inner join dbo.tr_invoiceProfiles as ip on ip.profileID = tmp.profileID
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID and m.activeMemberID = tmp.memberID
						inner join dbo.organizations as o on o.orgID = @orgID
						left outer join dbo.tr_invoiceTransactions as it
							inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
							on it.orgID = @orgID and it.invoiceID = i.invoiceID
						where i.orgID = @orgID 
						and istat.status in ('Closed','Delinquent')
						group by i.invoiceID, m.activeMemberID, ip.profileID, i.dateDue
						having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0;
					
						set @invCountInProfile = @@ROWCOUNT;
					
						update tmp
						set invCount = @invCountInProfile,
							isOldestInv = case when @invCountInProfile > 1 and tmpInv.invRowNum = 1 then 1 else 0 end
						from ##tmpInvSearchResult as tmp
						inner join @tmpInvCount as tmpInv on tmpInv.invoiceID = tmp.invoiceID;
					END
					
					select * from ##tmpInvSearchResult;
					
					IF OBJECT_ID('tempdb..##tmpInvSearchResult') is not null
						drop table ##tmpInvSearchResult;
				</cfquery>
				<cfif local.qryMyInvoices.recordcount is 0>
					<cfthrow type="MCSilent" message="Invoice not found">
				</cfif>
				<cfset local.invsFound = 1>
			<cfcatch type="MCSilent">
				<cfset local.invsFound = 0>
			</cfcatch>
			<cfcatch type="any">
				<cfif cfcatch.type neq "java.lang.ArrayIndexOutOfBoundsException"
					AND NOT findNoCase("must be a Base-64 encoded string",cfcatch.message) 
					AND NOT findNoCase("can't decode string",cfcatch.message)
					AND NOT findNoCase("search.asp",arguments.event.getTrimValue('item'))>
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfif>
				<cflocation url="/?pg=invoices" addtoken="false">
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfif NOT variables.isBot and local.invsFound is 0 and arguments.event.getValue('memberid') gt 0>
			<!--- get invoices assigned to (only) --->
			
			<cfquery name="local.qryMyInvoices" datasource="#application.dsn.memberCentral.dsn#">
				set nocount on;
				
				declare @orgID int, @memberID int;
				declare @tmpInvCount TABLE (invProfileID int, invCount int);
				set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
				set @memberID = <cfqueryparam value="#arguments.event.getValue('memberid')#" cfsqltype="CF_SQL_INTEGER">;
				
				insert into @tmpInvCount (invProfileID, invCount)
				select ip.profileID as invProfileID, count(i.invoiceID) as invCount
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = @orgID
				left outer join dbo.tr_invoiceTransactions as it
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
				where i.orgID = @orgID
				and istat.status in ('Closed','Delinquent')
				and m2.memberID = @memberID
				group by ip.profileID
				having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0;

				select i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.dateDue,
					i.invoiceCode, dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) as showLink, ip.profileName as invoiceProfile, 
					sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
					sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvCanPay, 
					ip.enforcePayOldest, ip.allowPartialPayment, tmp.invCount,
					case
					when ROW_NUMBER() over (partition by m.activeMemberID, ip.profileID order by i.dateDue, sum(it.cache_invoiceAmountAfterAdjustment)) = 1 then 1
					else 0 end as isOldestInv
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				inner join dbo.organizations as o on o.orgID = @orgID
				left outer join dbo.tr_invoiceTransactions as it 
					inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
				inner join @tmpInvCount as tmp on tmp.invProfileID = ip.profileID
				where i.orgID = @orgID 
				and istat.status in ('Closed','Delinquent')
				and m.activeMemberID = @memberID
				group by i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.dateDue, i.invoiceCode, ip.profileName, 
					ip.enforcePayOldest, ip.allowPartialPayment, tmp.invCount, m.activeMemberID, ip.profileID
				having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
				order by i.dateDue, 2;
			</cfquery>
		</cfif>

		<cfset local.qryInvAppContent = getInvoiceAppContent(siteID=arguments.event.getValue('mc_siteInfo.siteID'), contentTitle='InvoiceAppContent')>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div class="tsAppHeading" style="margin-bottom:8px;">Pay Invoice Online</div>
			<div>#local.qryInvAppContent.rawContent#</div>
			<cfif NOT variables.isBot and local.invsFound is 0 and arguments.event.getValue('memberid') is 0>
				<table class="tsAppBodyText">
				<tr>
					<td nowrap><cfinclude template="frm_search.cfm"></td>
					<td width="46" align="center"><img src="/assets/common/images/line-or.gif" width="20" height="140" alt="" /></td>
					<td>
						<div class="tsAppLegendTitle" style="border:1px solid ##ccc;padding:20px;line-height:1.5em;">
							<a href="/?pg=login">Login to #arguments.event.getValue('mc_siteinfo.sitename')#</a><br/>
							to review and pay any of<br/>
							your outstanding invoices.
						</div>
					</td>
				</tr>
				</table>
			<cfelse>
				<cfinclude template="dsp_listInvoices.cfm">
			</cfif>		
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="show" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.lstInvoice = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('item'),"xPcmKx","%","ALL")))),"M3mbeR_CenTR@l")>
			<cfif listLen(local.lstInvoice,'|') is not 3>
				<cfthrow type="MCSilent">
			</cfif>
			<cfset local.strInvoice = { invoicenumber=getToken(local.lstInvoice,1,'|'), invoiceCode=getToken(local.lstInvoice,3,'|') } >

			<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
				select top 1 i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber
				from dbo.tr_invoices as i
				inner join dbo.organizations as o on o.orgID = i.orgID
				where o.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">
				and o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) = <cfqueryparam value="#local.strInvoice.invoicenumber#" cfsqltype="CF_SQL_VARCHAR">
				and i.invoiceCode = <cfqueryparam value="#local.strInvoice.invoiceCode#" cfsqltype="CF_SQL_CHAR">
			</cfquery>
			<cfif local.qryInvoice.recordcount is 0>
				<cfthrow type="MCSilent" message="Invoice not found">
			</cfif>
			
			<!--- save to a file and send to browser --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="inv")>
			<cfset local.strInvoice = CreateObject("component","model.admin.transactions.invoice").generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceID=local.qryInvoice.invoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.strInvoice.invoicePath, displayName=ListLast(local.strInvoice.invoicePath,"/"), deleteSourceFile=1)>
			<cfif not local.docResult>
				<cfthrow type="MCSilent">			
			</cfif>
		<cfcatch type="MCSilent">
			<cfsavecontent variable="local.data">
				<cfoutput><script language="javascript">top.$.colorbox.close();</script></cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>	
		</cfcatch>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfsavecontent variable="local.data">
				<cfoutput><script language="javascript">top.$.colorbox.close();</script></cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>	
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="lookupInvoice" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<!--- validate inv number, amount, code --->
		<cfset arguments.event.setValue('vn',val(ReReplace(arguments.event.getValue('vn',0),'[^0-9]','','ALL')))>
		<cfset arguments.event.setValue('vc',ucase(ReReplace(arguments.event.getValue('vc',''),'[^A-Z]','','ALL')))>
		
		<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select top 1 i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, i.invoiceCode
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			inner join dbo.organizations as o on o.orgID = @orgID
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			where i.orgID = @orgID
			and ins.status in ('Closed','Delinquent')
			and i.invoiceNumber = <cfqueryparam value="#arguments.event.getValue('vn')#" cfsqltype="CF_SQL_BIGINT">
			and i.invoiceCode = <cfqueryparam value="#left(arguments.event.getValue('vc'),8)#" cfsqltype="CF_SQL_CHAR">
			group by i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), i.invoiceCode
			having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0;
		</cfquery>
		<cfif local.qryInvoice.recordcount is 0>
			<cflocation url="/?pg=invoices&ve=1" addtoken="no"> 
		<cfelse>
			<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryInvoice.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryInvoice.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
			<cflocation url="/?pg=invoices&item=#local.stInvEnc#" addtoken="no"> 
		</cfif>
	</cffunction>

	<cffunction name="listPayProfiles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.requestStruct = structNew()>
	
		<cfif arguments.event.valueExists('encid')>
			<cfset local.requestStruct = decryptEncStringListPayProfiles(encString=arguments.event.getValue('encid'))>
		</cfif>

		<cfif structKeyExists(local.requestStruct, "m")>
			<cfset local.memberID = local.requestStruct.m>
			
			<!--- set MCIDME cookie --->
			<cfset application.objPlatform.setSignedCookie(cookiename="mcidme", value="#local.memberID#|#arguments.event.getValue('mc_siteinfo.orgID')#|#GetTickCount()#", expires="90")>
		<cfelseif arguments.event.getValue('memberid') gt 0>
			<cfset local.memberID = arguments.event.getValue('memberid')>
		<cfelse>
			<cflocation url="/?pg=login" addtoken="no">
		</cfif>

		<cfset local.orgID = val(arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.siteID = val(arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfset local.procFeeSupportedGatewayIDs = "10"> <!--- AuthorizeCCCIM --->

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tr_getScheduledPaymentsForMember">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
			<cfprocresult name="local.qrySubs" resultset="1">
			<cfprocresult name="local.qryContribs" resultset="2">
			<cfprocresult name="local.qryInvs" resultset="3">
		</cfstoredproc>

		<cfset local.payProfileIDList = listAppend(valueList(local.qrySubs.payProfileID), valueList(local.qryContribs.payProfileID))>
		<cfset local.payProfileIDList = listAppend(local.payProfileIDList, valueList(local.qryInvs.payProfileID))>
		<!--- removing duplicates and null values --->
		<cfset local.payProfileIDList = listRemoveDuplicates(local.payProfileIDList)>

		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;

			SELECT p.profileID, p.profileName, p.tabTitle AS onScreenLabel, p.profileCode, g.gatewayClass, g.gatewayID, g.tokenStore,
				ROW_NUMBER() OVER (ORDER BY p.profilename) AS rowNum, 1 AS sortOrder
			FROM dbo.mp_profiles AS p
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
				AND g.gatewayType IN ('AuthorizeCCCIM','SageCCCIM','AffiniPayCC')
				AND g.isActive = 1
			WHERE p.siteID = @siteID
			AND p.status = 'A'
				UNION ALL
			SELECT p.profileID, p.profileName, 'Bank Account' AS onScreenLabel, p.profileCode, g.gatewayClass, g.gatewayID, g.tokenStore,
				ROW_NUMBER() OVER (ORDER BY p.profileID) AS rowNum, 2 AS sortOrder
			FROM dbo.mp_profiles AS p
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
				AND g.tokenStore = 'BankDraft'
				AND g.isActive = 1
			WHERE p.siteID = @siteID
			AND p.status = 'A'
			ORDER BY sortOrder, rowNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryMPProfilesForAdd" dbtype="query">
			SELECT profileID, profileName, onScreenLabel, profileCode, gatewayClass, gatewayID, tokenStore, sortOrder
			FROM local.qryMerchantProfiles
			WHERE tokenStore <> 'BankDraft'
				UNION
			SELECT profileID, profileName, onScreenLabel, profileCode, gatewayClass, gatewayID, tokenStore, sortOrder
			FROM local.qryMerchantProfiles
			WHERE tokenStore = 'BankDraft'
			AND rowNum = 1
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberPayProfiles">
			EXEC dbo.tr_getMemberPayProfiles
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">,
				@orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">,
				@limitToPayProfileID = NULL;
		</cfquery>

		<!--- merchant profiles --->
		<cfset local.strProfilesData = structNew()>
		<cfoutput query="local.qryMemberPayProfiles" group="profileID">
			<cfset local.strTmp = structNew()>
			<cfset local.strTmp['profileID'] = local.qryMemberPayProfiles.profileID>
			<cfset local.strTmp['profileName'] = local.qryMemberPayProfiles.profileName>
			<cfset local.strTmp['profileonscreenlabel'] = local.qryMemberPayProfiles.tabTitle>
			<cfset local.strTmp['gatewayID'] = local.qryMemberPayProfiles.gatewayID>
			<cfset local.strTmp['enableProcessingFeeDonation'] = local.qryMemberPayProfiles.enableProcessingFeeDonation>
			<cfset local.strTmp['processFeeDonationFeePercent'] = (local.qryMemberPayProfiles.enableProcessingFeeDonation eq 1 and IsNumeric(local.qryMemberPayProfiles.processFeeDonationFeePercent) and local.qryMemberPayProfiles.processFeeDonationFeePercent gt 0) ? local.qryMemberPayProfiles.processFeeDonationFeePercent : 0>
			<cfset local.strTmp['processingFeeLabel'] = local.qryMemberPayProfiles.processingFeeLabel>
			<cfset local.strTmp['processFeeDonationFETitle'] = local.qryMemberPayProfiles.processFeeDonationFETitle>
			<cfset local.strTmp['processFeeDonationFEMsg'] = local.qryMemberPayProfiles.processFeeDonationFEMsg>
			<cfset local.strTmp['processFeeDonationDefaultSelect'] = local.qryMemberPayProfiles.processFeeDonationDefaultSelect>
			<cfset local.strTmp['processFeeContributionsFELabel'] = local.qryMemberPayProfiles.processFeeContributionsFELabel>
			<cfset local.strTmp['processFeeContributionsFEDenyLabel'] = local.qryMemberPayProfiles.processFeeContributionsFEDenyLabel>
			<cfset local.strTmp['processFeeSubscriptionsFELabel'] = local.qryMemberPayProfiles.processFeeSubscriptionsFELabel>
			<cfset local.strTmp['processFeeSubscriptionsFEDenyLabel'] = local.qryMemberPayProfiles.processFeeSubscriptionsFEDenyLabel>
			<cfset local.strTmp['processFeeOtherPaymentsFELabel'] = local.qryMemberPayProfiles.processFeeOtherPaymentsFELabel>
			<cfset local.strTmp['processFeeOtherPaymentsFEDenyLabel'] = local.qryMemberPayProfiles.processFeeOtherPaymentsFEDenyLabel>
			<cfset local.strProfilesData[local.qryMemberPayProfiles.profileID] = local.strTmp>
		</cfoutput>

		<!--- payment methods grouped --->
		<cfset local.strMPPayProfiles = structNew("ordered")>
		<cfset local.strBDMPProfiles = {}>
		<cfloop query="local.qryMerchantProfiles">
			<cfset local.thisMPProfileID = local.qryMerchantProfiles.profileID>
			<cfset local.strMPPayProfiles[local.thisMPProfileID] = {}>

			<cfif local.qryMerchantProfiles.tokenStore EQ 'BankDraft'>
				<cfset local.strAllowedBankAcctTypes = CreateObject("component","model.system.platform.gateways.BankDraft").allowedAccountTypes(profileID=local.thisMPProfileID)>
				<cfset local.strBDMPProfiles[local.thisMPProfileID] = []>
				<cfif local.strAllowedBankAcctTypes.business>
					<cfset local.strBDMPProfiles[local.thisMPProfileID].append('business')>
				</cfif>
				<cfif local.strAllowedBankAcctTypes.personal>
					<cfset local.strBDMPProfiles[local.thisMPProfileID].append('personal')>
				</cfif>
				
				<cfquery name="local.qryThisMPProfilePayProfiles" dbtype="query">
					SELECT payProfileID, nickname, detail, expiration, failedLastDate, profileID, profileName, tabTitle, 
						enableSurcharge, surchargePercent, surchargeEligible, gatewayClass, gatewayID, cardType, 
						MPPayProfileGroup, bankAccountType, tokenStore
					FROM local.qryMemberPayProfiles
					WHERE tokenStore = 'BankDraft'
					<cfif NOT local.strAllowedBankAcctTypes.business>
						AND bankAccountType <> 'Business'
					</cfif>
					<cfif NOT local.strAllowedBankAcctTypes.personal>
						AND bankAccountType <> 'Personal'
					</cfif>
					ORDER BY MPPayProfileGroup;
				</cfquery>
			<cfelse>
				<cfquery name="local.qryThisMPProfilePayProfiles" dbtype="query">
					SELECT payProfileID, nickname, detail, expiration, failedLastDate, profileID, profileName, tabTitle, 
						enableSurcharge, surchargePercent, surchargeEligible, gatewayClass, gatewayID, cardType, 
						MPPayProfileGroup, bankAccountType, tokenStore
					FROM local.qryMemberPayProfiles
					WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisMPProfileID#">
					ORDER BY MPPayProfileGroup;
				</cfquery>
			</cfif>

			<cfoutput query="local.qryThisMPProfilePayProfiles" group="MPPayProfileGroup">
				<cfset local.thisArrMemberPayProfiles = []>
				
				<cfoutput>
					<cfset local.strSource = structNew()>
					<cfset local.strSource['payProfileID'] = local.qryThisMPProfilePayProfiles.payProfileID>
					<cfset local.strSource['profileID'] = local.qryThisMPProfilePayProfiles.profileID>
					<cfif local.qryThisMPProfilePayProfiles.gatewayClass eq "creditcard">
						<cfset local.strSource['displayLabel'] = local.qryThisMPProfilePayProfiles.detail & (len(local.qryThisMPProfilePayProfiles.nickname) ? " - " & local.qryThisMPProfilePayProfiles.nickname : "")>
					<cfelseif local.qryThisMPProfilePayProfiles.gatewayClass eq "bankdraft">
						<cfset local.strSource['displayLabel'] = local.qryThisMPProfilePayProfiles.bankAccountType & " " & local.qryThisMPProfilePayProfiles.detail & (len(local.qryThisMPProfilePayProfiles.nickname) ? " - " & local.qryThisMPProfilePayProfiles.nickname : "")>
					</cfif>
					<cfset local.strSource['surchargeText'] = local.qryThisMPProfilePayProfiles.enableSurcharge eq 1 ? (IsNumeric(local.qryThisMPProfilePayProfiles.surchargePercent) AND local.qryThisMPProfilePayProfiles.surchargePercent gt 0 and val(local.qryThisMPProfilePayProfiles.surchargeEligible) eq 1 ? "#val(local.qryThisMPProfilePayProfiles.surchargePercent)#% surcharge applies" : "No surcharge applies") : "">
					<cfset local.strSource["issueMessage"] = len(local.qryThisMPProfilePayProfiles.failedLastDate) ? "Issue: Failed " & dateformat(local.qryThisMPProfilePayProfiles.failedLastDate,'m/d/yy') : "">
					<cfset arrayAppend(local.thisArrMemberPayProfiles,local.strSource)>
				</cfoutput>

				<cfset local.strMPPayProfiles[local.thisMPProfileID]['#local.qryThisMPProfilePayProfiles.MPPayProfileGroup#'] = { "arrMemberPayProfiles":duplicate(local.thisArrMemberPayProfiles) }>
			</cfoutput>
		</cfloop>
		
		<cfset local.strPayProfilesData = structNew()>
		<cfloop query="local.qryMemberPayProfiles">
			<cfset local.strPayProfilesData[local.qryMemberPayProfiles.payProfileID] = {
				"type": local.qryMemberPayProfiles.cardType,
				"detail": right(local.qryMemberPayProfiles.detail,4),
				"expiration": dateformat(local.qryMemberPayProfiles.expiration,'mm/yy'),
				"nickname": local.qryMemberPayProfiles.nickname,
				"extrainfo": {
					"mpprofileid": local.qryMemberPayProfiles.profileID,
					"tokenstore": lCase(local.qryMemberPayProfiles.tokenStore),
					"bankaccttype": local.qryMemberPayProfiles.tokenStore EQ 'bankdraft' ? (listFindNoCase("business",local.qryMemberPayProfiles.bankAccountType) ? 'business' : 'personal' ) : ''
				}
			}>
		</cfloop>

		payProfileID, nickname, detail, expiration, failedLastDate, profileID, profileName, tabTitle, 
						enableSurcharge, surchargePercent, surchargeEligible, gatewayClass, gatewayID, cardType, 
						MPPayProfileGroup, bankAccountType, tokenStore

		<cfset local.arrAllPresentProfileIDs = arrayMap(structKeyArray(local.strProfilesData), function(profileID) { return profileID; })>

		<cfset local.arrScheduledPayments = arrayNew(1)>

		<cfloop query="local.qrySubs">
			<cfset local.objData = {
				"type":"sub",
				"referenceID": local.qrySubs.subscriberID,
				"dispOrder": local.qrySubs.dispOrder,
				"subscriberID": local.qrySubs.subscriberID,
				"subscriptionID": local.qrySubs.subscriptionID,
				"subscriptionName": local.qrySubs.subscriptionName,
				"statusName": local.qrySubs.statusName,
				"rateName": local.qrySubs.ratename,
				"subStartDate":dateformat(local.qrySubs.subStartDate,'m/d/yy'),
				"subEndDate":dateformat(local.qrySubs.subEndDate,'m/d/yy'),
				"billedAmt": len(local.qrySubs.billedAmt) ? "$#trim(numberformat(local.qrySubs.billedAmt,"9,999.99"))#" : "",
				"dueAmt": len(local.qrySubs.dueAmt) ? "$#trim(numberformat(local.qrySubs.dueAmt,"9,999.99"))#" : "",
				"nextInvDueDate":len(local.qrySubs.nextInvDueDate) ? dateformat(local.qrySubs.nextInvDueDate,'m/d/yy') : "",
				"nextInvDueAmt": len(local.qrySubs.nextInvDueDate) ? "$#trim(numberformat(local.qrySubs.nextInvDueAmt,"9,999.99"))#" : "",
				"overDueInvDueDate":len(local.qrySubs.overDueInvDueDate) ? dateformat(local.qrySubs.overDueInvDueDate,'m/d/yy') : "",
				"overDueInvDueAmt": len(local.qrySubs.overDueInvDueDate) ? "$#trim(numberformat(local.qrySubs.overDueInvDueAmt,"9,999.99"))#" : "",
				"totalOverdueAmt": len(local.qrySubs.totalOverdueAmt) ? "$#trim(numberformat(local.qrySubs.totalOverdueAmt,"9,999.99"))#" : "",
				"earliestOverdueInvoiceDate": len(local.qrySubs.earliestOverdueInvoiceDate) ? dateformat(local.qrySubs.earliestOverdueInvoiceDate,'m/d/yy') : "",
				"eligibleProfileIDList": local.qrySubs.eligibleProfileIDList,
				"MPProfileID": val(local.qrySubs.MPProfileID),
				"payProfileID": val(local.qrySubs.payProfileID),
				"payProcessFee": val(local.qrySubs.payProcessFee),
				"processFeePercent": local.qrySubs.processFeePercent,
				"ovEnableProcessingFeeDonation": "#local.qrySubs.ovEnableProcessingFeeDonation#",
				"ovProcessFeeDonationFETitle": local.qrySubs.ovProcessFeeDonationFETitle,
				"ovProcessFeeDonationFEMsg": local.qrySubs.ovProcessFeeDonationFEMsg,
				"ovProcessFeeDonationDefaultSelect": "#local.qrySubs.ovProcessFeeDonationDefaultSelect#"
			}>
			<cfset arrayAppend(local.arrScheduledPayments, local.objData)>
		</cfloop>

		<cfloop query="local.qryContribs">
			<cfsavecontent variable="local.frequencyAndAmountDetail">
			<cfoutput>
				<cfif local.qryContribs.totalPledgeRecurring gt 0>
					#replace(DollarFormat(local.qryContribs.totalPledgeRecurring),".00","")# 
						<cfif local.qryContribs.isPerpetual>
							perpetually
						<cfelse>
							until #DateFormat(local.qryContribs.endDate, "m/d/yyyy")#
						</cfif><br/>
				<cfelse>
					<cfif not len(local.qryContribs.nextInvDueDate)>
						#replace(DollarFormat(local.qryContribs.totalPledgeFirst),".00","")# on #DateFormat(local.qryContribs.firstPaymentDate, "m/d/yyyy")#<br/>
					</cfif>
				</cfif>
			</cfoutput>
			</cfsavecontent>

			<cfset local.objData = {
				"type":"cp",
				"referenceID": local.qryContribs.contributionID,
				"dispOrder": local.qryContribs.dispOrder,
				"contributionID": local.qryContribs.contributionID,
				"programName": local.qryContribs.programName,
				"statusName": local.qryContribs.statusName,
				"rateName": local.qryContribs.rateName,
				"amtFreqInfo": local.frequencyAndAmountDetail,
				"frequency": local.qryContribs.frequency,
				"nextInvDueDate":len(local.qryContribs.nextInvDueDate) ? dateformat(local.qryContribs.nextInvDueDate,'m/d/yy') : "",
				"nextInvDueAmt": len(local.qryContribs.nextInvDueDate) ? "$#trim(numberformat(local.qryContribs.nextInvDueAmt,"9,999.99"))#" : "",
				"overDueInvDueDate":len(local.qryContribs.overDueInvDueDate) ? dateformat(local.qryContribs.overDueInvDueDate,'m/d/yy') : "",
				"overDueInvDueAmt": len(local.qryContribs.overDueInvDueDate) ? "$#trim(numberformat(local.qryContribs.overDueInvDueAmt,"9,999.99"))#" : "",
				"totalOverdueAmt": len(local.qryContribs.totalOverdueAmt) ? "$#trim(numberformat(local.qryContribs.totalOverdueAmt,"9,999.99"))#" : "",
				"earliestOverdueInvoiceDate": len(local.qryContribs.earliestOverdueInvoiceDate) ? dateformat(local.qryContribs.earliestOverdueInvoiceDate,'m/d/yy') : "",
				"nextInstallmentDueDate":len(local.qryContribs.nextInstallmentDueDate) ? dateformat(local.qryContribs.nextInstallmentDueDate,'m/d/yy') : "",
				"nextInstallmentDueAmt": len(local.qryContribs.nextInstallmentDueDate) ? "$#trim(numberformat(local.qryContribs.nextInstallmentDueAmt,"9,999.99"))#" : "",
				"eligibleProfileIDList": local.qryContribs.eligibleProfileIDList,
				"MPProfileID": val(local.qryContribs.MPProfileID),
				"payProfileID": val(local.qryContribs.payProfileID),
				"payProcessFee": val(local.qryContribs.payProcessFee),
				"processFeePercent": local.qryContribs.processFeePercent,
				"ovEnableProcessingFeeDonation": "#local.qryContribs.ovEnableProcessingFeeDonation#",
				"ovProcessFeeDonationFETitle": local.qryContribs.ovProcessFeeDonationFETitle,
				"ovProcessFeeDonationFEMsg": local.qryContribs.ovProcessFeeDonationFEMsg,
				"ovProcessFeeDonationDefaultSelect": "#local.qryContribs.ovProcessFeeDonationDefaultSelect#"
			}>
			<cfset arrayAppend(local.arrScheduledPayments, local.objData)>
		</cfloop>

		<cfloop query="local.qryInvs">
			<cfset local.objData = {
				"type":"inv",
				"referenceID": local.qryInvs.invoiceID,
				"dispOrder": local.qryInvs.dispOrder,
				"invoiceID": local.qryInvs.invoiceID,
				"invoiceNumber": local.qryInvs.invoiceNumber,
				"invoiceCode": local.qryInvs.invoiceCode,
				"invDueDate": dateformat(local.qryInvs.dueDate,'m/d/yy'),
				"invDueAmt": "$#trim(numberformat(local.qryInvs.dueAmt,"9,999.99"))#",
				"invDueAmtUnformatted": local.qryInvs.dueAmt,
				"isOverDue": local.qryInvs.isOverDue,
				"eligibleProfileIDList": len(local.qryInvs.eligibleProfileIDList) ? local.qryInvs.eligibleProfileIDList : valueList(local.qryMPProfilesForAdd.profileID),
				"MPProfileID": val(local.qryInvs.MPProfileID),
				"payProfileID": val(local.qryInvs.payProfileID),
				"payProcessFee": val(local.qryInvs.payProcessFee),
				"processFeePercent": local.qryInvs.processFeePercent,
				"ovEnableProcessingFeeDonation": "#local.qryInvs.ovEnableProcessingFeeDonation#",
				"ovProcessFeeDonationFETitle": local.qryInvs.ovProcessFeeDonationFETitle,
				"ovProcessFeeDonationFEMsg": local.qryInvs.ovProcessFeeDonationFEMsg,
				"ovProcessFeeDonationDefaultSelect": "#local.qryInvs.ovProcessFeeDonationDefaultSelect#"
			}>
			<cfset arrayAppend(local.arrScheduledPayments, local.objData)>
		</cfloop>

		<cfset local.strEligibleProfilesForSched = arrayReduce(local.arrScheduledPayments, function(strResult, thisPayment) {
			var refKey = "#thisPayment.type#_#thisPayment.referenceID#";
			strResult[refKey] = listRemoveDuplicates(thisPayment.eligibleProfileIDList);
			return strResult;
		}, {})>

		<!--- include pay profile info --->
		<cfif ListLen(local.payProfileIDList)>
			<cfloop array="#local.arrScheduledPayments#" item="local.thisPayment">
				<cfif local.thisPayment.payProfileID>
					<cfquery dbtype="query" name="local.qryThisPayProfile">
						SELECT detail, expiration, nickname, profileID, tabTitle, failedLastDate, profileName, enableSurcharge,
							surchargePercent, surchargeEligible, enableProcessingFeeDonation, processFeeDonationFeePercent,
							processingFeeLabel, gatewayID, gatewayClass, cardType, bankAccountType
						FROM [local].qryMemberPayProfiles
						WHERE payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisPayment.payProfileID#">
					</cfquery>

					<cfif local.qryThisPayProfile.recordCount>
						<cfset local.thisPayment["profileID"] = local.qryThisPayProfile.profileID>
						<cfset local.thisPayment["profileName"] = local.qryThisPayProfile.profileName>
						<cfset local.thisPayment["gatewayID"] = local.qryThisPayProfile.gatewayID>
						<cfset local.thisPayment["gatewayClass"] = local.qryThisPayProfile.gatewayClass>
						<cfset local.thisPayment["cardType"] = local.qryThisPayProfile.cardType>
						<cfset local.thisPayment["bankAccountType"] = len(local.qryThisPayProfile.bankAccountType) ? local.qryThisPayProfile.bankAccountType & " Checking" : "">
						<cfset local.thisPayment["cardDetail"] = local.qryThisPayProfile.detail>
						<cfset local.thisPayment["cardExpiration"] = dateformat(local.qryThisPayProfile.expiration,'mm/yy')>
						<cfset local.thisPayment["nickname"] = local.qryThisPayProfile.nickname>
						<cfset local.thisPayment["paymentProfileName"] = local.qryThisPayProfile.tabTitle>
						<cfset local.thisPayment["cardFailedLastDate"] = len(local.qryThisPayProfile.failedLastDate) ? dateformat(local.qryThisPayProfile.failedLastDate,'m/d/yy') : "">
						<cfset local.thisPayment["enableSurcharge"] = local.qryThisPayProfile.enableSurcharge>
						<cfset local.thisPayment["surchargeEligible"] = val(local.qryThisPayProfile.surchargeEligible)>
						<cfset local.thisPayment["surchargePercent"] = (local.qryThisPayProfile.enableSurcharge eq 1 and IsNumeric(local.qryThisPayProfile.surchargePercent) and local.qryThisPayProfile.surchargePercent gt 0 and val(local.qryThisPayProfile.surchargeEligible) eq 1) ? local.qryThisPayProfile.surchargePercent : "">

						<cfset local.isPFDSupportedGateway = listFindNoCase(local.procFeeSupportedGatewayIDs,local.qryThisPayProfile.gatewayID)>
						<cfset local.thisPayment["enableProcessingFeeDonation"] = local.qryThisPayProfile.enableProcessingFeeDonation and (not len(local.thisPayment.ovEnableProcessingFeeDonation) or (local.thisPayment.ovEnableProcessingFeeDonation eq 1 and local.isPFDSupportedGateway))>
						<cfset local.thisPayment["processFeeDonationFeePercent"] = (local.qryThisPayProfile.enableProcessingFeeDonation eq 1 and IsNumeric(local.qryThisPayProfile.processFeeDonationFeePercent) and local.qryThisPayProfile.processFeeDonationFeePercent gt 0) ? local.qryThisPayProfile.processFeeDonationFeePercent : "">
						<cfset local.thisPayment["processingFeeLabel"] = local.qryThisPayProfile.processingFeeLabel>
					</cfif>
				</cfif>
			</cfloop>
		</cfif>

		<!--- sort by display order --->
		<cfset arraySort(local.arrScheduledPayments, function(e1, e2) {
			return compare(parseDateTime(e1.dispOrder), parseDateTime(e2.dispOrder));
		})>

		<!--- adding alternate label to the array for use in handling different payment method actions --->
		<cfset local.arrScheduledPayments.map(function(thisRow) { 
			var altOptionLabel = '';
			if(thisRow.type eq "sub"){
				altOptionLabel = "<strong>#thisRow.subscriptionName#</strong> #thisRow.rateName#";
				if(len(thisRow.nextInvDueDate)) altOptionLabel &= " - <i>#thisRow.nextInvDueAmt# due #thisRow.nextInvDueDate#</i>";
				else altOptionLabel &= " - <i>Due: #thisRow.dueAmt#</i>";
			}
			elseif(thisRow.type eq "cp"){
				altOptionLabel = "<strong>#thisRow.programName#</strong> #thisRow.rateName#";
				if(len(thisRow.nextInvDueDate)) altOptionLabel &= " - <i>#thisRow.nextInvDueAmt# due #thisRow.nextInvDueDate#</i>";
				elseif(len(thisRow.nextInstallmentDueDate)) altOptionLabel &= " - <i>#thisRow.nextInstallmentDueAmt# due #thisRow.nextInstallmentDueDate#</i>";
			}
			elseif(thisRow.type eq "inv"){
				altOptionLabel = "<strong>Invoice #thisRow.invoiceNumber#</strong> - <i>#thisRow.invDueAmt# due #thisRow.invDueDate#</i>";
			}

			arguments.thisRow['altOptionLabel'] = altOptionLabel;
		})>

		<!--- scheduled payments grouped by pay profiles --->
		<cfset local.arrPaymentsWithPayProfile = arrayFilter(local.arrScheduledPayments, function(item) {
			return val(item.payProfileID) gt 1;
		})>
		<cfset local.strGroupedScheduledPayments = arrayReduce(local.arrPaymentsWithPayProfile, function(strResult, thisPayment) {
			var strSchedule = { "referenceType":thisPayment.type, "referenceID":thisPayment.referenceID, "label":thisPayment.altOptionLabel, "assocMPProfileID":thisPayment.MPProfileID };

			if (structKeyExists(strResult, thisPayment.payProfileID) and isArray(strResult[thisPayment.payProfileID]))
				ArrayAppend(strResult[thisPayment.payProfileID], strSchedule);
			else strResult[thisPayment.payProfileID] = [strSchedule];

			return strResult;
		}, {})>

		<!--- all scheduled payments info labels --->
		<cfset local.strAllSchedDisplayLabels = arrayReduce(local.arrScheduledPayments, function(strResult, thisPayment) {
			var thisRefKey = "#thisPayment.type#_#thisPayment.referenceID#";
			strResult[thisRefKey]["paymentScheduleLabel"] = thisPayment.altOptionLabel;
			strResult[thisRefKey]["linkedPayMethodLabel"] = thisPayment.payProfileID
				? "<strong>#thisPayment.cardDetail#</strong>" & " Exp " & thisPayment.cardExpiration
					& (len(thisPayment.nickname) ? " <strong>#thisPayment.nickname#</strong> " : "")
					& " - #thisPayment.paymentProfileName# " 
					& (thisPayment.enableSurcharge eq 1 ? (IsNumeric(thisPayment.surchargePercent) and thisPayment.surchargePercent gt 0 and val(thisPayment.surchargeEligible) eq 1 ? " - <i>#thisPayment.surchargePercent#% surcharge applies</i>" : " - <i>No surcharge applies</i>") : "")
					& (len(thisPayment.cardFailedLastDate) ? ' <span class="pm-text-red">Issue: Failed ' & thisPayment.cardFailedLastDate & '</span>' : "")
				: "";
			
			strResult[thisRefKey]["paymentAmtUnformatted"] = thisPayment.type eq "inv" ? thisPayment.invDueAmtUnformatted : 0;

			strResult[thisRefKey]["ovEnableProcessingFeeDonation"] = thisPayment.ovEnableProcessingFeeDonation;
			strResult[thisRefKey]["ovProcessFeeDonationFETitle"] = thisPayment.ovProcessFeeDonationFETitle;
			strResult[thisRefKey]["ovProcessFeeDonationFEMsg"] = thisPayment.ovProcessFeeDonationFEMsg;
			strResult[thisRefKey]["ovProcessFeeDonationDefaultSelect"] = thisPayment.ovProcessFeeDonationDefaultSelect;

			if(thisPayment.payProfileID gt 0){
				strResult[thisRefKey]["gatewayID"] = thisPayment.gatewayID;
				strResult[thisRefKey]["payProcessFee"] = thisPayment.payProcessFee;
				strResult[thisRefKey]["processFeePercent"] = thisPayment.processFeePercent;
			}
			else {
				strResult[thisRefKey]["gatewayID"] = 0;
				strResult[thisRefKey]["payProcessFee"] = 0;
				strResult[thisRefKey]["processFeePercent"] = 0;
			}

			return strResult;
		}, {})>

		<cfset local.arrOtherPayMethods = arrayNew(1)>
		<cfset local.payMethodsAssociatedToAtLeastOneItem = listRemoveDuplicates(local.arrScheduledPayments.map((item) => (item?.payProfileID ?: 0)).filter((item) => (item gt 0)).toList())>

		<cfloop query="local.qryMemberPayProfiles">
			<cfif not local.payMethodsAssociatedToAtLeastOneItem.listFind(local.qryMemberPayProfiles.payProfileID)>
				<cfset local.objData = {
					"payProfileID": local.qryMemberPayProfiles.payProfileID,
					"profileID": local.qryMemberPayProfiles.profileID,
					"gatewayClass": local.qryMemberPayProfiles.gatewayClass,
					"cardType": local.qryMemberPayProfiles.cardType,
					"bankAccountType": len(local.qryMemberPayProfiles.bankAccountType) ? local.qryMemberPayProfiles.bankAccountType & " Checking" : "",
					"cardDetail": local.qryMemberPayProfiles.detail,
					"cardExpiration": dateformat(local.qryMemberPayProfiles.expiration,'mm/yy'),
					"nickname": local.qryMemberPayProfiles.nickname,
					"paymentProfileName": local.qryMemberPayProfiles.tabTitle,
					"cardFailedLastDate": len(local.qryMemberPayProfiles.failedLastDate) ? dateformat(local.qryMemberPayProfiles.failedLastDate,'m/d/yy') : "",
					"enableSurcharge": local.qryMemberPayProfiles.enableSurcharge,
					"surchargeEligible": val(local.qryMemberPayProfiles.surchargeEligible),
					"surchargePercent": (local.qryMemberPayProfiles.enableSurcharge eq 1 and IsNumeric(local.qryMemberPayProfiles.surchargePercent) and local.qryMemberPayProfiles.surchargePercent gt 0 and val(local.qryMemberPayProfiles.surchargeEligible) eq 1) ? local.qryMemberPayProfiles.surchargePercent : "",
					"enableProcessingFeeDonation": local.qryMemberPayProfiles.enableProcessingFeeDonation,
					"processFeeDonationFeePercent": (local.qryMemberPayProfiles.enableProcessingFeeDonation eq 1 and IsNumeric(local.qryMemberPayProfiles.processFeeDonationFeePercent) and local.qryMemberPayProfiles.processFeeDonationFeePercent gt 0) ? local.qryMemberPayProfiles.processFeeDonationFeePercent : "",
					"processingFeeLabel": local.qryMemberPayProfiles.processingFeeLabel,
					"payProcessFee": 0,
					"processFeePercent": ""
				}>
				<cfset arrayAppend(local.arrOtherPayMethods, local.objData)>
			</cfif>
		</cfloop>

		<cfset local.qryManagePayMethodsContent = getInvoiceAppContent(siteID=local.siteID, contentTitle='ManagePayMethodsContent')>

		<cfset local.redirectURL = "/?#getBaseQueryString(false)#">

		<cfset local.strMerchantProfilesFormData = {}>
		<cfloop query="local.qryMerchantProfiles">
			<cfset local.thisProfileID = local.qryMerchantProfiles.profileID>
			<cfset local.strProfileForm = application.objPayments.getGatewayInputFormLoader(siteid=local.siteID, profilecode=local.qryMerchantProfiles.profileCode,
				pmid=local.memberID, formHolderElementID="divPaymentProfileForm#local.thisProfileID#", editMode='frontEndManageV2')>
			
			<cfif local.strProfileForm.success>
				<cfif len(local.strProfileForm.headcode)>
					<cfhtmlhead text="#application.objCommon.minText(local.strProfileForm.headcode)#">
				</cfif>
				<cfset local.strMerchantProfilesFormData[local.thisProfileID]['strActionFn'] = local.strProfileForm.strActionFn>
				<cfif structKeyExists(local.strProfileForm, "arrMemProfilesOnFile") and arrayLen(local.strProfileForm.arrMemProfilesOnFile)>
					<cfset local.strMerchantProfilesFormData[local.thisProfileID]['arrMemProfilesOnFile'] = local.strProfileForm.arrMemProfilesOnFile>
				</cfif>
			<cfelse>
				<!--- return err msg --->
				<cfreturn returnAppStruct('<div class="alert alert-danger">#local.strProfileForm.errMsg#</div>',"echo")>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_managePayMethods.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateScheduledPaymentMethods" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="schedPayments" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false, "errmsg":"" }>
		<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.mcproxy_orgID)>
		<cfset local.arrUpdateSchedPayments = deserializeJSON(arguments.schedPayments)>

		<cftry>
			<cfloop array="#local.arrUpdateSchedPayments#" index="local.thisSchedPayment">
				<cfset local.referenceType = local.thisSchedPayment.refType>
				<cfset local.referenceID = val(local.thisSchedPayment.refID)>
				<cfset local.newMPPayProfileID = val(local.thisSchedPayment.MPPayProfileID)>
				<cfset local.newMPProfileID = val(local.thisSchedPayment.MPProfileID)>
				<cfif local.newMPPayProfileID AND NOT local.newMPProfileID>
					<cfset local.newMPPayProfileID = 0>
				</cfif>
				<cfset local.payProcessFee = 0>
				<cfset local.retainCurrentFeePercent = 0>

				<cfif NOT listFindNoCase("sub,cp,inv",local.referenceType) OR NOT local.referenceID>
					<cfthrow message="Invalid Schedule Payments.">
				</cfif>

				<cfquery name="local.qryCurrentSchedPaymentInfo" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					DECLARE @referenceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">;

					<cfswitch expression="#local.referenceType#">
						<cfcase value="sub">
							SELECT payProfileID AS MPPayProfileID, MPProfileID, payProcessFee
							FROM dbo.sub_subscribers
							WHERE subscriberID = @referenceID;
						</cfcase>
						<cfcase value="cp">
							SELECT payProfileID AS MPPayProfileID, MPProfileID, payProcessFee
							FROM dbo.cp_contributionPayProfiles
							WHERE contributionID = @referenceID;
						</cfcase>
						<cfcase value="inv">
							SELECT payProfileID AS MPPayProfileID, MPProfileID, payProcessFee
							FROM dbo.tr_invoices
							WHERE invoiceID = @referenceID;
						</cfcase>
						<cfdefaultcase>
							SELECT 0 AS MPPayProfileID, 0 AS MPProfileID, 0 AS payProcessFee;
						</cfdefaultcase>
					</cfswitch>
				</cfquery>

				<cfset local.resetPayProcessFee = val(local.qryCurrentSchedPaymentInfo.MPProfileID) NEQ local.newMPProfileID>

				<!--- for the scheduled payment that was acted upon, the passed in payProcessFee flag will be used --->
				<cfif local.thisSchedPayment.keyExists("isSourceSchedPmt") AND local.thisSchedPayment.isSourceSchedPmt AND local.newMPPayProfileID AND local.thisSchedPayment.keyExists("payProcessFee")>
					<cfset local.payProcessFee = local.thisSchedPayment.payProcessFee>
					<cfset local.retainCurrentFeePercent = 0>
				<cfelse>
					<!--- if pay profile has changed, clearing the process fee settings for now --->
					<cfif local.resetPayProcessFee>
						<cfset local.payProcessFee = 0>
					<cfelse>
						<cfset local.payProcessFee = val(local.qryCurrentSchedPaymentInfo.payProcessFee)>
						<cfset local.retainCurrentFeePercent = local.payProcessFee ? 1 : 0>
					</cfif>
				</cfif>
				
				<cfswitch expression="#local.referenceType#">
					<cfcase value="sub">
						<cfstoredproc procedure="sub_updatePaymentProfile" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">
							<cfif local.newMPPayProfileID GT 0>
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.newMPProfileID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.newMPPayProfileID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.payProcessFee#">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.retainCurrentFeePercent#">
							<cfelse>
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
							</cfif>
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
						</cfstoredproc>
					</cfcase>
					<cfcase value="cp">
						<cfstoredproc procedure="cp_updatePaymentProfile" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">
							<cfif local.newMPPayProfileID GT 0>
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.newMPProfileID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.newMPPayProfileID#">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.payProcessFee#">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.retainCurrentFeePercent#">
							<cfelse>
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
								<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
							</cfif>
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
						</cfstoredproc>
					</cfcase>
					<cfcase value="inv">
						<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
							SET XACT_ABORT, NOCOUNT ON;
							BEGIN TRY

								IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
									DROP TABLE ##tmpAuditLog;
								CREATE TABLE ##tmpAuditLog (auditCode varchar(10), msg varchar(max));

								DECLARE @invoiceID int, @orgID int, @siteID int, @existingPayProfileID int, @MPProfileID int, @payProfileID int,
									@existingPayProcessFee bit, @existingProcessFeePercent decimal(5,2), @payProcessFee bit,
									@processFeePercent decimal(5,2), @invoiceNumber varchar(20), @retainCurrentFeePercent bit,
									@enteredByMemberID int;
								SET @enteredByMemberID = <cfqueryparam value="#local.recordedByMemberID#" cfsqltype="CF_SQL_INTEGER">;
								SET @invoiceID = <cfqueryparam value="#local.referenceID#" cfsqltype="CF_SQL_INTEGER">;
								SET @orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
								SET @siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;
								SET @MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newMPProfileID#">;
								SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newMPPayProfileID#">;
								SET @payProcessFee = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.payProcessFee#">;
								SET @retainCurrentFeePercent = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.retainCurrentFeePercent#">;

								SELECT @existingPayProfileID = i.payProfileID, @existingPayProcessFee = i.payProcessFee, 
									@existingProcessFeePercent = i.processFeePercent,
									@invoiceNumber = o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
								FROM dbo.tr_invoices AS i
								INNER JOIN dbo.organizations AS o ON o.orgID = i.orgID
								WHERE i.invoiceID = @invoiceID;

								IF @payProcessFee = 1 BEGIN
									IF @retainCurrentFeePercent = 1
										SELECT @processFeePercent = processFeePercent
										FROM dbo.tr_invoices
										WHERE invoiceID = @invoiceID;
									ELSE
										SELECT @processFeePercent = processFeeDonationFeePercent
										FROM dbo.mp_profiles
										WHERE profileID = @MPProfileID
										AND enableProcessingFeeDonation = 1;
								END

								IF ISNULL(@existingPayProfileID ,0) <> @payProfileID
									INSERT INTO ##tmpAuditLog (auditCode, msg)
									SELECT 'INV', 'Pay Profile ' + 
										CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
											WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
											WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
										END + ' Invoice ' + @invoiceNumber
									FROM dbo.tr_invoices as i
									LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
									LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
									WHERE i.invoiceID = @invoiceID;

								IF ISNULL(@existingPayProcessFee ,0) <> ISNULL(@payProcessFee,0)
									INSERT INTO ##tmpAuditLog (auditCode, msg)
									SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Pay Processing Fees changed from ' + CASE WHEN ISNULL(@existingPayProcessFee ,0) = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN ISNULL(@payProcessFee,0) = 1 THEN 'Yes' ELSE 'No' END;

								IF ISNULL(@existingProcessFeePercent ,0) <> ISNULL(@processFeePercent,0)
									INSERT INTO ##tmpAuditLog (auditCode, msg)
									SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Processing Fee Percentage changed from ' + CAST(ISNULL(@existingProcessFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '%';

								BEGIN TRAN;
									UPDATE dbo.tr_invoices
									SET payProfileID = NULLIF(@payProfileID,0),
										MPProfileID = CASE WHEN @payProfileID > 0 THEN @MPProfileID ELSE NULL END,
										payProcessFee = ISNULL(@payProcessFee,0),
										processFeePercent = @processFeePercent
									WHERE invoiceID = @invoiceID;

									IF EXISTS (SELECT 1 FROM ##tmpAuditLog) BEGIN
										INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
										SELECT '{ "c":"auditLog", "d": {
											"AUDITCODE":"' + auditCode + '",
											"ORGID":' + cast(@orgID as varchar(10)) + ',
											"SITEID":' + cast(@siteID as varchar(10)) + ',
											"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
											"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
											"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
										FROM ##tmpAuditLog;
									END
								COMMIT TRAN;

								IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL
									DROP TABLE ##tmpAuditLog;

							END TRY
							BEGIN CATCH
								IF @@trancount > 0 ROLLBACK TRANSACTION;
								EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
							END CATCH
						</cfquery>
					</cfcase>
				</cfswitch>
			</cfloop>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset local.data.success = false>
			<cfset local.data.errmsg = "Unable to update scheduled payments.">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateProcessFeeDonation" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="false">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="schedPayment" type="string" required="true">
		<cfargument name="MPProfileID" type="numeric" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">
		<cfargument name="payProcessFee" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success": false, "errmsg":"" }>
		<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.mcproxy_orgID)>

		<cftry>
			<cfset local.referenceType = ListGetAt(arguments.schedPayment, 1, "_")>
			<cfset local.referenceID = ListGetAt(arguments.schedPayment, 2, "_")>

			<!--- check if valid update --->
			<cfquery name="local.qryCurrentPayProfile" datasource="#application.dsn.membercentral.dsn#">
				DECLARE @referenceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">, @payProfileID int, @currentValPayProcessFee bit;

				<cfswitch expression="#local.referenceType#">
					<cfcase value="sub">
						SELECT @payProfileID = payProfileID, @currentValPayProcessFee = payProcessFee
						FROM dbo.sub_subscribers
						WHERE subscriberID = @referenceID;
					</cfcase>
					<cfcase value="cp">
						SELECT @payProfileID = payProfileID, @currentValPayProcessFee = payProcessFee
						FROM dbo.cp_contributionPayProfiles
						WHERE contributionID = @referenceID;
					</cfcase>
					<cfcase value="inv">
						SELECT @payProfileID = payProfileID, @currentValPayProcessFee = payProcessFee
						FROM dbo.tr_invoices
						WHERE invoiceID = @referenceID;
					</cfcase>
				</cfswitch>

				SELECT @payProfileID AS payProfileID, @currentValPayProcessFee as currentValPayProcessFee;
			</cfquery>

			<cfif local.qryCurrentPayProfile.payProfileID neq arguments.payProfileID>
				<!--- invalid update --->
				<cfthrow message="invalid update">
			</cfif>

			<cfif local.qryCurrentPayProfile.currentValPayProcessFee eq arguments.payProcessFee>
				<!--- no changes --->
				<cfset local.data.success = true>
				<cfset local.data.errmsg = "no changes">
				<cfreturn local.data>
			</cfif>

			<cfswitch expression="#local.referenceType#">
				<cfcase value="sub">
					<cfstoredproc procedure="sub_updatePaymentProfile" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.payProcessFee#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
					</cfstoredproc>
				</cfcase>
				<cfcase value="cp">
					<cfstoredproc procedure="cp_updatePaymentProfile" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.referenceID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.payProcessFee#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
					</cfstoredproc>
				</cfcase>
				<cfcase value="inv">
					<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
								DROP TABLE ##tmpAuditLog;
							CREATE TABLE ##tmpAuditLog (auditCode varchar(10), msg varchar(max));

							DECLARE @invoiceID int, @orgID int, @siteID int, @MPProfileID int, @payProfileID int,
								@existingPayProcessFee bit, @existingProcessFeePercent decimal(5,2), @payProcessFee bit,
								@processFeePercent decimal(5,2), @invoiceNumber varchar(20), @enteredByMemberID int;
							SET @enteredByMemberID = <cfqueryparam value="#local.recordedByMemberID#" cfsqltype="CF_SQL_INTEGER">;
							SET @invoiceID = <cfqueryparam value="#local.referenceID#" cfsqltype="CF_SQL_INTEGER">;
							SET @orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">;
							SET @siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;
							SET @MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.MPProfileID#">;
							SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">;
							SET @payProcessFee = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.payProcessFee#">;

							SELECT @existingPayProcessFee = i.payProcessFee, 
								@existingProcessFeePercent = i.processFeePercent,
								@invoiceNumber = o.orgCode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber)
							FROM dbo.tr_invoices AS i
							INNER JOIN dbo.organizations AS o ON o.orgID = i.orgID
							WHERE i.invoiceID = @invoiceID;

							IF @payProcessFee = 1
								SELECT @processFeePercent = processFeeDonationFeePercent
								FROM dbo.mp_profiles
								WHERE profileID = @MPProfileID
								AND enableProcessingFeeDonation = 1;

							IF ISNULL(@existingPayProcessFee ,0) <> ISNULL(@payProcessFee,0)
								INSERT INTO ##tmpAuditLog (auditCode, msg)
								SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Pay Processing Fees changed from ' + CASE WHEN ISNULL(@existingPayProcessFee ,0) = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN ISNULL(@payProcessFee,0) = 1 THEN 'Yes' ELSE 'No' END;

							IF ISNULL(@existingProcessFeePercent ,0) <> ISNULL(@processFeePercent,0)
								INSERT INTO ##tmpAuditLog (auditCode, msg)
								SELECT 'INV', 'Invoice ' + @invoiceNumber + ' Processing Fee Percentage changed from ' + CAST(ISNULL(@existingProcessFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '%';

							BEGIN TRAN;
								UPDATE dbo.tr_invoices
								SET payProcessFee = ISNULL(@payProcessFee,0),
									processFeePercent = @processFeePercent
								WHERE invoiceID = @invoiceID;

								IF EXISTS (SELECT 1 FROM ##tmpAuditLog) BEGIN
									INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
									SELECT '{ "c":"auditLog", "d": {
										"AUDITCODE":"' + auditCode + '",
										"ORGID":' + cast(@orgID as varchar(10)) + ',
										"SITEID":' + cast(@siteID as varchar(10)) + ',
										"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
										"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
										"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
									FROM ##tmpAuditLog;
								END
							COMMIT TRAN;

							IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL
								DROP TABLE ##tmpAuditLog;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfcase>
			</cfswitch>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset local.data.success = false>
			<cfset local.data.errmsg = cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : "")>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<!--- ----------------- -------->
	<!--- Buy now functions -------->
	<!--- ----------------- -------->
	<cffunction name="buyNow_parseItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfargument name="strBuyNowReturn" type="struct" required="yes">

		<cfscript>
		var local = structNew();

		// custom template overrides
		arguments.strBuyNowReturn.notIdentifiedTemplate = "NotLoggedIn_inv";
		arguments.strBuyNowReturn.notFoundTemplate = "ItemNotFound_inv";
		
		// setup item parsing
		arguments.strBuyNowReturn.ItemType = arguments.strBuyNow.itemType;				// INV
		if (listLen(arguments.strBuyNow.itemKey,"-") eq 3) {
			arguments.strBuyNowReturn.ItemKey = arguments.strBuyNow.itemKey;					// INV-#invoicenumber#-#partialamount#
			arguments.strBuyNowReturn.ItemID = listGetAt(arguments.strBuyNow.itemKey,2,"-");	// #invoicenumber#
			arguments.strBuyNowReturn.partialPaymentAmt = val(listGetAt(arguments.strBuyNow.itemKey,3,"-"));	// #partialamount#
		} else {
			arguments.strBuyNowReturn.ItemKey = arguments.strBuyNow.itemKey;				// INV-#invoicenumber#
			arguments.strBuyNowReturn.ItemID = listRest(arguments.strBuyNow.itemKey,"-");	// #invoicenumber#
		}
		arguments.strBuyNowReturn.ItemFolder = "invoices";
		arguments.strBuyNowReturn.thisCFC = this;
		</cfscript>

		<!--- make sure invoicenumber is valid and not already paid --->
		<cfquery name="arguments.strBuyNowReturn.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select top 1 i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, ip.allowPartialPayment, 
				m2.memberID, m2.firstname + ' ' + isnull(m2.middlename + ' ','') + m2.lastname as membername, i.invoiceCode,
				sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as AmtDue,
				min(ts.stateIDForTax) as stateIDForTax, min(ts.zipForTax) as zipForTax
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = i.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			inner join dbo.organizations as o on o.orgID = @orgID
			left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
			left outer join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = it.transactionID
			where i.orgID = @orgID
			and ins.status in ('Closed','Delinquent')
			and o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) = <cfqueryparam value="#arguments.strBuyNowReturn.ItemID#" cfsqltype="CF_SQL_VARCHAR">
			group by i.invoiceID, o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber), ip.allowPartialPayment, 
				m2.memberID, m2.firstname + ' ' + isnull(m2.middlename + ' ','') + m2.lastname, i.invoiceCode
			having sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) > 0
		</cfquery>
		<cfif arguments.strBuyNowReturn.qryInvoice.recordcount is 0>
			<cfreturn arguments.strBuyNowReturn>
		</cfif>

		<!--- is partial payment --->
		<cfif structKeyExists(arguments.strBuyNowReturn,"partialPaymentAmt")>
			<cfif arguments.strBuyNowReturn.qryInvoice.allowPartialPayment is 1 and arguments.strBuyNowReturn.partialPaymentAmt gt 0 and arguments.strBuyNowReturn.partialPaymentAmt lte arguments.strBuyNowReturn.qryInvoice.AmtDue>
				<cfset arguments.strBuyNowReturn.isPartialPayment = true>
			<cfelse>
				<cfset arguments.strBuyNowReturn.isPartialPayment = false>
				<cfset structDelete(arguments.strBuyNowReturn,"partialPaymentAmt")>
				<cflocation url="/?pg=buyNow&item=INV-#arguments.strBuyNowReturn.ItemID#" addtoken="false">
			</cfif>
		<cfelse>
			<cfset arguments.strBuyNowReturn.isPartialPayment = false>
		</cfif>

		<!--- merchant profiles allowed --->
		<cfstoredproc procedure="tr_getMerchantProfilesForInvoice" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.strBuyNowReturn.qryInvoice.invoiceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocresult name="local.qryMerchantProfiles" resultset="1">
		</cfstoredproc>

		<cfscript>
		// basics
		arguments.strBuyNowReturn.itemOK = true;
		arguments.strBuyNowReturn.buyNowPageTitle = "Pay Invoice Online";
		arguments.strBuyNowReturn.receiptTitle = "Payment Received";

		// pricing defaults
		arguments.strBuyNowReturn.showPaymentArea = true;		// payment is required unless overridden below
		arguments.strBuyNowReturn.showShippingArea = false;
		arguments.strBuyNowReturn.offerCoupon = false;
		arguments.strBuyNowReturn.noPaymentStatement = "No payment is due for this invoice.";
		arguments.strBuyNowReturn.onReceiptStatement = "";
		arguments.strBuyNowReturn.purchaserTitle = "Payer";

		arguments.strBuyNowReturn.paymentGateways = QueryFilter(local.qryMerchantProfiles, function(thisRow) { return arguments.thisRow.gatewayClass neq 'offline'; });
		</cfscript>
		
		<cfreturn arguments.strBuyNowReturn>
	</cffunction>
	
	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.strResponse = { success=false, response='' }>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcUser.memberData.memberID>
		</cfif>

		<!--- determine payment profileID and profileCode --->
		<cfset arguments.event.paramValue('profileid',0)>
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			select profileID, profileCode, gatewayID, gatewayType, enableProcessingFeeDonation, processFeeDonationFeePercent,
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, processingFeeLabel, 
				enableSurcharge, surchargePercent, surchargeRevenueGLAccountID
			from arguments.strBuyNow.paymentGateways
			where profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfif arguments.strBuyNow.isPartialPayment and arguments.strBuyNow.qryInvoice.allowPartialPayment is 1 and arguments.strBuyNow.partialPaymentAmt gt 0 and arguments.strBuyNow.partialPaymentAmt lte arguments.strBuyNow.qryInvoice.AmtDue>
			<cfset local.paymentAmount = arguments.strBuyNow.partialPaymentAmt>
		<cfelse>
			<cfset local.paymentAmount = arguments.strBuyNow.qryInvoice.AmtDue>
		</cfif>

		<!--- prepare fields for gateway and send --->
		<cfif local.paymentAmount gt 0>

			<!--- get fields --->
			<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
			<cfset local.tmpFields = structNew()>
			<cfloop query="local.qryGatewayProfileFields">
				<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
			</cfloop>

			<!--- get info on file if applicable --->
			<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
			<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
				<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=local.useMID, profileID=local.qryMerchantProfile.profileID)>
				<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
			</cfif>

			<!--- Surcharge / Processing Fee Donation --->
			<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.paymentAmount, 
				stateIDForTax=val(arguments.strBuyNow.qryInvoice.stateIDForTax), zipForTax=arguments.strBuyNow.qryInvoice.zipForTax,
				processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
				surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

			<!--- failed --->
			<cfif NOT local.additionalFeesInfo.success>
				<cfset local.strResponse.response = "Unable to get additional payment fees info.">
				<cfreturn local.strResponse>
			</cfif>

			<cfset local.finalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

			<!--- prepare fields for gateway and send --->
			<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
				profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
				statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.finalAmountToCharge, 
				x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Invoice #arguments.strBuyNow.qryInvoice.invoicenumber#',
				offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>
			
			<!--- apple or google pay token --->
			<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
				<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
			</cfif>

			<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
				<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
				<cfset QueryAddRow(local.qryLevel3Data, {
					"name": "Invoice #arguments.strBuyNow.qryInvoice.invoicenumber#",
					"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Invoice #arguments.strBuyNow.qryInvoice.invoicenumber#",
					"itemPriceExcDiscount": local.paymentAmount,
					"itemPriceIncDiscount": local.paymentAmount,
					"discount": 0,
					"qty": 1,
					"total": local.paymentAmount
				})>
				<cfif local.additionalFeesInfo.additionalFees GT 0>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "#local.additionalFeesInfo.additionalFeesLabel#",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
						"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
						"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
						"discount": 0,
						"qty": 1,
						"total": local.additionalFeesInfo.additionalFees
					})>
					<!--- Surcharge --->
					<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
						<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
					</cfif>
				</cfif>
				<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
			</cfif>
			<cfset structAppend(local.strTemp,local.tmpFields)>
			<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

			<!--- if payment not successful --->
			<cfif local.paymentResponse.responseCode is not 1>
				<cfset local.strResponse.response = local.paymentResponse.publicResponseReasonText>
				<cfreturn local.strResponse>
			</cfif>

			<!--- Surcharge / Record Processing Fee Donation --->
			<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
				<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
					siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.useMID, 
					recordedByMemberID=local.useMID, statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
					GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
					paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
				
				<!--- if not successful --->
				<cfif NOT local.strRecordAdditionalPmtFees.success>
					<cfset local.strResponse.response = "Unable to record additional payment fees.">
					<cfreturn local.strResponse>
				</cfif>

				<cfset local.additionalPaymentFeeInvoiceID = local.strRecordAdditionalPmtFees.invoiceID>
			</cfif>

			<!--- allocate payment --->
			<cfset local.allocateSuccess = false>
			<cfif local.paymentAmount gt 0 and local.paymentResponse.mc_transactionID gt 0>
				<cftry>
					<cfset local.strACCTemp = { recordedOnSiteID=arguments.event.getValue('mc_siteinfo.siteID'), recordedByMemberID=local.useMID, 
												statsSessionID=val(session.cfcUser.statsSessionID), amount=local.paymentAmount,
												transactionDate=now(), paymentTransactionID=local.paymentResponse.mc_transactionID, 
												invoiceID=arguments.strBuyNow.qryInvoice.invoiceID }>
					<cfset local.strACCAllocate = local.objAccounting.allocateToInvoice(argumentcollection=local.strACCTemp)>
					<cfif local.strACCAllocate.rc is not 0>
						<cfthrow message="Failed to allocate payment to invoice">
					<cfelse>
						<cfset local.allocateSuccess = true>
					</cfif>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
					<cfset local.strResponse.response = cfcatch.message>
					<cfreturn local.strResponse>
				</cfcatch>
				</cftry>
			</cfif>

			<cfif local.allocateSuccess>
				<!--- send payment receipt to payer and staff --->
				<cftry>
					<!--- Get Payer Info --->
					<cfset local.qryPurchaser = application.objMember.getMemberInfo(memberid=local.useMID)>
					<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryPurchaser.memberID)>
					
					<cfset local.strResponse.invoiceID = arguments.strBuyNow.qryInvoice.invoiceID>
					<cfset local.strResponse.invoicenumber = arguments.strBuyNow.qryInvoice.invoicenumber>
					<cfsavecontent variable="local.strResponse.emailconfirmationcontent">
						<cfoutput>
							A payment of #dollarFormat(local.paymentAmount)#<cfif local.additionalFeesInfo.additionalFees GT 0> (+ #dollarFormat(local.additionalFeesInfo.additionalFees)# #local.additionalFeesInfo.additionalFeesLabel#)</cfif> toward invoice #arguments.strBuyNow.qryInvoice.invoicenumber# has been received. 
							<br/><br/>
							<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
							<tr>
								<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Details</td>
							</tr>
							<tr>
								<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">
									#dollarFormat(local.finalAmountToCharge)# #local.paymentResponse.transactiondetail# made on #DateFormat(now(),"m/d/yyyy")#
									<br/><br/>
									Paid by 
									<div style="margin-left:20px;margin-top:6px;">
										#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#<br/>
										<cfif len(local.qryPurchaser.company)>#local.qryPurchaser.company#<br/></cfif>
										<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
										<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
										<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
										#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
									</div>
									<br/>
								</td>
							</tr>
							</table>
						</cfoutput>
					</cfsavecontent>

					<cfset local.invoiceIDList = arguments.strBuyNow.qryInvoice.invoiceID>
					<cfif local.keyExists("additionalPaymentFeeInvoiceID")>
						<cfset local.invoiceIDList = listAppend(local.invoiceIDList,local.additionalPaymentFeeInvoiceID)>
					</cfif>

					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="inv")>
					<cfset local.arrInvoicePaths = arrayNew(1)>
					<cfset local.objInvoice = CreateObject("component","model.admin.transactions.invoice")>
					<cfloop list="#local.invoiceIDList#" index="local.thisInvoiceID">
						<cfset local.strInvoice = local.objInvoice.generateInvoice(siteID=arguments.event.getValue('mc_siteinfo.siteid'), invoiceID=local.thisInvoiceID, tmpFolder=local.strFolder.folderPath, encryptFile=true, namedForBundle=false)>
						<cfset arrayAppend(local.arrInvoicePaths, { file:local.strInvoice.displayName, folderpath:local.strFolder.folderPath })>
					</cfloop>


					<!--- store receipt in session so it can be sent and resent --->
					<cfset local.strResponse.resendKey = createUUID()>
					<cfset local.tmpStr = { 
						"siteID"=arguments.event.getValue('mc_siteinfo.siteID'), 
						"invoiceID"=arguments.strBuyNow.qryInvoice.invoiceID, 
						"emailconfirmationcontent"=local.strResponse.emailconfirmationcontent,
						"arrInvoicePaths"=local.arrInvoicePaths
					}>
					<cfset local.strInvoiceReceipts = application.mcCacheManager.sessionGetValue(keyname='strInvoiceReceipts', defaultValue={})>
					<cfset structInsert(local.strInvoiceReceipts, local.strResponse.resendKey, local.tmpStr)>
					<cfset application.mcCacheManager.sessionSetValue(keyname='strInvoiceReceipts', value=local.strInvoiceReceipts)>
					
					<cfset local.strEmailConfirmation = generatePaymentConfirmationEmail(siteID=arguments.event.getValue('mc_siteinfo.siteID'), invoiceID=arguments.strBuyNow.qryInvoice.invoiceID, emailMode="Payer")>
					<cfset local.strResponse.emailTitle = local.strEmailConfirmation.emailTitle>
					<cfif arrayLen(local.strEmailConfirmation.mailCollection["to"])>
						<cfset local.strResponse.emailSentTo = local.strEmailConfirmation.mailCollection["to"][1].email>
						<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
							emailto=local.strEmailConfirmation.mailCollection["to"],
							emailreplyto=local.strEmailConfirmation.mailCollection["replyto"],
							emailsubject=local.strEmailConfirmation.mailCollection["subject"],
							emailtitle=local.strEmailConfirmation.emailTitle,
							emailhtmlcontent=local.strResponse.emailconfirmationcontent,
							emailAttachments=local.arrInvoicePaths,
							siteID=arguments.event.getValue('mc_siteinfo.siteid'),
							memberID=local.strEmailConfirmation.memberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EMAILINV"),
							sendingSiteResourceID=arguments.event.getValue('mc_siteInfo.siteSiteResourceID')
						)>
					</cfif>
					
					<cfset local.strEmailConfirmation = generatePaymentConfirmationEmail(siteID=arguments.event.getValue('mc_siteinfo.siteID'), invoiceID=arguments.strBuyNow.qryInvoice.invoiceID, emailMode="Staff")>
					<cfif arrayLen(local.strEmailConfirmation.mailCollection["to"])>
						<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
							emailto=local.strEmailConfirmation.mailCollection["to"],
							emailreplyto=local.strEmailConfirmation.mailCollection["replyto"],
							emailsubject=local.strEmailConfirmation.mailCollection["subject"],
							emailtitle=local.strEmailConfirmation.emailTitle,
							emailhtmlcontent=local.strResponse.emailconfirmationcontent,
							emailAttachments=local.arrInvoicePaths,
							siteID=arguments.event.getValue('mc_siteinfo.siteid'),
							memberID=arguments.event.getValue('mc_siteInfo.sysmemberid'),
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EMAILINV"),
							sendingSiteResourceID=arguments.event.getValue('mc_siteInfo.siteSiteResourceID')
						)>
					</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
				</cfcatch>
				</cftry>
			</cfif>
		</cfif>

		<cfset local.strResponse.success = true>

		<cfreturn local.strResponse>
	</cffunction>	
	
	<cffunction name="buyNow_receipt" access="public" output="no" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="decryptEncStringListPayProfiles" access="public" output="false" returntype="struct">
		<cfargument name="encString" type="string" required="true">
		<cfscript>
			var local = structNew();
			try {
				local.decryptString = decrypt(arguments.encString,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex");
				local.requestInfo = deserializeJSON(local.decryptString);
			} catch(any e){
				local.requestInfo = structNew();
			}
			return local.requestInfo;
		</cfscript>
	</cffunction>

	<cffunction name="generatePaymentConfirmationEmail" access="private" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="invoiceID" type="numeric" required="yes">
		<cfargument name="emailMode" type="string" required="yes" hint="Payer or Staff">
		<cfargument name="toOverride" type="string" required="no" default="">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
			select o.orgcode + dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber, ip.notifyEmail, 
				m2.memberid, m2.firstname, m2.middlename, m2.lastname, s.sitecode
			from dbo.tr_invoices as i 
			inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
			inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			inner join dbo.sites as s on s.orgID = o.orgID and s.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			where i.invoiceID = <cfqueryparam value="#arguments.invoiceID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryInvoice.sitecode)>
		<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.qryInvoice.memberID)>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.mc_siteInfo.defaultOrgIdentityID)>

		<cfset local.returnStruct.emailTitle = "#local.mc_siteInfo.sitename# Payment Confirmation">
		<cfset local.returnStruct.memberID = local.qryInvoice.memberID>

		<!--- mail collection --->
		<cfscript>
		local.returnStruct.mailCollection = structNew();

		if (arguments.emailMode eq "Payer") {
			local.returnStruct.mailCollection["from"] = local.mc_siteInfo.networkEmailFrom & ' ("' & local.mc_siteinfo.orgname & '")';
			local.returnStruct.mailCollection["replyto"] = local.qryOrgIdentity.email;
		} else if (arguments.emailMode eq "Staff") {
			local.returnStruct.mailCollection["from"] = local.mc_siteInfo.networkEmailFrom & ' ("#local.qryInvoice.firstName# #local.qryInvoice.lastname#")';
			if (len(local.qryMainEmail.email))
				local.returnStruct.mailCollection["replyto"] = local.qryMainEmail.email;
			else
				local.returnStruct.mailCollection["replyto"] = local.qryOrgIdentity.email;
		}
		local.returnStruct.mailCollection["subject"] = 'Payment Confirmation of Invoice #local.qryInvoice.invoiceNumber#';
		local.returnStruct.mailCollection["mailerid"] = local.mc_siteinfo.sitename;

		local.emailTo = "";
		if (arguments.emailMode eq "Payer") {
			if (len(arguments.toOverride))
				local.emailTo = arguments.toOverride;
			else
				local.emailTo = local.qryMainEmail.email;
		} else if (arguments.emailMode eq "Staff") {
			local.emailTo = local.qryInvoice.notifyEmail;
		}

		local.returnStruct.mailCollection["to"] = [];
		if (len(local.emailTo)) {
			local.emailTo = replace(replace(local.emailTo,',',';','ALL'),' ','','ALL');
			local.toEmailArr = listToArray(local.emailTo,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				if (len(local.toEmailArr[local.i]) and isValid("regex",local.toEmailArr[local.i],application.regEx.email)) {
					local.returnStruct.mailCollection["to"].append({ name:'', email:local.toEmailArr[local.i] });
				}
			}
		}
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendPaymentReceipt" access="public" output="false" returntype="struct">
		<cfargument name="receiptUUID" type="string" required="true">
		<cfargument name="sendToEmail" type="string" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.strInvoiceReceipts = application.mcCacheManager.sessionGetValue(keyname='strInvoiceReceipts', defaultValue={})>
			<cfif isStruct(local.strInvoiceReceipts) and structKeyExists(local.strInvoiceReceipts,arguments.receiptUUID)>
				<cfset local.strReceipt = local.strInvoiceReceipts[arguments.receiptUUID]>
				<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode)>
				<cfset local.strEmailConfirmation = generatePaymentConfirmationEmail(siteID=local.strReceipt.siteID, invoiceID=local.strReceipt.invoiceID, emailMode="Payer", toOverride=arguments.sendToEmail)>
				<cfif len(arguments.sendToEmail) and arrayLen(local.strEmailConfirmation.mailCollection["to"])>
					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.mc_siteInfo.orgname, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.strEmailConfirmation.mailCollection["to"],
						emailreplyto=local.strEmailConfirmation.mailCollection["replyto"],
						emailsubject=local.strEmailConfirmation.mailCollection["subject"],
						emailtitle=local.strEmailConfirmation.emailTitle,
						emailhtmlcontent=local.strReceipt.emailconfirmationcontent,
						emailAttachments=local.strReceipt.arrInvoicePaths,
						siteID=local.mc_siteInfo.siteid,
						memberID=local.strEmailConfirmation.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EMAILINV"),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					)>
					<cfset local.data.success = true>
				<cfelse>
					<cfthrow message="Recipient not valid.">
				</cfif>
			<cfelse>
				<cfthrow message="Receipt not found.">
			</cfif>
		<cfcatch type="Any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getInvoiceAppContent" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="contentTitle" type="string" required="true">

		<cfset var qryContent = "">
		
		<cfquery name="qryContent" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@invoiceAppSRID int, @contentID int;
			
			SELECT @invoiceAppSRID = dbo.fn_getSiteResourceIDForResourceType('Invoices',@siteID);

			SELECT @contentID = c.contentID
			FROM dbo.cms_content AS c
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
				AND sr.siteResourceID = c.siteResourceID
				AND sr.parentSiteResourceID = @invoiceAppSRID
				AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.cms_contentLanguages AS cl ON c.contentID = cl.contentID
				AND cl.languageID = 1
				AND cl.contentTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentTitle#">
			WHERE c.siteID = @siteID;
			
			SELECT contentID, rawContent 
			FROM dbo.fn_getContent(@contentID,1);
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryContent>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>Pay Invoice Online</h4>
				<cfif arguments.event.valueExists('message')>
					<p class="tsAppBodyText">
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>The link you accessed is not valid. Contact your association for assistance.</b></cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

</cfcomponent>
