<cfoutput>
<cfset local.freeSubIDList = "">
<cfloop from="1" to="#arrayLen(local.arrSubs)#" index="local.i">
	<cfset local.thisSub = duplicate(local.arrSubs[local.i])>
	<cfset local.hasSubAddOns = structKeyExists(local.thisSub,'addOns') AND StructCount(local.thisSub.addOns)>
	<cfset local.hasSingleRate = local.thisSub.qryRates.recordCount EQ 1>
	<cfset local.offerAsFreeAddOnSub = false>
	<cfset local.isAlreadySubscribed = structKeyExists(arguments,'listSubscribed') ? listFind(arguments.listSubscribed, local.thisSub.subscriptionID) gt 0 : false>
	
	<!--- Add On Sub --->
	<cfif structCount(arguments.strAddOn)>
		<cfset local.setID = arguments.strAddOn.setID>
		<cfset local.addOnID = arguments.strAddOn.addOnID>
		<cfset local.subInputFieldName = "subAddOn#arguments.strAddOn.addOnID#">
		<cfset local.subInputFieldID = "subAddOn#arguments.strAddOn.addOnID#_#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldType = arguments.strAddOn.maxAllowed NEQ 1 ? "checkbox" : "radio">
		<cfset local.thisSubSelected = structKeyExists(local.thisSub,'isSelected') and local.thisSub.isSelected>
		<cfset local.ratePctOff = val(arguments.strAddOn.PCPctOffEach)>
		<cfset local.freeSubsCount = val(arguments.strAddOn.PCNum)>
	
	<!--- Root Sub --->
	<cfelse>
		<cfset local.setID = 0>
		<cfset local.addOnID = 0>
		<cfset local.subInputFieldName = "sub#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldID = "sub#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldType = "checkbox">
		<cfset local.thisSubSelected = true>
		<cfset local.ratePctOff = 0>
		<cfset local.freeSubsCount = 0>
	</cfif>

	<!--- single sub rate display vars --->
	<cfif local.hasSingleRate>		
		<cfset local.rateNameForDisplay = local.thisSub.qryRates.rateName>
		<cfset local.rateAmtDisplay = "">
		<cfset local.rateNumInstallments = local.thisSub.qryRates.numInstallments>
		
		<cfif arguments.parentSubscriptionID EQ 0>
			<cfset local.numPaymentsToUse = local.thisSub.qryRates.numInstallments>
			<cfset local.rateFreqName = local.thisSub.qryRates.frequencyName>
			<cfset local.rateFreqShortName = local.thisSub.qryRates.frequencyShortName>
		<cfelse>
			<cfset local.numPaymentsToUse = arguments.strParentFreq.numPaymentsToUse>
			<cfset local.rateFreqName = arguments.strParentFreq.frequencyName>
			<cfset local.rateFreqShortName = arguments.strParentFreq.frequencyShortName>
		</cfif>

		<cfset local.strRateAmt = manageSubscription_getRateToUse(rateAmt=local.thisSub.qryRates.rateAmt,
			rateInstallments=local.rateNumInstallments, numPaymentsToUse=local.numPaymentsToUse, pcPctOff=local.ratePctOff)>
		
		<cfif local.strRateAmt.rateToUse GT 0>
			<cfset local.rateAmtDisplay = dollarFormat(local.strRateAmt.rateToUse)>
		<cfelseif len(arguments.freeRateDisplay)>
			<cfset local.rateAmtDisplay = arguments.freeRateDisplay>
		</cfif>
	</cfif>

	<!--- free sub? --->
	<cfif local.thisSubSelected AND arguments.strEditSubs.keyExists(local.thisSub.subscriptionID) AND arguments.strEditSubs[local.thisSub.subscriptionID].keyExists('PCFree') AND arguments.strEditSubs[local.thisSub.subscriptionID].PCFree EQ 1>
		<cfset local.offerAsFreeAddOnSub = true>
		<cfset local.freeSubIDList = listAppend(local.freeSubIDList,local.thisSub.subscriptionID)>
	</cfif>

	<!--- Subscription --->
	<div class="d-flex align-items-center no-gutters mb-2 flex-wrap">
		<input type="#local.subInputFieldType#" 
			name="#local.subInputFieldName#" 
			id="#local.subInputFieldID#" 
			value="#local.thisSub.subscriptionID#" 
			data-subscriptionname="#encodeForHTMLAttribute(local.thisSub.subscriptionname)#" 
			data-setid="#local.setID#"
			data-addonid="#local.addOnID#"
			data-parentsubscriptionid="#arguments.parentSubscriptionID#"
			<cfif local.offerAsFreeAddOnSub>
				data-isfreeaddonsub="1"
			</cfif>
			<cfif arguments.parentSubscriptionID EQ 0>
				class="subSelector d-none" 
				checked
			<cfelse>
				onchange="chooseSub(this);"
				class="my-auto mr-1 subSelector" 
				<cfif local.thisSubSelected> checked</cfif>
			</cfif>
			data-issubscribed="#int(local.isAlreadySubscribed)#"
			<cfif local.isAlreadySubscribed> disabled title="Already Subscribed"</cfif>
		>
		<cfif local.addOnID GT 0>
			<div class="<cfif local.hasSingleRate>col-auto<cfelse>col</cfif> pl-2">
				<label for="#local.subInputFieldID#" class="d-inline-block mb-0<cfif local.isAlreadySubscribed> text-dim</cfif>"<cfif local.isAlreadySubscribed> title="Already Subscribed"</cfif>>
					#local.thisSub.subscriptionname#
					<cfif local.hasSingleRate AND len(local.rateAmtDisplay)>
						<span id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#_rateAmtDisp" class="addOnSub#local.thisSub.subscriptionID#SingleRateLabel addOnSubSingleRateLabel<cfif NOT local.thisSubSelected OR local.offerAsFreeAddOnSub> d-none</cfif>">
							- #local.rateAmtDisplay#
							<cfif local.strRateAmt.rateToUse GT 0 AND local.rateFreqShortName NEQ 'F'>
								#local.rateFreqName#
							</cfif>
						</span>
					</cfif>
				</label>
			</div>
		</cfif>
	</div>

	<!--- Subscription Rates --->
	<cfif local.hasSingleRate>
		<cfset local.strRate = duplicate(local.thisSub.qryRates)>

		<cfif arguments.parentSubscriptionID EQ 0>
			<cfset local.subTermDatesCD = getSubTermDates(termDateRFID=local.strRate.rfid, subTermFlag=local.thisSub.rateTermDateFlag)>
			<cfset local.subTermStartDate = local.subTermDatesCD.subTermStartDate>
			<cfset local.subTermEndDate = local.subTermDatesCD.subTermEndDate>
			<cfset local.subTermGraceEndDate = local.subTermDatesCD.subTermGraceEndDate>
		<cfelse>
			<cfset local.subTermStartDate = local.strRate.termAFStartDate>
			<cfset local.subTermEndDate = local.strRate.termAFEndDate>
			<cfset local.subTermGraceEndDate = local.strRate.graceEndDate>
		</cfif>
		
		<input type="radio" 
			name="sub#local.thisSub.subscriptionID#_rfid" 
			id="sub#local.thisSub.subscriptionID#_rfid_#local.strRate.rfid#" 
			class="subRateRadio d-none"
			value="#local.strRate.rfid#" 
			data-rateid="#val(local.strRate.rateID)#" 
			data-freqid="#val(local.strRate.frequencyID)#" 
			data-freq="#local.rateFreqShortName#"
			data-freqname="#local.rateFreqName#"
			data-price="#val(local.strRateAmt.rateToUse)#" 
			data-termprice="#val(local.strRateAmt.rateTotal)#" 
			rate-subscriptionid="#local.thisSub.subscriptionID#" 
			data-linkedsubinputid="#local.subInputFieldID#"
			data-ratename="#encodeForHTMLAttribute(local.strRate.rateName)#"
			data-rateinstallments="#local.numPaymentsToUse#"
			data-termstartdate="#dateformat(local.subTermStartDate,'m/d/yyyy')#"
			data-termenddate="#dateformat(local.subTermEndDate,'m/d/yyyy')#"
			data-graceenddate="#dateformat(local.subTermGraceEndDate,'m/d/yyyy')#"
			data-recogstartdate="#dateformat(local.strRate.recogAFStartDate,'m/d/yyyy')#"
			data-recogenddate="#dateformat(local.strRate.recogAFEndDate,'m/d/yyyy')#"
			checked>
	<cfelse>
		<cfquery name="local.strRateFreqCount" dbtype="query" returntype="struct" columnKey="rateID">
			SELECT rateID, MAX(rateFreqOrder) AS freqCount
			FROM [local].thisSub.qryRates
			GROUP BY rateID
		</cfquery>

		<div id="sub#local.thisSub.subscriptionID#_rates" class="mb-3 <cfif arguments.parentSubscriptionID GT 0>pl-5<cfelse>pl-1</cfif>">
			<cfloop query="local.thisSub.qryRates">
				<cfset local.numPaymentsToUse = arguments.parentSubscriptionID EQ 0 ? local.thisSub.qryRates.numInstallments : arguments.strParentFreq.numPaymentsToUse>
				<cfset local.rateFreqName = local.thisSub.qryRates.frequencyName>
				<cfset local.rateFreqShortName = local.thisSub.qryRates.frequencyShortName>
				<cfset local.rateNumInstallments = local.thisSub.qryRates.numInstallments>

				<cfset local.strRateAmt = manageSubscription_getRateToUse(rateAmt=local.thisSub.qryRates.rateAmt,
					rateInstallments=local.rateNumInstallments, numPaymentsToUse=local.numPaymentsToUse, pcPctOff=local.ratePctOff)>

				<cfif arguments.parentSubscriptionID EQ 0>
					<cfset local.subTermDatesCD = getSubTermDates(termDateRFID=local.thisSub.qryRates.rfid, subTermFlag=local.thisSub.rateTermDateFlag)>
					<cfset local.subTermStartDate = local.subTermDatesCD.subTermStartDate>
					<cfset local.subTermEndDate = local.subTermDatesCD.subTermEndDate>
					<cfset local.subTermGraceEndDate = local.subTermDatesCD.subTermGraceEndDate>
				<cfelse>
					<cfset local.subTermStartDate = local.thisSub.qryRates.termAFStartDate>
					<cfset local.subTermEndDate = local.thisSub.qryRates.termAFEndDate>
					<cfset local.subTermGraceEndDate = local.thisSub.qryRates.graceEndDate>
				</cfif>
				
				<div class="d-flex align-items-center no-gutters mb-2 flex-wrap sub#local.thisSub.subscriptionID#_rateWrapper">
					<input type="radio" 
						name="sub#local.thisSub.subscriptionID#_rfid" 
						id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#" 
						value="#local.thisSub.qryRates.rfid#" 
						onchange="chooseRate(this)" 
						data-rateid="#val(local.thisSub.qryRates.rateID)#" 
						data-freqid="#val(local.thisSub.qryRates.frequencyID)#" 
						data-freq="#local.rateFreqShortName#"
						data-freqname="#local.rateFreqName#"
						data-price="#val(local.strRateAmt.rateToUse)#" 
						data-termprice="#val(local.strRateAmt.rateTotal)#" 
						rate-subscriptionid="#local.thisSub.subscriptionID#" 
						data-linkedsubinputid="#local.subInputFieldID#"
						data-ratename="#encodeForHTMLAttribute(local.thisSub.qryRates.rateName)#"
						data-rateinstallments="#local.numPaymentsToUse#"
						data-termstartdate="#dateformat(local.subTermStartDate,'m/d/yyyy')#"
						data-termenddate="#dateformat(local.subTermEndDate,'m/d/yyyy')#"
						data-graceenddate="#dateformat(local.subTermGraceEndDate,'m/d/yyyy')#"
						data-recogstartdate="#dateformat(local.thisSub.qryRates.recogAFStartDate,'m/d/yyyy')#"
						data-recogenddate="#dateformat(local.thisSub.qryRates.recogAFEndDate,'m/d/yyyy')#"
						<cfif arguments.parentSubscriptionID GT 0 and local.thisSubSelected and local.thisSub.qryRates.currentRow eq 1>checked</cfif> <!--- simulating addon selection action by auto selecting first option if addon sub is already selected --->
						class="subRateRadio my-auto mr-2">
					
					<div class="mcsubs-col">
						<label for="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#" class="d-inline-block mb-0">
							#local.thisSub.qryRates.rateName#
							<span id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#_rateAmtDisp" class="subRateAmtDisp sub#local.thisSub.subscriptionID#RateAmtDisp<cfif local.offerAsFreeAddOnSub> d-none</cfif>">
								<cfif local.strRateAmt.rateToUse GT 0>
									- #dollarFormat(local.strRateAmt.rateToUse)#
								<cfelseif len(arguments.freeRateDisplay)>
									- #arguments.freeRateDisplay#
								</cfif>
								<cfif local.rateFreqShortName NEQ 'F'>
									#local.rateFreqName#
								</cfif>
							</span>
						</label>
					</div>
				</div>
			</cfloop>
		</div>
	</cfif>
	
	<!--- Subscription AddOns --->
	<cfif local.hasSubAddOns AND local.thisSub.currAddOnRecursionLevel + 1 EQ local.thisSub.maxAddOnRecursionLevel>
		<div id="sub#local.thisSub.subscriptionID#_addons" class="pt-2 d-none">
			#manageSubscription_renderAddOnForm(strSubAddOns=local.thisSub.addOns, subscriptionID=local.thisSub.subscriptionID, strParentFreq=arguments.strParentFreq,
				freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, inlineAddOn=true, listSubscribed=arguments.listSubscribed)#
		</div>
	</cfif>
</cfloop>

<cfif structCount(arguments.strAddOn)>
	<input type="hidden" name="addOn#arguments.strAddOn.addOnID#_freeSubs" id="addOn#arguments.strAddOn.addOnID#_freeSubs" value="#local.freeSubIDList#">
</cfif>
</cfoutput>