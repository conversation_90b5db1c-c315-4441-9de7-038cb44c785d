<cfsavecontent variable="local.searchAppJS">
	<cfoutput>
	<script type="text/javascript">
		function gotoBKContainer(bkContainerID) {
			let bkContainer = $('##'+bkContainerID);
			if(bkContainer.length && bkContainer.is(':visible')) {
				$('html, body').animate({
					scrollTop: bkContainer.offset().top - 300
				}, 750);
			}
		}
		function bk_slideToggle(id,p,el) {
			$('##'+id).slideToggle(500);
			gotoBKContainer(id);
			$(el).find('.mc-arrow-down').toggle();
			$(el).find('.mc-arrow-up').toggle();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.searchAppJS,'\s{2,}',' ','ALL')#">