<cfcomponent extends="Bucket">

	<cfset variables.thisBucketTypeID = 48>
	<cfset variables.thisBucketType = "daubertStudy">
	<cfset variables.thisBucketCartItemTypeID = 0> <!--- ignored --->
	<cfset variables.thisBucketMaxPerPage = 10>
	<cfset variables.thisBucketMaxShown = 10000>
	<cfset variables.reportTypes = {
		"expChallengeStudy": { "name": "Expert Challenge Study", "price": 175, "acctcode": 5029, "timeframe": "1 to 3 business days", "orderTitle": "Daubert Challenge Study Order" },
		"expProfilerRpt": { "name": "Expert Profiler Report", "price": 625, "acctcode": 5016, "timeframe": "10 business days", "orderTitle": "Expert Knowledge Map Order" }
	}>

	<cffunction name="showHeader" access="private" output="false" returntype="string">
		<cfargument name="bucketName" type="string" required="yes">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var header = "">
		
		<cfsavecontent variable="header">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/daubertStudy/header.cfm">
		</cfsavecontent>

		<cfreturn header>
	</cffunction>

	<cffunction name="showSearchForm" access="public" output="false" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchID" required="no" type="numeric" default="0">
		<cfargument name="viewDirectory" type="string" required="no" default="default">
		
		<cfset var local = StructNew()>
		<cfset local.qryBucketInfo = getBucketInfo(arguments.bucketID)>
		
		<!--- load search if passed in --->
		<cfset local.strSearchForm = prepSearchForSearchForm(searchID=arguments.searchID, bucketID=arguments.bucketID)>
		
		<!--- show common JS --->
		<cfset showCommonJS(bucket=variables.thisBucketType, bucketID=arguments.bucketID, viewDirectory=arguments.viewDirectory)>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/commonSearchFormJS.cfm">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/daubertStudy/searchForm.cfm">
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="prepSearchForSearchForm" access="private" returntype="struct" output="no" hint="parses the searchXML and populates search form">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
	
		<cfset var local = structNew()>
		
		<cfscript>
		local.returnStruct = StructNew();

		if (arguments.searchID gt 0) {
			// convert origin searchXML to bucket searchXML
			local.searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID);

			// read/clean from xml
			local.returnStruct.s_fname = replace(local.searchXML.search["s_fname"].xmlText,chr(34),'','ALL');
			local.returnStruct.s_lname = replace(local.searchXML.search["s_lname"].xmlText,chr(34),'','ALL');
		} else {
			local.returnStruct.s_fname = '';
			local.returnStruct.s_lname = '';
		}

		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="prepSearch" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var searchXML = application.objSearchTranslate.convertSearchXML(arguments.searchID,variables.thisBucketTypeID)>
		
		<cfreturn prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=searchXML)>
	</cffunction>

	<cffunction name="prepSearchFromXML" access="private" returntype="struct" output="no" hint="parses the searchXML and prepares search criteria">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="searchXML" required="yes" type="xml">
	
		<cfset var local = structNew()>
		
		<cfscript>
		// get bucket info to get any restrictions
		local.qryBucketInfo = getBucketInfo(arguments.bucketID);

		// read/clean from xml
		local.s_fname = arguments.searchXML.search["s_fname"].xmlText;
		local.s_lname = arguments.searchXML.search["s_lname"].xmlText;

		// return search struct
		local.returnStruct = structNew();
		structInsert(local.returnStruct,"expertfname","");
		structInsert(local.returnStruct,"expertlname","");

		//prepare expertname keywords
		local.keywordsexpertname = "";
		if (Len(local.s_fname)){
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_fname,chr(7));
			local.returnStruct.expertfname = replace(local.s_fname,chr(34),'','ALL');
		}
		if (Len(local.s_lname)){
			local.keywordsexpertname = listAppend(local.keywordsexpertname,local.s_lname,chr(7));
			local.returnStruct.expertlname = replace(local.s_lname,chr(34),'','ALL');
		}

		// do i have enough criteria to run a search?
		if (not len(local.keywordsexpertname))
			structInsert(local.returnStruct,"searchAccepted",false);
		else
			structInsert(local.returnStruct,"searchAccepted",true);
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResultsCount" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["itemcount"] = 'N/A'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResultsCount(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">

		<cfset var local = StructNew()>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
			<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=-1)>
		<cfelse>
			<cfset local.cachedItemCount = getCachedBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID)>
			<cfif local.cachedItemCount gte 0>
				<cfset StructInsert(local.returnStruct,"itemcount",local.cachedItemCount)>
				<cfset StructInsert(local.returnStruct,"success",true)>
			<cfelse>
				<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
				<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResultsCount',local.returnStruct.ExecutionTime,local.returnStruct.itemCount)>
				<cfset saveBucketCount(searchID=arguments.searchID,bucketID=arguments.bucketID,itemCount=local.returnStruct.itemCount)>
				<!--- Remove structkeys not expected by caller --->
				<cfset structDelete(local.returnStruct, "ExecutionTime")/>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>

	<cffunction name="getResultsCountForSearchIndex" access="public" output="no" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="firstName" required="true" type="string">
		<cfargument name="lastName" required="true" type="string">

		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.searchXML">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid>#val(arguments.bucketID)#</bid>
				<s_fname>#xmlFormat(trim(replace(arguments.firstName,chr(34),'','ALL')))#</s_fname>
				<s_lname>#xmlFormat(trim(replace(arguments.lastName,chr(34),'','ALL')))#</s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>
		<cfset local.searchXML = XMLParse(local.searchXML)>
		
		<cfset local.strSearch = prepSearchFromXML(siteID=arguments.siteID,bucketID=arguments.bucketID,searchXML=local.searchXML)>
		<cfset local.returnStruct = StructNew()>
		<cfif NOT local.strSearch.searchAccepted>
			<cfset StructInsert(local.returnStruct,"itemcount",'N/A')>
			<cfset StructInsert(local.returnStruct,"success",true)>
		<cfelse>
			<cfset local.returnStruct = runResultsCount(siteID=arguments.siteID,bucketID=arguments.bucketID,strSearch=local.strSearch)>
		</cfif>
		<cfreturn local.returnStruct/>
	</cffunction>

	<cffunction name="runResultsCount" access="private" returntype="struct" output="no" hint="searches and returns a count">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="strSearch" required="yes" type="struct">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>
		
		<cfset local.returnStruct = StructNew()>

		<cfset local.strPayload = {
			"columnFilters": {"first_name": "", "last_name": ""},
			"sort": {"field": "", "type": ""}
		}>

		<cfif len(arguments.strSearch.expertfname)>
			<cfset local.strPayload.columnFilters["first_name"] = arguments.strSearch.expertfname>
		</cfif>
		<cfif len(arguments.strSearch.expertlname)>
			<cfset local.strPayload.columnFilters["last_name"] = arguments.strSearch.expertlname>
		</cfif>
		
		<cfset local.executionTime = getTickCount()>
		<cfset local.strAPIResponse = callAPI(payload=serializeJSON(local.strPayload))>
		<cfset local.executionTime = getTickCount() - local.executionTime>
		
		<!--- count up the number of challenges for the entire search, with 1 as a minimum for each expert --->
		<cfset local.totalCount = 0>
		<cfif NOT StructIsEmpty(local.strAPIResponse.strResult) and StructKeyExists(local.strAPIResponse.strResult, "results")>
			<cfset local.totalCount = arrayReduce(local.strAPIResponse.strResult.results, function(prev, datael) {
				var prevCount = arguments.prev;
				if (structKeyExists(arguments.datael,"cases") and structKeyExists(arguments.datael.cases,"total_challenges"))
					prevCount = prevCount + arguments.datael.cases["total_challenges"];
				else 
					prevCount++;
				return prevCount;
			}, local.totalCount)>
		</cfif>

		<cfset StructInsert(local.returnStruct,"executionTime",local.executionTime)>
		<cfset StructInsert(local.returnStruct,"itemcount",local.totalCount)>
		<cfset StructInsert(local.returnStruct,"success",true)>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getResults" access="public" returntype="struct" output="no" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="mcproxy_siteID" required="no">
		<cfargument name="searchID" required="no">
		<cfargument name="bucketID" required="no">
		<cfargument name="startRow" required="no">
		<cfargument name="sortType" required="no">
		<cfargument name="viewDirectory" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["errmsg"] = 'Invalid Request'>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "mcproxy_siteID") OR NOT IsNumeric(arguments.mcproxy_siteID)) OR
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID)) OR
			(NOT StructKeyExists(arguments, "bucketID") OR NOT IsNumeric(arguments.bucketID)) OR
			(NOT StructKeyExists(arguments, "startRow") OR NOT IsNumeric(arguments.startRow)) OR
			NOT StructKeyExists(arguments, "sortType") OR
			(NOT StructKeyExists(arguments, "viewDirectory") OR NOT ListFind(variables.viewDirectories, arguments.viewDirectory))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = doGetResults(siteID=arguments.mcproxy_siteID, searchID=arguments.searchID,
				bucketID=arguments.bucketID, startrow=arguments.startrow, sortType=arguments.sortType, viewDirectory=arguments.viewDirectory)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="doGetResults" access="private" returntype="struct" output="no" hint="searches and returns a query result">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="startRow" required="yes" type="numeric">
		<cfargument name="sortType" required="yes" type="string">
		<cfargument name="viewDirectory" required="yes" type="string">

		<cfset var local = StructNew()>

		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfset local.strSearch = prepSearch(siteID=arguments.siteID,searchID=arguments.searchID,bucketID=arguments.bucketID)>
		<cfset local.qryBucketInfo = getBucketInfo(bucketID=arguments.bucketID)>
		<cfset local.returnStruct = StructNew()>

		<cfif NOT local.strSearch.searchAccepted>
			<cfreturn showSearchNotAccepted(searchID=arguments.searchID,bucketID=arguments.bucketID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)>
		<cfelseif NOT variables.cfcuser_isSiteAdmin AND val(local.qryBucketInfo.restrictToGroupID) GT 0 AND local.qryBucketInfo.isMemberInRestrictedGroup NEQ 1>
			<cfreturn showNotAllowed(siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID, bucketName=local.qryBucketInfo.bucketName, 
				accessDeniedMessage=local.qryBucketInfo.accessDeniedMessage, viewDirectory=arguments.viewDirectory)>
		<cfelse>
			<cfset local.totalChallengeCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID).itemCount>
			<cfset local.NumCurrentPage = int((int(arguments.startrow) + variables.thisBucketMaxPerPage - 1) / variables.thisBucketMaxPerPage)>

			<cfset local.strPayload = {
				"columnFilters": {"first_name": "", "last_name": ""},
				"sort": {"field": "full_name", "type": "asc"},
				"page": local.NumCurrentPage,
				"perPage": variables.thisBucketMaxPerPage
			}>

			<cfif len(local.strSearch.expertfname)>
				<cfset local.strPayload.columnFilters["first_name"] = local.strSearch.expertfname>
			</cfif>
			<cfif len(local.strSearch.expertlname)>
				<cfset local.strPayload.columnFilters["last_name"] = local.strSearch.expertlname>
			</cfif>
			
			<cfset local.executionTime = getTickCount()>
			<cfset local.strAPIResponse = callAPI(payload=serializeJSON(local.strPayload))>
			<cfset local.executionTime = getTickCount() - local.executionTime>
			
			<cfset local.totalExpertCount = 0>
			<cfif NOT StructIsEmpty(local.strAPIResponse.strResult) and StructKeyExists(local.strAPIResponse.strResult, "count")>
				<cfset local.totalExpertCount = val(local.strAPIResponse.strResult.count)>
			</cfif>

			<cfset local.arrExpert = []>
			<cfif NOT StructIsEmpty(local.strAPIResponse.strResult) and StructKeyExists(local.strAPIResponse.strResult, "results")>
				<cfset local.arrExpert = local.strAPIResponse.strResult.results>
			</cfif>
			
			<cfset local.strExpertResults = structNew()>
			<cfset local.strExpertResults['arrusstates'] = getUSStatesArray()>
			<cfset local.strExpertResults['arrexperts'] = []>
			<cfloop array="#local.arrExpert#" item="local.thisExpert">
				<cfset local.arrSummaryCharts = getExpertSummary(strExpert=local.thisExpert)>
				<cfset local.tmpStr = {
					"id": local.thisExpert.id, 
					"name": (StructKeyExists(local.thisExpert,"salutation") && len(local.thisExpert.salutation) ? local.thisExpert.salutation & ' ' : '') & local.thisExpert.full_name & (StructKeyExists(local.thisExpert,"suffix") && len(local.thisExpert.suffix) ? ', ' & local.thisExpert.suffix : ''),
					"location": "",
					"arrdisciplines": local.thisExpert.disciplines,
					"numchallenges": 0,
					"numcases": 0,
					"totalline": "",
					"arrsummaryindiv": local.arrSummaryCharts 
				}>
				<cfif StructKeyExists(local.thisExpert,"addresses") and arrayLen(local.thisExpert.addresses)>
					<cfif StructKeyExists(local.thisExpert.addresses[1],"city") and len(local.thisExpert.addresses[1].city) and StructKeyExists(local.thisExpert.addresses[1],"state") and len(local.thisExpert.addresses[1].state)>
						<cfset local.tmpStr.location = "#local.thisExpert.addresses[1].city#, #local.thisExpert.addresses[1].state#">
					<cfelseif StructKeyExists(local.thisExpert.addresses[1],"city") and len(local.thisExpert.addresses[1].city) and NOT StructKeyExists(local.thisExpert.addresses[1],"state")>
						<cfset local.tmpStr.location = local.thisExpert.addresses[1].city>
					<cfelseif NOT StructKeyExists(local.thisExpert.addresses[1],"city") and StructKeyExists(local.thisExpert.addresses[1],"state") and len(local.thisExpert.addresses[1].state)>
						<cfset local.tmpStr.location = local.thisExpert.addresses[1].state>
					</cfif>
				</cfif>
				<cfif structKeyExists(local.thisExpert,"grounds_of_challenge") and isArray(local.thisExpert["grounds_of_challenge"]) and arrayLen(local.thisExpert["grounds_of_challenge"])>
					<cfset local.tmpStr.numchallenges = arrayReduce(local.thisExpert["grounds_of_challenge"], function(prev, element) {
						return arguments.prev + arguments.element.total;
					}, 0)>
				</cfif>
				<cfif structKeyExists(local.thisExpert,"state_wise_cases") and isArray(local.thisExpert["state_wise_cases"]) and arrayLen(local.thisExpert["state_wise_cases"])>
					<cfset local.tmpStr.numcases = arrayReduce(local.thisExpert["state_wise_cases"], function(prev, element) {
						return arguments.prev + arguments.element.total;
					}, 0)>
				</cfif>
				<cfif local.tmpStr.numchallenges gt 0 and local.tmpStr.numcases gt 0>
					<cfset local.tmpStr.totalline = "#local.tmpStr.numchallenges# Challenge#(local.tmpStr.numchallenges gt 1 ? 's' : '')#, #local.tmpStr.numcases# Case#(local.tmpStr.numcases gt 1 ? 's' : '')#">
				<cfelseif local.tmpStr.numchallenges gt 0>
					<cfset local.tmpStr.totalline = "#local.tmpStr.numchallenges# Challenge#(local.tmpStr.numchallenges gt 1 ? 's' : '')#">
				<cfelseif local.tmpStr.numcases gt 0>
					<cfset local.tmpStr.totalline = "#local.tmpStr.numcases# Case#(local.tmpStr.numcases gt 1 ? 's' : '')#">
				<cfelse>
					<cfset local.tmpStr.totalline = "2 Reports Available">
				</cfif>

				<cfset ArrayAppend(local.strExpertResults['arrexperts'], local.tmpStr)>
			</cfloop>

			<cfset local.arrReportTypes = [
				{ 
					"name": variables.reportTypes.expChallengeStudy.name,
					"cost": "from $#variables.reportTypes.expChallengeStudy.price#",
					"desc": "<div>All Prior Cases and Challenges.</div><div>Delivered in #variables.reportTypes.expChallengeStudy.timeframe#.</div>",
					"badge": "",
					"btntxt": "Order Report",
					"btnact": "expChallengeStudy"
				},
				{ 
					"name": variables.reportTypes.expProfilerRpt.name,
					"cost": "$#variables.reportTypes.expProfilerRpt.price#",
					"desc": "<div>All Cases and Challenges, PLUS</div><div>Comprehensive Background Report.</div><div>Delivered in #variables.reportTypes.expProfilerRpt.timeframe#.</div>",
					"badge": "BEST VALUE",
					"sampbtntxt": "View Sample Report",
					"btntxt": "Order Report",
					"btnact": "expProfilerRpt"
				}
			]>
			
			<cfset saveStats(arguments.searchID,'#variables.thisBucketType#.getResults',local.executionTime)>

			<!--- adjust maxperpage based on actual data if necessary and get page variables --->
			<cfscript>
			local.MaxPerPage = iif(arrayLen(local.arrExpert) gt variables.thisBucketMaxPerPage,variables.thisBucketMaxPerPage,arrayLen(local.arrExpert));
			if (local.MaxPerPage gt 0) {
				local.NumTotalPages = Ceiling(local.totalExpertCount / variables.thisBucketMaxPerPage);
			} else {
				local.NumTotalPages = 0;
				local.NumCurrentPage = 0;
			}
			</cfscript>
			
			<!--- return content --->
			<cfsavecontent variable="local.stResults">
				<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/daubertStudy/results.cfm">
			</cfsavecontent>

			<cfset local.stResults = ReReplace(local.stResults,'\s{2,}',' ','ALL')>
			<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
			<cfset StructInsert(local.returnStruct,"numTotalPages",val(local.NumTotalPages))>
			<cfset StructInsert(local.returnStruct,"numCurrentPage",val(local.NumCurrentPage))>
			<cfset StructInsert(local.returnStruct,"thisBucketCartItemTypeID",variables.thisBucketCartItemTypeID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.totalChallengeCount)>
			<cfset StructInsert(local.returnStruct,"strsummaryindiv",local.strExpertResults)>
			<cfset StructInsert(local.returnStruct,"success",true)>
		
			<cfreturn local.returnStruct>
		</cfif>
	</cffunction>

	<cffunction name="callAPI" access="private" returntype="struct" output="false">
		<cfargument name="payload" type="string" required="false" default="">

		<cfset var local = structnew()>
	
		<!--- This token <NAME_EMAIL> on 1/20/22 who said it was good for 10 years from that date. --->
		<cfset local.strReturn = { arguments=arguments, apiResult='', jsonResult='', strResult={}, statusCode='' }>

		<cftry>
			<cfhttp method="get" url="https://dj.expertwitnessprofiler.org/expert/expert/affiliate_experts" result="local.strReturn.apiResult" charset="utf-8">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="header" name="X-API-KEY" value="9641d295-9bc7-4865-9602-96d73e2c34b6">
				<cfif len(arguments.payload)>
					<cfhttpparam type="url" name="query" value="#arguments.payload#">
				</cfif>
			</cfhttp>
			<cfset local.strReturn.statusCode = local.strReturn.apiResult.status_code>
			<cfset local.strReturn.jsonResult = toString(trim(local.strReturn.apiResult.fileContent))>
			<cfset local.strReturn.strResult = deserializeJSON(local.strReturn.jsonResult)>
		<cfcatch type="any">
			<cfset local.strReturn.jsonResult = "{}">
			<cfset local.strReturn.statusCode = 500>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveSearchForm" access="public" output="no" returntype="numeric" hint="saves the form vars to a search and returns the searchid">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="formvars" required="yes" type="struct">

		<cfset var local = structNew()>
		
		<cfif not variables.initRun>
			<cfset init()>
		</cfif>

		<cfsavecontent variable="local.xmlSearch">
			<cfoutput>
			<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2">
				<bid><cfif StructKeyExists(arguments.formvars,"bid")>#val(arguments.formvars.bid)#</cfif></bid>
				<s_fname><cfif StructKeyExists(arguments.formvars,"s_fname")>#xmlFormat(trim(replace(arguments.formvars.s_fname,chr(34),'','ALL')))#</cfif></s_fname>
				<s_lname><cfif StructKeyExists(arguments.formvars,"s_lname")>#xmlFormat(trim(replace(arguments.formvars.s_lname,chr(34),'','ALL')))#</cfif></s_lname>
			</search>
			</cfoutput>
		</cfsavecontent>
		
		<cfset local.searchID = saveSearchXML(val(arguments.formvars.bid),local.xmlSearch)>
		
		<cfreturn local.searchID>
	</cffunction>

	<cffunction name="getExpertSummary" access="private" output="false" returntype="array">
		<cfargument name="strExpert" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.arrCharts = []>

		<cfif structKeyExists(arguments.strExpert,"state_wise_cases") and isArray(arguments.strExpert["state_wise_cases"]) and arrayLen(arguments.strExpert["state_wise_cases"])>
			<cfset local.strChart = {
				"charttype":"map",
				"charttitle":"",
				"containerid":"mc-ExpertStatewiseCasesChart-#arguments.strExpert.id#",
				"arrlocation":[]
			}>
			<cfset local.strChart['arrlocation'] = arguments.strExpert["state_wise_cases"].map(
				function(item,index) { 
					return { "statename":(structKeyExists(arguments.item,"court_state")?arguments.item["court_state"]:'Unknown'), "value":val(arguments.item.total) };
				}
			)>
			
			<cfset local.totalCount = 0>
			<cfloop array="#arguments.strExpert["state_wise_cases"]#" item="local.thisCaseCount">
				<cfset local.totalCount += local.thisCaseCount.total>
			</cfloop>
			<cfset local.strChart["charttitle"] = "Statewide Cases (#local.totalCount# Total)">

			<cfset ArrayAppend(local.arrCharts, local.strChart)>
		</cfif>

		<cfif structKeyExists(arguments.strExpert,"grounds_of_challenge") and isArray(arguments.strExpert["grounds_of_challenge"]) and arrayLen(arguments.strExpert["grounds_of_challenge"])>
			<cfset local.strChart = {
				"charttype":"bar",
				"charttitle":"",
				"containerid":"mc-ExpertGroundsOfChallengeChart-#arguments.strExpert.id#",
				"arrdata":[]
			}>
			<cfset local.strChart['arrdata'] = arguments.strExpert["grounds_of_challenge"].map(
				function(item,index) { 
					return { "key":arguments.item["ground_challenge"], "value":val(arguments.item.total) };
				}
			)>

			<cfset local.totalCount = 0>
			<cfloop array="#arguments.strExpert["grounds_of_challenge"]#" item="local.thisGroundOfChallenge">
				<cfset local.totalCount += local.thisGroundOfChallenge.total>
			</cfloop>
			<cfset local.strChart["charttitle"] = "By Grounds of Challenge (#local.totalCount# Total)">

			<cfset ArrayAppend(local.arrCharts, local.strChart)>
		</cfif>

		<cfreturn local.arrCharts>
	</cffunction>

	<cffunction name="getUSStatesArray" access="private" output="false" returntype="array">
		<cfset var local = structNew()>
		<cfset local.arrUSStates = []>

		<cfquery name="local.qryUSStates" datasource="#application.dsn.tlasites_trialsmith.dsn#" result="local.qryStat">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @USStatesXML xml, @totalCount int;
			DECLARE @tmpUSStates TABLE (stateCode varchar(4), stateName varchar(40), orderPref int);

			INSERT INTO @tmpUSStates (stateCode, stateName, orderPref)
			select [code], [Name], orderPref
			from memberCentral.dbo.ams_states
			where countryID = 1;

			SELECT @USStatesXML = ISNULL((
				select statecode, statename
				from @tmpUSStates as rowdata
				order by orderPref, statecode
				FOR XML AUTO, ELEMENTS, ROOT('usstates')
			),'<usstates/>');

			SELECT @USStatesXML as USStatesXML;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif arrayLen(XMLSearch(local.qryUSStates.USStatesXML,'/usstates/child::node()'))>
			<cfset var arrUSStateCodes = XMLSearch(local.qryUSStates.USStatesXML,'/usstates/rowdata/statecode')>
			<cfset var arrUSStateNames = XMLSearch(local.qryUSStates.USStatesXML,'/usstates/rowdata/statename')>
			<cfset local.arrUSStates = arrUSStateCodes.map(
				function(item,index) { 
					return { "statecode":arguments.item.xmlText, "statename":arrUSStateNames[arguments.index].xmlText }; 
				}
			)>
		</cfif>

		<cfreturn local.arrUSStates>
	</cffunction>

	<cffunction name="showNotAllowed" access="private" output="false" returntype="struct">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		<cfargument name="accessDeniedMessage" required="yes" type="string">
		<cfargument name="includeBucketCount" required="no" type="boolean" default="true">
		<cfargument name="viewDirectory" type="string" required="no" default="default">

		<cfset var local = StructNew()>
		<cfset local.returnStruct = StructNew()>

		<cfsavecontent variable="local.stResults">
			<cfinclude template="/views/search/#arguments.viewDirectory#/buckets/daubertStudy/notAllowed.cfm">
		</cfsavecontent>

		<cfset StructInsert(local.returnStruct,"resulthtml",local.stResults)>
		<cfif arguments.includeBucketCount>
			<cfset local.strCount = getResultsCount(mcproxy_siteID=arguments.siteID, searchID=arguments.searchID, bucketID=arguments.bucketID)>
			<cfset StructInsert(local.returnStruct,"itemcount",local.strCount.itemCount)>
		<cfelse>
			<cfset StructInsert(local.returnStruct,"itemcount",-1)>
		</cfif>
		<cfset StructInsert(local.returnStruct,"success",true)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="handleOneClickPurchase" access="public" output="false" returntype="string">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfreturn "No rights.">
		</cfif>

		<cfset local.expertID = arguments.event.getValue('expertID','')>
		<cfset local.expertName = arguments.event.getTrimValue('expertName','')>
		<cfset local.expertLocation = arguments.event.getTrimValue('expertLocation','')>
		<cfset local.expertArea = arguments.event.getTrimValue('expertArea','')>
		<cfset local.rptType = arguments.event.getValue('rptType','')>
		<cfset local.rptOrderStep = arguments.event.getValue('rptOrderNextStep','init')>
		
		<cfset local.user_billingstate = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
		<cfset local.user_billingzip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>
		<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.user_billingzip, billingState=local.user_billingstate)>
		<cfif NOT local.strBillingZip.isvalidzip>
			<cfset local.user_billingzip = "">
		</cfif>
		
		<cfset local.thisReportType = duplicate(variables.reportTypes[local.rptType])>
		<cfset local.dspFile = "billingInfo">

		<cfswitch expression="#local.rptOrderStep#">
			<cfcase value="billing">
				<cfset local.qryTSMemberData = application.objMember.getTSMemberData(depoMemberDataID=session.cfcuser.memberData.depoMemberDataID)>
				<cfset local.qryStates = application.objCommon.getTSStates()>
			</cfcase>
			<cfcase value="payment">
				<cfset structAppend(local, calculateCosts(billingState=local.user_billingstate, billingZIP=local.user_billingzip, 
							accountCode=local.thisReportType.acctCode, amountBilled=local.thisReportType.price))>
			</cfcase>
			<cfcase value="processpayment">
				<cfset structAppend(local, calculateCosts(billingState=local.user_billingstate, billingZIP=local.user_billingzip, 
							accountCode=local.thisReportType.acctCode, amountBilled=local.thisReportType.price))>
			
				<cfset local.cidForGather = "olddid_#session.cfcuser.memberData.depoMemberDataID#">
				<cfset local.strArgs = { "customerid"=local.cidForGather, "amount"=local.totalPrice, "chargeDesc"="TrialSmith.com #local.thisReportType.name#", 
					"merchantProfile"='TS', "TransactionDepoMemberDataID"=session.cfcuser.memberData.depoMemberDataID }>
				<cfif arguments.event.valueExists('p_TS_tokenData') AND len(arguments.event.getTrimValue('p_TS_tokenData'))>
					<cfset local.strArgs['tokenData'] = deserializeJSON(arguments.event.getTrimValue('p_TS_tokenData'))>
				</cfif>

				<cfset local.strPaymentResponseResult = CreateObject("component","model.buyNow.BuyNow").chargeCC_TS(argumentcollection=local.strArgs)>
				<cfif local.strPaymentResponseResult.ccsuccess>
					<cfset local.transactionDesc = "#local.expertName# - #local.thisReportType.orderTitle# - #local.thisReportType.timeframe#">
					<cfset local.caseRef = arguments.event.getTrimValue('caseRef','One Click Purchase')>
					<cfif NOT structKeyExists(session.mcStruct,"doccartCaseRefs")>
						<cfset session.mcstruct['doccartCaseRefs'] = arrayNew(1)>
					</cfif>
					<cfif len(local.caseRef) and local.caseRef neq "One Click Purchase" and session.mcstruct.doccartCaseRefs.indexOf(local.caseRef) lt 0>
						<cfset arrayAppend(session.mcstruct.doccartCaseRefs,local.caseRef)>
					</cfif>

					<cfquery name="local.qryCreateTransactions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @depoMemberDataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.depoMemberDataID#">;
							DECLARE @tlamemberstate varchar(20), @pending char(2), @madeWhilePending char(2);

							SELECT @tlamemberstate = tlamemberstate, @pending = pending 
							FROM dbo.depomemberdata 
							WHERE depomemberdataid = @depoMemberDataID;

							IF @pending = 0 
								SET @madeWhilePending = 1;
							ELSE 
								SET @madeWhilePending = 0;
							
							INSERT INTO dbo.depoTransactions (depoMemberdataid, amountbilled, salestaxamount, accountcode, datepurchased, 
								description, SourceState, orgcode, linksource, linkterms, madeWhilePending, promoCode, caseRef,
								stateForTax, zipForTax, statsSessionID)
							VALUES (
								@depoMemberDataID,
								<cfqueryparam value="#NumberFormat(local.subtotal,"0.00")#" cfsqltype="CF_SQL_DOUBLE">,
								<cfqueryparam value="#NumberFormat(local.taxAmount,"0.00")#" cfsqltype="CF_SQL_DOUBLE">,
								<cfqueryparam value="#local.thisReportType.acctCode#" cfsqltype="CF_SQL_VARCHAR">,
								GETDATE(),
								<cfqueryparam value="#local.transactionDesc#" cfsqltype="CF_SQL_VARCHAR">,
								@tlamemberstate, 'TS', '', '', @madeWhilePending, null, 
								<cfqueryparam value="#local.caseRef#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.user_billingstate#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.user_billingzip#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">
							);

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>

					<cfquery datasource="#application.dsn.tlasites_trialsmith.dsn#" name="local.qryProfile">
						select detail
						from dbo.ccMemberPaymentProfiles
						where payProfileID = <cfqueryparam value="#arguments.event.getValue('p_TS_mppid',0)#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
					<cfset local.memberPayProfileDetail = local.qryProfile.detail>
					<cfset local.qryTSMemberData = application.objMember.getTSMemberData(depoMemberDataID=session.cfcuser.memberData.depoMemberDataID)>

					<cfsavecontent variable="local.confirmationHTML">
						<cfoutput>
						<style>
							td.frmText{padding: 6px;}
						</style>
						<h4>Confirmation</h4>
						<div>
							<p>We have received your #local.thisReportType.orderTitle# and your request has been submitted to our legal research team.</p>
							<p>If you have questions, need assistance, or would like to check the status of your order, please call ************.<br />
							<br />
							To ensure our mail reaches your inbox, please add our email (<EMAIL>) to your contact list.<br />
							<br />
							Thank you!<br />
							TrialSmith Support<br />
							<EMAIL><br />
							************<br />
							<a href="https://www.trialsmith.com">https://www.trialsmith.com</a></p>
						</div>
						
						<br/>
						<div>Here are the details of your request:</div>
						<br/>
						<!--@@specialcontent@@-->
						<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="1">
						<tr>
							<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Contact Information</td>
						</tr>
						<tr><td class="frmText b" width="50%">First Name:</td><td class="frmText">#local.qryTSMemberData.FirstName#&nbsp;</td></tr>						 
						<tr><td class="frmText b">Last Name:</td><td class="frmText">#local.qryTSMemberData.LastName#&nbsp;</td></tr>
						<tr><td class="frmText b">Law Firm:</td><td class="frmText">#local.qryTSMemberData.BillingFirm#&nbsp;</td></tr>
						<tr><td class="frmText b">Email:</td><td class="frmText">#local.qryTSMemberData.Email#&nbsp;</td></tr>
						<tr><td class="frmText b">Address:</td><td class="frmText">#local.qryTSMemberData.BillingAddress#&nbsp;</td></tr>
						<cfif len(trim(local.qryTSMemberData.BillingAddress2))>
						<tr><td class="frmText b">&nbsp;</td><td class="frmText">#local.qryTSMemberData.BillingAddress2#&nbsp;</td></tr>
						</cfif>
						<tr><td class="frmText b">City:</td><td class="frmText">#local.qryTSMemberData.BillingCity#&nbsp;</td></tr>
						<tr><td class="frmText b">State:</td><td class="frmText">#local.qryTSMemberData.BillingState#&nbsp;</td></tr>
						<tr><td class="frmText b">Zip:</td><td class="frmText">#local.qryTSMemberData.BillingZip#&nbsp;</td></tr>
						<tr><td class="frmText b">Phone:</td><td class="frmText">#local.qryTSMemberData.Phone#&nbsp;</td></tr>
						<tr>
							<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Delivery</td>
						</tr>
						<tr><td class="frmText b" >Timeframe:</td><td class="frmText b">Receive in #local.thisReportType.timeframe#</td></tr>
						<tr>
							<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Expert Summary Requested</td>
						</tr>
						<tr>
							<td class="frmText b" width="50%">Expert Name:</td><td class="frmText">#local.expertName#&nbsp;</td></tr>
							<tr><td class="frmText b" width="50%">Expert Location:</td><td class="frmText">#local.expertLocation#&nbsp;</td></tr>
							<tr><td class="frmText b" width="50%">About the Expert's Area of Expertise:</td><td class="frmText" style="white-space: pre-wrap;">#local.expertArea#&nbsp;</td></tr>
							<tr><td colspan="2">&nbsp;</td>
						</tr>
						<tr>
							<td colspan="2" style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Payment Information</td>
						</tr>
						<tr><td class="frmText b">Total Amount </td><td class="frmText">#DollarFormat(local.subTotal)#<cfif local.taxAmount GT 0> (+ #DollarFormat(local.taxAmount)# tax)</cfif></td></tr>
						<tr>
							<td class="frmText b">Payment Method: </td>
							<cfif len(local.strPaymentResponseResult.transactionDetail)>
								<td class="frmText">#local.strPaymentResponseResult.transactionDetail#</td>
							<cfelseif len(local.memberPayProfileDetail)>
								<td class="frmText"> Credit Card - #local.memberPayProfileDetail#</td>
							<cfelse>
								<td class="frmText b">None selected.</td>
							</cfif>
						</tr>
						</table>
						</cfoutput>
					</cfsavecontent>

					<!--- email settings --->
					<cfset local.emailsubject = "#local.thisReportType.orderTitle# Received">
					<cfset local.emailtitle = "#arguments.event.getValue('mc_siteinfo.sitename')# - #local.thisReportType.orderTitle#">
					<cfset local.memberEmail = application.objMember.getMainEmail(memberID=session.cfcuser.memberdata.memberID).email>
					<cfset local.TSStaffConfirmationTo = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>">
					<cfif NOT len(local.memberEmail) AND len(local.qryTSMemberData.Email)>
						<cfset local.memberEmail = local.qryTSMemberData.Email>
					</cfif>
					
					<cfif len(local.memberEmail)>
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="", email="<EMAIL>" },
							emailto=[{ name="", email=local.memberEmail }],
							emailreplyto="<EMAIL>",
							emailsubject=local.emailsubject,
							emailtitle=local.emailtitle,
							emailhtmlcontent=local.confirmationHTML,
							siteID=arguments.event.getValue('mc_siteinfo.siteID'),
							memberID=session.cfcuser.memberdata.memberID,
							messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
							sendingSiteResourceID=arguments.event.getValue('mc_siteinfo.siteSiteResourceID')
						)>
						<cfset local.emailSentToUser = local.responseStruct.success>
					<cfelse>
						<cfset local.emailSentToUser = false>
					</cfif>

					<cfset local.tsMemberNumber = "<a href='https://admin.trialsmith.com/MemberEdit.cfm?depoMemberDataID=#session.cfcuser.memberdata.depoMemberDataID#' target='_blank'>#session.cfcuser.memberdata.depoMemberDataID#</a>">

					<cfsavecontent variable="local.specialText">
						<cfoutput>
						<div style="padding-bottom:4px;">TS Admin DepoID : <b>#local.tsMemberNumber#</b></div>
						<cfif NOT local.emailSentToUser>
							<div style="padding-bottom:4px;"><b>We were not able to send this member an e-mail confirmation.</b></div>
						</cfif>
						<br/>
						</cfoutput>
					</cfsavecontent>

					<cfset local.confirmationHTMLToStaff = replaceNoCase(local.confirmationHTML,"<!--@@specialcontent@@-->",local.specialText)>

					<cfscript>
						local.arrEmailTo = [];
						local.staffEmailTo = replace(local.TSStaffConfirmationTo,",",";","all");
						local.toEmailArr = listToArray(local.staffEmailTo,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="", email="<EMAIL>" },
						emailto=local.arrEmailTo,
						emailreplyto="",
						emailsubject="New #local.thisReportType.orderTitle# Request",
						emailtitle="New #local.thisReportType.orderTitle# Request",
						emailhtmlcontent=local.confirmationHTMLToStaff,
						siteID=arguments.event.getValue('mc_siteinfo.siteID'),
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=arguments.event.getValue('mc_siteinfo.siteSiteResourceID')
					)>

					<cfset local.rptOrderStep = "confirmation">
				<cfelse>
					<cfreturn local.strPaymentResponseResult.ccresponse>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfif len(local.dspFile)>
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/search/# arguments.event.getValue('viewDirectory')#/buckets/daubertStudy/#local.dspFile#.cfm">
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "No rights.">
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="calculateCosts" access="private" output="false" returntype="struct">
		<cfargument name="billingState" type="string" required="true">
		<cfargument name="billingZIP" type="string" required="true">
		<cfargument name="accountCode" type="string" required="true">
		<cfargument name="amountBilled" type="numeric" required="true">

		<cfset var qrySalesTax = "">

		<cfquery name="qrySalesTax" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteCode varchar(10) = 'TS',
				@billingState varchar(2) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">,
				@billingZIP varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingZIP#">,
				@acctCode varchar(4) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountCode#">,
				@amountBilled numeric(18,2) = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#arguments.amountBilled#" scale="2">;

			SELECT dbo.fn_tax_getTax(@siteCode,@billingState,@billingZIP,@acctCode,@amountBilled) AS salesTax
		</cfquery>

		<cfreturn {
			"subtotal": arguments.amountBilled,
			"taxAmount": qrySalesTax.salesTax,
			"totalPrice": arguments.amountBilled + qrySalesTax.salesTax
		}>
	</cffunction>
</cfcomponent>