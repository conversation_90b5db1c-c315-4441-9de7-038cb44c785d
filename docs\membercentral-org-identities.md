# MemberCentral Org Identities Analysis

## Purpose and Concept
Org Identities in MemberCentral serve as organizational contact and branding profiles that allow organizations to maintain multiple distinct identities for different purposes:

- **Legal/Corporate Identity** - Official business information
- **Marketing/Brand Identity** - Public-facing brand information  
- **Support/Service Identity** - Customer service contact details
- **Department-Specific Identities** - Different divisions or services

## Database Structure

### Core Table: `orgIdentities`
```sql
orgIdentityID (PK)
orgID (FK to organizations)
organizationName
organizationShortName  
address1, address2
city, stateID, postalCode
phone, fax, email, website
XUserName (for X/Twitter integration)
```

### Key Relationships
- `organizations.defaultOrgIdentityID` → Primary organizational identity
- `sites.defaultOrgIdentityID` → Site-specific default identity
- `sites.loginOrgIdentityID` → Identity displayed on login forms

## Usage Across MemberCentral Modules

### Email Communications (platformMail)
- **`email_consentLists.orgIdentityID`** - Provides sender identity and contact information
- **Unsubscribe Links** - Contact information displayed in email footers
- **Email Headers** - Sender name and reply-to addresses derived from org identity

### Site Management
- **`sites.defaultOrgIdentityID`** - Default organizational identity for the site
- **`sites.loginOrgIdentityID`** - Support contact information displayed on login forms
- **Site Branding** - Organization name and contact details throughout the site

### SeminarWeb Integration
- **`tblParticipants.orgIdentityID`** - Branding and contact information for seminar participants
- **Certificate Programs** - Organization identity on certificates and communications
- **Support Information** - Contact details for seminar-related inquiries

### Publications System
- **`pub_publications.orgIdentityID`** - Publisher identity for newsletters and publications
- **Email Editions** - Sender information for publication emails
- **Contact Information** - Publisher contact details in publications

### Mailing Lists
- **`lists_lists.orgIdentityID`** - List owner/administrator contact information
- **List Management** - Contact details for list-related communications
- **Digest Communications** - Sender identity for digest emails

## Administrative Interface

### Management Tools
- **Organization Admin Panel** - CRUD operations for org identities
- **Website Admin Panel** - Site-specific identity configuration
- **Org Identity Selector Widget** - Reusable component for selecting identities

### Key Features
- **Usage Tracking** - Prevents deletion of identities currently in use
- **Validation** - Ensures unique organization names within an organization
- **Preview Functionality** - View how identity information appears to users
- **Bulk Operations** - Manage multiple identities efficiently

## Business Logic and Workflows

### Identity Selection Logic
1. **Default Fallback** - Uses `organizations.defaultOrgIdentityID` when no specific identity is set
2. **Context-Specific** - Different modules can use different identities for the same organization
3. **Inheritance** - Sites inherit from organization defaults unless overridden

### Usage Validation
- System checks usage across all modules before allowing deletion
- Tracks usage in: consent lists, sites, participants, publications, mailing lists
- Provides usage counts in admin interfaces

### Contact Information Display
- Login forms show support contact from `loginOrgIdentityID`
- Email footers use contact info from associated consent list identity
- Publications display publisher contact from publication identity

## Key Benefits
1. **Flexibility** - Organizations can maintain separate identities for different purposes
2. **Consistency** - Centralized management ensures accurate contact information
3. **Compliance** - Proper sender identification for email communications
4. **Branding** - Professional presentation across all member interactions
5. **Support** - Clear contact information for member assistance
6. **Scalability** - Supports complex organizational structures with multiple brands/divisions
