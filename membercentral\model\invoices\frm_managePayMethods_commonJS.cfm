<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script language="javascript">
		var procFeeSupportedGatewayIDs = '#local.procFeeSupportedGatewayIDs#';
		var #toScript(serializeJSON(local.strProfilesData),"strProfilesData")#;
		var #toScript(serializeJSON(local.strMPPayProfiles),"strMPPayProfiles")#;
		var #toScript(serializeJSON(local.strPayProfilesData),"strPayProfilesData")#;
		
		var #toScript(serializeJSON(local.arrAllPresentProfileIDs),"arrAllPresentProfileIDs")#;
		var #toScript(serializeJSON(local.strGroupedScheduledPayments),"strGroupedSchedPayments")#;
		var #toScript(serializeJSON(local.strEligibleProfilesForSched),"strEligibleProfilesForSched")#;
		var #toScript(serializeJSON(local.strMerchantProfilesFormData),"strMerchantProfilesFormData")#;
		var #toScript(serializeJSON(local.strAllSchedDisplayLabels),"strAllSchedDisplayLabels")#;
		var #toScript(serializeJSON(local.strBDMPProfiles),"strBDMPProfiles")#;
		strProfilesData = $.parseJSON(strProfilesData);
		strMPPayProfiles = $.parseJSON(strMPPayProfiles);
		strPayProfilesData = $.parseJSON(strPayProfilesData);
		arrAllPresentProfileIDs = $.parseJSON(arrAllPresentProfileIDs);
		strGroupedSchedPayments = $.parseJSON(strGroupedSchedPayments);
		strEligibleProfilesForSched = $.parseJSON(strEligibleProfilesForSched);
		strMerchantProfilesFormData = $.parseJSON(strMerchantProfilesFormData);
		strAllSchedDisplayLabels = $.parseJSON(strAllSchedDisplayLabels);
		strBDMPProfiles = $.parseJSON(strBDMPProfiles);

		function getProcessFeeFormHTML(profileID,refKey,payProfileID,updateMode){
			if(!strPayProfilesData.hasOwnProperty(payProfileID)){
				return 'PayProfile data not found in JSON';
			}
			var thisPayProfileData = strPayProfilesData[payProfileID];
			var imageSource = thisPayProfileData.type.length ? '/assets/common/images/payment/'+ thisPayProfileData.type.toLowerCase() +'_brandmark.png' : '/assets/common/images/blank.gif';
			var strProcessFeeDisp = getProcessFeeDisplayData(profileID,refKey);

			var formHTML = $('##mc_addProcessingFeeForm').html();
			formHTML = formHTML.replace("[title]", strProcessFeeDisp.processFeeDonationFETitle)
				.replace("[msg]", strProcessFeeDisp.processFeeDonationFEMsg)
				.replace("[carddetail]", thisPayProfileData.detail)
				.replace("[cardiconpath]", imageSource)
				.replace("[cardexp]", thisPayProfileData.expiration)
				.replace("[cardnickname]", thisPayProfileData.nickname)
				.replace("[felabel]", strProcessFeeDisp.processFeeFELabel)
				.replace("[denylabel]", strProcessFeeDisp.processFeeFEDenyLabel)
				.replace("[feLabelExtra]", strProcessFeeDisp.processFeeFELabelExtra.length ? '<br/><span class="pm-font-size-sm pm-text-gray">* ' + strProcessFeeDisp.processFeeFELabelExtra + '</span>' : '')
				.replace("[isChecked]", (strProcessFeeDisp.processFeeDonationDefaultSelect == 1 || updateMode == 'remove') ? 'checked' : '');

			return formHTML;
		}
		function initProcessFeeForm(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var radioGroupSelector = thisFormEl.find('input[name="processFeeDonation"]');
			radioGroupSelector.change(function () {
				var checkedValue = thisFormEl.find('input[name="processFeeDonation"]:checked').val() || '';
				thisFormEl.find('##save-button,##next-button').prop('disabled',checkedValue.length == 0);
			}).trigger('change');
		}
		function changePayMethod(payProfileID,profileID,refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var actionMode = thisFormEl.data('mode') || 'assocpaymethod';
			if(!thisFormEl.is(':visible') || actionMode != 'assocpaymethod'){
				$('.pm-action-form').html('').hide();
				thisFormEl.data('mode','assocpaymethod').data('profileid',profileID).data('refkey',refKey).data('isnewpaymethod',0).data('step',1).data('currentpayprofileid',payProfileID);
				thisFormEl.html($('##mc_assocForm').html());
				thisFormEl.find('.changePayMethodFormTitle').html(payProfileID > 0 ? 'Choose another method of payment:' : 'Choose a method of payment:');
				$('html,body').animate({ scrollTop: $('##' + refKey + '.paymethod-row').offset().top - 150 }, 'normal');
				populatePaymentMethodsDropdown(refKey,payProfileID);
				if(payProfileID > 0)
					populateSchedPaymentsForCurrentPP(refKey,payProfileID,'schedPayment','payMethodTiedSchedPayments',false,false,true);
				thisFormEl.stop().slideToggle('normal');
			}
		}
		function toggleStepAssocPM(btnEl,dir){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var refKey = thisFormEl.data('refkey');
			var isNewPayMethod = thisFormEl.data('isnewpaymethod');
			var currentPayProfileID = thisFormEl.data('currentpayprofileid');
			var newPayProfileID = thisFormEl.find('##newPayProfileID').val();
			var prevStep = thisFormEl.data('step') || 1;
			var currentStep = dir == 'next' ? prevStep + 1 : prevStep - 1;

			/* different steps if "Add New Method of Payment" is opted */
			if(isNewPayMethod == 0){
				var stepsCount = 1;
				var additionalSchedsCount = getEligibleAdditionalSchedsCount(refKey,'schedPayment');
				var profileID = getProfileIDFromPayMethodDropdownSelection(refKey,'assocPayMethodSelection');
				var strProcessFeeDisp = getProcessFeeDisplayData(profileID,refKey);
				if(strProcessFeeDisp.enableProcessingFeeDonation == 1){
					stepsCount = additionalSchedsCount > 0 ? 3 : 2;
				}

				/* proceeding beyond the final step will trigger form submission */
				if(currentStep == (stepsCount + 1)){
					savePayMethodChange(btnEl,profileID);
					return;
				}

				var isFinalStep = (currentStep == stepsCount);
				toggleFinalStepButtonLabel(thisFormEl,isFinalStep);
				toggleActionStepButton(refKey,false);
				thisFormEl.find('.assocNewPMSection').hide();
				thisFormEl.find('div.payMethodTiedSchedPayments').hide();
				thisFormEl.find('##prev-button').toggle(currentStep > 1).prop('disabled',false);

				switch (currentStep) {
					case 1:
						thisFormEl.find('##assocPMSelection .pm-custom-dropdown').removeClass('pm-custom-dropdown-disabled');
						thisFormEl.find('##assocPMSelection').show();
						if(newPayProfileID > 0 && newPayProfileID != currentPayProfileID)
							toggleActionStepButton(refKey,true);
						break;
					case 2:
						thisFormEl.find('##assocNewPMProcessingFee').html('');
						var processFeeFormHTML = getProcessFeeFormHTML(profileID,refKey,newPayProfileID,'add');
						thisFormEl.find('##assocNewPMProcessingFee').html(processFeeFormHTML).show();
						/* footer action already present on association form */
						thisFormEl.find('.addProcessingFeeFormContainer .footerActions').remove();
						initProcessFeeForm(refKey);
						break;
					case 3:
						thisFormEl.find('##assocPMSelection .pm-custom-dropdown').addClass('pm-custom-dropdown-disabled');
						thisFormEl.find('##assocPMSelection').show();
						if(additionalSchedsCount){
							thisFormEl.find('div.payMethodTiedSchedPayments').show();
						}
						toggleActionStepButton(refKey,true);
						break;
				}

				thisFormEl.data('step',currentStep);
			}
			else {
				var profileID = thisFormEl.find('select##profileID').val() || 0;
				var paymethodLabel = getPaymethodLabelFromProfileDropdownSelection(refKey);
				var createdPayProfileID = parseInt(thisFormEl.find('##createdPayProfileID').val()) || 0;

				var arrValidSteps = [1,2,3];
				/* once profile is selected, check to see if the PFD step & eligible additional payment schedules step are to be shown */
				if(currentStep >= 3) {
					var strProcessFeeDisp = getProcessFeeDisplayData(profileID,refKey);
					if(strProcessFeeDisp.enableProcessingFeeDonation == 1)
						arrValidSteps.push(4);

					/* initialize additional payments checkbox options on 3rd step after the profile type selection */
					if(currentStep == 3){
						/* reset only on 3rd step, or else the selection will be lost for the final step */
						initEligibleSchedPaymentsForSelectedPP(profileID,refKey,'schedPayment','payMethodTiedSchedPayments',false,false);
					}
					var additionalSchedsCount = getEligibleAdditionalSchedsCount(refKey,'schedPayment');
					if(additionalSchedsCount > 0)
						arrValidSteps.push(5);
				}

				var finalStepNum = Math.max(...arrValidSteps);

				/* proceeding beyond the final step will trigger form submission */
				if(currentStep == (finalStepNum + 1)){
					if(createdPayProfileID > 0){
						thisFormEl.find('##newPayProfileID').val(createdPayProfileID);
						savePayMethodChange(btnEl,profileID);
					}
					else {
						onErrorMPPAction('Unable to fetch new payment profile ID.',refKey);
					}
					return;
				}

				thisFormEl.find('.assocNewPMSection').hide();
				var isFinalStep = (currentStep == finalStepNum);
				toggleFinalStepButtonLabel(thisFormEl,isFinalStep);
				thisFormEl.find('div.payMethodTiedSchedPayments').hide();
				thisFormEl.find('##prev-button').toggle(currentStep > 2).prop('disabled',false);
				var autoAdvance = $.inArray(currentStep, arrValidSteps) == -1;

				switch (currentStep) {
					case 2:
						var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(refKey) ? strEligibleProfilesForSched[refKey].toString().split(',') : [];
						thisFormEl.find('select##profileID option').each(function() {
							var optionValue = $(this).val();
							if(!arrEligibleProfileIDs.includes(optionValue)) {
								$(this).remove();
							}
						});

						thisFormEl.find('##next-button').text('Continue');
						thisFormEl.find('##assocNewPMTypeSelection').show();
						thisFormEl.find('select##profileID').change(); /* handles enabling the continue button */
						break;
					case 3:
						toggleActionStepButton(refKey,false);
						if(profileID > 0){
							/* advancing after selecting profile type */
							thisFormEl.find('##assocNewPMCOFForm div.addCOFFormTitle').html('Add ' + paymethodLabel + ':');

							var strFormAction = getProfileFormActionData(profileID,0,'add');
							if(strFormAction.actionFn){
								thisFormEl.find('.addPayMethodInputFormHolder').html('<div id="divPaymentProfileForm'+ profileID +'" data-refid="'+refKey+'" data-onsavefn="onCompleteAssocAddPMAction" data-oncancelfn="onCancelMPPAction" data-onerrorfn="onErrorMPPAction"></div>').show();
								strFormAction.initFn();
								strFormAction.actionFn();
							}
							else onErrorMPPAction('Unable to fetch profile information.',refKey);
							
							thisFormEl.find('##assocNewPMCOFForm').show();
						}
						else onErrorMPPAction('Unable to read method of payment type.',refKey);
						break;
					case 4:
						if(!autoAdvance){
							var processFeeFormHTML = getProcessFeeFormHTML(profileID,refKey,createdPayProfileID,'add');
							thisFormEl.find('##assocNewPMProcessingFee').html(processFeeFormHTML).show();
							/* foooter action already present on association form */
							thisFormEl.find('.addProcessingFeeFormContainer .footerActions').remove();
							initProcessFeeForm(refKey);
							/* disabling back button for PFD step if advanced after new method of payment creation */
							toggleActionStepButton(refKey,false,'prev-button');
						}
						break;
					case 5:
						if(!autoAdvance && profileID > 0){
							/* post add COF */
							thisFormEl.find('##assocNewPMPostCOF div.addCOFFormTitle').html(paymethodLabel + ' Added Successfully');
							thisFormEl.find('##addedPayMethodInfo').html('<span class="pm-text-gray">'+ paymethodLabel +' Details Loading...</span>');
							displayAddedCardInfo(createdPayProfileID,refKey);
							thisFormEl.find('##assocNewPMPostCOF').show();
							thisFormEl.find('div.payMethodTiedSchedPayments').show();
							toggleActionStepButton(refKey,true);
							$('html,body').animate({ scrollTop: $('##'+refKey).offset().top - 150 }, 'normal');
						}
						break;
				}

				thisFormEl.data('step',currentStep);
				if(autoAdvance) toggleStepAssocPM(btnEl,dir);
			}
		}
		function onCompleteAssocAddPMAction(obj,refKey){
			if(obj.payprofileid && obj.payprofileid > 0){
				var thisFormEl = getFormElementSelector(refKey);
				thisFormEl.find('##createdPayProfileID').val(obj.payprofileid);
				var proceedToNextStepFn = function(){
					let thisFormData = thisFormEl.data();
					if (thisFormData.checkeligibleschedpayments) {
						initEligibleSchedPaymentsForSelectedPP(thisFormData.esp_profileid,thisFormData.esp_refkey,thisFormData.esp_checkboxname,thisFormData.esp_containerclassname,thisFormData.esp_displaysection,thisFormData.esp_showcurrentscheduleonly);
					}
					var btnEl = thisFormEl.find('button##next-button');
					toggleStepAssocPM(btnEl,'next');
				}
				updatePayProfileDataJSON(obj.payprofileid,refKey,proceedToNextStepFn);
			}
			else onErrorMPPAction('We were unable to add the pay method.',refKey);
		}
		function savePayMethodChange(btnEl,profileID){
			var errMsg = '';
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var type = thisFormEl.data('type');
			var referenceID = thisFormEl.data('referenceid');
			var currentPayProfileID = thisFormEl.data('payprofileid');
			var newPayProfileID = thisFormEl.find('##newPayProfileID').val();
			var processFeeDonation = thisFormEl.find('input[name="processFeeDonation"]:checked').val() || 0;

			$(btnEl).prop('disabled',true);

			var schedPaymentsList = $("input[name='schedPayment']:checked").map(function() {
				return $(this).val();
			}).get().toString();

			var thisPaymentRefKey = type + '_' + referenceID;
			if(schedPaymentsList.length) schedPaymentsList += ',' + thisPaymentRefKey;
			else schedPaymentsList = thisPaymentRefKey;

			if(newPayProfileID == 0)
				errMsg = 'Choose a method of payment';
			else if(newPayProfileID == currentPayProfileID)
				errMsg = 'The selected payment method is currently assigned';
			
			if(errMsg.length){
				alert(errMsg);
				$(btnEl).prop('disabled',false);
				return false;
			}

			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					closePayProfileActionForm(btnEl);
					reloadPage(thisPaymentRefKey);
				} else {
					alert('We were unable to update the associated pay profile. Try again.');
					$(btnEl).prop('disabled',false);
				}
			};

			let arrUpdateSchedPayments = [{ reftype: type, refID: referenceID, MPPayProfileID: newPayProfileID, MPProfileID: profileID, isSourceSchedPmt: 1, payProcessFee: processFeeDonation }];

			$("input[name='schedPayment']:checked").each(function() {
				let val = $(this).val();
				let tmpStr = { reftype: val.split('_')[0], refID: val.split('_')[1], MPPayProfileID: newPayProfileID, MPProfileID: profileID };
			
				var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(val) ? strEligibleProfilesForSched[val].toString().split(',') : [];
				var isEligiblePayProfile = arrEligibleProfileIDs.includes(profileID.toString());

				/* check bd mp profiles */
				if (!isEligiblePayProfile && arrEligibleProfileIDs.length && strBDMPProfiles.hasOwnProperty(profileID)) {
					let eligibleBDMPProfileID = arrEligibleProfileIDs.filter(pid => strBDMPProfiles.hasOwnProperty(pid));
					eligibleBDMPProfileID = eligibleBDMPProfileID.length ? eligibleBDMPProfileID[0] : 0;
					if (eligibleBDMPProfileID > 0 && strPayProfilesData.hasOwnProperty(newPayProfileID) && strPayProfilesData[newPayProfileID].extrainfo.tokenstore == 'bankdraft' 
							&& strBDMPProfiles[eligibleBDMPProfileID].includes(strPayProfilesData[newPayProfileID].extrainfo.bankaccttype)) {
						isEligiblePayProfile = true;
						tmpStr.MPProfileID = eligibleBDMPProfileID;
					}
				}
				
				if (isEligiblePayProfile) {
					arrUpdateSchedPayments.push(tmpStr);
				}
			});

			var objParams = {schedPayments:JSON.stringify(arrUpdateSchedPayments )};
			TS_AJX('INV','updateScheduledPaymentMethods',objParams,saveResult,saveResult,10000,saveResult);
		}
		function populatePaymentMethodsDropdown(refKey,currentPayProfileID){
			var dropdownHTML = '';
			var currentlyAssignedOpt = '';
			var currentlyAssignedProfileID = 0;
			var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(refKey) ? strEligibleProfilesForSched[refKey].toString().split(',') : [];
			var arrPayProfileGroups = [];

			$.each(strMPPayProfiles, function(thisGroupMPProfileID, thisMPPayProfileGroups) {
				$.each(thisMPPayProfileGroups, function(profileScreenLabel, MPPayProfile) {
					// Skip to the next iteration
					if (arrPayProfileGroups.includes(profileScreenLabel)) 
						return true;

					var arrOptionsHTML = [];

					$.each(MPPayProfile.arrMemberPayProfiles, function(_, strPayProfile) {
						var optionLabel = getPMDropdownOption(strPayProfile);
						if(strPayProfile.payProfileID == currentPayProfileID){
							currentlyAssignedProfileID = thisGroupMPProfileID;
							currentlyAssignedOpt = optionLabel;
						} else {
							arrOptionsHTML.push(optionLabel);
						}
					});

					/* list eligible profiles only */
					if(arrOptionsHTML.length && arrEligibleProfileIDs.includes(thisGroupMPProfileID.toString())){
						arrPayProfileGroups.push(profileScreenLabel);
						var thisGroupHTML = getPMDropdownProfileGroupHTML(thisGroupMPProfileID,profileScreenLabel,arrOptionsHTML.join(""));
						dropdownHTML += thisGroupHTML;
					}
				});
			});
			if(currentlyAssignedOpt.length){
				var currentlyAssignedGroupHTML = getPMDropdownProfileGroupHTML(currentlyAssignedProfileID,'Currently Assigned:',currentlyAssignedOpt);
				dropdownHTML = currentlyAssignedGroupHTML + dropdownHTML;
			}

			$('##assocPayMethodSelection.pm-custom-dropdown ul.dropdown-list').append(dropdownHTML);
			initPaymentMethodsDropdown(currentPayProfileID,refKey);
		}
		function initPaymentMethodsDropdown(currentPayProfileID,refKey){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('##newPayProfileID').val(0);

			var onSelectOption = function(thisEl,selectedValue){
				thisFormEl.find('##newPayProfileID').val(selectedValue);
				
				if(selectedValue == 0){
					toggleFinalStepButtonLabel(thisFormEl,false);
					toggleActionStepButton(refKey,false);
					toggleSchedPaymentsCheckOption(refKey, false, null, 'schedPayment', 'payMethodTiedSchedPayments');
					initAddNewPMFormForAssocPM(refKey);
				}
				else if(selectedValue > 0){
					var isSameSelection = selectedValue == currentPayProfileID;
					if(!isSameSelection){
						var profileID = $(thisEl).closest('li.dropdown-group').data("profileid") || 0;
						var enableProcessingFeeDonation = getProcessFeeDisplayData(profileID,refKey).enableProcessingFeeDonation;
						var displaySection = !enableProcessingFeeDonation;
						initEligibleSchedPaymentsForSelectedPP(profileID,refKey,'schedPayment','payMethodTiedSchedPayments',displaySection,false);
						toggleFinalStepButtonLabel(thisFormEl,!enableProcessingFeeDonation);
					}
					else {
						toggleSchedPaymentsCheckOption(refKey, false, null, 'schedPayment', 'payMethodTiedSchedPayments');
						toggleFinalStepButtonLabel(thisFormEl,true);
					}
					toggleActionStepButton(refKey,!isSameSelection);
				}
			};

			initCustomDropdown('assocPayMethodSelection',onSelectOption);
		}
		function initAddNewPMFormForAssocPM(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.data('isnewpaymethod',1).data('step',1);

			thisFormEl.find('select##profileID').change(function () {
				var selectedValue = $(this).val() || 0;
				thisFormEl.find('##next-button').prop('disabled',selectedValue == 0);
			});

			var btnEl = thisFormEl.find('button##next-button');
			toggleStepAssocPM(btnEl,'next');
		}
		function getPMDropdownOption(strPayProfile){
			return '<li class="dropdown-option" data-value="'+ strPayProfile.payProfileID +'">'
				+ '<strong>'+ strPayProfile.displayLabel +'</strong>'
				+ (strPayProfile.surchargeText.length ? ' - <span class="pm-text-gray"><i>' + strPayProfile.surchargeText + '</i></span>' : '')
				+ (strPayProfile.issueMessage.length ? ' - <span class="pm-text-red">' + strPayProfile.issueMessage + '</span>' : '')
				+ '</li>';
		}
		function getPMDropdownProfileGroupHTML(profileID,optGroupLabel,optionsHTML){
			return '<li class="dropdown-group" data-profileid="'+ profileID +'">'
				+ '<div class="group-label">'+ optGroupLabel +'</div>'
				+ '<ul class="group-options">' + optionsHTML + '</ul>'
			+ '</li>';
		}
		function populateSchedPaymentsForCurrentPP(refKey,payProfileID,checkboxName,containerClassName,includeAll,checked,disabled){
			var optionsHTML = '';
			if (strGroupedSchedPayments.hasOwnProperty(payProfileID)) {
				$.each(strGroupedSchedPayments[payProfileID], function(_, schedPayment) {
					var thisSchedRefKey = schedPayment.referenceType + '_' + schedPayment.referenceID;
					/* exclude the selected scheduled payment based on the flag */
					if(includeAll || thisSchedRefKey != refKey){
						optionsHTML += '<label class="schedWithSamePM"><input type="checkbox" name="'+ checkboxName +'" value="'+ thisSchedRefKey +'"'+(checked ? ' checked' : '')+(disabled ? ' disabled' : '')+' data-issourceschedule="'+ (thisSchedRefKey == refKey ? '1' : '0') +'"/><span>'+ schedPayment.label +'</span></label>';
					}
				});
			}
			if(optionsHTML.length){
				$('##' + refKey + '_actionform div.'+ containerClassName +' div.checkbox-container').html(optionsHTML);
			}
		}
		function initEligibleSchedPaymentsForSelectedPP(profileID,refKey,checkboxName,containerClassName,displaySection,showCurrentScheduleOnly){
			var thisFormEl = getFormElementSelector(refKey);
			var eligibleCount = 0;

			toggleSchedPaymentsCheckOption(refKey, false, null, checkboxName, containerClassName); /* resets initially */

			thisFormEl.data('checkeligibleschedpayments',true);
			thisFormEl.data('esp_profileid',profileID);
			thisFormEl.data('esp_refkey',refKey);
			thisFormEl.data('esp_checkboxname',checkboxName);
			thisFormEl.data('esp_containerclassname',containerClassName);
			thisFormEl.data('esp_displaysection',displaySection);
			thisFormEl.data('esp_showcurrentscheduleonly',showCurrentScheduleOnly);

			thisFormEl.find('div.' + containerClassName + ' .schedWithSamePM').each(function() {
				var thisCheckbox = $(this).find('input[type="checkbox"][name="'+ checkboxName +'"]');
				var thisSchedRefKey = thisCheckbox.val();
				var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(thisSchedRefKey) ? strEligibleProfilesForSched[thisSchedRefKey].toString().split(',') : [];
				var isEligiblePayProfile = arrEligibleProfileIDs.includes(profileID.toString());

				/* check bd mp profiles */
				if (!isEligiblePayProfile && arrEligibleProfileIDs.length && strBDMPProfiles.hasOwnProperty(profileID)) {
					let payProfileID = 0;
					if (thisFormEl.find('##newPayProfileID').length && thisFormEl.find('##newPayProfileID').val() > 0) {
						payProfileID = thisFormEl.find('##newPayProfileID').val();
					} else if (thisFormEl.find('##substitutePayProfileID').length && thisFormEl.find('##substitutePayProfileID').val() > 0) {
						payProfileID = thisFormEl.find('##substitutePayProfileID').val();
					} else if (thisFormEl.find('##createdPayProfileID').length && thisFormEl.find('##createdPayProfileID').val() > 0) {
						payProfileID = thisFormEl.find('##createdPayProfileID').val();
					}
					let eligibleBDMPProfileID = arrEligibleProfileIDs.filter(pid => strBDMPProfiles.hasOwnProperty(pid));
					eligibleBDMPProfileID = eligibleBDMPProfileID.length ? eligibleBDMPProfileID[0] : 0;
					if (payProfileID > 0 && eligibleBDMPProfileID > 0 && strPayProfilesData.hasOwnProperty(payProfileID) && strPayProfilesData[payProfileID].extrainfo.tokenstore == 'bankdraft' 
							&& strBDMPProfiles[eligibleBDMPProfileID].includes(strPayProfilesData[payProfileID].extrainfo.bankaccttype)) {
						isEligiblePayProfile = true;
					}
				}
				
				/* if showCurrentScheduleOnly fag is set, only display the schedule currently being acted upon */
				if(showCurrentScheduleOnly)
					isEligiblePayProfile = isEligiblePayProfile && thisSchedRefKey == refKey;

				toggleSchedPaymentsCheckOption(refKey, isEligiblePayProfile, this, checkboxName, containerClassName);
				if(isEligiblePayProfile) eligibleCount++;
			});
			/* display the section only if at least one eligible scheduled payment is available */
			thisFormEl.find('div.' + containerClassName).toggle(displaySection && eligibleCount > 0);
		}
		function toggleSchedPaymentsCheckOption(refKey,f,thisEl,checkboxName,containerClassName){
			if(thisEl){
				/* disable and hide this scheduled payment checkbox option */
				$(thisEl).find('input[type="checkbox"][name="'+ checkboxName +'"]').prop('checked', f).prop('disabled', !f);
				$(thisEl).toggle(f);
			}
			else if(!f) {
				/* disable all options */
				$('##' + refKey + '_actionform input[type="checkbox"][name="'+ checkboxName +'"]').prop('checked', false).prop('disabled', true);
				$('##' + refKey + '_actionform div.'+ containerClassName +', ##' + refKey + '_actionform div.'+ containerClassName +' label.schedWithSamePM').hide();
			}
		}
		function updatePayMethod(payProfileID,profileID,refKey,paymethodLabel,formTitle){
			var thisFormEl = getFormElementSelector(refKey);
			var actionMode = thisFormEl.data('mode') || 'updatepaymethod';
			if(!thisFormEl.is(':visible') || actionMode != 'updatepaymethod' || refKey == 'mempaymethodaction'){
				$('.pm-action-form').html('').hide();
				thisFormEl.data('mode','updatepaymethod');
				thisFormEl.html($('##mc_updPayMethodForm').html());
				thisFormEl.find('.updateCOFFormTitle').html(formTitle);
				$('html,body').animate({ scrollTop: $('##' + refKey).offset().top - 150 }, 'normal');

				var strFormAction = getProfileFormActionData(profileID,payProfileID,'edit');
				if(strFormAction.actionFn){
					thisFormEl.find('.updatePayMethodInputFormHolder').html('<div id="divPaymentProfileForm'+ profileID +'" data-refid="'+refKey+'" data-onsavefn="onCompleteMPPAction" data-oncancelfn="onCancelMPPAction" data-onerrorfn="onErrorMPPAction"></div>').show();
					strFormAction.initFn();
					strFormAction.actionFn(strFormAction.actionFnParam);
				}
				else onErrorMPPAction('Unable to fetch payment profile information.',refKey);

				if(refKey == 'mempaymethodaction'){
					thisFormEl.show();
				}
				else {
					thisFormEl.stop().slideToggle('normal');
				}
			}
		}
		function removePayMethod(payProfileID,profileID,refKey,paymethodLabel,formTitle){
			var thisFormEl = getFormElementSelector(refKey);
			var actionMode = thisFormEl.data('mode') || 'removepaymethod';
			var isTiedToScheduledPayments = strGroupedSchedPayments.hasOwnProperty(payProfileID);
			if(!thisFormEl.is(':visible') || actionMode != 'removepaymethod' || refKey == 'mempaymethodaction'){
				$('.pm-action-form').html('').hide();
				thisFormEl.data('mode','removepaymethod').data('step',1).data('payprofileid',payProfileID).data('profileid',profileID).data('paymethodlabel',paymethodLabel).data('refkey',refKey).data('isnewpaymethod',0);
				thisFormEl.html($('##mc_removePayMethodForm').html());
				thisFormEl.find('.removePayMethodFormTitle').html(formTitle);
				toggleActionStepButton(refKey,false);

				var initStepContainerID = 'removePMStep1' + (isTiedToScheduledPayments ? 'a' : 'b');
				if(!isTiedToScheduledPayments){
					/* if the card is not tied to any schedule, the UI will display only a warning message before form submission */
					$('##removePMStep1b span##permDeleteWarningText').text('Deleting this ' + paymethodLabel + ' is permanent. Are you sure you want to delete this ' + paymethodLabel + '?');
					thisFormEl.find('##next-button').prop('disabled',false).text('Delete ' + paymethodLabel).addClass('pm-act-button-danger');
				}
				else {
					populateRemovePayMethodFormData(payProfileID,refKey,paymethodLabel);
					initRemovePayMethodForm(refKey);
				}

				$('##' + initStepContainerID).show();
				$('html,body').animate({ scrollTop: $('##' + refKey).offset().top - 150 }, 'normal');
				thisFormEl.stop().slideToggle('normal');
			}
		}
		function populateRemovePayMethodFormData(payProfileID,refKey,paymethodLabel){
			var paymethodLabel = paymethodLabel.toLowerCase();
			$('##removePMStep1a span##remCardStep1Opt1Label').text('Delete this ' + paymethodLabel + ' from my profile');
			$('##removePMStep1a span##remCardStep1Opt2Label').text('Keep this ' + paymethodLabel + ', but remove it from scheduled payment(s)');

			/* populate the list of scheduled payments tied to this card for step 2 */
			populateSchedPaymentsForCurrentPP(refKey,payProfileID,'schedPaymentToDissociate','schedPaymentsCheckOptionsToDissociate',true,true,false);
			
			/* populate pay method dropdown options (final step) */
			var dropdownHTML = '';
			var arrEligibleProfileIDs = [];
			/* check against eligible profiles only */
			if(refKey == 'mempaymethodaction'){
				arrEligibleProfileIDs = $.map(arrAllPresentProfileIDs,String);
			} else {
				arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(refKey) ? strEligibleProfilesForSched[refKey].toString().split(',') : [];
			}

			var arrPayProfileGroups = [];

			$.each(strMPPayProfiles, function(thisGroupMPProfileID, thisMPPayProfileGroups) {
				$.each(thisMPPayProfileGroups, function(profileScreenLabel, MPPayProfile) {
					var arrOptionsHTML = [];
					// Skip to the next iteration
					if (arrPayProfileGroups.includes(profileScreenLabel)) 
						return true;

					$.each(MPPayProfile.arrMemberPayProfiles, function(_, strPayProfile) {
						/* exclude the pay profile that is to be removed */
						if(strPayProfile.payProfileID != payProfileID){
							var optionLabel = getPMDropdownOption(strPayProfile);
							arrOptionsHTML.push(optionLabel);
						}
					});

					if(arrOptionsHTML.length && arrEligibleProfileIDs.includes(thisGroupMPProfileID.toString())){
						arrPayProfileGroups.push(profileScreenLabel);
						var thisGroupHTML = getPMDropdownProfileGroupHTML(thisGroupMPProfileID,profileScreenLabel,arrOptionsHTML.join(""));
						dropdownHTML += thisGroupHTML;
					}
				});
			});

			$('##removePMNewPayMethodSelection.pm-custom-dropdown .dropdown-list').append(dropdownHTML);
		}
		function initRemovePayMethodForm(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('input[type="radio"][name="removeMode"]').on('change', function() {
				if ($(this).is(':checked')) {
					var selectedValue = $(this).val();
					if(selectedValue.length) toggleActionStepButton(refKey,true);
				}
			});
			thisFormEl.find('input[type="radio"][name="assignNewPMForScheds"]').on('change', function() {
				if ($(this).is(':checked')) {
					var selectedValue = $(this).val();
					if(selectedValue.length) {
						toggleActionStepButton(refKey,true);
						/* selecting the [remove account & do not assign a new method] option in step 2 makes this step the final one, requiring form submission upon proceeding */
						var isFinalStep = selectedValue == 0;
						toggleFinalStepButtonLabel(thisFormEl,isFinalStep);
						if(isFinalStep){
							thisFormEl.find('##substitutePayProfileID').val(0);
							toggleSchedPaymentsCheckOption(refKey, false, null, 'schedPaymentToReassign', 'payMethodTiedSchedPayments');
						}
					}
				}
			});
			thisFormEl.find("input[name='schedPaymentToDissociate']").on('change', function() {
				var thisCheckbox = $(this);
				if (thisCheckbox.data('issourceschedule') == '1' && !thisCheckbox.is(':checked')) {
					thisCheckbox.prop('checked', true); // prevent unchecking for the scheduled payment which the remove process was initiated from
				}
				/* at least one option must be checked to continue */
				var isAnyChecked = thisFormEl.find("input[name='schedPaymentToDissociate']:checked").length > 0;
				toggleActionStepButton(refKey,isAnyChecked);
			});

			thisFormEl.find('##substitutePayProfileID').val(0);

			var onSelectOption = function(thisEl,selectedValue){
				thisFormEl.find('##substitutePayProfileID').val(selectedValue);
				
				if(selectedValue == 0){
					thisFormEl.find("input[name='schedPaymentToReassign']").off('change'); /* unbind change event for checkboxes on this step */
					toggleFinalStepButtonLabel(thisFormEl,false);
					toggleActionStepButton(refKey,false);
					toggleSchedPaymentsCheckOption(refKey, false, null, 'schedPaymentToReassign', 'payMethodTiedSchedPayments');
					initAddNewPMFormForRemovePM(refKey);
				}
				else if(selectedValue > 0){
					toggleActionStepButton(refKey,true);
					var profileID = $(thisEl).closest('li.dropdown-group').data("profileid") || 0;
					var enableProcessingFeeDonation = getProcessFeeDisplayData(profileID,refKey).enableProcessingFeeDonation;
					initEligibleSchedPaymentsForSelectedPP(profileID,refKey,'schedPaymentToReassign','payMethodTiedSchedPayments',true,enableProcessingFeeDonation);
					checkForEligibleSchedsToReassign(refKey);
					toggleFinalStepButtonLabel(thisFormEl,!enableProcessingFeeDonation);
					bindChangeEventForSchedPaymentsToReassign(refKey,profileID);
				}
			};

			initCustomDropdown('removePMNewPayMethodSelection',onSelectOption);
		}
		function bindChangeEventForSchedPaymentsToReassign(refKey,profileID){
			var thisFormEl = getFormElementSelector(refKey);
			var enableProcessingFeeDonation = getProcessFeeDisplayData(profileID,refKey).enableProcessingFeeDonation;
			thisFormEl.find("input[name='schedPaymentToReassign']").off("change").on('change', function() {
				/* if PFD step is to follow, do not allow unchecking the source schedule (in both step 4 & 6) */
				if(enableProcessingFeeDonation){
					var thisCheckbox = $(this);
					if (thisCheckbox.data('issourceschedule') == '1' && !thisCheckbox.is(':checked')) {
						thisCheckbox.prop('checked', true); // prevent unchecking for the source scheduled payment
					}
				}

				var isAnyChecked = thisFormEl.find("input[name='schedPaymentToReassign']:checked").length > 0;
				/* at least one option must be checked to proceed */
				toggleActionStepButton(refKey,isAnyChecked);
			});
		}
		function checkForEligibleSchedsToReassign(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			/* there should be atleast one eligible scheduled payment for the selected profile */
			var eligibleOptionsCount = thisFormEl.find("input[name='schedPaymentToReassign']:not(:disabled):visible").length;
			if(eligibleOptionsCount == 0){
				onErrorMPPAction('No eligible scheduled payments were found for reassignment with the selected method of payment.',refKey);
				toggleActionStepButton(refKey,false);
			}
			else clearMPPFormErrorMsg(refKey);
		}
		function populateSchedPaymentOptionsFromSelection(payProfileID,refKey,arrFilterOptions){
			var thisFormEl = getFormElementSelector(refKey);
			var schedPaymentsHTML = '';
			var checkOptionsReassignHTML = '';
			$.each(strGroupedSchedPayments[payProfileID], function(_, schedPayment) {
				var thisRefKey = schedPayment.referenceType + '_' + schedPayment.referenceID;
				if($.inArray(thisRefKey,arrFilterOptions) != -1){
					schedPaymentsHTML += '<div class="pm-mb-1 schedPayListRow" data-refid="'+ thisRefKey +'">'+ schedPayment.label +'</div>';
					checkOptionsReassignHTML += '<label class="schedWithSamePM"><input type="checkbox" name="schedPaymentToReassign" value="'+ thisRefKey +'" data-issourceschedule="'+ (thisRefKey == refKey ? '1' : '0') +'" disabled/><span>'+ schedPayment.label +'</span></label>';
				}
			});
			thisFormEl.find('##removePMStep3 div.schedPaymentsListForPayProfile').html(schedPaymentsHTML);
			thisFormEl.find('div.payMethodTiedSchedPayments div.checkbox-container').html(checkOptionsReassignHTML);
		}
		function toggleActionStepButton(refKey,f,ovButtonID){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('##' + (ovButtonID && ovButtonID.length ? ovButtonID : 'next-button')).prop('disabled',!f);
		}
		function toggleFinalStepButtonLabel(thisFormEl,f){
			thisFormEl.find('##next-button').text(f ? 'Save' : 'Continue').toggleClass('pm-act-button-primary', f);
		}
		function toggleStepRemovePM(btnEl,dir){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var refKey = thisFormEl.data('refkey');
			var payProfileID = thisFormEl.data('payprofileid');
			var payMethodLabel = thisFormEl.data('paymethodlabel').toLowerCase();
			var prevStep = thisFormEl.data('step');
			var currentStep = dir == 'next' ? prevStep + 1 : prevStep - 1;
			var autoAdvance = false;
			var arrValidSteps = [1];

			var profileID = 0;
			var removeMode = '';
			var isNewPayMethod = false;
			var isTiedToScheduledPayments = strGroupedSchedPayments.hasOwnProperty(payProfileID);
			/* removing card from 'Additional Methods of Payment' section has just one step */
			if (isTiedToScheduledPayments){
				if(currentStep <= 3 && thisFormEl.data('isnewpaymethod') == 1)
					thisFormEl.data('isnewpaymethod',0);
				isNewPayMethod = thisFormEl.data('isnewpaymethod') == 1;

				if(currentStep > 1){
					removeMode = thisFormEl.find('input[type="radio"][name="removeMode"]:checked').val();
					/* for [Delete this card from my profile] option, skip step 2 (tied schedules selection step) */
					if(removeMode == 'DETACH'){
						arrValidSteps.push(2);
					}
				}

				arrValidSteps.push(3);

				/* selecting the [remove account & do not assign a new method] option in step 3 makes this step the final one, requiring form submission upon proceeding */
				if(currentStep > 3){
					var selectedValue  = thisFormEl.find('input[type="radio"][name="assignNewPMForScheds"]:checked').val();
					if(selectedValue && selectedValue.length && selectedValue == 1){
						arrValidSteps.push(4);
					}
				}

				if(currentStep > 4){
					if(isNewPayMethod)
						profileID = thisFormEl.find('select##profileID').val() || 0;
					else
						profileID = getProfileIDFromPayMethodDropdownSelection(refKey,'removePMNewPayMethodSelection');
					
					var strProcessFeeDisp = getProcessFeeDisplayData(profileID,refKey);
					if(strProcessFeeDisp.enableProcessingFeeDonation == 1)
						arrValidSteps.push(5);
				}

				/* step 6 to be shown if new paymethod is added or PFD is applicable */
				if(isNewPayMethod || arrValidSteps.includes(5)){
					arrValidSteps.push(6);
				}
			}

			clearMPPFormErrorMsg(refKey);

			var finalStepNum = Math.max(...arrValidSteps);

			/* clicking the next button from final step should trigger form submission */
			if(currentStep == (finalStepNum+1)){
				doRemovePayMethod(refKey);
				return;
			}

			var currentStepContainerID = 'removePMStep' + currentStep;
			var isFinalStep = (currentStep == finalStepNum);
			toggleFinalStepButtonLabel(thisFormEl,isFinalStep);
			toggleActionStepButton(refKey,false);
			var autoAdvance = $.inArray(currentStep, arrValidSteps) == -1;
			var currStepForSwitch = autoAdvance ? 999 : currentStep; /* just to skip the steps switch */

			thisFormEl.find('div.payMethodTiedSchedPayments').hide(); /* initially hide this step which is common for step 4 & 6 */
			thisFormEl.find('##prev-button').toggle(currentStep > 1).prop('disabled',false);

			switch (currStepForSwitch) {
				case 1:
					var isTiedToScheduledPayments = strGroupedSchedPayments.hasOwnProperty(payProfileID);
					currentStepContainerID += (isTiedToScheduledPayments ? 'a' : 'b');
					thisFormEl.find('input[type="radio"][name="removeMode"]').trigger('change'); /* handles enabling the next button during backward navigation */
					break;
				case 2:
					thisFormEl.find('input[name="schedPaymentToDissociate"]').eq(0).trigger('change');
					thisFormEl.find('div.schedPaymentsCheckOptionsToDissociate').toggle(removeMode == 'DETACH');
					var checkOptionsCount = thisFormEl.find('input[name="schedPaymentToDissociate"]').length;
					var thisTitle = checkOptionsCount > 1
						? 'The method of payment being removed is tied to the scheduled payment(s) below. You may optionally move other scheduled payments to a new method of payment as well:'
						: 'The method of payment being removed is tied to the scheduled payment(s) below. You may optionally move the scheduled payment to a new method of payment:';
					thisFormEl.find('div.schedPaymentsCheckOptionsToDissociate .dissociateSchedsOptionTitle').html(thisTitle);
					break;
				case 3:
					var actionLabel = removeMode == 'DELETE' ? 'Delete' : 'Remove';
					thisFormEl.find('##removePMStep3 div##step2WarningText').html((removeMode == 'DELETE' ? 'Deleting' : 'Removing') + ' this ' + payMethodLabel + ' will leave the following upcoming scheduled payment(s) with no method of payment:');
					thisFormEl.find('##removePMStep3 span##remCardStep2Opt1Label').text(actionLabel + ' ' + payMethodLabel + ' & change method of payment for upcoming scheduled payment(s)');
					thisFormEl.find('##removePMStep3 span##remCardStep2Opt2Label').text(actionLabel + ' ' + payMethodLabel + ' & don\'t assign a new method of payment to upcoming scheduled payment(s)');

					var checkedOptionsOnly = (removeMode == 'DELETE' ? false : true);
					var arrSelectedSchedPayments = getSchedsToDissociateForRemovePM(refKey,checkedOptionsOnly);
					thisFormEl.find('##removePMStep3 .schedPayListRow').hide();
					thisFormEl.find('##removePMStep3 .schedPayListRow').each(function() {
						var thisRefID = $(this).data('refid');
						if($.inArray(thisRefID,arrSelectedSchedPayments) != -1)
							$(this).show();
					});
					/* based on the selection from step 2, populate entries for the warning message in step 3 & reassign checkbox options in step 4 */
					populateSchedPaymentOptionsFromSelection(payProfileID,refKey,arrSelectedSchedPayments);

					if(dir == 'next') thisFormEl.find('input[name="assignNewPMForScheds"]').prop('checked', false); /* resets selection when navigated from the previous step */
					else thisFormEl.find('input[type="radio"][name="assignNewPMForScheds"]').trigger('change'); /* handles enabling the next button during backward navigation */

					break;
				case 4:
					var assignNewPMForScheds = $('input[type="radio"][name="assignNewPMForScheds"]:checked').val();
					/* step 4 should only be accessible if [remove account & change method of payment] option was selected in step 2 */
					if(assignNewPMForScheds == 1){
						if(dir == 'next'){
							/* resets all selections when navigated from the previous step */
							thisFormEl.find('##substitutePayProfileID').val(0);
							thisFormEl.find('##removePMNewPayMethodSelection.pm-custom-dropdown li.dropdown-option').removeClass("selected");
							thisFormEl.find('##removePMNewPayMethodSelection.pm-custom-dropdown .dropdown-label').html('Select Pay Method');
							toggleSchedPaymentsCheckOption(refKey, false, null, 'schedPaymentToReassign', 'payMethodTiedSchedPayments');
							thisFormEl.find('##removePMNewPMTypeSelection select##profileID').html('');
							thisFormEl.find('##removePMNewPMCOFForm .addPayMethodInputFormHolder').html('');
							thisFormEl.find('.removePMNewPMSection').hide();
							thisFormEl.find('##removePMSubstitutePMSelection').show();
						}
						else if(dir == 'prev' && !isNewPayMethod) {
							thisFormEl.find('##removePMNewPayMethodSelection .dropdown-list li.dropdown-option.selected').trigger('click'); /* reset checkboxes display */
							var additionalSchedsCount = getEligibleAdditionalSchedsCount(refKey,'schedPaymentToReassign');
							thisFormEl.find('div.payMethodTiedSchedPayments').toggle(additionalSchedsCount > 0);

							profileID = getProfileIDFromPayMethodDropdownSelection(refKey,'removePMNewPayMethodSelection');
							if(profileID > 0){
								var enableProcessingFeeDonation = getProcessFeeDisplayData(profileID,refKey).enableProcessingFeeDonation;
								toggleFinalStepButtonLabel(thisFormEl,!enableProcessingFeeDonation);
								toggleActionStepButton(refKey,true);
							}
						}
						else {
							/* invalid action */
							closePayProfileActionForm(btnEl);
						}
					}
					break;
				case 5:
					var processFeeDonationSelection = (dir == 'prev' ? (thisFormEl.find('input[name="processFeeDonation"]:checked').val() || '') : '');
					var substitutePayProfileID = parseInt($('##substitutePayProfileID').val()) || 0;
					var processFeeFormHTML = getProcessFeeFormHTML(profileID,refKey,substitutePayProfileID,'add');
					thisFormEl.find('##removePMStep5').html(processFeeFormHTML).show();
					/* footer action already present on remove form */
					thisFormEl.find('.addProcessingFeeFormContainer .footerActions').remove();
					initProcessFeeForm(refKey);

					if(processFeeDonationSelection != '')
						thisFormEl.find('input[name="processFeeDonation"][value=\''+processFeeDonationSelection+'\']').prop("checked", true).trigger('change');

					/* disabling back button for PFD step if advanced after new method of payment creation */
					if(isNewPayMethod)
						toggleActionStepButton(refKey,false,'prev-button');

					break;
				case 6:
					if(profileID > 0){
						$('.removePMStep6Sections').hide();
						if(isNewPayMethod){
							/* post add COF */
							var paymethodLabel = getPaymethodLabelFromProfileDropdownSelection(refKey);
							thisFormEl.find('##removePMStep6 ##removePMNewPMPostCOF div.addCOFFormTitle').html(paymethodLabel + ' Added Successfully');
							thisFormEl.find('##addedPayMethodInfo').html('<span class="pm-text-gray">'+ paymethodLabel +' Details Loading...</span>');
							var createdPayProfileID = parseInt(thisFormEl.find('##createdPayProfileID').val()) || 0;
							thisFormEl.find('##substitutePayProfileID').val(createdPayProfileID);
							displayAddedCardInfo(createdPayProfileID,refKey);
							thisFormEl.find('##removePMNewPMPostCOF').show();
							bindChangeEventForSchedPaymentsToReassign(refKey,profileID);
							$('html,body').animate({ scrollTop: $('##'+refKey).offset().top - 150 }, 'normal');
							if(!arrValidSteps.includes(5))
								toggleActionStepButton(refKey,false,'prev-button');
						}
						else {
							thisFormEl.find('##removePMSubstitutePMSelectionRO').show();
							var selText = thisFormEl.find('##removePMNewPayMethodSelection .dropdown-label').html();
							thisFormEl.find('##removePMSubstitutePMSelectionRO div.dropdown-label').html(selText);
						}
						initEligibleSchedPaymentsForSelectedPP(profileID,refKey,'schedPaymentToReassign','payMethodTiedSchedPayments',true,false);
						checkForEligibleSchedsToReassign(refKey);
						thisFormEl.find('##next-button').prop('disabled',false);
					}
					break;
			}
			
			thisFormEl.find('.removePMSteps').hide();
			thisFormEl.data('step',currentStep);
			$('##' + currentStepContainerID).show();
			if(autoAdvance) toggleStepRemovePM(btnEl,dir);
		}
		function initAddNewPMFormForRemovePM(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var continueBtnEl = thisFormEl.find('button##continue-button-newpm');

			thisFormEl.find('##removePMNewPMTypeSelection select##profileID').html($('##mc_merchantProfileOptions').html()); /* initialize with all options */

			var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(refKey) ? strEligibleProfilesForSched[refKey].toString().split(',') : [];
			thisFormEl.find('##removePMNewPMTypeSelection select##profileID option').each(function() {
				var optionValue = $(this).val();
				if(!arrEligibleProfileIDs.includes(optionValue)) {
					$(this).remove();
				}
			});

			thisFormEl.find('##removePMNewPMTypeSelection select##profileID').change(function () {
				var selectedValue = $(this).val() || 0;
				continueBtnEl.prop('disabled',selectedValue == 0);
			});
			
			toggleNewPMSectionsForRemovePM(continueBtnEl,1);
		}
		function toggleNewPMSectionsForRemovePM(btnEl,sectionNum){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var refKey = thisFormEl.data('refkey');
			thisFormEl.data('isnewpaymethod',1);
			var profileID = thisFormEl.find('select##profileID').val() || 0;
			var paymethodLabel = getPaymethodLabelFromProfileDropdownSelection(refKey);

			if(sectionNum > 2) return;

			toggleActionStepButton(refKey,false);
			thisFormEl.find('##removePMSubstitutePMSelection,.removePMNewPMSection').hide();

			switch (sectionNum) {
				case 1:
					thisFormEl.find('##removePMNewPMTypeSelection').show();
					thisFormEl.find('select##profileID').change(); /* handles enabling the continue button */
					break;
				case 2:
					if(profileID > 0){
						/* advancing after selecting profile type */
						thisFormEl.find('##removePMStep4 ##removePMNewPMCOFForm div.addCOFFormTitle').html('Add ' + paymethodLabel + ':');

						var strFormAction = getProfileFormActionData(profileID,0,'add');
						if(strFormAction.actionFn){
							thisFormEl.find('##removePMNewPMCOFForm .addPayMethodInputFormHolder').html('<div id="divPaymentProfileForm'+ profileID +'" data-refid="'+refKey+'" data-onsavefn="onCompleteAddNewPMForRemovePM" data-oncancelfn="onCancelAddNewPMForRemovePM" data-onerrorfn="onErrorAddNewPMForRemovePM"></div>').show();
							strFormAction.initFn();
							strFormAction.actionFn();
						}
						else onErrorMPPAction('Unable to fetch profile information.',refKey);
						
						toggleActionStepButton(refKey,false);
						thisFormEl.find('##removePMNewPMCOFForm').show();
					}
					else onErrorMPPAction('Unable to read method of payment type.',refKey);
					break;
			}
		}
		function onCompleteAddNewPMForRemovePM(obj,refKey){
			var thisFormEl = getFormElementSelector(refKey);
			if(obj.payprofileid && obj.payprofileid > 0){
				thisFormEl.find('##removePMNewPMTypeSelection ##createdPayProfileID').val(obj.payprofileid);
				thisFormEl.find('##substitutePayProfileID').val(obj.payprofileid);
				var proceedToNextStepFn = function(){
					let thisFormData = thisFormEl.data();
					if (thisFormData.checkeligibleschedpayments) {
						initEligibleSchedPaymentsForSelectedPP(thisFormData.esp_profileid,thisFormData.esp_refkey,thisFormData.esp_checkboxname,thisFormData.esp_containerclassname,thisFormData.esp_displaysection,thisFormData.esp_showcurrentscheduleonly);
					}
					var btnEl = thisFormEl.find('button##next-button');
					toggleStepRemovePM(btnEl,'next');
				}
				updatePayProfileDataJSON(obj.payprofileid,refKey,proceedToNextStepFn);
			}
			else onErrorMPPAction('We were unable to add the pay method.',refKey);
		}
		function onCancelAddNewPMForRemovePM(obj,refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var btnEl = thisFormEl.find('button##prev-button');
			toggleStepRemovePM(btnEl,'prev');
		}
		function onErrorAddNewPMForRemovePM(msg,refKey){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('##removePMNewPMCOFForm .addPayMethodInputFormHolder').html('Error');
			toggleActionStepButton(refKey,true,'prev-button');
			thisFormEl.find('.profileErrorForm div.msg').html(msg);
			thisFormEl.find('.profileErrorForm').show();
			$('html,body').animate({ scrollTop: $('##' + refKey).offset().top - 150 }, 'normal');
		}
		function doRemovePayMethod(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var profileID = thisFormEl.data('profileid');
			var payProfileID = thisFormEl.data('payprofileid');
			var isTiedToScheduledPayments = strGroupedSchedPayments.hasOwnProperty(payProfileID);
			var removeMode = $('##' + refKey + '_actionform input[type="radio"][name="removeMode"]:checked').val() || '';
			var action = isTiedToScheduledPayments ? removeMode : 'DELETE';
			var processFeeDonation = thisFormEl.find('input[name="processFeeDonation"]:checked').val() || 0;

			thisFormEl.find('##next-button,##prev-button').prop('disabled',true);

			if(!isTiedToScheduledPayments){
				doDeletePayMethodFinal(profileID,payProfileID,refKey);
			}
			else {
				var substitutePayProfileID = parseInt($('##substitutePayProfileID').val()) || 0;
				var ovMPProfileID = 0;

				/* if deleting the card, pass all related scheduled payments to detach the pay method from them */
				/* if keeping the card, dissociate only the checked ones */
				var checkedOptionsOnly = (action == 'DELETE' ? false : true);
				var schedPaymentsListToDissociate = getSchedsToDissociateForRemovePM(refKey,checkedOptionsOnly).toString();

				/* if the [change method of payment for upcoming scheduled payments] option is selected, pass the selected scheduled payments for reassign from the final step */
				var schedPaymentsListForReassign = '';
				if(substitutePayProfileID > 0){
					var schedPaymentsListForReassign = $("input[name='schedPaymentToReassign']:checked").map(function() {
						return $(this).val();
					}).get().toString();

					/* if [assign a new method of payment] is opted, there should be atleast one eligible scheduled payment for the selected profile, or display an error message */
					if(schedPaymentsListForReassign == ''){
						onErrorMPPAction('No eligible scheduled payments were found for reassignment with the selected method of payment.',refKey);
						toggleActionStepButton(refKey,true,'prev-button');
					}
					ovMPProfileID = getProfileIDFromPayMethodDropdownSelection(refKey,'removePMSubstitutePMSelection');
				}

				if (!schedPaymentsListToDissociate.length) return false;

				let arrUpdateSchedPayments = [];
				schedPaymentsListToDissociate.split(',').forEach(function(thisSchedRefKey, index) {
					let tmpStr = { reftype: thisSchedRefKey.split('_')[0], refID: thisSchedRefKey.split('_')[1], MPPayProfileID: 0, MPProfileID: profileID };
					if (refKey == thisSchedRefKey) {
						tmpStr.isSourceSchedPmt = 1;
						tmpStr.payProcessFee = processFeeDonation;
					}
					arrUpdateSchedPayments.push(tmpStr);
				});

				if (substitutePayProfileID > 0 && schedPaymentsListForReassign.length) {
					schedPaymentsListForReassign.split(',').forEach(function(thisSchedRefKey, index) {
						let arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(thisSchedRefKey) ? strEligibleProfilesForSched[thisSchedRefKey].toString().split(',') : [];
						let isEligiblePayProfile = arrEligibleProfileIDs.includes(profileID.toString());
						let thisMPProfileID = profileID;

						/* check bd mp profiles */
						if (!isEligiblePayProfile && arrEligibleProfileIDs.length && strBDMPProfiles.hasOwnProperty(profileID)) {
							let eligibleBDMPProfileID = arrEligibleProfileIDs.filter(pid => strBDMPProfiles.hasOwnProperty(pid));
							eligibleBDMPProfileID = eligibleBDMPProfileID.length ? eligibleBDMPProfileID[0] : 0;
							if (eligibleBDMPProfileID > 0 && strPayProfilesData.hasOwnProperty(substitutePayProfileID) && strPayProfilesData[substitutePayProfileID].extrainfo.tokenstore == 'bankdraft' 
									&& strBDMPProfiles[eligibleBDMPProfileID].includes(strPayProfilesData[substitutePayProfileID].extrainfo.bankaccttype)) {
								isEligiblePayProfile = true;
								thisMPProfileID = eligibleBDMPProfileID;
							}
						}
						
						if (isEligiblePayProfile) {
							let targetSchedPayment = arrUpdateSchedPayments.find(item => item.reftype == thisSchedRefKey.split('_')[0] && Number(item.refID) == Number(thisSchedRefKey.split('_')[1]));
							targetSchedPayment.MPPayProfileID = substitutePayProfileID;
							targetSchedPayment.MPProfileID = thisMPProfileID;
						}
					});
				}

				var saveResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						if(action == 'DELETE'){
							doDeletePayMethodFinal(profileID,payProfileID,refKey);
						}
						else {
							onCancelMPPAction({},refKey);
							reloadPage(refKey);
						}
					} else {
						var errMsg = substitutePayProfileID > 0 ? 'We were unable to reassign the scheduled payments with newly selected pay method.' : 'We were unable to remove the pay method from the scheduled payments.';
						onErrorMPPAction(errMsg,refKey);
					}
				};

				var objParams = {schedPayments:JSON.stringify(arrUpdateSchedPayments )};
				TS_AJX('INV','updateScheduledPaymentMethods',objParams,saveResult,saveResult,10000,saveResult);
			}
		}
		function getSchedsToDissociateForRemovePM(refKey,checkedOnly){
			var optionsSelector = (checkedOnly ? "input[name='schedPaymentToDissociate']:checked" : "input[name='schedPaymentToDissociate']");
			return $('##' + refKey + '_actionform ' + optionsSelector).map(function() {
				return $(this).val();
			}).get();
		}
		function doDeletePayMethodFinal(profileID,payProfileID,refKey){
			var strFormAction = getProfileFormActionData(profileID,payProfileID,'remove');
			if(strFormAction.actionFn){
				var thisFormEl = getFormElementSelector(refKey);
				thisFormEl.find('.removePayMethodInputFormHolder').html('<div id="divPaymentProfileForm'+ profileID +'" data-refid="'+refKey+'" data-onsavefn="onCompleteMPPAction" data-oncancelfn="onCancelMPPAction" data-onerrorfn="onErrorMPPAction"></div>').show();
				strFormAction.initFn();
				strFormAction.actionFn(strFormAction.actionFnParam);
			}
			else {
				onErrorMPPAction('Unable to fetch payment profile information.',refKey);
			}
		}
		function addPayMethod(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var actionMode = thisFormEl.data('mode') || 'addpaymethod';
			if(!thisFormEl.is(':visible') || actionMode != 'addpaymethod'){
				$('.pm-action-form').html('').hide();
				thisFormEl.data('mode','addpaymethod').data('step',1).data('refkey',refKey);
				thisFormEl.html($('##mc_addPayMethodForm').html());
				$('html,body').animate({ scrollTop: $('##' + refKey).offset().top - 150 }, 'normal');
				initAddPayMethodForm(refKey);
				thisFormEl.find('select##profileID').change();
				thisFormEl.stop().slideToggle('normal');
			}
		}
		function initAddPayMethodForm(refKey){
			$('##' + refKey + '_actionform select##profileID').change(function () {
				var selectedValue = $(this).val() || 0;
				$('##' + refKey + '_actionform ##continue-button').prop('disabled',selectedValue == 0);
			});
		}
		function continueStepAddPM(btnEl){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var refKey = thisFormEl.data('refkey');
			var currentStep = thisFormEl.data('step') + 1;
			var profileID = thisFormEl.find('select##profileID').val() || 0;
			var paymethodLabel = getPaymethodLabelFromProfileDropdownSelection(refKey);

			if(currentStep == 4){
				doSaveAddPayMethod(refKey,profileID);
				return;
			}

			switch (currentStep) {
				case 2:
					if(profileID > 0){
						thisFormEl.find('##addPMStep2 div.addCOFFormTitle').html('Add ' + paymethodLabel + ':');

						var strFormAction = getProfileFormActionData(profileID,0,'add');
						if(strFormAction.actionFn){
							var thisFormEl = getFormElementSelector(refKey);
							thisFormEl.find('.addPayMethodInputFormHolder').html('<div id="divPaymentProfileForm'+ profileID +'" data-refid="'+refKey+'" data-onsavefn="onCompleteAddPMAction" data-oncancelfn="onCancelMPPAction" data-onerrorfn="onErrorMPPAction"></div>').show();
							strFormAction.initFn();
							strFormAction.actionFn();
						}
						else onErrorMPPAction('Unable to fetch profile information.',refKey);
					}
					else onErrorMPPAction('Unable to read method of payment type.',refKey);
					break;
				case 3:
					if(profileID > 0){
						thisFormEl.find('##addPMStep3 div.addCOFFormTitle').html(paymethodLabel + ' Added Successfully');
						thisFormEl.find('##addedPayMethodInfo').html('<span class="pm-text-gray">'+ paymethodLabel +' Details Loading...</span>');
						var createdPayProfileID = parseInt(thisFormEl.find('##createdPayProfileID').val()) || 0;
						displayAddedCardInfo(createdPayProfileID,refKey);

						var showSchedPayments = function() {
							var checkOptionsHTML = '';
							$.each(strEligibleProfilesForSched, function(thisSchedRefKey, thisProfileIDList) {
								var arrProfileIDs = thisProfileIDList.split(',');
								let isEligiblePayProfile = arrProfileIDs.length && arrProfileIDs.includes(profileID.toString());

								/* check bd mp profiles */
								if (!isEligiblePayProfile && arrProfileIDs.length && strBDMPProfiles.hasOwnProperty(profileID)) {
									let eligibleBDMPProfileID = arrProfileIDs.filter(pid => strBDMPProfiles.hasOwnProperty(pid));
									eligibleBDMPProfileID = eligibleBDMPProfileID.length ? eligibleBDMPProfileID[0] : 0;
									if (eligibleBDMPProfileID > 0 && strPayProfilesData.hasOwnProperty(createdPayProfileID) && strPayProfilesData[createdPayProfileID].extrainfo.tokenstore == 'bankdraft' 
											&& strBDMPProfiles[eligibleBDMPProfileID].includes(strPayProfilesData[createdPayProfileID].extrainfo.bankaccttype)) {
										isEligiblePayProfile = true;
									}
								}

								if(isEligiblePayProfile && strAllSchedDisplayLabels.hasOwnProperty(thisSchedRefKey)){
									var strLabelData = strAllSchedDisplayLabels[thisSchedRefKey];
									checkOptionsHTML += '<label class="qualifyingSched">'
										+ '<input type="checkbox" name="schedPayment" value="'+ thisSchedRefKey +'"/>'
										+ '<span>'
											+ '<span>' + strLabelData['paymentScheduleLabel'] + '</span>'
											+ '<br/>' + ((strLabelData['linkedPayMethodLabel'].length) ? '<span class="pm-check-sublabel pm-text-gray">' + strLabelData['linkedPayMethodLabel'] + '</span>' : '<span class="pm-text-orange">No method of payment on file</span>')
										+ '</span>'
									+ '</label>';
								}
							});
							if(checkOptionsHTML.length){
								$('##addPMStep3 div.optAssignToSchedPayments div.checkbox-container').html(checkOptionsHTML);
								$('##addPMStep3 div.optAssignToSchedPayments').show();
							}

							$('html,body').animate({ scrollTop: $('##mempaymethodaction').offset().top - 150 }, 'normal');
						};

						updatePayProfileDataJSON(createdPayProfileID,refKey,showSchedPayments);
					}
					break;
			}

			thisFormEl.data('step',currentStep);
			thisFormEl.find('div.pm-form-footer-actions').toggle(currentStep != 2);
			$('.addPMSteps').hide();
			$('##addPMStep' + currentStep).show();
		}
		function onCompleteAddPMAction(obj,refKey){
			if(obj.payprofileid && obj.payprofileid > 0){
				$('##' + refKey + '_actionform ##createdPayProfileID').val(obj.payprofileid);
				var btnEl = $('##' + refKey + '_actionform button##continue-button');
				continueStepAddPM(btnEl);
			}
			else onErrorMPPAction('We were unable to add the pay method.',refKey);
		}
		function displayAddedCardInfo(payProfileID,refKey) {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true' && r.data && Object.keys(r.data).length) {
					let cardInfoTemplateSource = $('##mc_paymethodInfoCard').html();
					let cardInfoTemplate = Handlebars.compile(cardInfoTemplateSource);
					var strPayMethod = r.data;
					let objPayProfile = {
						iconfullpath: strPayMethod.gatewayclass == 'bankdraft' ? '/assets/common/images/payment/check.png' : (strPayMethod.cardtype.length ? '/assets/common/images/payment/' + strPayMethod.cardtype.toLowerCase() + '_brandmark.png' : '/assets/common/images/blank.gif'),
						cardnumber: '**** ' + strPayMethod.detail.slice(-4),
						expdate: (strPayMethod.expiration.length ? 'Exp ' + strPayMethod.expiration : ''),
						profileonscreenlabel: strPayMethod.gatewayclass == 'bankdraft' ? strPayMethod.bankaccounttype : strPayMethod.profileonscreenlabel,
						conditionalSurchargeDiv:  strPayMethod.gatewayclass == 'creditcard' && strPayMethod.enablesurcharge ? '<div class="pm-text-gray"><i>' + (strPayMethod.surchargeeligible && strPayMethod.surchargepercent.toString().length ? strPayMethod.surchargepercent + '% surcharge applies' : 'No surcharge applies') + '</i></div>' : '',
						addeddate: strPayMethod.dateadded
					};
					$('##' + refKey + '_actionform ##addedPayMethodInfo').html(cardInfoTemplate(objPayProfile));
				} else {
					onErrorMPPAction('There was a problem loading the card details.',refKey,true);
				}
			};
			var objParam = { memberID:#local.memberID#, payProfileID:payProfileID };
			TS_AJX('GATEPAY','getMemberPaymentProfileInfo',objParam,getResult,getResult,10000,getResult);
		}
		function updatePayProfileDataJSON(payProfileID,refKey,callBackFn) {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true' && r.data && Object.keys(r.data).length) {
					var strPayMethod = r.data;
					strPayProfilesData[payProfileID] = {
						type: strPayMethod.cardtype,
						detail: strPayMethod.detail.slice(-4),
						expiration: strPayMethod.expiration,
						nickname: strPayMethod.nickname,
						extrainfo: {
							mpprofileid: strPayMethod.mpprofileid,
							tokenstore: strPayMethod.tokenstore.toLowerCase(),
							bankaccttype: strPayMethod.tokenstore.toLowerCase() == 'bankdraft' ? (strPayMethod.bankaccounttype.includes('business') ? 'business' : 'personal') : ''
						}
					}
				} else {
					onErrorMPPAction('There was a problem updating the payprofile data json.',refKey,true);
				}
				if(callBackFn) callBackFn();
			};
			var objParam = { memberID:#local.memberID#, payProfileID:payProfileID };
			TS_AJX('GATEPAY','getMemberPaymentProfileInfo',objParam,getResult,getResult,10000,getResult);
		}
		function doSaveAddPayMethod(refKey,profileID){
			var thisFormEl = getFormElementSelector(refKey);
			var createdPayProfileID = parseInt(thisFormEl.find('##createdPayProfileID').val()) || 0;
			var selectedSchedPaymentsList = thisFormEl.find(".optAssignToSchedPayments input[name='schedPayment']:checked").map(function() {
				return $(this).val();
			}).get().toString();

			/* if no scheduled payments are present/selected for associating the new card, just reload the page */
			if(selectedSchedPaymentsList.length == 0){
				onCancelMPPAction({},refKey);
				reloadPage(refKey);
				return false;
			}

			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					onCancelMPPAction({},refKey);
					reloadPage(refKey);
				} else {
					onErrorMPPAction('We were unable to assign the newly added pay method to the selected schedules.',refKey);
				}
			};

			thisFormEl.find('##continue-button').prop('disabled',true);

			let arrUpdateSchedPayments = [];

			selectedSchedPaymentsList.split(',').forEach(function(thisSchedRefKey, index) {
				let tmpStr = { reftype: thisSchedRefKey.split('_')[0], refID: thisSchedRefKey.split('_')[1], MPPayProfileID: createdPayProfileID, MPProfileID: profileID };
			
				var arrEligibleProfileIDs = strEligibleProfilesForSched.hasOwnProperty(thisSchedRefKey) ? strEligibleProfilesForSched[thisSchedRefKey].toString().split(',') : [];
				var isEligiblePayProfile = arrEligibleProfileIDs.includes(profileID.toString());

				/* check bd mp profiles */
				if (!isEligiblePayProfile && arrEligibleProfileIDs.length && strBDMPProfiles.hasOwnProperty(profileID)) {
					let eligibleBDMPProfileID = arrEligibleProfileIDs.filter(pid => strBDMPProfiles.hasOwnProperty(pid));
					eligibleBDMPProfileID = eligibleBDMPProfileID.length ? eligibleBDMPProfileID[0] : 0;
					if (eligibleBDMPProfileID > 0 && strPayProfilesData.hasOwnProperty(createdPayProfileID) && strPayProfilesData[createdPayProfileID].extrainfo.tokenstore == 'bankdraft' 
							&& strBDMPProfiles[eligibleBDMPProfileID].includes(strPayProfilesData[createdPayProfileID].extrainfo.bankaccttype)) {
						isEligiblePayProfile = true;
						tmpStr.MPProfileID = eligibleBDMPProfileID;
					}
				}
				
				if (isEligiblePayProfile) {
					arrUpdateSchedPayments.push(tmpStr);
				}
			});

			var objParams = {schedPayments:JSON.stringify(arrUpdateSchedPayments )};
			TS_AJX('INV','updateScheduledPaymentMethods',objParams,saveResult,saveResult,10000,saveResult);
		}
		function addProcessingFeeDonation(payProfileID,profileID,refKey,updateMode){
			var thisFormEl = getFormElementSelector(refKey);
			var actionMode = thisFormEl.data('mode') || 'addprocessingfee';

			if(thisFormEl.is(':visible') && actionMode == 'addprocessingfee')
				return false;

			$('.pm-action-form').html('').hide();

			if(!strAllSchedDisplayLabels.hasOwnProperty(refKey))
				return false;

			thisFormEl.data('mode','addprocessingfee').data('profileid',profileID).data('refkey',refKey).data('processfeeupdatemode',updateMode);

			var formHTML = getProcessFeeFormHTML(profileID,refKey,payProfileID,updateMode);
			formHTML = formHTML.replace('[fnSaveOnclick]', 'saveProcessingFeeDonation(this);');
			thisFormEl.html(formHTML);
			initProcessFeeForm(refKey);
			thisFormEl.find('.addProcessingFeeFormContainer').addClass('pm-mx-2 pm-mt-3 pm-mb-4');
			

			$('html,body').animate({ scrollTop: $('##' + refKey + '.paymethod-row').offset().top - 150 }, 'normal');
			thisFormEl.stop().slideToggle('normal');
		}
		function saveProcessingFeeDonation(btnEl){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var checkedValue = thisFormEl.find('input[name="processFeeDonation"]:checked').val() || '';
			if(checkedValue.length){
				$(btnEl).prop('disabled',true);
				var updateMode = thisFormEl.data('processfeeupdatemode');
				var payProcessFeeCurrentVal = (updateMode == 'remove' ? 1 : 0);
				if (payProcessFeeCurrentVal != checkedValue){
					var saveResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							closePayProfileActionForm(btnEl);
							reloadPage(thisFormEl.data('refkey'));
						} else {
							alert('We were unable to ' + updateMode + ' the processing fee. Try again.');
							$(btnEl).prop('disabled',false);
						}
					};
					var objParams = { schedPayment:thisFormEl.data('refkey'), MPProfileID:thisFormEl.data('profileid'), payProfileID:thisFormEl.data('payprofileid'), payProcessFee:checkedValue };
					TS_AJX('INV','updateProcessFeeDonation',objParams,saveResult,saveResult,10000,saveResult);
				}
				else {
					closePayProfileActionForm(btnEl);
				}
			}
			else {
				alert('Choose an option.');
				return false;
			}
		}
		function getProcessFeeDisplayData(profileID,refKey){
			var strResult = {
				enableProcessingFeeDonation:0,
				processFeeDonationFeePercent:0,
				processingFeeLabel:'',
				processFeeDonationFETitle:'',
				processFeeDonationFEMsg:'',
				processFeeDonationDefaultSelect:0,
				processFeeFELabel:'',
				processFeeFEDenyLabel:'',
				processFeeFELabelExtra:''
			};

			if(!strProfilesData.hasOwnProperty(profileID) || !strAllSchedDisplayLabels.hasOwnProperty(refKey))
				return strResult;
			
			var type = refKey.split('_')[0];
			var thisSchedData = strAllSchedDisplayLabels[refKey];
			var thisProfileData = strProfilesData[profileID];

			var arrProcFeeSupportedGatewayIDs = procFeeSupportedGatewayIDs.split(',');
			var isPFDSupportedGateway = arrProcFeeSupportedGatewayIDs.includes(thisProfileData.gatewayID.toString());

			strResult.enableProcessingFeeDonation = thisProfileData.enableProcessingFeeDonation == 1 && (thisSchedData.ovEnableProcessingFeeDonation.length == 0 || (thisSchedData.ovEnableProcessingFeeDonation.toString() == '1' && isPFDSupportedGateway));
			strResult.processFeeDonationFeePercent = (thisProfileData.enableProcessingFeeDonation == 1 && thisProfileData.processFeeDonationFeePercent > 0) ? thisProfileData.processFeeDonationFeePercent : 0;
			strResult.processingFeeLabel = thisProfileData.processingFeeLabel;
			strResult.processFeeDonationFETitle = thisSchedData.ovProcessFeeDonationFETitle.length ? thisSchedData.ovProcessFeeDonationFETitle : thisProfileData.processFeeDonationFETitle;
			strResult.processFeeDonationFEMsg = thisSchedData.ovProcessFeeDonationFEMsg.length ? thisSchedData.ovProcessFeeDonationFEMsg : thisProfileData.processFeeDonationFEMsg;
			strResult.processFeeDonationDefaultSelect = thisSchedData.ovProcessFeeDonationDefaultSelect.toString().length ? thisSchedData.ovProcessFeeDonationDefaultSelect : thisProfileData.processFeeDonationDefaultSelect;
			
			var processFeePercent = thisSchedData.processFeePercent > 0 ? thisSchedData.processFeePercent : strResult.processFeeDonationFeePercent;
			processFeePercent = parseFloat(processFeePercent);
			switch (type) {
				case "sub":
					strResult.processFeeFELabel = thisProfileData.processFeeSubscriptionsFELabel.replace(/{{PERCENT}}/gi, processFeePercent + "%");
					strResult.processFeeFEDenyLabel = thisProfileData.processFeeSubscriptionsFEDenyLabel;
					break;
				case "cp":
					strResult.processFeeFELabel = thisProfileData.processFeeContributionsFELabel.replace(/{{PERCENT}}/gi, processFeePercent + "%");
					strResult.processFeeFEDenyLabel = thisProfileData.processFeeContributionsFEDenyLabel;
					break;
				case "inv":
					var processingFee = (thisSchedData.paymentAmtUnformatted * processFeePercent / 100).toFixed(2);
					strResult.processFeeFELabel = thisProfileData.processFeeOtherPaymentsFELabel.replace(/{{AMOUNT}}/gi, '$' + processingFee);
					strResult.processFeeFELabelExtra = 'Estimate based on paying ' + processFeePercent + '% of the current amount due ($'+ thisSchedData.paymentAmtUnformatted.toFixed(2) +').';
					strResult.processFeeFEDenyLabel = thisProfileData.processFeeOtherPaymentsFEDenyLabel;
					break;
			}

			return strResult;
		}
		function initCustomDropdown(selectorID,onSelectFn){
			$('##' + selectorID + '.pm-custom-dropdown .dropdown-label').on("click", function () {
				var $parent = $(this).closest('.pm-custom-dropdown');
				var $list = $parent.find('.dropdown-list');
				if ($parent.hasClass("open")) {
					$list.hide();
					$parent.removeClass("open");
				} else {
					$list.show();
					$parent.addClass("open");
				}
			});

			$('##' + selectorID + '.pm-custom-dropdown ul.group-options > li, ##' + selectorID + '.pm-custom-dropdown ul.dropdown-list > li:not(.dropdown-group)').on("click", function () {
				var selectedText = $(this).text();
				var selectedValue = parseInt($(this).data("value"));
				$('##' + selectorID + '.pm-custom-dropdown li.dropdown-option').removeClass("selected");
				$(this).addClass("selected");
				
				$('##' + selectorID + '.pm-custom-dropdown .dropdown-label').html(selectedText);
				$('##' + selectorID + '.pm-custom-dropdown').removeClass("open");
				$('##' + selectorID + '.pm-custom-dropdown .dropdown-list').hide();

				onSelectFn(this,selectedValue);
			});
			
			$(document).on("click", function (e) {
				if (!$(e.target).closest('##' + selectorID + '.pm-custom-dropdown').length) {
					$('##' + selectorID + '.pm-custom-dropdown').removeClass("open");
					$('##' + selectorID + '.pm-custom-dropdown .dropdown-list').hide();
				}
			});
		}
		function getEligibleAdditionalSchedsCount(refKey,checkboxName){
			var thisFormEl = getFormElementSelector(refKey);
			return thisFormEl.find('div.payMethodTiedSchedPayments input[type="checkbox"][name="'+ checkboxName +'"]:not(:disabled)').length;
		}
		function getProfileIDFromPayMethodDropdownSelection(refKey,containerID){
			var thisFormEl = getFormElementSelector(refKey);
			return thisFormEl.find('##'+ containerID +' .dropdown-list li.dropdown-option.selected').closest('li.dropdown-group').data("profileid") || 0;
		}
		function getPaymethodLabelFromProfileDropdownSelection(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			var gatewayClass = thisFormEl.find('select##profileID').find(':selected').data('gatewayclass') || '';
			return gatewayClass == 'bankdraft' ? 'Bank Account' : 'Card';
		}
		function roundDueAmt(amt) {
			return Number(formatCurrency(amt).replace(/\$|\,/g,''));
		}
		function formatCurrency(num) {
			num = num.toString().replace(/\$|\,/g,'');
			if(isNaN(num)) num = "0";
			num = Math.abs(num);
			sign = (num == (num = Math.abs(num)));
			num = Math.floor(num*100+0.***********);
			cents = num%100;
			num = Math.floor(num/100).toString();
			if(cents<10) cents = "0" + cents;
			for (var i = 0; i < Math.floor((num.length-(1+i))/3); i++) num = num.substring(0,num.length-(4*i+3))+','+num.substring(num.length-(4*i+3));
			return (((sign)?'':'-') + num + '.' + cents);
		}
		function getProfileFormActionData(profileID,payProfileID,action){
			var strReturn = { actionFn:null, actionFnParam:'', initFn:null };
			if(strMerchantProfilesFormData.hasOwnProperty(profileID)){
				var strProfile = strMerchantProfilesFormData[profileID];
				strReturn.initFn = eval(strProfile['strActionFn']['init']['fnName']);
				if(action == 'add'){
					strReturn.actionFn = eval(strProfile['strActionFn']['add']['fnName']);
				}
				else if (payProfileID > 0 && $.inArray(action,['edit','remove']) !== -1){
					$.each(strProfile['arrMemProfilesOnFile'], function(_, thisPayProfile) {
						if(thisPayProfile.payProfileID == payProfileID){
							strReturn.actionFn = eval(thisPayProfile['strActionFn'][action]['fnName']);
							strReturn.actionFnParam = thisPayProfile['strActionFn'][action]['fnParam'];
							return false;
						}
					});
				}
			}
			return strReturn;
		}
		function closePayProfileActionForm(btnEl){
			var thisFormEl = getFormElmSelectorFromButton(btnEl);
			var refKey = thisFormEl.data('refkey');
			var actionMode = thisFormEl.data('mode') || '';
			var scrollToEl = refKey == 'mempaymethodaction' ? $('##memberPayMethodsList') : $('##' + refKey);
			thisFormEl.stop().slideToggle('normal');
			$('html,body').animate({ scrollTop: scrollToEl.offset().top - 150 }, 'normal');

			if(actionMode.length > 0 && $.inArray(actionMode,['assocpaymethod','addpaymethod','removepaymethod']) != -1){
				var createdPayProfileID = thisFormEl.find('##createdPayProfileID').val() || 0;
				/* if new pay method is already added, reload the page on cancel button click */
				if(parseInt(createdPayProfileID) > 0){
					reloadPage(refKey);
				}
			}
		}
		function onCancelMPPAction(obj,refKey){
			var scrollToEl = refKey == 'mempaymethodaction' ? $('##memberPayMethodsList') : $('##' + refKey);
			$('html,body').animate({ scrollTop: scrollToEl.offset().top - 150 }, 'normal');
			$('##' + refKey + '_actionform').stop().slideToggle('normal');
		}
		function onErrorMPPAction(msg,refKey,hideClose){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('.profileErrorForm div.msg').html(msg);
			thisFormEl.find('.profileErrorForm button##cancel-button').toggle(hideClose ? false : true);
			thisFormEl.find('.profileErrorForm').show();
			$('html,body').animate({ scrollTop: $('##' + refKey).offset().top - 150 }, 'normal');
		}
		function clearMPPFormErrorMsg(refKey){
			var thisFormEl = getFormElementSelector(refKey);
			thisFormEl.find('.profileErrorForm div.msg').html('');
			thisFormEl.find('.profileErrorForm').hide();
		}
		function onCompleteMPPAction(obj,refKey){
			onCancelMPPAction(obj,refKey);
			reloadPage(refKey);
		}
		function reloadPage(scrollToRefKey){
			if(scrollToRefKey) {
				scrollToRefKey = (scrollToRefKey == 'mempaymethodaction' ? 'additionalPMSection' : scrollToRefKey);
				localStorage.setItem('pm_scrollto', scrollToRefKey);
			}
			self.location.href = "#local.redirectURL#";
		}
		function getFormElementSelector(refKey){
			return $('##' + refKey + '_actionform');
		}
		function getFormElmSelectorFromButton(btnEl){
			return $(btnEl).closest('.pm-action-form');
		}
		function delayPMFunc(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		}
		function scrollToElement(elID) {
			let scrollTo = $('##'+elID);
			if(scrollTo.length) {
				$('html, body').animate({
					scrollTop: scrollTo.offset().top - 150
				}, 500);
			}
		}
		$(function() {
			delayPMFunc(500).then(function() {
				let scrollto = localStorage.getItem('pm_scrollto');
				if (scrollto != null) {
					scrollToElement(scrollto);
					localStorage.removeItem('pm_scrollto');
				}
			});
		});
	</script>

	<style type="text/css">
		.pm-container {font-size:15px;line-height:1.4;}
		.info-col {margin:.2em;}
		.payment-table { width: 100%; border-collapse: collapse; border: 1px solid ##ccc; }
		.payment-table .pm-action-form { border-top: 1px dotted darkgrey; margin-top:1em; padding-top:1em; }
		.paymethod-row { padding: .5em; }
		.paymethod-row:not(:last-child) { border-bottom: 1px solid ##ccc; }
		.pm-flex-row { display: flex; flex-wrap: wrap; }
		.pm-flex-row > div { box-sizing: border-box !important; }
		.pm-flex-row > div:first-child, .pm-flex-row > div:nth-child(2) {
			flex: 1 0 calc((100% - 280px) / 2);
			/* max-width: calc((100% - 280px) / 2); */
			min-width:125px;
		}
		.pm-flex-row > div:last-child {	flex: 1 0 auto;  min-width:125px;}
		.paymethod-row:nth-child(odd) { background-color: ##f9f9f9; }
		.paymethod-row.status-warning { border: 1px solid orange; border-left: 4px solid orange; }
		.paymethod-row.status-error { border: 1px solid red; border-left: 4px solid red; }
		.tsAppHeading.subheading { font-size:10.5pt; margin-bottom:.5em; }
		##mempaymethodaction .pm-action-form { padding: 9px; border: 1px solid darkgrey; margin-top: 15px; border-radius: 10px; }
		.addProcessingFeeFormContainer { width:340px; }
		select##profileID.custom-select { width:260px; }
		.pm-d-flex { display:flex!important; }
		.pm-flex-column {flex-direction: column !important;}
		.pm-col-auto {flex:0 0 auto;width:auto;max-width:100%;padding-right:5px;padding-left:5px;}
		.pm-flex-wrap { flex-wrap:wrap!important; }
		.pm-text-red { color: red!important; }
		.pm-text-orange { color: orange!important; }
		.pm-text-gray { color: gray!important; }
		.pm-text-forestgreen { color: forestgreen!important; }
		.pm-no-records { padding:.5em; }
		.pm-mt-1 {margin-top:.25em!important;}
		.pm-mt-2 {margin-top:.5em!important;}
		.pm-mt-3 {margin-top:.75em!important;}
		.pm-mt-4 { margin-top:1em!important; }
		.pm-mb-1 { margin-bottom:.25em!important; }
		.pm-mb-2 { margin-bottom:.5em!important; }
		.pm-mb-3 { margin-bottom:.75em!important; }
		.pm-mb-4 { margin-bottom:1em!important; }
		.pm-mr-1 {margin-right:.25em!important;}
		.pm-ml-1 {margin-left:.25em!important;}
		.pm-ml-2, .pm-mx-2 {margin-left:.5em!important;}
		.pm-mr-2, .pm-mx-2 {margin-right:.5em!important;}
		.pm-mr-3 {margin-right:1em!important;}
		.pm-ml-2 {margin-left:.5em!important;}
		.pm-ml-auto {margin-left:auto !important;}
		.pm-pb-1 { padding-bottom:.25em!important; }
		.pm-font-size-sm { font-size:.85em!important; }
		.pm-font-size-md {font-size:.95em!important;}
		.pm-font-size-lg {font-size:1.0875em!important;}
		.pm-font-size-xl {font-size:1.425em!important;}
		.pm-font-weight-bold {font-weight:bold!important;}
		.pm-align-content-center {align-content:center!important;}
		.pm-align-items-center {align-items:center;}
		.pm-justify-items-center {justify-items:center!important;}
		.pm-cardbox {flex:1 0 auto; border:1px solid ##ccc!important;border-radius:5px!important;box-sizing: border-box!important;padding:15px!important;width:250px!important;}
		.pm-cardinfo-box { min-height:15px; }
		.pm-cardinfo-box .pm-cardbox { width:260px!important; }
		.pm-check-sublabel { display: inline-block; padding-top: 2px; margin-left:5px; }
		.pm-card-container {width:260px!important;padding:5px;display:flex;}
		.pm-border-bottom { border-bottom: 1px solid ##ccc !important;}
		.pm-border-danger {border-color:##f83245 !important;}
		.pm-alert-warning { border: 1px solid ##ffc966; background-color: ##fff4cc; color: ##cc7a00; padding: 10px; margin: 10px 0; border-radius: 5px; }
		.pm-alert-error { border: 1px solid ##f19797; background-color: ##ffe6e6; color: ##cc0000; padding: 10px; margin: 10px 0; border-radius: 5px; }
		.pm-text-decoration-none {text-decoration:none !important;}
		.pm-dropdown {display: inline-block;position:relative;outline:none;}
		.pm-dropbtn {cursor: pointer;transition: 0.35s ease-out;}
		.pm-dropdown .pm-dropdown-content {position: absolute;top:10%;right:50%;background-color: ##f7f7f7;min-width:245px;box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
			z-index: 100000;visibility: hidden;opacity: 0;transition: 0.35s ease-out;}
		.pm-dropdown-content a {color: black;padding: 12px 16px;display: block;text-decoration: none;transition: 0.35s ease-out;}
		.pm-dropdown-content a:hover {background-color: ##eaeaea;}
		.pm-dropdown:focus .pm-dropdown-content { outline: none;transform: translateY(20px);visibility: visible;opacity: 1;}
		.pm-dropbtn:hover, .pm-dropdown:focus .pm-dropbtn {transform: translateY(-2px);}
		.pm-dropdown .pm-dropbtnmask {position: absolute;  top: 0; right: 0; bottom: 0; left: 0; opacity: 0;cursor: pointer;z-index: 10;display: none;}
		.pm-dropdown:focus .pm-dropbtnmask {display: inline-block;}
		.pm-dropdown .pm-dropbtnmask:focus .pm-dropdown-content {outline: none;visibility: hidden;opacity: 0;}
		a.pm-dropbtn {text-decoration:none !important;}
		.pm-dropbtn i {width:30px;height:30px;text-align:center;line-height:30px;}
		.pm-dropbtn i:hover {background-color:##eee;border-radius: 50%;}
		.pm-dropdown i {float:none !important;}

		.pm-custom-dropdown { position: relative; font-family: Arial, sans-serif; border-radius: 8px; max-width:100%; }
		.pm-custom-dropdown .dropdown-label { padding: 10px 15px; border: 1px solid ##ccc; background-color: ##fff; cursor: pointer; display: flex; justify-content: space-between; align-items: center; border-radius: 8px; }
		.pm-custom-dropdown .dropdown-arrow { font-size: 20px; color: ##888; }
		.pm-custom-dropdown .dropdown-list { display: none; position: absolute; top: 100%; left: 0; width: 100%; border: 1px solid ##ccc; background-color: ##fff; list-style: none; padding: 0; margin: 0; z-index: 10; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border-radius: 8px; max-height: 300px; overflow-y: auto; }
		.pm-custom-dropdown .dropdown-group { padding: 0; }
		.pm-custom-dropdown .group-label { font-weight: bold; color: gray; padding: 10px 15px; border-radius: 8px 8px 0 0; }
		.pm-custom-dropdown .group-options { list-style: none; margin: 0; padding: 0; }
		.pm-custom-dropdown .group-options li { padding: 10px 30px; cursor: pointer; transition: background-color 0.3s ease; }
		.pm-custom-dropdown .group-options li:hover { background-color: ##f0f0f0; }
		.pm-custom-dropdown li.dropdown-option.selected { background-color: ##f0f0f0; }
		.pm-custom-dropdown.open .dropdown-list { display: block; }
		.pm-custom-dropdown .dropdown-list > li:not(.dropdown-group) { padding: 10px 15px; cursor: pointer; transition: background-color 0.3s ease; } 
		.pm-custom-dropdown .dropdown-list > li:not(.dropdown-group):hover { background-color: ##f0f0f0; }
		.pm-custom-dropdown .dropdown-label::after { content: '\25BC'; font-size: 10px; color: ##888; margin-left: 10px; }
		.pm-custom-dropdown.pm-custom-dropdown-disabled { pointer-events: none; cursor: not-allowed; opacity: 0.6; }  
		.pm-custom-dropdown.pm-custom-dropdown-disabled .dropdown-label { background-color: ##e0e0e0; border-color: ##d0d0d0; color: ##a0a0a0; }
		.pm-form-footer-actions { margin-top:1.5em!important; margin-bottom:1em!important; }
		button.pm-act-button { background-color: ##f0f0f0; border: 1px solid ##ccc; border-radius: 8px; padding: 6px 14px; font-size: 14px; color: ##333; cursor: pointer; transition: background-color 0.3s ease, box-shadow 0.3s ease; }
		button.pm-act-button:hover { background-color: ##e0e0e0; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); }
		button.pm-act-button:active { background-color: ##d6d6d6; box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.2); }
		button.pm-act-button:disabled { background-color: ##eaeaea; border-color: ##dcdcdc; color: ##a0a0a0; cursor: default; box-shadow: none; }
		button.pm-act-button.pm-act-button-primary { background-color: ##007BFF; border-color: ##0056b3; color: ##ffffff; }
		button.pm-act-button.pm-act-button-primary:hover { background-color: ##0056b3; }
		button.pm-act-button.pm-act-button-primary:active { background-color: ##003f7f; }
		button.pm-act-button.pm-act-button-primary:disabled { background-color: ##cce5ff; border-color: ##b8daff; color: ##b4bfc9; }
		button.pm-act-button.pm-act-button-danger { background-color: ##e63946; border-color: ##d62839; color: ##ffffff; }
		button.pm-act-button.pm-act-button-danger:hover { background-color: ##d62839; }
		button.pm-act-button.pm-act-button-danger:active { background-color: ##b71d28; }
		button.pm-act-button.pm-act-button-danger:disabled { background-color: ##f9d2d5; border-color: ##f4aeb2; color: ##6c757d; }

		.checkbox-container { display: flex; flex-direction: column; gap: 10px; font-family: Arial, sans-serif; color: ##333; align-items: flex-start }
		.checkbox-container label { display: flex; align-items: center; gap: 8px; font-size: 14px; line-height: 1.4; }
		.checkbox-container input[type="checkbox"] { width: 16px; height: 16px; accent-color: ##007bff; cursor: pointer; margin:0 }
		.checkbox-container span { font-weight: 500; color: ##555; }
		.checkbox-container.pm-radio-group { gap: 3px; }
		.checkbox-container.checkbox-with-sublabel label { align-items: flex-start!important; }
		.checkbox-container.checkbox-with-sublabel input[type="checkbox"] { margin-top: 2px!important; }

		@media (min-width: 768px) {
			.pm-custom-dropdown { width:70%; }
		}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.pageJS#">