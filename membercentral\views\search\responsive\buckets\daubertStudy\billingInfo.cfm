<cfoutput>
<cfswitch expression="#local.rptOrderStep#">
	<cfcase value="init">
		<cfsavecontent variable="local.pageHead">
			<cfoutput>
			<script type="text/javascript">
				function hideAlert() { $('##err_billInfo').html('').hide(); }
				function showAlert(msg) { 
					$('##err_billInfo').html(msg).show(); 
					$('.mc-modal-body').animate({ scrollTop: 0 }, 500);
				}
			</script>
			<style>
				.mc-black-button {background-color: ##000;color: ##fff;font-weight: bold;text-transform: uppercase;padding: 14px 32px;border: none;border-radius: 16px;font-size: 14px;letter-spacing: 1px;cursor: pointer;
					width:100%;max-width:380px;display:block;text-align: center;transition: background 0.2s ease;}
				.mc-black-button:hover {background-color: ##2d2d2d;}
			</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.pageHead)#">
		
		<div id="expert#local.expertID#order" class="bk-h-100">
			<form id="frmDCSReport" name="frmDCSReport" onsubmit="return false">
				<input type="hidden" name="expertID" value="#local.expertID#">
				<input type="hidden" name="expertName" value="#local.expertName#">
				<input type="hidden" name="expertLocation" value="#local.expertLocation#">
				<input type="hidden" name="expertArea" value="#local.expertArea#">
				<input type="hidden" name="rptType" value="#local.rptType#">
				<input type="hidden" name="rptOrderNextStep" value="billing">
				
				<div id="expert#local.expertID#billing"></div>
				<div id="expert#local.expertID#payment"></div>
			</form>
			<div id="expert#local.expertID#confirmation" style="display:none;"></div>
		</div>
	</cfcase>
	<cfcase value="billing">
		<div class="bk-card-box bk-p-3 bk-mt-1">
			<div class="bk-mb-2 bk-border-bottom bk-pb-2">
				<h5 class="bk-mt-0 bk-mb-0">EXPERT INFORMATION</h5>
			</div>
			<div class="bk-font-size-xs bk-text-dim bk-mb-1">Expert Name:</div>
			<div id="expName#local.expertID#" class="bk-font-size-md bk-font-weight-bold"></div>
			<div class="bk-font-size-xs bk-text-dim bk-mb-1 bk-mt-2 expLoc#local.expertID#" style="display:none;">Location:</div>
			<div id="expLoc#local.expertID#" class="bk-font-size-md bk-font-weight-bold expLoc#local.expertID#" style="display:none;"></div>
			<div class="bk-font-size-xs bk-text-dim bk-mb-1 bk-mt-2 expArea#local.expertID#" style="display:none;">Area of Expertise:</div>
			<div id="expArea#local.expertID#" class="bk-font-size-md bk-font-weight-bold expArea#local.expertID#" style="display:none;"></div>
		</div>

		<div id="err_billInfo" class="alert alert-danger" style="display:none;margin:6px 0;"></div>
		<div id="DCSOrderBillInfo" class="bk-card-box bk-p-3 bk-mt-2">
			<div class="bk-mb-2 bk-border-bottom bk-pb-2">
				<h5 class="bk-mt-0 bk-mb-0">BILLING INFORMATION</h5>
			</div>
			<div class="bk-d-flex bk-mb-2">
				<div class="bk-col">
					<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">Address</div>
					<input type="text" id="BillingAddress" name="BillingAddress" class="bk-formcontrol bk-w-100" value="#local.qryTSMemberData.BillingAddress#" maxlength="100">
				</div>
			</div>
			<div class="bk-d-flex bk-mb-2">
				<div class="bk-col">
					<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">City</div>
					<input type="text" id="BillingCity" name="BillingCity" class="bk-formcontrol bk-w-100" value="#local.qryTSMemberData.BillingCity#" maxlength="50">
				</div>
				<div class="bk-col">
					<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">State/Province</div>
					<select id="billingstate" name="billingstate" class="bk-formcontrol bk-w-100">
						<option value=""></option>
						<cfloop query="local.qryStates">
							<option value="#local.qryStates.code#" #local.user_billingstate eq local.qryStates.code ?"selected='selected'":""#>#local.qryStates.name#</option>
						</cfloop>
					</select>
				</div>
				<div class="bk-col">
					<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">Postal Code</div>
					<input type="text" id="billingzip" name="billingzip" class="bk-formcontrol bk-w-100" value="#local.user_billingzip#" maxlength="25">
				</div>
			</div>
			<div class="bk-d-flex">
				<div class="bk-col">
					<div class="bk-font-size-xs bk-font-weight-bold bk-mb-1">E-mail</div>
					<input type="text" id="userMainEmail" name="userMainEmail" class="bk-formcontrol bk-w-100" value="#local.qryTSMemberData.Email#" maxlength="100">
				</div>
			</div>
			<button type="button" name="btnSaveDCSOrderBillInfo" id="btnSaveDCSOrderBillInfo" class="mc-black-button bk-mt-3 bk-mx-auto">Continue</button>
		</div>
	</cfcase>
	<cfcase value="payment">
		<div class="bk-card-box bk-p-3 bk-mt-2">
			<div class="bk-mb-2 bk-border-bottom bk-pb-2">
				<h5 class="bk-mt-0 bk-mb-0">CASE REFERENCE</h5>
			</div>
			<div class="bk-d-flex bk-mb-2">
				<div class="bk-col">
					<div class="bk-mb-1" style="font-size:0.75em;">Add reference which will appear on your monthly statement.</div>
					<input type="text" id="caseref" name="caseref" class="bk-formcontrol bk-w-100" value="" maxlength="50">
				</div>
			</div>
			<div id="selCaseRefBadges" class="well bk-p-2 bk-mt-2 bk-mb-0" style="display:none;"></div>
		</div>

		<div class="bk-card-box bk-p-3 bk-mt-2">
			<div class="bk-mb-2 bk-border-bottom bk-pb-2">
				<h5 class="bk-mt-0 bk-mb-0">PAYMENT METHOD</h5>
			</div>
			<cfset local.strPaymentForm = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").gather(merchantOrgcode='TS', customerid='olddid_#session.cfcuser.memberdata.depomemberdataid#', editMode='frontEndPayment', 
						autoShowForm=1, chargeInfo={ "amt":local.totalPrice, "acceptApplePay":1, "acceptGooglePay":1 })>
			<cfif len(local.strPaymentForm.headcode)>
				#local.strPaymentForm.headcode#
			</cfif>
			<cfif len(local.strPaymentForm.inputForm)>
				<div id="CIMTable">
					<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_TS_fld_','ALL')#</div>
				</div>
			</cfif>

			<cfif len(local.strPaymentForm.jsvalidation)>
				<cfsavecontent variable="local.extrapayJS">
					<cfoutput>
					#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_TS_fld_','ALL')#
					</cfoutput>
				</cfsavecontent>
			</cfif>

			<cfset local.strSiteInfo = application.objSiteInfo.getSiteInfo("TS")>
			<cfset local.strOrgIdentityInfo = CreateObject("component","model.admin.organization.organization").getOrgIdentityDetailsStruct(orgID=local.strSiteInfo.orgID, orgIdentityID=local.strSiteInfo.defaultOrgIdentityID)>
			<cfset local.qrySecureData = createObject("component","model.admin.custom.mc.mc.PlatformSettings").getPlatformSettingsContent(contentTitle='Platformwide_Secure_Checkout_Policy')>
			
			<cfset local.arrEcomLinks = []>
			<cfif local.qrySecureData.rawContent neq "">
				<cfset local.arrEcomLinks.append('<a href="/?pg=buyNow&viewPolicy=secureCheckout" class="buynow-mt-sm-2" target="_blank">Secure Checkout</a>')>
			</cfif>
			<cfif StructKeyExists(local.strSiteInfo,"deliveryPolicyURL") AND len(local.strSiteInfo.deliveryPolicyURL)>
				<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=delivery" class="buynow-mt-sm-2" target="_blank">Delivery Policy</a>')>
			</cfif>
			<cfif StructKeyExists(local.strSiteInfo,"privacyPolicyURL") AND len(local.strSiteInfo.privacyPolicyURL)>
				<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=privacy" class="buynow-mt-sm-2" target="_blank">Privacy Policy</a>')>
			</cfif>
			<cfif StructKeyExists(local.strSiteInfo,"rrPolicyURL") AND len(local.strSiteInfo.rrPolicyURL)>
				<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=refund" class="buynow-mt-sm-2" target="_blank">Refunds and Returns</a>')>
			</cfif>
			<cfif StructKeyExists(local.strSiteInfo,"tcURL") AND len(local.strSiteInfo.tcURL)>
				<cfset local.arrEcomLinks.append('<a href="#local.strSiteInfo.scheme#://#local.strSiteInfo.mainHostName#/?pg=buyNow&viewPolicy=terms" class="buynow-mt-sm-2" target="_blank">Terms and Conditions</a>')>
			</cfif>

			<div class="bk-text-center bk-mt-3" style="font-size:11px;">
				<cfif arrayLen(local.arrEcomLinks)>
					<div>#arrayToList(local.arrEcomLinks,' | ')#</div>
				</cfif>
				<address style="margin-bottom:0;">
					#local.strOrgIdentityInfo.orgname# <span>#local.strOrgIdentityInfo.address1#<cfif len(local.strOrgIdentityInfo.address2)> #local.strOrgIdentityInfo.address2#</cfif><cfif len(local.strOrgIdentityInfo.city)> #local.strOrgIdentityInfo.city#</cfif><cfif len(local.strOrgIdentityInfo.state)>, #local.strOrgIdentityInfo.state#</cfif><cfif len(local.strOrgIdentityInfo.postalcode)> #local.strOrgIdentityInfo.postalcode#</cfif></span><br/>
					#local.strOrgIdentityInfo.phone# <cfif len(local.strOrgIdentityInfo.email)><a href="mailto:#local.strOrgIdentityInfo.email#">#local.strOrgIdentityInfo.email#</a></cfif>
				</address>
			</div>
		</div>

		<div class="bk-card-box bk-p-3 bk-mt-2 bk-mb-3">
			<div class="bk-mx-auto bk-w-100 bk-font-size-sm" style="max-width:400px;">
				<div class="bk-d-flex bk-mb-1">
					<div class="bk-col">Subtotal:</div>
					<div class="bk-col bk-text-right bk-pr-3">#dollarFormat(local.subtotal)#</div>
				</div>
				<div class="bk-d-flex bk-mb-1">
					<div class="bk-col">Tax:</div>
					<div class="bk-col bk-text-right bk-pr-3">#dollarFormat(local.taxAmount)#</div>
				</div>
				<div class="bk-d-flex bk-mb-1">
					<div class="bk-col">Total:</div>
					<div class="bk-col bk-text-right bk-pr-3">#dollarFormat(local.totalPrice)#</div>
				</div>
				<button type="button" name="btnOrderDCSRpt" id="btnOrderDCSRpt" class="mc-black-button bk-mx-auto bk-mt-4">Pay #dollarFormat(local.totalPrice)#</button>
			</div>
		</div>
	</cfcase>
	<cfcase value="confirmation">
		<div class="bk-card-box bk-p-3 bk-mt-1">#local.confirmationHTML#</div>

		<script type="text/javascript">
			#toScript(session.mcstruct.doccartCaseRefs,"vars_caserefs")#
			$('.mc-modal-body').animate({ scrollTop: 0 }, 500);
		</script>
	</cfcase>
</cfswitch>
</cfoutput>