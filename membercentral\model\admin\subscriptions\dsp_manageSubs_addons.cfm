<cfoutput>
<cfloop collection="#local.addOns#" item="local.addOnID">
	<cfset local.thisSubAddOn = duplicate(local.addOns[local.addOnID])>
	<cfset local.thisAddOnSubsCount = arrayLen(local.thisSubAddOn.subscriptions)>
	
	<cfset local.recursionLevel = 0>
	<cfif structKeyExists(local.thisSubAddOn, 'recursionlevel')>
		<cfset local.recursionLevel = local.thisSubAddOn.recursionLevel>
	</cfif>

	<cfset local.selectionMessage = "">
	<cfif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0 AND local.thisAddOnSubsCount eq 1>
		<cfset local.selectionMessage = 'You may optionally select this item:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You may optionally select one or more of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed is 1>
		<cfset local.selectionMessage = 'You may optionally select only one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed EQ 0 AND local.thisSubAddOn.strAddOn.maxAllowed GT 0>
		<cfset local.selectionMessage = 'You may optionally select up to #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed is 1 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You must select at least one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed GT 0 AND local.thisSubAddOn.strAddOn.maxAllowed EQ 0>
		<cfset local.selectionMessage = 'You must select at least #local.thisSubAddOn.strAddOn.minAllowed# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed is 1 AND local.thisSubAddOn.strAddOn.maxAllowed is 1>
		<cfset local.selectionMessage = 'You must select only one of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed gt 1 AND local.thisSubAddOn.strAddOn.minAllowed eq local.thisSubAddOn.strAddOn.maxAllowed>
		<cfset local.selectionMessage = 'You must select #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	<cfelseif local.thisSubAddOn.strAddOn.minAllowed gt 0 AND local.thisSubAddOn.strAddOn.maxAllowed GT 0>
		<cfset local.selectionMessage = 'You must select #local.thisSubAddOn.strAddOn.minAllowed# to #min(local.thisSubAddOn.strAddOn.maxAllowed,local.thisAddOnSubsCount)# of the following:'>
	</cfif>

	<cfset local.addOnCardClasses = "mb-2">
	<cfset local.addOnCardBodyClasses = "subAddOnFormContainer sub#arguments.subscriptionID#_addons">
	<cfset local.addOnCardTitleClasses = "d-flex no-gutters sub#arguments.subscriptionID#_addonID#local.addOnID#_titleContainer subAddOn#local.addOnID#TitleContainer mb-0">

	<cfif arguments.inlineAddOn>
		<cfset local.addOnCardClasses = "#local.addOnCardClasses# inlineAddOn">
		<cfset local.addOnCardBodyClasses = "#local.addOnCardBodyClasses# pl-4">
		<cfset local.addOnCardTitleClasses = "#local.addOnCardTitleClasses# innerAddOnTitleContainer">
		<cfset local.addOnCardDataAttributes = 'data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#"'>
	<cfelse>
		<cfset local.addOnCardClasses = "#local.addOnCardClasses# mt-2 card addOnCards mainSubCard">
		<cfset local.addOnCardBodyClasses = "#local.addOnCardBodyClasses# card card-box addOnCardBody d-none">
		<cfset local.addOnCardDataAttributes = 'data-errfields="" data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#" 
												data-linkedsummarycontainer="sub#arguments.subscriptionID#_addonID#local.addOnID#_summary" 
												data-linkedformcontainer="sub#arguments.subscriptionID#_addonID#local.addOnID#_formWrapper" 
												data-linkedtitlecontainer="subAddOn#local.addOnID#TitleContainer" 
												data-linkedtitleerrorcontainer="addOnTitleAlert"'>
	</cfif>

	<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_card" class="#local.addOnCardClasses#" #local.addOnCardDataAttributes#>
		<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_formWrapper" 
			class="#local.addOnCardBodyClasses#"
			data-addonid="#local.addOnID#"
			data-subscriptionid="#arguments.subscriptionID#" 
			data-childsetid="#local.thisSubAddOn.strAddOn.setID#" 
			data-minallowed="#local.thisSubAddOn.strAddOn.minAllowed#"
			data-maxallowed="#local.thisSubAddOn.strAddOn.maxAllowed#"
			data-itemsfree="#local.thisSubAddOn.strAddOn.PCNum#"
			data-displaypriceelement="subAddOn#local.addOnID#TotalPrice">
			
			<div<cfif not arguments.inlineAddOn> class="card-header bg-light"</cfif>>
				<div class="#local.addOnCardTitleClasses#">
					<div id="subSet#local.thisSubAddOn.strAddOn.setID#" 
						class="font-weight-bold col" 
						data-setname="#encodeForHTMLAttribute(local.thisSubAddOn.strAddOn.setname)#" 
						data-recursionlevel="#local.recursionLevel#"
						data-inlineaddon="#arguments.inlineAddOn ? 1 : 0#">
						<span<cfif arguments.inlineAddOn> class="font-size-md"</cfif>>#local.thisSubAddOn.strAddOn.setname#</span>
						<cfif local.thisSubAddOn.strAddOn.minAllowed GT 0>
							<span class="text-danger">*</span>
						</cfif>
						<cfif NOT arguments.inlineAddOn>
							<span class="subAddOn#local.addOnID#TotalPrice text-nowrap"></span>
						</cfif>
						<span class="text-normal selectedSubsCount#local.addOnID# ml-2 font-size-sm text-nowrap"></span>
						<div class="addOnTitleAlert font-size-sm"></div>
					</div>
				</div>
			</div>
			<div class="<cfif not arguments.inlineAddOn>card-body py-3<cfelse>mt-2</cfif>">
				<div class="addOnSubFrontEndContent">
					#local.thisSubAddOn.strAddOn.frontEndContent#
				</div>
				<cfif structKeyExists(local.thisSubAddOn, 'subscriptions') AND local.thisAddOnSubsCount>
					<cfif len(local.selectionMessage)>
						<div class="mb-3 font-weight-bold font-size-sm addOnSelMsg">#local.selectionMessage#</div>
					</cfif>
					<div class="mt-3 subsContainer">
						#manageSubscription_renderSubscriptionsForm(arrSubs=local.thisSubAddOn.subscriptions, strAddOn=local.thisSubAddOn.strAddOn, parentSubscriptionID=arguments.subscriptionID, 
							strParentFreq=arguments.strParentFreq, freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, recursionlevel=local.recursionLevel,
							listSubscribed=arguments.listSubscribed)#
					</div>
					<cfif NOT arguments.inlineAddOn>
						<div class="d-flex no-gutters align-items-center addOnFooterContainer mt-3 border-lightgray border-top pt-2">
							<div class="col-auto">
								<button type="button" class="btn btn-sm btn-primary w-sm-100 subCardConfirmBtn" onclick="confirmSubAddOn(#local.addOnID#,#arguments.subscriptionID#,true);return false;">
									Confirm
								</button>
							</div>
							<div class="col-auto pl-2">
								<button type="button" name="btnCancelAddOnChanges" class="btn btn-sm btn-secondary subCardCancelBtn d-none" onclick="cancelSubAddOnChanges(#local.addOnID#,#arguments.subscriptionID#);">Cancel</button>
							</div>
							<div class="addOnBottomAlert ml-2 mb-0"></div>
						</div>
					</cfif>
				</cfif>
			</div>
		</div>
		<cfif NOT arguments.inlineAddOn>
			<div id="sub#arguments.subscriptionID#_addonID#local.addOnID#_summary" class="card card-box subCardSummary">
				<div class="card-header bg-light">
					<div class="d-flex no-gutters">
						<div class="font-weight-bold col">
							<span>#local.thisSubAddOn.strAddOn.setname#</span>
							<cfif local.thisSubAddOn.strAddOn.minAllowed GT 0>
								<span class="text-danger">*</span>
							</cfif>
							<span class="subAddOn#local.addOnID#TotalPrice text-nowrap"></span> 
							<span class="text-normal selectedSubsCount#local.addOnID# ml-2 font-size-sm text-nowrap"></span>
						</div>
					</div>
				</div>
				<div class="card-body py-3">
					#local.thisSubAddOn.strAddOn.frontEndContent#
					<div class="addOnSummary<cfif len(local.thisSubAddOn.strAddOn.frontEndContent)> pl-2</cfif>">
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
						<div class="mcsubs-skeleton mcsubs-skeleton-text"></div>
					</div>
					<div class="mt-3">
						<button type="button" data-editmode="editsubaddon" data-addonid="#local.addOnID#" data-subscriptionid="#arguments.subscriptionID#" class="btn btn-sm btn-secondary w-sm-100 subCardEditBtn d-none"></button>
					</div>
				</div>
			</div>
		</cfif>
	</div>

	<!--- Subscription AddOns --->
	<cfloop from="1" to="#arrayLen(local.thisSubAddOn.subscriptions)#" index="local.i">
		<cfset local.thisSub = duplicate(local.thisSubAddOn.subscriptions[local.i])>
			
		<cfif structKeyExists(local.thisSub,'addOns') AND StructCount(local.thisSub.addOns) AND local.thisSub.currAddOnRecursionLevel + 1 NEQ local.thisSub.maxAddOnRecursionLevel>
			<cfset local.thisSubSelected = structKeyExists(local.thisSub,'isSelected') and local.thisSub.isSelected>

			<div id="sub#local.thisSub.subscriptionID#_addons"<cfif NOT local.thisSubSelected> class="d-none"</cfif>>
				#manageSubscription_renderAddOnForm(strSubAddOns=local.thisSub.addOns, subscriptionID=local.thisSub.subscriptionID, strParentFreq=arguments.strParentFreq,
					freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, inlineAddOn=false, listSubscribed=arguments.listSubscribed)#
			</div>
		</cfif>
	</cfloop>
</cfloop>
</cfoutput>