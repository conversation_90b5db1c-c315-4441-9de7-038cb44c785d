# MemberCentral Store Functionality Analysis

## Overview
The MemberCentral Store is a comprehensive e-commerce system fully integrated with the member management, accounting, and communication systems.

## Database Architecture

### Core Tables
- **store** - Store configuration (siteID, GLAccountID, shippingGLAccountID, email settings)
- **store_products** - Product catalog (productID, contentID, GLAccountID, status)
- **store_categories** - Product categorization hierarchy
- **store_productFormats** - Product variants (digital, physical, etc.)
- **store_rates** - Pricing tiers with date ranges and GL account mapping
- **store_ProductCategoryLinks** - Many-to-many product-category relationships
- **store_orders** - Order headers (memberID, totals, shipping info, status)
- **store_orderDetails** - Order line items (productItemID, formatID, quantity, ratePaid)
- **store_cartItems** - Session-based shopping cart storage

### Key Relationships
- `store.siteID` → `sites.siteID` (organization linkage)
- `store_products.storeID` → `store.storeID`
- `store_orders.memberID` → `ams_members.memberID`
- `store_orderDetails.orderID` → `store_orders.orderID`
- GL Account integration via `GLAccountID` and `ShippingGLAccountID` fields

## Business Logic Components

### CFC Architecture
- **store.cfc** - Main controller handling product display, cart operations, checkout flow
- **shoppingCart.cfc** - Cart management, order creation, quantity validation
- **storeReg.cfc** - User registration and authentication for store purchases
- **StoreAdmin.cfc** - Administrative functions for store management

### Request Flow
1. User browses products → `store.cfc` controller
2. Add to cart → `shoppingCart.cfc` manages session-based cart
3. Checkout → Registration/authentication via `storeReg.cfc`
4. Payment → Integration with merchant profiles and payment gateways
5. Order completion → `store_finalizeOrder` procedure processes transaction

## Transaction Integration

### GL Accounting
- Store orders create entries in `tr_transactions` table via `tr_createTransaction_sale`
- Each order detail becomes a separate transaction linked via `tr_applications`
- GL accounting automatically handled through debit/credit GL account assignments
- Transaction types: 'Sale' for products, separate transactions for shipping

### Order Processing
1. **Cart Management** - Session-based storage in `store_cartItems`
2. **Order Creation** - `store_finalizeOrder` procedure handles:
   - Moving cart items to `store_orderDetails`
   - Creating transaction records
   - Generating affirmations for digital products
   - Setting up stream delivery for video content
3. **Payment Processing** - Integration with merchant profiles and payment gateways
4. **Fulfillment** - Automated delivery of digital products, shipping for physical items

## Integration Points

### Email Notifications (platformMail)
- Order confirmations via `objEmailWrapper.sendMailESQ`
- Automated email templates with merge fields
- Integration with SendGrid for delivery
- Email tracking and delivery status monitoring

### Queue Processing (platformQueue)
- Background processing for order fulfillment
- Email queue management
- Automated tasks for inventory updates
- Integration with Service Broker for reliable messaging

### Member Integration
- Full integration with `ams_members` system
- Organization-based access control
- Member pricing tiers and discounts
- Purchase history tracking

### Digital Product Delivery
- Affirmations system for continuing education credits
- Stream delivery for video content
- Automated fulfillment upon payment completion
- Integration with content management system

## Key Technical Patterns
1. **Session Management** - Cart state maintained in application cache
2. **Transaction Safety** - Extensive use of database transactions with rollback
3. **GL Integration** - Automatic accounting entries with proper debit/credit mapping
4. **Responsive Design** - Bootstrap-based templates with mobile support
5. **Error Handling** - Comprehensive try/catch blocks with logging
6. **Security** - Parameter validation and SQL injection prevention
