<cfcomponent>
	<!--- testmode settings:
		0: no testing. real deal.   
		1: testing using auth test request.    
	--->
	<cfif application.MCEnvironment neq "production">
		<cfset variables.x_testmode = 1>
		<cfset variables.x_solution_id = "AAA100302"> <!--- this is auth sandbox's solution id --->
	<cfelse>
		<cfset variables.x_testmode = 0>
		<cfset variables.x_solution_id = "AAA174267"> <!--- this is membercentral's solution id --->
	</cfif>
	<cfset variables.doBINLookup = 1>

	<cfset variables.requesttimeout="60">

	<cffunction name="gather" access="package" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="usePopup" type="boolean" required="yes">
		<cfargument name="usePopupDIVName" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="hideSelect" type="boolean" required="no" default="0">
		<cfargument name="offerDelete" type="boolean" required="no" default="0">
		<cfargument name="autoShowForm" type="boolean" required="no" default="1">
		<cfargument name="overrideCustomerID" type="string" required="no" default="">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment|controlPanelManage">
		<cfargument name="paymentFeatures" type="struct" required="no" default="#application.objPayments.setDefaultPayFeaturesStruct()#">
		<cfargument name="chargeInfo" type="struct" required="no" default="#structNew()#">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { jsvalidation='', inputForm='', headcode='' }>
		<cfset local.isValidSSID = application.objPlatformStats.isValidStatsSessionID()>

		<!--- sandbox only supports USD, so ensure we send USD when not in prod --->
		<cfif variables.x_testmode is 1>
			<cfset querySetCell(arguments.qryGatewayID, "currencyType", "USD", 1)>
		</cfif>

		<cfif NOT local.isValidSSID>
			<cfsavecontent variable="local.returnStruct.inputForm">
				<cfoutput>
				There is a problem with your browser that prevents us from loading the payment form.<br/><br/>
				Support staff have just been notified of this issue and will work to correct it as soon as possible.<br/><br/>
				If you continue to see this message, contact Support for assistance and provide this error code: <b>ACCC-G-InvalidSession</b>.
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset structAppend(local, getCardPermissions(editMode=arguments.editMode), true)>

		<!--- override allowReassociate if Cards on file not being shown --->
		<cfif not arguments.showCOF>
			<cfset local.allowReassociate = 0>
		</cfif>

		<cfset local.hostNameWithProtocol = getHostNameWithProtocol()>
		<cfset local.qryCardTypes = application.objPayments.getProfileCardTypes(siteID=arguments.siteID, profileCode=arguments.qryGateWayID.profileCode)>
		<cfset local.qryProfilesOnFile = getProfilesOnFile(profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=arguments.showCOF, statsSessionID=session.cfcuser.statsSessionID)>

		<!--- payment features --->
		<cfscript>
		local.strPaymentFeatures = application.objPayments.setDefaultPayFeaturesStruct(usePaymentFeatures=arguments.paymentFeatures);
		local.strPaymentFeatures.processingFee.enable = local.strPaymentFeatures.processingFee.enable AND arguments.qryGateWayID.enableProcessingFeeDonation AND val(arguments.qryGateWayID.processFeeDonationFeePercent) GT 0;

		if (NOT application.objPlatform.isRequestSecure()) {
			local.strPaymentFeatures.applePay.enable = 0;
			local.strPaymentFeatures.applePay.googlePay = 0;
		}

		if (local.strPaymentFeatures.applePay.enable EQ 1 AND NOT existsApplePayDomain(profileID=arguments.qryGateWayID.profileID)) {
			local.registerDomainSuccess = registerCurrentHostNameWithApplePay(siteID=arguments.siteID, profileID=arguments.qryGateWayID.profileID);
			if (NOT local.registerDomainSuccess) {
				local.strPaymentFeatures.applePay.enable = 0;
			}
		}

		if (local.strPaymentFeatures.applePay.enable EQ 1 OR local.strPaymentFeatures.googlePay.enable EQ 1) {
			local.acceptedCardTypes = valueList(local.qryCardTypes.cardType);
			local.applePayCardTypes = [];
			local.googlePayCardTypes = [];
			if (listFindNoCase(local.acceptedCardTypes,"American Express")) {
				local.applePayCardTypes.append("amex");
				local.googlePayCardTypes.append("AMEX");
			}
			if (listFindNoCase(local.acceptedCardTypes,"Discover")) {
				local.applePayCardTypes.append("discover");
				local.googlePayCardTypes.append("DISCOVER");
			}
			if (listFindNoCase(local.acceptedCardTypes,"MasterCard")) {
				local.applePayCardTypes.append("masterCard");
				local.googlePayCardTypes.append("MASTERCARD");
			}
			if (listFindNoCase(local.acceptedCardTypes,"VISA")) {
				local.applePayCardTypes.append("visa");
				local.googlePayCardTypes.append("VISA");
			}

			local.qryCountries = application.objCommon.getCountries();
			local.countryCode = QueryFilter(local.qryCountries, function(thisRow) { return arguments.thisRow.countryID EQ application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCountryID; }).countryCode;
		}

		local.strPaymentFeatures.surcharge.enable = arguments.qryGateWayID.enableMCPay EQ 1 AND arguments.qryGateWayID.enableSurcharge EQ 1;
		if (local.strPaymentFeatures.surcharge.enable EQ 1) {
			local.strPaymentFeatures.surcharge.msg = replaceNoCase(local.strPaymentFeatures.surcharge.msg, "{{PERCENT}}", "#arguments.qryGateWayID.surchargePercent#%");
		}
		
		local.paymentFeaturesJSON = serializeJSON(local.strPaymentFeatures);

		// charge info
		local.chargeInfo = duplicate(arguments.chargeInfo);
		if (NOT structCount(local.chargeInfo)) local.chargeInfo = { "amt":0, "processingfees":0 };
		local.chargeInfo['amtincprocessingfees'] = numberFormat(precisionEvaluate(local.chargeInfo.amt + local.chargeInfo.processingfees),"0.00");
		
		local.chargeInfoJSON = serializeJSON(local.chargeInfo);
		local.isControlPanel = findNoCase("controlPanel",arguments.editMode) GT 0 OR (arguments.keyExists('adminForm') AND arguments.adminForm EQ 1);
		local.isFrontEnd = NOT local.isControlPanel AND findNoCase("frontend",arguments.editMode) GT 0;
		</cfscript>
	
		<!--- get associated items if needed --->
		<cfif local.showAssociatedItems is 1>
			<cfset local.qryAssociatedInvoices = application.objPayments.getMemberPayProfileInvoices(memberID=arguments.pmid, includeOpenInvoices=local.includeOpenInvoices)>
			<cfset local.qryAssociatedSubscriptionTypes = application.objPayments.getMemberPayProfileSubscriptions(memberID=arguments.pmid)>
			<cfset local.qryAssociatedContributions = application.objPayments.getMemberPayProfileContributions(memberID=arguments.pmid)>
		</cfif>

		<!--- get membernumber, name --->
		<!--- force lookup of activeMemberID in case the member is merged mid-session --->
		<cfset local.qryMemNum = application.objPayments.getActiveMemberData(memberID=arguments.pmid)>
		
		<!--- if not showing COF, use statssessionID appended to pmid to generate a new customer profile. --->
		<!--- Helps reduce the liklihood of hitting the 10 card limit and also lets them enter in exact info from a card on file they cant see --->
		<cfif len(arguments.overrideCustomerID)>
			<cfset local.cidToUse = arguments.overrideCustomerID>
			<cfset local.cpidToUse = "">
		<cfelse>
			<cfif NOT arguments.showCOF>
				<cfset local.cidToUse = arguments.pmid & "_" & session.cfcuser.statsSessionID & "_" & RandRange(10000, 99999)>
				<cfset local.cpidToUse = "">
			<cfelse>
				<!--- customer profileID to use for adding new profiles --->
				<!--- get the one with the least amount of payment profiles. auth caps them at 10 --->
				<cfquery name="local.qryCustProfile" dbtype="query" maxrows="1">
					select customerProfileID, count(*) as theCount
					from [local].qryProfilesOnFile
					group by customerProfileID
					order by theCount
				</cfquery>		

				<cfset local.cidToUse = arguments.pmid>
				<cfset local.cpidToUse = local.qryCustProfile.customerProfileID>
			</cfif>
		</cfif>
		
		<cfif local.qryProfilesOnFile.recordcount is 0>
			<cfset local.hideCancelOnAdd = 1>
		<cfelse>
			<cfset local.hideCancelOnAdd = 0>
		</cfif>
		
		<cfset local.EncAddPayProfile = "<data><pmid>#arguments.pmid#</pmid><cid>#local.cidToUse#</cid><cn>#xmlformat(local.qryMemNum.membernumber)# - #xmlformat(local.qryMemNum.fullname)#</cn><action>addPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.cpidToUse#</cpid><hcnl>#local.hideCancelOnAdd#</hcnl><em>#arguments.editMode#</em><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
		<cfset local.EncAddPayProfileReturn = "<data><p>#arguments.pmid#</p><cid>#local.cidToUse#</cid><cn>#xmlformat(local.qryMemNum.membernumber)# - #xmlformat(local.qryMemNum.fullname)#</cn><c>#arguments.showCOF#</c><s>#arguments.siteid#</s><o>#arguments.usePopup#</o><h>#arguments.hideSelect#</h><od>#arguments.offerDelete#</od><d>#arguments.usePopupDIVName#</d><action>addPaymentProfileReturn</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.cpidToUse#</cpid><ocid>#xmlformat(arguments.overrideCustomerID)#</ocid><em>#arguments.editMode#</em><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
		<cfset local.EncAddPayProfileReturn = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfileReturn,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
		<cfset local.cardWidth = 275>
		<cfset local.btnWidth = 240>
		<cfset local.formActionWidth = arguments.hideSelect ? local.cardWidth : (local.cardWidth + 20)>
		<cfset local.loadingIcon = local.isFrontEnd ? '<i class="icon-spin icon-spinner"></i>' : '<i class="fa-light fa-circle-notch fa-spin fa-lg"></i>'>
	
		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			#getCommonStyles()#
			<script language="javascript">
				var #toScript(local.chargeInfo,"chargeInfo#arguments.qryGateWayID.profileID#")#
				
				function p_#arguments.qryGateWayID.profileID#_resizeIFrame() {
					$('##iframeManageForm#arguments.qryGateWayID.profileID#Loading').hide();
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					ifr.contents().find('body, div.container, body > div.bodyText').css({margin:0});
					ifr.contents().find('body, div.inner-content, body > div.container-fluid, div.container').css({padding:0});
					ifr.show();
					var nh = ifr.contents().find("div##zoneMain").height();
					if (nh > 0) ifr.attr('height',nh+40 + 'px');
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfile() {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','700px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#')
							.load(function() { p_#arguments.qryGateWayID.profileID#_initPaymentProfileForm(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						var _popupWidth = 550;
						var windowWidth = $(window).width();
						if(windowWidth < 585) {
							_popupWidth = windowWidth-30;
						}

						top.$.colorbox( {innerWidth:_popupWidth, innerHeight:330, href:'/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#', iframe:true, overlayClose:false} );
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_initPaymentProfileForm() {
					<!--- let parent know that payment form is ready and if there is anything to broadcast --->
					parent.postMessage({ success:true, messagetype:'MCPaymentFormLoadEvent', profileid:#arguments.qryGateWayID.profileID# },'#JSStringFormat(local.hostNameWithProtocol)#');
					p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function p_#arguments.qryGateWayID.profileID#_onCompleteCCPayProfileAction(act) {
					$('##paymentMethod#arguments.qryGateWayID.profileID#').val('cc');
					switch (act.toLowerCase()) {
						case 'cardadded':
						case 'cardupdated':
							<cfif local.strPaymentFeatures.processingFee.enable AND arguments.editMode EQ 'frontEndPayment'>
								p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(3);
							<cfelse>
								p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(2);
							</cfif>
							break;
					
						default:
							p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(2);
							break;
					}
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();

					let mcp_addPayProfileReturnLink = '/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfileReturn)#';
					
					<cfif NOT (local.strPaymentFeatures.processingFee.enable OR local.strPaymentFeatures.surcharge.enable)>
						<!--- post message to control app when card is added/updated --->
						if (obj && obj.a && obj.a=='save' && obj.mccardevent && ['cardadded','cardupdated'].indexOf(obj.mccardevent.toLowerCase()) != -1) {
							p_#arguments.qryGateWayID.profileID#_onSelectPayProfile(obj.mccardevent,obj.payprofileid,0);
						}
					</cfif>

					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#').hide();
						$('###arguments.usePopupDIVName#').show();
						if (obj && obj.a && obj.a=='save') {
							$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr('pofcount','0').html('<span>#local.loadingIcon# Reloading...</span>');
							p_#arguments.qryGateWayID.profileID#_scrollTo('divInputFormWrapper#arguments.qryGateWayID.profileID#');
							ifr.attr('src',mcp_addPayProfileReturnLink)
								.load(function() { p_#arguments.qryGateWayID.profileID#_onCompleteCCPayProfileAction(obj.mccardevent); });
						} else 
							ifr.attr('width','1px').attr('height','1px').attr('src','about:blank');
						if (obj && obj.err) p_#arguments.qryGateWayID.profileID#_showAlert(obj.err);
					<cfelse>
						if (obj && obj.a && obj.a=='save') {
							$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr('pofcount','0').html('<span>#local.loadingIcon# Reloading...</span>');
							top.$.colorbox.close();
							$('##iframeManageForm#arguments.qryGateWayID.profileID#').attr('src',mcp_addPayProfileReturnLink)
								.load(function() { p_#arguments.qryGateWayID.profileID#_onCompleteCCPayProfileAction(obj.mccardevent); });
						} else top.$.colorbox.close();
						if (obj && obj.err) p_#arguments.qryGateWayID.profileID#_showAlert(obj.err);
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_refreshPaymentProfile() {
					var objRet = new Object();
						objRet.a = 'save';
						objRet.mccardevent = 'cardRefreshed';
					if (p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
				}
				function p_#arguments.qryGateWayID.profileID#_editPaymentProfile(we) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','438px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
							.load(function() { p_#arguments.qryGateWayID.profileID#_initPaymentProfileForm(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						var _popupWidth = 550;
						var windowWidth = $( window ).width();
						if( windowWidth < 585 ){
							_popupWidth = windowWidth-30;
						}
						top.$.colorbox( { innerWidth:_popupWidth, innerHeight:480, href:'/?pg=buyNow&mode=direct&wizard=' + escape(we), iframe:true, overlayClose:false} );
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_cancelForm(ppid) {
					$('##cardFrmContainer'+ppid).html('').hide();
				}
				<cfif arguments.offerDelete>
					function p_#arguments.qryGateWayID.profileID#_removePaymentProfile(ppid,cpid,cppid) {
						let cardRemoveTemplateSource = $('##mcp-authcim-removecard-template#arguments.qryGateWayID.profileID#').html();
						var cardRemoveTemplate = Handlebars.compile(cardRemoveTemplateSource);
						$('##cardFrmContainer'+ppid).html('#local.loadingIcon# Please wait...');
						$('##cardFrmContainer'+ppid).slideToggle(300);
						$('##cardFrmContainer'+ppid).html(cardRemoveTemplate({payprofileid:ppid, cpid:cpid, cppid:cppid}));
					}
					function p_#arguments.qryGateWayID.profileID#_doRemovePaymentProfile(ppid,cpid,cppid) {
						$('##btnRemoveCard'+ppid).html('#local.loadingIcon# Removing...').prop("disabled", true);
						
						let removeResult = function(r) {
							if (r.success && r.success == 'true') {
								let objRet = { a:'save', mccardevent:'cardRemoved' };
								p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
								
							} else {
								alert('We were unable to remove this card. Try again.');
								$("##btnRemoveCard"+ppid).html('Yes, Remove Card').prop("disabled", false);
							}
						};

						let objParams = { profileID:#arguments.qryGateWayID.profileID#, pmid:#arguments.pmid#, cpid:cpid, cppid:cppid };
						TS_AJX('AUTHORIZECCCIM','removePaymentProfile',objParams,removeResult,removeResult,20000,removeResult);
					}
				</cfif>
				function p_#arguments.qryGateWayID.profileID#_reassignPaymentProfile(we) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','210px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
							.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						top.$.colorbox( {innerWidth:350, innerHeight:480, href:'/?pg=buyNow&mode=direct&wizard=' + escape(we), iframe:true, overlayClose:false} );
					</cfif>
				}
				<cfif local.showAssociatedItems is 1>
					function p_#arguments.qryGateWayID.profileID#_mppid_toggleAssocItems(x) {
						$('##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_container_'+x+', ##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_'+x+'_show, ##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_'+x+'_hide').toggleClass('mcp-authcim-d-none');
					}
				</cfif>
				function p_#arguments.qryGateWayID.profileID#_hideAlert() { 
					$('##divInputForm#arguments.qryGateWayID.profileID#Step2 .inputFormStepErr').html('').addClass('mcp-authcim-d-none'); 
					p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function p_#arguments.qryGateWayID.profileID#_showAlert(msg) { 
					$('##divInputForm#arguments.qryGateWayID.profileID#Step2 .inputFormStepErr').html(msg).removeClass('mcp-authcim-d-none'); 
					p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function p_#arguments.qryGateWayID.profileID#_delay(t) {
					return new Promise(resolve => setTimeout(resolve, t));
				}
				function p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(ovStep) {
					let mode = typeof ovStep != "undefined" ? 'loadstep' : 'init';
					
					if ($('.inputForm#arguments.qryGateWayID.profileID#Step').length) {
						$('.inputForm#arguments.qryGateWayID.profileID#Step').addClass('mcp-authcim-d-none');
						$('##divInputForm#arguments.qryGateWayID.profileID#StepButtons').addClass('mcp-authcim-d-none');
						$('##divInputForm#arguments.qryGateWayID.profileID#StepLoading').removeClass('mcp-authcim-d-none');
					}

					<!--- to handle inline loading of payment forms --->
					return new Promise(async function(resolve,reject) {
						let maxCounter = 20, counter = 0;
						while(true) {
							counter++;
							await p_#arguments.qryGateWayID.profileID#_delay(400);
							if ($('##divInputForm#arguments.qryGateWayID.profileID#Step1').length || counter == maxCounter) break;
						}

						resolve();

					}).then(function() {
						let stepNum = 1, 
							totalSteps = $('.inputForm#arguments.qryGateWayID.profileID#Step').length,
							hasMultiplePayMethods = #local.strPaymentFeatures.applePay.enable OR local.strPaymentFeatures.googlePay.enable ? 1 : 0#;
						
						if (typeof ovStep != "undefined" && ovStep >= 1 && ovStep <= totalSteps) {
							stepNum = ovStep;
						} else {
							if (hasMultiplePayMethods) {
								stepNum = 1;
							} else {
								stepNum = 2;
							}
						}

						if (mode == 'init') {
							<!--- remove duplicate custom form hidden fields like p_x_mppid, pfd, paymentMethod that was created by going back and coming back to the payment form --->
							$('input.mcp-authcim-input-field#arguments.qryGateWayID.profileID#').each(function(x){
								let thisInput = $(this).attr('name');
								$('input[type="hidden"][name="'+thisInput+'"]:not(input.mcp-authcim-input-field#arguments.qryGateWayID.profileID#)').remove();
							});
							if ($('##divBtnWrapper#arguments.qryGateWayID.profileID#').length) {
								$('##divBtnWrapper#arguments.qryGateWayID.profileID#').hide();
							}
						}

						if ($('##divInputForm#arguments.qryGateWayID.profileID#StepButtons').length) {
							$('##divInputForm#arguments.qryGateWayID.profileID#StepButtons').addClass('mcp-authcim-d-none');
							$('##divInputForm#arguments.qryGateWayID.profileID#StepButtons .inputForm#arguments.qryGateWayID.profileID#StepBtn').addClass('mcp-authcim-d-none');
							$('.inputFormStepErr').html('').addClass('mcp-authcim-d-none');
							$('.inputForm#arguments.qryGateWayID.profileID#StepButtonContainer').addClass('mcp-authcim-d-none');

							let payMethod = $('##paymentMethod#arguments.qryGateWayID.profileID#').val();

							if (stepNum > 1 && payMethod == 'cc') {
								let prevStep = stepNum - 1, nextStep = stepNum + 1;
								let arrStepsAvailable = [];

								if (hasMultiplePayMethods) arrStepsAvailable.push(1);
								if ($('##divInputForm#arguments.qryGateWayID.profileID#Step2').length) arrStepsAvailable.push(2);
								if ($('##divInputForm#arguments.qryGateWayID.profileID#Step3').length) arrStepsAvailable.push(3);

								let showPrevStepBtn = arrStepsAvailable.includes(prevStep), 
									showNextStepBtn = arrStepsAvailable.includes(nextStep), 
									showCCPayBtn = $('##divBtnWrapper#arguments.qryGateWayID.profileID#').length && stepNum == totalSteps && payMethod == 'cc';

								if (showPrevStepBtn) $('##inputForm#arguments.qryGateWayID.profileID#PrevStepBtn').removeClass('mcp-authcim-d-none');
								if (showNextStepBtn) $('##inputForm#arguments.qryGateWayID.profileID#NextStepBtn').removeClass('mcp-authcim-d-none');
								if (showCCPayBtn) $('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn').removeClass('mcp-authcim-d-none');
								
								if (showPrevStepBtn || showNextStepBtn || showCCPayBtn) {
									if ($('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn').text().length == 0) {
										let btnText = 'Pay Now';
										if (Number(chargeInfo#arguments.qryGateWayID.profileID#.amt) > 0) {
											btnText = 'Pay $' + p_#arguments.qryGateWayID.profileID#_dspAmt(chargeInfo#arguments.qryGateWayID.profileID#.amt) + ' Now';
										} else if ($('##divBtnWrapper#arguments.qryGateWayID.profileID#').length) {
											btnText = $('##divBtnWrapper#arguments.qryGateWayID.profileID#').find('button').text();
										}
										p_#arguments.qryGateWayID.profileID#_setPayNowButtonText(btnText);
									}

									$('##inputForm#arguments.qryGateWayID.profileID#StepButtonContainer_cc').removeClass('mcp-authcim-d-none');
									$('##divInputForm#arguments.qryGateWayID.profileID#StepButtons').removeClass('mcp-authcim-d-none');
								}
							}
						}

						window['p_#arguments.qryGateWayID.profileID#_showStep'+stepNum](mode);
						$('##divInputForm#arguments.qryGateWayID.profileID#StepLoading').addClass('mcp-authcim-d-none');

						if (stepNum > 1) {
							p_#arguments.qryGateWayID.profileID#_scrollTo('divInputFormWrapper#arguments.qryGateWayID.profileID#');
						}

					}).catch(function(err) {
						console.log(err);
					});
				}
				function p_#arguments.qryGateWayID.profileID#_showStep1(mode) {
					if (mode != 'init') {
						if ($('.applepay#arguments.qryGateWayID.profileID#container').length && $('.applepay#arguments.qryGateWayID.profileID#container').attr('data-init') == 0) {
							onApplePayLoaded#arguments.qryGateWayID.profileID#();
						}
						if ($('.gpay#arguments.qryGateWayID.profileID#container').length && $('.gpay#arguments.qryGateWayID.profileID#container').attr('data-init') == 0) {
							onGooglePayLoaded#arguments.qryGateWayID.profileID#();
						}
					}

					$('##divInputForm#arguments.qryGateWayID.profileID#Step1').removeClass('mcp-authcim-d-none');
				}
				function p_#arguments.qryGateWayID.profileID#_showStep2(mode) {
					let payMethod = $('##paymentMethod#arguments.qryGateWayID.profileID#').val();

					if (payMethod != 'cc') return false;

					$('##divInputForm#arguments.qryGateWayID.profileID#Step2').removeClass('mcp-authcim-d-none');

					if ($('##divManageFormWrapper#arguments.qryGateWayID.profileID#').length == 0)
						$('###arguments.usePopupDIVName#').after('<div id="divManageFormWrapper#arguments.qryGateWayID.profileID#" style="display:none;"><div id="iframeManageForm#arguments.qryGateWayID.profileID#Loading">#local.loadingIcon# Loading...<br/><br/></div><iframe name="iframeManageForm#arguments.qryGateWayID.profileID#" id="iframeManageForm#arguments.qryGateWayID.profileID#" frameborder="0" allowTransparency="true" scrolling="no" style="display:none;"></iframe></div>');

					<cfif NOT arguments.usePopup and arguments.autoShowForm is 1>
						if ($('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr('pofcount') == 0)
							p_#arguments.qryGateWayID.profileID#_addPaymentProfile();
					</cfif>
					<cfif local.strPaymentFeatures.surcharge.enable>
						p_#arguments.qryGateWayID.profileID#_manageSurcharge();
					</cfif>
				}
				<cfif local.strPaymentFeatures.surcharge.enable>
					function p_#arguments.qryGateWayID.profileID#_manageSurcharge() {
						let selectedPayProfile = $('input[type="radio"][name="p_#arguments.qryGateWayID.profileID#_mppid"]:checked');
						
						if (! selectedPayProfile.length) return false;

						let payBtnText = 'Pay Now';
						let chargeAmt = Number(chargeInfo#arguments.qryGateWayID.profileID#.amt);
						let surchargeEligibleCard = selectedPayProfile.data('sgen') == 1;
						
						if (chargeAmt > 0) {
							if (surchargeEligibleCard) {
								let amtIncSurcharge = parseFloat(chargeAmt + (chargeAmt * Number(#arguments.qryGateWayID.surchargePercent# / 100))).toFixed(2);
								payBtnText = 'Pay $' + p_#arguments.qryGateWayID.profileID#_dspAmt(amtIncSurcharge) + ' Now';
							} else {
								payBtnText = 'Pay $' + p_#arguments.qryGateWayID.profileID#_dspAmt(chargeAmt) + ' Now';
							}
						} else if ($('##divBtnWrapper#arguments.qryGateWayID.profileID#').length) {
							payBtnText = $('##divBtnWrapper#arguments.qryGateWayID.profileID#').find('button').text();
						}
						p_#arguments.qryGateWayID.profileID#_setPayNowButtonText(payBtnText);
					}
				</cfif>
				<cfif local.strPaymentFeatures.processingFee.enable>
					function p_#arguments.qryGateWayID.profileID#_showStep3(mode) {
						let payMethod = $('##paymentMethod#arguments.qryGateWayID.profileID#').val();
						$('##inputForm#arguments.qryGateWayID.profileID#SelectedCard').html('');

						switch(payMethod) {
							case 'cc':
								let payProfileID = $('input[type="radio"][name="p_#arguments.qryGateWayID.profileID#_mppid"]:checked').val();
								$('##inputForm#arguments.qryGateWayID.profileID#SelectedCard').html($('##payProfileWrapper#arguments.qryGateWayID.profileID#_'+payProfileID).find('.mcp-authcim-cof-info').html());
								$('##inputForm#arguments.qryGateWayID.profileID#SelectedCard').find('.mcp-authcim-cof-actions').remove();
								break;
							
							<cfif local.strPaymentFeatures.processingFee.enable>
								case 'applepay':
								case 'googlepay':
									$('##inputForm#arguments.qryGateWayID.profileID#StepButtonContainer_'+payMethod).removeClass('mcp-authcim-d-none');
									$('##divInputForm#arguments.qryGateWayID.profileID#StepButtons').removeClass('mcp-authcim-d-none');
									break;
							</cfif>
						}
						
						if (typeof processFeeLabel#arguments.qryGateWayID.profileID# == "string" && processFeeLabel#arguments.qryGateWayID.profileID#.length) {
							$('##processFeeLabel#arguments.qryGateWayID.profileID#').html(processFeeLabel#arguments.qryGateWayID.profileID#);
						}

						$('##divInputForm#arguments.qryGateWayID.profileID#Step3').removeClass('mcp-authcim-d-none');
						<cfif arguments.editMode EQ 'controlPanelPayment'>
							updatePaymentAmt(#arguments.qryGateWayID.profileID#);
						<cfelse>
							p_#arguments.qryGateWayID.profileID#_manageProcessingFee();
						</cfif>
					}
					function p_#arguments.qryGateWayID.profileID#_manageProcessingFee() {
						$('##divInputForm#arguments.qryGateWayID.profileID#Step3 .inputFormStepErr').html('').addClass('mcp-authcim-d-none');

						let payMethod = $('##paymentMethod#arguments.qryGateWayID.profileID#').val();
						let processingFeeBtn = $('##processFeeDonation#arguments.qryGateWayID.profileID#_yes');
						
						if (Number(chargeInfo#arguments.qryGateWayID.profileID#.amt) > 0) {
							let payAmtText = '';

							if (processingFeeBtn.is(':checked')) {
								payAmtText = 'Pay $' + p_#arguments.qryGateWayID.profileID#_dspAmt(chargeInfo#arguments.qryGateWayID.profileID#.amtincprocessingfees) + ' Now';
							} else {
								payAmtText = 'Pay $' + p_#arguments.qryGateWayID.profileID#_dspAmt(chargeInfo#arguments.qryGateWayID.profileID#.amt) + ' Now';
							}

							switch(payMethod) {
								case 'cc':
								p_#arguments.qryGateWayID.profileID#_setPayNowButtonText(payAmtText);
									break;
								
								case 'applepay':
								case 'googlepay':
									$('.chargeAmtDisp#arguments.qryGateWayID.profileID#').html(payAmtText);
									break;
							}

							if (! $('input[type="radio"][name="processFeeDonation#arguments.qryGateWayID.profileID#"]:checked').length) {
								$('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn').prop('disabled',true);
								$('.inputForm#arguments.qryGateWayID.profileID#StepButtonContainer')
									.find('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn,.applepay#arguments.qryGateWayID.profileID#container,.gpay#arguments.qryGateWayID.profileID#container')
									.addClass('mcp-authcim-opacity-2');
							} else {
								$('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn').prop('disabled',false);
								$('.inputForm#arguments.qryGateWayID.profileID#StepButtonContainer')
									.find('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn,.applepay#arguments.qryGateWayID.profileID#container,.gpay#arguments.qryGateWayID.profileID#container')
									.removeClass('mcp-authcim-opacity-2');
							}
						}
					}
				</cfif>
				function p_#arguments.qryGateWayID.profileID#_getCurrentStep() {
					if (! $('##divInputForm#arguments.qryGateWayID.profileID#Step1').hasClass('mcp-authcim-d-none')) {
						return 1;
					} else if (! $('##divInputForm#arguments.qryGateWayID.profileID#Step2').hasClass('mcp-authcim-d-none')) {
						return 2;
					} else if (! $('##divInputForm#arguments.qryGateWayID.profileID#Step3').hasClass('mcp-authcim-d-none')) {
						return 3;
					}
				}
				function p_#arguments.qryGateWayID.profileID#_prevStep() {
					p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(p_#arguments.qryGateWayID.profileID#_getCurrentStep() - 1);
				}
				function p_#arguments.qryGateWayID.profileID#_nextStep() {
					if (! p_#arguments.qryGateWayID.profileID#_validateStep()) {
						return false;
					}

					p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(p_#arguments.qryGateWayID.profileID#_getCurrentStep() + 1);
				}
				function p_#arguments.qryGateWayID.profileID#_validateStep() {
					let payMethod = $('##paymentMethod#arguments.qryGateWayID.profileID#').val();
					let currentStep = p_#arguments.qryGateWayID.profileID#_getCurrentStep(), arrErrs = [];
					
					if (payMethod == 'cc' && ! $('input[type="radio"][name="p_#arguments.qryGateWayID.profileID#_mppid"]:checked').length) {
						arrErrs.push('Select a Card.');
					}

					<cfif local.strPaymentFeatures.processingFee.enable>
						if (currentStep == 3 && ! $('input[type="radio"][name="processFeeDonation#arguments.qryGateWayID.profileID#"]:checked').length) {
							arrErrs.push('Choose an Option.');
						}
					</cfif>

					if (arrErrs.length) {
						$('##divInputForm#arguments.qryGateWayID.profileID#Step'+currentStep).find('.inputFormStepErr').html(arrErrs.join('<br/>')).removeClass('mcp-authcim-d-none');
						return false;
					}

					return true;
				}
				function p_#arguments.qryGateWayID.profileID#_setPayNowButtonText(txt) {
					$('##inputForm#arguments.qryGateWayID.profileID#CCPayBtn').text(txt);
					$('###arguments.usePopupDIVName#').find('button[type="submit"]:not([data-prevent-text-update])').text(txt);
				}
				function p_#arguments.qryGateWayID.profileID#_pay() {
					if (! p_#arguments.qryGateWayID.profileID#_validateStep()) {
						return false;
					}

					<cfif local.isFrontEnd>
						let enableProcFee = 0;
						<cfif local.strPaymentFeatures.processingFee.enable>
							enableProcFee = $('##processFeeDonation#arguments.qryGateWayID.profileID#_yes').is(':checked') ? 1 : 0;
						</cfif>
						p_#arguments.qryGateWayID.profileID#_onSelectPayProfile('cardadded', $('input[type="radio"][name="p_#arguments.qryGateWayID.profileID#_mppid"]:checked').val(), enableProcFee);
					<cfelse>
						$('##divBtnWrapper#arguments.qryGateWayID.profileID#').find('button').trigger('click');
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_selectCard(pid) {
					$('.payProfileWrapper#arguments.qryGateWayID.profileID#').removeClass('mcp-authcim-card-selected');
					$('##payProfileWrapper#arguments.qryGateWayID.profileID#_'+pid).addClass('mcp-authcim-card-selected');
					<cfif local.strPaymentFeatures.surcharge.enable>
						p_#arguments.qryGateWayID.profileID#_manageSurcharge();
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_toggleCardInfo(pid) {
					$('##cardInfoContainer'+pid).slideToggle(300);
				}
				function p_#arguments.qryGateWayID.profileID#_onSelectPayProfile(mccardevent,payprofileid,enableprocessingfee) {
					let message = { success:true, 
									messagetype:"MCGatewayEvent", 
									eventname:mccardevent, 
									profileid:#arguments.qryGateWayID.profileID#,
									payprofileid:payprofileid,
									enableprocessingfee:enableprocessingfee };
					window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
				}
				function p_#arguments.qryGateWayID.profileID#_onSelectPayMethod(pmtd) {
					$('##paymentMethod#arguments.qryGateWayID.profileID#').val(pmtd);

					switch(pmtd) {
						case 'cc':
							p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(2);
							break;
						<cfif local.strPaymentFeatures.processingFee.enable>
							case 'applepay':
							case 'googlepay':
								p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(3);
								break;
						</cfif>
					}
				}
				function p_#arguments.qryGateWayID.profileID#_scrollTo(elID) {
					$('html, body').animate({
						scrollTop: $('##'+elID).offset().top - 220
					}, 500);
				}
				function p_#arguments.qryGateWayID.profileID#_dspAmt(amt) {
					return Number(amt).toFixed(2).replace('.00','');
				}
				
				$(function() {
					p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps();
				});
			</script>
			<cfif local.strPaymentFeatures.googlePay.enable>
				<script type="application/javascript">
					const baseRequest#arguments.qryGateWayID.profileID# = { apiVersion: 2, apiVersionMinor: 0 };
					const tokenizationSpecification#arguments.qryGateWayID.profileID# = {
						type: 'PAYMENT_GATEWAY',
						parameters: {
							'gateway': 'authorizenet',
							'gatewayMerchantId': '#arguments.qryGateWayID.gatewayAccountID#'
						}
					};
					const allowedCardNetworks#arguments.qryGateWayID.profileID# = #serializeJson(local.googlePayCardTypes)#;
					const allowedCardAuthMethods#arguments.qryGateWayID.profileID# = ["PAN_ONLY", "CRYPTOGRAM_3DS"];
					const baseCardPaymentMethod#arguments.qryGateWayID.profileID# = {
						type: 'CARD',
						parameters: {
							allowedAuthMethods: allowedCardAuthMethods#arguments.qryGateWayID.profileID#,
							allowedCardNetworks: allowedCardNetworks#arguments.qryGateWayID.profileID#,
							assuranceDetailsRequired: true,
							billingAddressRequired: true,
							billingAddressParameters: {
								format: "MIN"
							}
						}
					};
					const cardPaymentMethod#arguments.qryGateWayID.profileID# = Object.assign({tokenizationSpecification: tokenizationSpecification#arguments.qryGateWayID.profileID#},baseCardPaymentMethod#arguments.qryGateWayID.profileID#);

					let paymentsClient#arguments.qryGateWayID.profileID# = null;

					function getGoogleIsReadyToPayRequest#arguments.qryGateWayID.profileID#() {
						return Object.assign({},baseRequest#arguments.qryGateWayID.profileID#,{allowedPaymentMethods: [baseCardPaymentMethod#arguments.qryGateWayID.profileID#]});
					}
					function getGooglePaymentDataRequest#arguments.qryGateWayID.profileID#() {
						const paymentDataRequest = Object.assign({}, baseRequest#arguments.qryGateWayID.profileID#);
						paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod#arguments.qryGateWayID.profileID#];
						paymentDataRequest.transactionInfo = getGoogleTransactionInfo#arguments.qryGateWayID.profileID#();
						paymentDataRequest.merchantInfo = {
							merchantId: '#arguments.qryGateWayID.googlePayMerchantID#',
							merchantName: '#encodeForHTML(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgName)#'
						};
						return paymentDataRequest;
					}
					function getGooglePaymentsClient#arguments.qryGateWayID.profileID#() {
						if ( paymentsClient#arguments.qryGateWayID.profileID# === null ) {
							paymentsClient#arguments.qryGateWayID.profileID# = new google.payments.api.PaymentsClient({environment: <cfif application.MCEnvironment NEQ 'production'>'TEST'<cfelse>'PRODUCTION'</cfif>});
						}
						return paymentsClient#arguments.qryGateWayID.profileID#;
					}
					function onGooglePayLoaded#arguments.qryGateWayID.profileID#() {
						<!--- to handle inline loading of payment forms --->
						return new Promise(async function(resolve,reject) {
							let maxDelay = 20, counter = 0;
							while(true) {
								counter++;
								await p_#arguments.qryGateWayID.profileID#_delay(1000);
								if ($('.gpay#arguments.qryGateWayID.profileID#container').length || counter == maxDelay) break;
							}

							resolve();

						}).then(function() {

							$('.gpay#arguments.qryGateWayID.profileID#container').attr('data-init',1);

							const paymentsClient = getGooglePaymentsClient#arguments.qryGateWayID.profileID#();
							paymentsClient.isReadyToPay(getGoogleIsReadyToPayRequest#arguments.qryGateWayID.profileID#())
								.then(function(response) {
									if (response.result) {
										addGooglePayButton#arguments.qryGateWayID.profileID#();
										prefetchGooglePaymentData#arguments.qryGateWayID.profileID#();
									}
								})
								.catch(function(err) {
									console.log(err);
								});
						}).catch(function(err) {
							console.log(err);
						});
					}
					function addGooglePayButton#arguments.qryGateWayID.profileID#() {
						const paymentsClient = getGooglePaymentsClient#arguments.qryGateWayID.profileID#();

						$('.gpay#arguments.qryGateWayID.profileID#container').each(function() {
							let gpayMode = $(this).data('gpaymode');

							let button = 
								paymentsClient.createButton({
									buttonColor: 'black',
									buttonType: gpayMode == 'select' ? 'plain' : 'pay',
									buttonRadius: 4,
									buttonSizeMode: 'fill',
									onClick: () => { 
										if (gpayMode == 'pay') {
											onGooglePaymentButtonClicked#arguments.qryGateWayID.profileID#();
										} else {
											p_#arguments.qryGateWayID.profileID#_onSelectPayMethod('googlepay');
										}
									},
									allowedPaymentMethods: [baseCardPaymentMethod#arguments.qryGateWayID.profileID#]
								});
							$(this)[0].appendChild(button);
						});
					}
					function getGoogleTransactionInfo#arguments.qryGateWayID.profileID#() {
						let amt = chargeInfo#arguments.qryGateWayID.profileID#.amt.toString();
						<cfif local.strPaymentFeatures.processingFee.enable>
							if ($('##processFeeDonation#arguments.qryGateWayID.profileID#_yes').is(':checked'))
								amt = chargeInfo#arguments.qryGateWayID.profileID#.amtincprocessingfees.toString();
						</cfif>

						return {
							countryCode: '#local.countryCode#',
							currencyCode: '#arguments.qryGateWayID.currencyType#',
							totalPriceStatus: 'FINAL',
							totalPrice: amt
						};
					}
					function prefetchGooglePaymentData#arguments.qryGateWayID.profileID#() {
						const paymentDataRequest = getGooglePaymentDataRequest#arguments.qryGateWayID.profileID#();
						paymentDataRequest.transactionInfo = {
							totalPriceStatus: 'NOT_CURRENTLY_KNOWN',
							currencyCode: '#arguments.qryGateWayID.currencyType#'
						};
						const paymentsClient = getGooglePaymentsClient#arguments.qryGateWayID.profileID#();
						paymentsClient.prefetchPaymentData(paymentDataRequest);
					}
					function onGooglePaymentButtonClicked#arguments.qryGateWayID.profileID#() {
						if (! p_#arguments.qryGateWayID.profileID#_validateStep()) {
							return false;
						}

						const paymentDataRequest = getGooglePaymentDataRequest#arguments.qryGateWayID.profileID#();
						paymentDataRequest.transactionInfo = getGoogleTransactionInfo#arguments.qryGateWayID.profileID#();
						const paymentsClient = getGooglePaymentsClient#arguments.qryGateWayID.profileID#();
						paymentsClient.loadPaymentData(paymentDataRequest)
							.then(function(paymentData) {
								processPayment#arguments.qryGateWayID.profileID#(paymentData);
							})
							.catch(function(err) {
								console.error(err);
							});
					}
					function processPayment#arguments.qryGateWayID.profileID#(paymentData) {
						let enableProcFees = 0;
						<cfif local.strPaymentFeatures.processingFee.enable>
							enableProcFees = $('##processFeeDonation#arguments.qryGateWayID.profileID#_yes').is(':checked') ? 1 : 0;
						</cfif>
						let message = { success:true, 
										messagetype:"MCGatewayEvent", 
										eventname:'gpaytokenready', 
										profileid:#arguments.qryGateWayID.profileID#,
										tokendata: { mctokensource:'googlePay', paymentData:paymentData },
										enableprocessingfee:enableProcFees };
						window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
					}

					<!--- Load Google Pay JS --->
					let googlePaySDK#arguments.qryGateWayID.profileID# = document.createElement('script');
					googlePaySDK#arguments.qryGateWayID.profileID#.onload = function () {
						onGooglePayLoaded#arguments.qryGateWayID.profileID#();
					};
					googlePaySDK#arguments.qryGateWayID.profileID#.async = true;
					googlePaySDK#arguments.qryGateWayID.profileID#.src = "https://pay.google.com/gp/p/js/pay.js";
					document.head.appendChild(googlePaySDK#arguments.qryGateWayID.profileID#);
				</script>
			</cfif>
			<cfif local.strPaymentFeatures.applePay.enable>
				<script type="application/javascript">					
					let p_#arguments.qryGateWayID.profileID#_applePaySession;
					
					function p_#arguments.qryGateWayID.profileID#_applePay() {
						if (! p_#arguments.qryGateWayID.profileID#_validateStep()) {
							return false;
						}

						let amt = chargeInfo#arguments.qryGateWayID.profileID#.amt.toString();
						<cfif local.strPaymentFeatures.processingFee.enable>
							if ($('##processFeeDonation#arguments.qryGateWayID.profileID#_yes').is(':checked'))
								amt = chargeInfo#arguments.qryGateWayID.profileID#.amtincprocessingfees.toString();
						</cfif>

						<!--- Creating an Apple Pay Session --->
						var paymentRequest = {
							countryCode: '#local.countryCode#',
							currencyCode: '#arguments.qryGateWayID.currencyType#',
							supportedNetworks: #serializeJson(local.applePayCardTypes)#,
							merchantCapabilities: ["supports3DS","supportsDebit","supportsCredit"],
							total: { label: '#encodeForHTML(application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgName)#', amount: amt },
						};
						p_#arguments.qryGateWayID.profileID#_applePaySession = new ApplePaySession(3, paymentRequest);
				
						<!--- Merchant Validation --->
						p_#arguments.qryGateWayID.profileID#_applePaySession.onvalidatemerchant = async function (event) {
							$.getJSON('/?event=proxy.ts_json&c=GATEPAY&m=applePayOVM', { profileID:#arguments.qryGateWayID.profileID# })
								.done(function (validationResponse) {
									let merchantSession = validationResponse.data;
									p_#arguments.qryGateWayID.profileID#_applePaySession.completeMerchantValidation(merchantSession);
								}).fail(function( jqxhr, textStatus, error ) {
									console.log(error);
									p_#arguments.qryGateWayID.profileID#_applePaySession.abort();
								});
						};
				
						<!--- payment authorization --->
						p_#arguments.qryGateWayID.profileID#_applePaySession.onpaymentauthorized = async function(event) {
							$.post('/?event=proxy.ts_json&c=GATEPAY&m=applePayOPA', { profileID: #arguments.qryGateWayID.profileID#, payload: JSON.stringify(event.payment)})
								.then(function (paymentResponse) {

									if (paymentResponse.success) {
										const result = { "status": ApplePaySession.STATUS_SUCCESS };
										p_#arguments.qryGateWayID.profileID#_applePaySession.completePayment(result);
										p_#arguments.qryGateWayID.profileID#_applePaySession = null;

										let enableProcFees = 0;
										<cfif local.strPaymentFeatures.processingFee.enable>
											enableProcFees = $('##processFeeDonation#arguments.qryGateWayID.profileID#_yes').is(':checked') ? 1 : 0;
										</cfif>

										let message = { success:true, 
														messagetype:"MCGatewayEvent", 
														eventname:'applepaytokenready', 
														profileid:#arguments.qryGateWayID.profileID#,
														tokendata: paymentResponse.data,
														enableprocessingfee:enableProcFees };
										window.postMessage(message,'#JSStringFormat(local.hostNameWithProtocol)#');
									
									} else {
										p_#arguments.qryGateWayID.profileID#_applePaySession.abort();
									} 

								}, function (error) {
									console.log(error);
									p_#arguments.qryGateWayID.profileID#_applePaySession.abort();
								});
						};
				
						p_#arguments.qryGateWayID.profileID#_applePaySession.oncancel = function(event) {};
				
						<!--- Begins the merchant validation process --->
						p_#arguments.qryGateWayID.profileID#_applePaySession.begin();
					}
					function onApplePayLoaded#arguments.qryGateWayID.profileID#() {
						<!--- to handle inline loading of payment forms --->
						return new Promise(async function(resolve,reject) {
							let maxDelay = 20, counter = 0;
							while(true) {
								counter++;
								await p_#arguments.qryGateWayID.profileID#_delay(1000);
								if ($('.applepay#arguments.qryGateWayID.profileID#container').length || counter == maxDelay) break;
							}

							resolve();

						}).then(function() {

							$('.applepay#arguments.qryGateWayID.profileID#container').attr('data-init',1);
							
							<!--- Apple Pay availability --->
							if (window.ApplePaySession) {
								let merchantIdentifier#arguments.qryGateWayID.profileID# = '#arguments.qryGateWayID.gatewayAccountID#';
								ApplePaySession.applePayCapabilities(merchantIdentifier#arguments.qryGateWayID.profileID#).then(function(capabilities) {
									switch (capabilities.paymentCredentialStatus) {
										case "paymentCredentialsAvailable":
											/* Display an Apple Pay button and offer Apple Pay as the primary payment option. */
											$('.applepay#arguments.qryGateWayID.profileID#container').show();
											break;
										case "paymentCredentialStatusUnknown":
											/* Display an Apple Pay button and offer Apple Pay as a payment option. */
											$('.applepay#arguments.qryGateWayID.profileID#container').show();
											break;
										case "paymentCredentialsUnavailable":
											/* Consider displaying an Apple Pay button. */
											$('.applepay#arguments.qryGateWayID.profileID#container').show();
											break;
										case "applePayUnsupported":
											/* Don't show an Apple Pay button or offer Apple Pay. */
											break;
									}
								});
							}

						}).catch(function(err) {
							console.log(err);
						});
					}

					<!--- Load Apple Pay JS --->
					let applePaySDK#arguments.qryGateWayID.profileID# = document.createElement('script');
					applePaySDK#arguments.qryGateWayID.profileID#.onload = function () {
						onApplePayLoaded#arguments.qryGateWayID.profileID#();
					};
					applePaySDK#arguments.qryGateWayID.profileID#.async = true;
					applePaySDK#arguments.qryGateWayID.profileID#.crossorigin = 'anonymous';
					applePaySDK#arguments.qryGateWayID.profileID#.src = "https://applepay.cdn-apple.com/jsapi/1.latest/apple-pay-sdk.js";
					document.head.appendChild(applePaySDK#arguments.qryGateWayID.profileID#);
				</script>
				<style type="text/css">
					apple-pay-button {
						--apple-pay-button-width: #local.btnWidth#px;
						--apple-pay-button-height: 40px;
						--apple-pay-button-border-radius: 4px;
						--apple-pay-button-padding: 4px;
						--apple-pay-button-box-sizing: border-box;
					}
				</style>
			</cfif>
			<cfif arguments.offerDelete>
				<script id="mcp-authcim-removecard-template#arguments.qryGateWayID.profileID#" type="text/x-handlebars-template">
					<div class="mcp-authcim-font-weight-bold">Remove Card</div>
					<div class="mcp-authcim-alert-warning mcp-authcim-p-2 mcp-authcim-font-size-sm mcp-authcim-mt-2 mcp-authcim-mb-2">This action is permanent.<br/>Are you sure you want to remove this card?</div>
					<div>
						<button type="button" id="btnRemoveCard{{payprofileid}}" class="btn btn-sm btn-danger" onclick="p_#arguments.qryGateWayID.profileID#_doRemovePaymentProfile({{payprofileid}},{{cpid}},{{cppid}});">Yes, Remove Card</button>
						<button type="button" class="btn btn-sm btn-secondary mcp-authcim-ml-2" onclick="p_#arguments.qryGateWayID.profileID#_cancelForm({{payprofileid}});">Cancel</button>
					</div>
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>
	
		<cfsavecontent variable="local.returnStruct.jsvalidation">
			<cfoutput>
			var hasPOF#arguments.qryGateWayID.profileID# = $('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr("pofcount") != 0;
			<cfif local.strPaymentFeatures.applePay.enable EQ 1 OR local.strPaymentFeatures.googlePay.enable EQ 1>
				if ($('##p_#arguments.qryGateWayID.profileID#_tokenData').length && $('##p_#arguments.qryGateWayID.profileID#_tokenData').val().length)
					hasPOF#arguments.qryGateWayID.profileID# = true;
			</cfif>
			if (!hasPOF#arguments.qryGateWayID.profileID#)
				arrReq.push('#NOT arguments.showCOF ? "Enter your payment information to continue." : "Add a card to continue."#');
			</cfoutput>
		</cfsavecontent>
	
		<cfsavecontent variable="local.returnStruct.inputForm">
			<cfoutput>
			<div id="divInputFormWrapper#arguments.qryGateWayID.profileID#" class="mcp-authcim-card-box mcp-authcim-shadow-none mcp-authcim-p-3 mcp-authcim-mb-3 mcp-authcim-w-sm-100" pofcount="#local.qryProfilesOnFile.recordcount#" style="width:#arguments.hideSelect?320:340#px;min-height:150px;font-size:15px;box-sizing:border-box;">
				<div id="divInputForm#arguments.qryGateWayID.profileID#Step1" class="inputForm#arguments.qryGateWayID.profileID#Step mcp-authcim-d-none">
					<div class="mcp-authcim-font-size-md mcp-authcim-font-weight-bold mcp-authcim-mb-3 mcp-authcim-border-bottom mcp-authcim-pb-1">Select a Payment Method</div>
					<input type="hidden" name="paymentMethod#arguments.qryGateWayID.profileID#" id="paymentMethod#arguments.qryGateWayID.profileID#" class="mcp-authcim-input-field#arguments.qryGateWayID.profileID#" value="#local.strPaymentFeatures.applePay.enable OR local.strPaymentFeatures.googlePay.enable ? '' : 'cc'#">
					<div class="mcp-authcim-d-flex mcp-authcim-flex-column">
						<button type="button" class="mcp-authcim-btn mcp-authcim-mb-3" onclick="p_#arguments.qryGateWayID.profileID#_onSelectPayMethod('cc');" style="width:#local.btnWidth#px;">
							<cfloop query="local.qryCardTypes">
								<img src="/assets/common/images/payment/#lCase(local.qryCardTypes.cardType)#-logo.png" alt="#local.qryCardTypes.cardType#">
							</cfloop>
						</button>
						<cfif local.strPaymentFeatures.applePay.enable>
							<div class="applepay#arguments.qryGateWayID.profileID#container mcp-authcim-mb-3" style="display:none;" data-init="0">
								<cfif local.strPaymentFeatures.processingFee.enable>
									<apple-pay-button buttonstyle="black" type="plain" locale="en-US" onclick="p_#arguments.qryGateWayID.profileID#_onSelectPayMethod('applepay');"></apple-pay-button>
								<cfelse>
									<apple-pay-button buttonstyle="black" type="pay" locale="en-US" onclick="p_#arguments.qryGateWayID.profileID#_applePay();"></apple-pay-button>
								</cfif>
							</div>
						</cfif>
						<cfif local.strPaymentFeatures.googlePay.enable>
							<cfif local.strPaymentFeatures.processingFee.enable>
								<div class="gpay#arguments.qryGateWayID.profileID#container mcp-authcim-mb-3" data-gpaymode="select" data-init="0" style="width:#local.btnWidth#px;height:40px;"></div>
							<cfelse>
								<div class="gpay#arguments.qryGateWayID.profileID#container mcp-authcim-mb-3" data-gpaymode="pay" data-init="0" style="width:#local.btnWidth#px;height:40px;"></div>
							</cfif>
						</cfif>
					</div>
				</div>
				<div id="divInputForm#arguments.qryGateWayID.profileID#Step2" class="inputForm#arguments.qryGateWayID.profileID#Step mcp-authcim-d-none">
					<cfif NOT arguments.hideSelect>
						<div class="mcp-authcim-font-size-md mcp-authcim-font-weight-bold <cfif local.strPaymentFeatures.surcharge.enable>mcp-authcim-mb-2<cfelse>mcp-authcim-mb-4</cfif> mcp-authcim-border-bottom mcp-authcim-pb-1">
							<cfif local.qryProfilesOnFile.recordcount is 1>
								Use the following card
							<cfelse>
								Select from the following cards
							</cfif>
						</div>
					</cfif>

					<div class="inputFormStepErr mcp-authcim-text-danger mcp-authcim-font-size-sm mcp-authcim-mb-2 mcp-authcim-d-none"></div>

					<cfif local.qryProfilesOnFile.recordcount is 0>
						<cfif arguments.showCOF>
							<div class="mcp-authcim-mb-3">There are no cards on file.</div>
						</cfif>
					<cfelse>

						<cfif local.strPaymentFeatures.surcharge.enable>
							<div class="mcp-authcim-mb-3 mcp-authcim-font-size-xs">#local.strPaymentFeatures.surcharge.msg#</div>
						</cfif>
	
						<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>
						<cfloop query="local.qryProfilesOnFile">
							<cfset local.EncEditPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>editPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.qryProfilesOnFile.customerProfileID#</cpid><cppid>#local.qryProfilesOnFile.paymentProfileID#</cppid><em>#arguments.editMode#</em><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
							<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							<cfif local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1>
								<cfset local.EncReassignPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>reassignPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.qryProfilesOnFile.customerProfileID#</cpid><cppid>#local.qryProfilesOnFile.paymentProfileID#</cppid><em>#arguments.editMode#</em><ioi>#local.includeOpenInvoices#</ioi><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
								<cfset local.EncReassignPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncReassignPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
							</cfif>
	
							<cfset local.addPaymentEncString = "">
	
							<cfif local.showAssociatedItems is 1>
								<cfquery name="local.qryAssociatedInvoicesThisCard" dbtype="query">
									select payProfileID, profileName, invoiceID, invoicenumber, dateDue, amountDue, status
									from [local].qryAssociatedInvoices
									where payProfileID = #local.qryProfilesOnFile.payProfileID#
									order by dateDue, invoicenumber
								</cfquery>
								<cfquery name="local.qryAssociatedInvoicesThisCardOverdue" dbtype="query">
									select invoiceID, invoicenumber, amountDue
									from [local].qryAssociatedInvoicesThisCard
									where dateDue < #now()#
								</cfquery>
								<cfquery name="local.qryThisBadCardtotals" dbtype="query">
									select count(*) as overdueInvoiceCount, sum(amountDue) as overdueAmount
									from [local].qryAssociatedInvoicesThisCardOverdue
								</cfquery>
								<cfif val(local.qryThisBadCardtotals.overdueAmount) gt 0>
									<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=arguments.pmid, t="Invoices #valuelist(local.qryAssociatedInvoicesThisCardOverdue.invoiceNumber)#", ta=local.qryThisBadCardtotals.overdueAmount, tmid=arguments.pmid, ad="v|#valuelist(local.qryAssociatedInvoicesThisCardOverdue.invoiceID)#")>
								</cfif>
	
								<cfquery name="local.qryAssociatedSubscriptionTypesThisCard" dbtype="query">
									select typename, subscriptionName
									from [local].qryAssociatedSubscriptionTypes
									where payProfileID = #local.qryProfilesOnFile.payProfileID#
									order by typename, subscriptionName
								</cfquery>
	
								<cfquery name="local.qryAssociatedContributionsThisCard" dbtype="query">
									select programName
									from [local].qryAssociatedContributions
									where payProfileID = #local.qryProfilesOnFile.payProfileID#
									order by programName
								</cfquery>
							</cfif>
	
							<div id="payProfileContainer#arguments.qryGateWayID.profileID#_#local.qryProfilesOnFile.payProfileID#" class="payProfileContainer#arguments.qryGateWayID.profileID#">
								<div class="mcp-authcim-d-flex<cfif local.qryProfilesOnFile.currentrow gt 1> mcp-authcim-mt-3</cfif>">
									<cfif NOT arguments.hideSelect>
										<input type="radio" name="p_#arguments.qryGateWayID.profileID#_mppid" value="#local.qryProfilesOnFile.payProfileID#" class="mcp-authcim-align-self-center mcp-authcim-mr-2 mcp-authcim-input-field#arguments.qryGateWayID.profileID#" data-sgen="#val(local.qryProfilesOnFile.surchargeEligible)#" onclick="p_#arguments.qryGateWayID.profileID#_selectCard(#local.qryProfilesOnFile.payProfileID#);"<cfif local.qryProfilesOnFile.currentrow EQ 1> checked</cfif>>
									</cfif>
									<cfset local.highlightFailedCard = listFindNoCase("frontEndManage,controlPanelManage,controlPanelPayment",arguments.editMode) AND len(local.qryProfilesOnFile.failedSinceDate)>
									<div id="payProfileWrapper#arguments.qryGateWayID.profileID#_#local.qryProfilesOnFile.payProfileID#" class="payProfileWrapper#arguments.qryGateWayID.profileID# mcp-authcim-card-box#local.highlightFailedCard ? ' mcp-authcim-border-danger mcp-authcim-border-2' : ' mcp-authcim-border-1'#<cfif NOT arguments.hideSelect AND local.qryProfilesOnFile.currentrow EQ 1> mcp-authcim-card-selected</cfif>" style="width:#local.cardWidth#px;">
										<div class="mc-card-body mcp-authcim-p-2">
											<div class="mcp-authcim-cof-info">
												<div class="mcp-authcim-d-flex mcp-authcim-align-items-center">
													<cfif len(local.qryProfilesOnFile.cardType)>
														<img src="/assets/common/images/payment/#lcase(local.qryProfilesOnFile.cardType)#_brandmark.png" style="width:40px;" class="mcp-authcim-mr-3 mcp-authcim-mb-0">
													<cfelse>
														<img src="/assets/common/images/blank.gif" style="width:40px;" class="mcp-authcim-mr-3">
													</cfif>
													<div class="mcp-authcim-d-flex mcp-authcim-flex-column">
														<div class="mcp-authcim-font-size-lg mcp-authcim-font-weight-bold">**** #right(local.qryProfilesOnFile.detail,4)#</div>
														<cfif len(local.qryProfilesOnFile.expiration)><div class="mcp-authcim-font-size-sm">Exp #DateFormat(local.qryProfilesOnFile.expiration,"mm/yy")#</div></cfif>
														<cfif len(local.qryProfilesOnFile.nickname)><div class="mcp-authcim-font-size-sm mcp-authcim-mt-1">#local.qryProfilesOnFile.nickname#</div></cfif>
														<cfif local.strPaymentFeatures.surcharge.enable><div class="mcp-authcim-font-size-sm mcp-authcim-mt-1 mcp-authcim-font-italic"><cfif local.qryProfilesOnFile.surchargeEligible is 1>#arguments.qryGateWayID.surchargePercent#% surcharge applies<cfelse>No surcharge applies</cfif></div></cfif>
													</div>
													<cfset local.arrActions = []>
													<cfset local.arrActions.append({ "title":"Update Card", "clickFn":"p_#arguments.qryGateWayID.profileID#_editPaymentProfile('#JSStringFormat(local.EncEditPayProfile)#');",
																"cpIconClass":"fa-solid fa-pen", "feIconClass":"icon-pencil" })>
													<cfif arguments.offerDelete>
														<cfset local.arrActions.append({ "title":"Remove Card", "clickFn":"p_#arguments.qryGateWayID.profileID#_removePaymentProfile(#local.qryProfilesOnFile.payProfileID#,#local.qryProfilesOnFile.customerProfileID#,#local.qryProfilesOnFile.paymentProfileID#);",
																"cpIconClass":"fa-solid fa-trash-alt", "feIconClass":"icon-trash" })>
													</cfif>
													<cfif listFindNoCase("controlPanelPayment,controlPanelManage",arguments.editMode) OR arguments.showCOF>
														<cfset local.arrActions.append({ "title":"Card Info", "clickFn":"p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);",
																"cpIconClass":"fa-solid fa-circle-info", "feIconClass":"icon-info-sign", "anchorClass":"#local.highlightFailedCard ? 'mcp-authcim-text-danger' : ''#" })>
													</cfif>
													<cfif local.showPayOverdueNow is 1 and len(local.addPaymentEncString)>
														<cfset local.arrActions.append({ "title":"Pay Overdue Invoices Now", "clickFn":"top.addPayment('#local.addPaymentEncString#');",
																"cpIconClass":"fa-solid fa-money-bill", "feIconClass":"icon-usd" })>
													</cfif>
													<div class="mcp-authcim-ml-auto mcp-authcim-cof-actions">
														<cfif local.highlightFailedCard>
															<a href="##" class="mcp-authcim-text-danger #local.isFrontEnd ? 'mcp-authcim-mr-2' : 'mcp-authcim-mr-1'#" onclick="p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);return false;">
																<i class="#local.isFrontEnd ? 'icon-info-sign' : 'fa-solid fa-circle-info'#"></i>
															</a>
														</cfif>
														<div class="mcp-authcim-dropdown" tabindex="1">
															<i class="mcp-authcim-dropbtnmask" tabindex="1"></i>
															<span class="mcp-authcim-dropbtn mcp-authcim-font-size-xl" role="listbox" aria-label="Card Actions"><i class="<cfif local.isFrontEnd>icon-ellipsis-horizontal<cfelse>fa-solid fa-ellipsis-stroke</cfif>"></i></span>
															<div class="mcp-authcim-dropdown-content mcp-authcim-font-size-xs">
																<cfloop array="#local.arrActions#" index="local.thisAction">
																	<a href="##" class="mcp-authcim-col-auto#local.thisAction.keyExists('anchorClass') ? ' #local.thisAction.anchorClass#' : ''#" title="#local.thisAction.title#" onclick="#local.thisAction.clickFn#return false;">
																		<i class="<cfif local.isFrontEnd>#local.thisAction.feIconClass#<cfelse>#local.thisAction.cpIconClass#</cfif>" style="width:20px;"></i>
																		<span class="mcp-authcim-ml-2">#local.thisAction.title#</span>
																	</a>
																</cfloop>
															</div>
														</div>
													</div>
												</div>
											</div>
											<div id="cardFrmContainer#local.qryProfilesOnFile.payProfileID#" class="mcp-authcim-mt-3" style="display:none"></div>
											<cfif arguments.showCOF>
												<div id="cardInfoContainer#local.qryProfilesOnFile.payProfileID#" class="mcp-authcim-mt-3 mcp-authcim-font-size-xs" style="display:none">
													<div class="mcp-authcim-d-flex mcp-authcim-mb-2">
														<div class="mcp-authcim-col"><span class="mcp-authcim-text-dim">Added</span><br/>#DateTimeFormat(local.qryProfilesOnFile.dateAdded,"m/d/yy h:nn tt")#<cfif local.showAddedBy is 1 and len(local.qryProfilesOnFile.addedByName)> by #local.qryProfilesOnFile.addedByName#</cfif></div>
													</div>
													<cfif len(local.qryProfilesOnFile.lastUpdatedDate)>
														<div class="mcp-authcim-d-flex mcp-authcim-mb-2">
															<div class="mcp-authcim-col"><span class="mcp-authcim-text-dim">Updated</span><br/>#DateTimeFormat(local.qryProfilesOnFile.lastUpdatedDate,"m/d/yy h:nn tt")#<cfif local.showlastUpdater is 1 and len(local.qryProfilesOnFile.updatedByName)> by #local.qryProfilesOnFile.updatedByName#</cfif></div>
														</div>
													</cfif>
													<cfif local.showFailureInfo is 1 and len(local.qryProfilesOnFile.failedSinceDate)>
														<div class="mcp-authcim-cof_fail">
															<cfif DateCompare(local.qryProfilesOnFile.failedSinceDate,local.qryProfilesOnFile.failedLastDate)>
																Failed <cfif local.qryProfilesOnFile.failedCount is 1>once on<cfelseif val(local.qryProfilesOnFile.failedCount) gt 1>#local.qryProfilesOnFile.failedCount# times since</cfif> #dateformat(local.qryProfilesOnFile.failedSinceDate,"m/d/yyyy")#
																&bull; 
																Last attempted #dateformat(local.qryProfilesOnFile.failedLastDate,"m/d/yyyy")# 
															<cfelse>
																Failed on #dateformat(local.qryProfilesOnFile.failedSinceDate,"m/d/yyyy")#
															</cfif>
															<cfif local.showAssociatedItems is 1 and local.qryAssociatedInvoicesThisCard.recordcount>
																<cfif len(local.qryProfilesOnFile.nextAllowedAutoChargeDate) and local.qryProfilesOnFile.nextAllowedAutoChargeDate gt now() and ((val(local.qryProfilesOnFile.maxFailedAutoAttempts) eq 0) or (local.qryProfilesOnFile.failedCount lt local.qryProfilesOnFile.maxFailedAutoAttempts))>
																	&bull; Next attempt will be #dateformat(local.qryProfilesOnFile.nextAllowedAutoChargeDate,"m/d/yyyy")#
																<cfelseif ((val(local.qryProfilesOnFile.maxFailedAutoAttempts) gt 0) and (local.qryProfilesOnFile.failedCount gte local.qryProfilesOnFile.maxFailedAutoAttempts))>
																	&bull; Maximum attempts reached
																</cfif>
															</cfif>
														</div>
													</cfif>
													<div class="mcp-authcim-text-center"><a href="##" onclick="p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);return false;">Hide Info</a></div>
												</div>
											</cfif>
										</div>
									</div>
								</div>
								<cfif arguments.showCOF AND local.showAssociatedItems is 1 and local.qryAssociatedSubscriptionTypesThisCard.recordcount + local.qryAssociatedInvoicesThisCard.recordcount + local.qryAssociatedContributionsThisCard.recordcount gt 0>
									<div id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#" style="margin-top:3px;">
										<div class="mcp-authcim-card-box" style="width:#local.cardWidth#px;border-radius:0.5rem;">
											<div class="mcp-authcim-card-header" id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_heading_#local.qryProfilesOnFile.currentrow#" style="cursor:pointer;" onclick="p_#arguments.qryGateWayID.profileID#_mppid_toggleAssocItems(#local.qryProfilesOnFile.currentrow#);">
												<div class="mcp-authcim-d-flex">
													<span>Associated With This Card</span>
													<div style="margin-left:auto;">
														<span id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#_show">
															<cfif arguments.editMode EQ 'frontEndManage'>
																<i class="icon-chevron-right"></i>
															<cfelse>
																<i class="fas fa-angle-right"></i>
															</cfif>
														</span>
														<span id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#_hide" class="mcp-authcim-d-none">
															<cfif arguments.editMode EQ 'frontEndManage'>
																<i class="icon-chevron-down"></i>
															<cfelse>
																<i class="fas fa-angle-down"></i>
															</cfif>
														</span>
													</div>
												</div>
											</div>
											<div id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_container_#local.qryProfilesOnFile.currentrow#" class="mcp-authcim-d-none" aria-labelledby="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_heading_#local.qryProfilesOnFile.currentrow#" data-parent="##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#" style="padding:5px;">
												<cfif len(local.addPaymentEncString) OR (local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1)>
													<div class="mcp-authcim-d-flex" style="margin-bottom:10px;border-top:1px solid ##ccc;padding:5px;">
														<cfif len(local.addPaymentEncString)>
															<div class="mcp-authcim-col">
																<div>OverDue</div>
																<div style="font-size:16px;">#dollarformat(local.qryThisBadCardtotals.overdueAmount)#</div>
																<cfif local.showPayOverdueNow is 1 and len(local.addPaymentEncString)>
																	<div>
																		<button type="button" class="btn<cfif arguments.editMode NEQ 'frontEndManage'> btn-xs btn-primary</cfif>" onclick="top.addPayment('#local.addPaymentEncString#');">Pay</button>
																	</div>
																</cfif>
															</div>
														</cfif>
														<cfif local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1>
															<div class="mcp-authcim-col">
																<div>Move to Another Card</div>
																<div>
																	<button type="button" class="btn<cfif arguments.editMode NEQ 'frontEndManage'> btn-xs btn-primary</cfif>" onclick="p_#arguments.qryGateWayID.profileID#_reassignPaymentProfile('#JSStringFormat(local.EncReassignPayProfile)#');">Reassign</button>
																</div>
															</div>
														</cfif>
													</div>
												</cfif>
												<div style="border-top:1px solid ##ccc;max-height:285px;overflow-y:auto;">
													<ul style="padding:10px 5px;">
														<cfif local.qryAssociatedSubscriptionTypesThisCard.recordcount>
															<cfloop query="local.qryAssociatedSubscriptionTypesThisCard">
																<li title="Subscription">#local.qryAssociatedSubscriptionTypesThisCard.typename#: #local.qryAssociatedSubscriptionTypesThisCard.subscriptionName#</li>
															</cfloop>
														</cfif>
														<cfif local.qryAssociatedContributionsThisCard.recordcount>
															<cfloop query="local.qryAssociatedContributionsThisCard">
																<li title="Contribution">#local.qryAssociatedContributionsThisCard.programName#</li>
															</cfloop>
														</cfif>
														<cfif local.qryAssociatedInvoicesThisCard.recordcount>
															<cfloop query="local.qryAssociatedInvoicesThisCard">
																<li title="Invoice">
																	<cfif local.qryAssociatedInvoicesThisCard.dateDue lt now()>
																		<div style="color:red;">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber#<cfif local.includeOpenInvoices is 1> (#local.qryAssociatedInvoicesThisCard.status#)</cfif> &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
																	<cfelse>
																		<div class="small">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber#<cfif local.includeOpenInvoices is 1> (#local.qryAssociatedInvoicesThisCard.status#)</cfif> &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
																	</cfif>
																</li>
															</cfloop>
														</cfif>
													</ul>
												</div>
											</div>
										</div>
									</div>
								</cfif>
							</div>
						</cfloop>
					</cfif>

					<cfif arguments.showCOF OR local.qryProfilesOnFile.recordcount is 0>
						<div class="mcp-authcim-mt-3">
							<a href="##" class="mcp-authcim-font-size-sm" onclick="p_#arguments.qryGateWayID.profileID#_addPaymentProfile();return false;">
								<cfif local.isFrontEnd><i class="icon-plus-sign"></i><cfelse><i class="fa-solid fa-circle-plus"></i></cfif> Add Credit/Debit Card
							</a>
						</div>
					</cfif>
				</div>
				<cfif local.strPaymentFeatures.processingFee.enable>
					<div id="divInputForm#arguments.qryGateWayID.profileID#Step3" class="inputForm#arguments.qryGateWayID.profileID#Step mcp-authcim-d-none">
						<div class="mcp-authcim-font-size-md mcp-authcim-font-weight-bold mcp-authcim-mb-2 mcp-authcim-border-bottom mcp-authcim-pb-1">#local.strPaymentFeatures.processingFee.title#</div>
						<div class="mcp-authcim-mb-3">#local.strPaymentFeatures.processingFee.msg#</div>
						<div id="inputForm#arguments.qryGateWayID.profileID#SelectedCard" class="mcp-authcim-mb-3"></div>
						<div class="inputFormStepErr mcp-authcim-text-danger mcp-authcim-font-size-sm mcp-authcim-mb-2 mcp-authcim-d-none"></div>
						<div class="mcp-authcim-d-flex mcp-authcim-line-height-1 mcp-authcim-mb-2">
							<span>
								<input type="radio" name="processFeeDonation#arguments.qryGateWayID.profileID#" id="processFeeDonation#arguments.qryGateWayID.profileID#_yes" class="mcp-authcim-procfee-radio mcp-authcim-input-field#arguments.qryGateWayID.profileID#" value="1" autocomplete="off"<cfif local.strPaymentFeatures.processingFee.select> checked</cfif> onchange="p_#arguments.qryGateWayID.profileID#_manageProcessingFee();">
							</span>
							<label id="processFeeLabel#arguments.qryGateWayID.profileID#" class="mcp-authcim-font-size-md mcp-authcim-m-0 mcp-authcim-align-self-center" for="processFeeDonation#arguments.qryGateWayID.profileID#_yes">#local.strPaymentFeatures.processingFee.label#</label>
						</div>
						<div class="mcp-authcim-d-flex mcp-authcim-line-height-1">
							<span>
								<input type="radio" name="processFeeDonation#arguments.qryGateWayID.profileID#" id="processFeeDonation#arguments.qryGateWayID.profileID#_no" class="mcp-authcim-procfee-radio mcp-authcim-input-field#arguments.qryGateWayID.profileID#" value="0" autocomplete="off" onchange="p_#arguments.qryGateWayID.profileID#_manageProcessingFee();">
							</span>
							<label class="mcp-authcim-font-size-md mcp-authcim-m-0 mcp-authcim-align-self-center" for="processFeeDonation#arguments.qryGateWayID.profileID#_no">#local.strPaymentFeatures.processingFee.denylabel#</label>
						</div>
					</div>
				</cfif>
				<cfif NOT listFindNoCase("controlPanelManage,frontEndManage",arguments.editMode)>
					<div id="divInputForm#arguments.qryGateWayID.profileID#StepButtons" class="mcp-authcim-mt-5 mcp-authcim-d-none">
						<div id="inputForm#arguments.qryGateWayID.profileID#StepButtonContainer_cc" class="mcp-authcim-d-flex mcp-authcim-d-none inputForm#arguments.qryGateWayID.profileID#StepButtonContainer mcp-authcim-w-sm-100" style="width:#local.formActionWidth#px;">
							<a href="##" id="inputForm#arguments.qryGateWayID.profileID#PrevStepBtn" class="mcp-authcim-align-self-center mcp-authcim-font-size-sm mcp-authcim-text-dim inputForm#arguments.qryGateWayID.profileID#StepBtn" onclick="p_#arguments.qryGateWayID.profileID#_prevStep();return false;">Go back</a>
							<div class="mcp-authcim-ml-auto">
								<button type="button" id="inputForm#arguments.qryGateWayID.profileID#NextStepBtn" class="btn btn-sm btn-primary inputForm#arguments.qryGateWayID.profileID#StepBtn mcp-authcim-d-none" onclick="p_#arguments.qryGateWayID.profileID#_nextStep();">
									Continue
								</button>
								<button type="button" id="inputForm#arguments.qryGateWayID.profileID#CCPayBtn" class="btn btn-sm btn-primary inputForm#arguments.qryGateWayID.profileID#StepBtn mcp-authcim-d-none" onclick="p_#arguments.qryGateWayID.profileID#_pay();"></button>
							</div>
						</div>
						<cfif local.strPaymentFeatures.processingFee.enable>
							<cfif local.strPaymentFeatures.applePay.enable>
								<div id="inputForm#arguments.qryGateWayID.profileID#StepButtonContainer_applepay" class="mcp-authcim-d-none inputForm#arguments.qryGateWayID.profileID#StepButtonContainer">
									<div class="chargeAmtDisp#arguments.qryGateWayID.profileID# mcp-authcim-mb-3 mcp-authcim-font-weight-bold mcp-authcim-text-center"></div>
									<div class="mcp-authcim-d-flex mcp-authcim-flex-column mcp-authcim-align-items-center">
										<div class="applepay#arguments.qryGateWayID.profileID#container mcp-authcim-mb-3" style="display:none;" data-init="0">
											<apple-pay-button buttonstyle="black" type="pay" locale="en-US" onclick="p_#arguments.qryGateWayID.profileID#_applePay();"></apple-pay-button>
										</div>
										<a href="##" class="mcp-authcim-font-size-sm mcp-authcim-text-dim" onclick="p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(1);return false;">Go back</a>
									</div>
								</div>
							</cfif>
							<cfif local.strPaymentFeatures.googlePay.enable>
								<div id="inputForm#arguments.qryGateWayID.profileID#StepButtonContainer_googlepay" class="mmcp-authcim-d-none inputForm#arguments.qryGateWayID.profileID#StepButtonContainer">
									<div class="chargeAmtDisp#arguments.qryGateWayID.profileID# mcp-authcim-mb-3 mcp-authcim-font-weight-bold mcp-authcim-text-center"></div>
									<div class="mcp-authcim-d-flex mcp-authcim-flex-column mcp-authcim-align-items-center">
										<div class="gpay#arguments.qryGateWayID.profileID#container mcp-authcim-mb-3" data-gpaymode="pay" data-init="0" style="width:#local.btnWidth#px;height:40px;"></div>
										<a href="##" class="mcp-authcim-font-size-sm mcp-authcim-text-dim" onclick="p_#arguments.qryGateWayID.profileID#_loadPaymentFormSteps(1);return false;">Go back</a>
									</div>
								</div>
							</cfif>
						</cfif>
					</div>
				</cfif>
				<div id="divInputForm#arguments.qryGateWayID.profileID#StepLoading">#local.loadingIcon# Loading...</div>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getInputFormLoaderData" access="package" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="formHolderElementID" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="editMode" type="string" required="no" default="frontEndManageV2" hint="frontEndManageV2">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errMsg="", headcode='', strActionFn:{}, arrMemProfilesOnFile:[] }>

		<cfset local.qryMemNum = application.objPayments.getActiveMemberData(memberID=arguments.pmid)>
		<cfset local.qryProfilesOnFile = getProfilesOnFile(profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=0, statsSessionID=session.cfcuser.statsSessionID, skipStatsSessionID=1)>

		<!--- customer profileID to use for adding new profiles, get the one with the least amount of payment profiles. auth caps them at 10 --->
		<cfquery name="local.qryCustProfile" dbtype="query" maxrows="1">
			select customerProfileID, count(*) as theCount
			from [local].qryProfilesOnFile
			group by customerProfileID
			order by theCount
		</cfquery>

		<cfset local.cidToUse = arguments.pmid>
		<cfset local.cpidToUse = local.qryCustProfile.customerProfileID>

		<cfset local.strPaymentFeatures = application.objPayments.setDefaultPayFeaturesStruct()>
		<cfset local.strPaymentFeatures.surcharge.enable = arguments.qryGateWayID.enableMCPay EQ 1 AND arguments.qryGateWayID.enableSurcharge EQ 1>
		<cfif local.strPaymentFeatures.surcharge.enable EQ 1>
			<cfset local.strPaymentFeatures.surcharge.msg = replaceNoCase(local.strPaymentFeatures.surcharge.msg, "{{PERCENT}}", "#val(arguments.qryGateWayID.surchargePercent)#%")>
		</cfif>
		<cfset local.paymentFeaturesJSON = serializeJSON(local.strPaymentFeatures)>

		<cfset local.chargeInfoJSON = serializeJSON({ "amt":0, "processingfees":0, "amtincprocessingfees":0 })>

		<cfset local.EncAddPayProfile = "<data><pmid>#arguments.pmid#</pmid><cid>#local.cidToUse#</cid><cn>#xmlformat(local.qryMemNum.membernumber)# - #xmlformat(local.qryMemNum.fullname)#</cn><action>addPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.cpidToUse#</cpid><hcnl>0</hcnl><em>#arguments.editMode#</em><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			<script language="javascript">
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfile() {
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					$('###arguments.formHolderElementID#').hide();
					ifr.hide();
					ifr .off('load');
					ifr	.attr('width','100%')
						.attr('height','700px')
						.attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#')
						.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
					p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(true);
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj) {
					var formHolderEl = $('###arguments.formHolderElementID#');
					var refID = formHolderEl.data('refid');

					if (obj && obj.a && obj.a=='save') {
						var onsavefn = eval(formHolderEl.data('onsavefn'));
						if(onsavefn) onsavefn(obj,refID);
						else p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
					if (obj && obj.a && obj.a=='cancel') {
						var oncancelfn = eval(formHolderEl.data('oncancelfn'));
						if(oncancelfn) oncancelfn(obj,refID);
						else p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
					if (obj && obj.err) {
						var onerrorfn = eval(formHolderEl.data('onerrorfn'));
						if(onerrorfn) onerrorfn(obj.err,refID);
						p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
				}
				function p_#arguments.qryGateWayID.profileID#_editPaymentProfile(we) {
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					$('###arguments.formHolderElementID#').hide();
					ifr.hide();
					ifr .off('load');
					ifr	.attr('width','100%')
						.attr('height','438px')
						.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
						.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });

					p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(true);
				}
				function p_#arguments.qryGateWayID.profileID#_removePaymentProfile(datastring) {
					var arrData = datastring.split('|');
					var cpid = arrData[0];
					var cppid = arrData[1];
					
					let removeResult = function(r) {
						if (r.success && r.success == 'true') {
							let objRet = { a:'save', mccardevent:'cardRemoved' };
							p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
							
						} else {
							let objRet = { err:'We were unable to remove this card. Try again.' };
							p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						}
					};

					let objParams = { profileID:#arguments.qryGateWayID.profileID#, pmid:#arguments.pmid#, cpid:cpid, cppid:cppid };
					TS_AJX('AUTHORIZECCCIM','removePaymentProfile',objParams,removeResult,removeResult,20000,removeResult);
				}
				function p_#arguments.qryGateWayID.profileID#_resizeIFrame() {
					$('##iframeManageForm#arguments.qryGateWayID.profileID#Loading').hide();
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					ifr.contents().find('body, div.container, body > div.bodyText').css({margin:0});
					ifr.contents().find('body, div.inner-content, body > div.container-fluid, div.container').css({padding:0});
					ifr.show();
					var nh = ifr.contents().find("div##zoneMain").height();
					if (nh > 0) ifr.attr('height',nh+40 + 'px');
				}
				function p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(f) {
					$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').toggle(f);
				}
				function p_#arguments.qryGateWayID.profileID#_initFormLoader() { 
					if ($('##divManageFormWrapper#arguments.qryGateWayID.profileID#').length == 0)
						$('###arguments.formHolderElementID#').after('<div id="divManageFormWrapper#arguments.qryGateWayID.profileID#" style="display:none;"><div id="iframeManageForm#arguments.qryGateWayID.profileID#Loading"><i class="icon-spin icon-spinner"></i> <span style="margin-left:3px;">Please Wait...</span><br/><br/></div><iframe name="iframeManageForm#arguments.qryGateWayID.profileID#" id="iframeManageForm#arguments.qryGateWayID.profileID#" frameborder="0" allowTransparency="true" scrolling="no" style="display:none;"></iframe></div>');
				}
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfset local.returnStruct.strActionFn["init"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_initFormLoader" }>
		<cfset local.returnStruct.strActionFn["add"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_addPaymentProfile" }>

		<cfloop query="local.qryProfilesOnFile">
			<cfset local.thisMemProfile = { "payProfileID": local.qryProfilesOnFile.payProfileID, "strActionFn": {} }>

			<cfset local.EncEditPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>editPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><cpid>#local.qryProfilesOnFile.customerProfileID#</cpid><cppid>#local.qryProfilesOnFile.paymentProfileID#</cppid><em>#arguments.editMode#</em><pfs>#local.paymentFeaturesJSON#</pfs><cinf>#local.chargeInfoJSON#</cinf></data>">
			<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

			<cfset local.thisMemProfile.strActionFn["edit"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_editPaymentProfile", "fnParam": "#JSStringFormat(local.EncEditPayProfile)#" }>
			<cfset local.thisMemProfile.strActionFn["remove"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_removePaymentProfile", "fnParam": "#JSStringFormat(local.qryProfilesOnFile.customerProfileID & '|' & local.qryProfilesOnFile.paymentProfileID)#" }>

			<cfset arrayAppend(local.returnStruct.arrMemProfilesOnFile, local.thisMemProfile)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getProfilesOnFile" access="private" returntype="query" output="no">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="skipStatsSessionID" type="boolean" required="no" default="0">
	
		<cfset var local = structNew()>

		<!--- if we arent showing COF (if not logged in, for example), still need to show the ones added in this session --->
		<!--- merge will put active COF on the new memberID, but merged member may still be in session so we need force lookup of activeMemberID anyway --->
		<cfquery name="local.qryProfilesOnFile" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @systemMemberID int, @cofMemberID int, @mpProfileID int;
			select @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);
			set @mpProfileID = <cfqueryparam value="#arguments.profileID#" cfsqltype="CF_SQL_INTEGER">;

			select mpp.payProfileID, mpp.detail, mpp.nickname, mpp.customerProfileID, mpp.paymentProfileID, mpp.dateAdded, 
				mpp.failedSinceDate, mpp.failedLastDate, mpp.nextAllowedAutoChargeDate, mpp.failedCount, mpp.lastUpdatedDate, 
				mp.maxFailedAutoAttempts, mpp.expiration, mpp.surchargeEligible,
				case when m.memberID <> @systemMemberID then am.firstname + ' ' + am.lastname else '' end as addedByName,
				case when m2.memberID <> @systemMemberID then am2.firstname + ' ' + am2.lastname else '' end as updatedByName,
				ct.cardType
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.mp_profiles mp on mp.profileID = mpp.profileID
				and mp.profileID = @mpProfileID
				and mpp.memberID = @cofMemberID
				and mpp.status = 'A'			
			inner join dbo.ams_members m on m.memberID = mpp.addedByMemberID
			inner join dbo.ams_members am on am.memberID = m.activeMemberID
			left outer join dbo.ams_members m2 
				inner join dbo.ams_members am2 on am2.memberID = m2.activeMemberID
				on m2.memberID = mpp.lastUpdatedByMemberID
			left outer join dbo.mp_cardTypes as ct on ct.cardTypeID = mpp.cardTypeID
			<cfif NOT arguments.showCOF AND NOT arguments.skipStatsSessionID>
				where mpp.addedStatsSessionID = <cfqueryparam value="#arguments.statsSessionID#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			ORDER BY mpp.payProfileID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryProfilesOnFile>
	</cffunction>

	<cffunction name="createCustomerProfile" access="private" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerName" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { customerProfileId='', head='', errMessage='' }>

		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"createCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#arguments.qryGateWayID.gatewayUsername#", 
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#" 
						},
						"profile": {
							"merchantCustomerId": "#left(encodeForHTML(arguments.customerID),20)#",
							"description": "#left(encodeForHTML(arguments.customerName),255)#"
						}
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile could not be created because it already exists --->
				<!--- java needed because cf doesnt support look ahead/behinds --->
				<cfset local.objPattern = CreateObject("java","java.util.regex.Pattern").Compile("(?<=A duplicate record with ID )([0-9]+)(?= already exists\.)")>
				<cfset local.arrIDs = []>
				<cfloop array="#local.strAuthorize.arrErrors#" index="local.thisErr">
					<cfif local.thisErr.code EQ 'E00039'>
						<cfset local.objMatcher = local.objPattern.Matcher(local.thisErr.text)>
						<cfloop condition="local.objMatcher.Find()">
							<cfset ArrayAppend(local.arrIDs,local.objMatcher.Group())>
						</cfloop>
					</cfif>
				</cfloop>
				<cfif arrayLen(local.arrIDs) is 1>
					<cfset local.returnStruct.customerProfileId = local.arrIDs[1]>					
				<cfelse>
					<cfthrow message="Error creating customer profile.">
				</cfif>
			<cfelse>
				<cfset local.returnStruct.customerProfileId = local.strAuthorize.strAPIResponse.customerProfileId>
			</cfif>
			
			<!--- error if we dont have a customerProfileID now. --->
			<cfif NOT len(local.returnStruct.customerProfileid)>
				<cfthrow message="Error creating customer profile.">
			</cfif>
			
		<cfcatch type="any">
			<cfset local.returnStruct.errMessage = cfcatch.message>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(local.returnStruct.errMessage)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerName" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="hideCancel" type="boolean" required="yes">
		<cfargument name="paymentFeatures" type="struct" required="yes">
		<cfargument name="editMode" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- create customer profile id if it is blank/invalid --->
		<cfif NOT isValidCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerProfileid=arguments.customerProfileid)>
			<cfset local.strCustProfile = createCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerID=arguments.customerID, customerName=arguments.customerName)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>
		
		<cfif NOT listFindNoCase("frontEndPayment,frontEndManage,frontEndManageV2",arguments.editMode) AND application.objPayments.isValidMemberToPrefillCardInfo(memberid=arguments.pmid)>
			<cfset local.qryMember = application.objMember.getMemberInfo(memberid=arguments.pmid)>
			<cfset local.qryMemberAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=val(local.qryMember.orgID), memberid=val(local.qryMember.memberid))>
			
			<!--- prefill data --->
			<cfset local.strPrefill = { 
					fld_1_ = local.qryMember.firstname,
					fld_2_ = local.qryMember.lastname,
					fld_4_ = '',
					fld_6_ = '',
					fld_11_ = local.qryMemberAddr.postalCode,
					fld_12_ = local.qryMemberAddr.address1,
					fld_13_ = local.qryMemberAddr.city,
					fld_14_ = local.qryMemberAddr.stateName,
					fld_15_ = local.qryMemberAddr.country
				}>
		<cfelse>
			<cfset local.strPrefill = { 
					fld_1_ = '',
					fld_2_ = '',
					fld_4_ = '',
					fld_6_ = '',
					fld_11_ = '',
					fld_12_ = '',
					fld_13_ = '',
					fld_14_ = '',
					fld_15_ = ''
				}>
		</cfif>
		
		<cfreturn showPaymentProfileForm(qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=arguments.qryGatewayProfileFields, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill, hideCancel=arguments.hideCancel, paymentFeatures=arguments.paymentFeatures)>
	</cffunction>

	<cffunction name="editPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="paymentFeatures" type="struct" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- get payment profile info --->
		<cftry>
			<cfset local.strPaymentProfile = getCustomerPaymentProfile(gatewayUsername=arguments.qryGateWayID.gatewayUsername, gatewayPassword=arguments.qryGateWayID.gatewayPassword,
					customerProfileid=arguments.customerProfileid, customerPaymentProfileId=arguments.customerPaymentProfileId)>

			<cfif NOT local.strPaymentProfile.success>
				<cfthrow message="Unable to locate card for editing.">
			<cfelse>
				<cfquery name="local.qryPayProfile" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
					declare @cofMemberID int;
					select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);
		
					select top 1 nickname
					from dbo.ams_memberPaymentProfiles
					where memberID = @cofMemberID
					and profileID = <cfqueryparam value="#arguments.qryGateWayID.profileID#" cfsqltype="CF_SQL_INTEGER">
					and customerProfileID = <cfqueryparam value="#arguments.customerProfileId#" cfsqltype="CF_SQL_VARCHAR">
					and paymentProfileID = <cfqueryparam value="#arguments.customerPaymentProfileId#" cfsqltype="CF_SQL_VARCHAR">
					and [status] = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.paymentProfile = local.strPaymentProfile.data.paymentProfile>
				<cfset local.strPrefill = { 
					fld_1_ = local.paymentProfile.billTo.keyExists('firstName') ? local.paymentProfile.billTo.firstName : "",
					fld_2_ = local.paymentProfile.billTo.keyExists('lastName') ? local.paymentProfile.billTo.lastName : "",
					fld_4_ = local.paymentProfile.payment.creditCard.cardNumber,
					fld_6_ = local.paymentProfile.payment.creditCard.expirationDate,
					fld_11_ = local.paymentProfile.billTo.keyExists('zip') ? local.paymentProfile.billTo.zip : "",
					fld_12_ = local.paymentProfile.billTo.keyExists('address') ? local.paymentProfile.billTo.address : "",
					fld_13_ = local.paymentProfile.billTo.keyExists('city') ? local.paymentProfile.billTo.city : "",
					fld_14_ = local.paymentProfile.billTo.keyExists('state') ? local.paymentProfile.billTo.state : "",
					fld_15_ = local.paymentProfile.billTo.keyExists('country') ? local.paymentProfile.billTo.country : "",
					nickname = local.qryPayProfile.nickname
				}>
			</cfif>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfreturn showPaymentProfileForm(qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=arguments.qryGatewayProfileFields, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill, hideCancel=0, paymentFeatures=arguments.paymentFeatures)>
	</cffunction>

	<cffunction name="showPaymentProfileForm" access="private" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="strPrefill" type="struct" required="yes">
		<cfargument name="hideCancel" type="boolean" required="yes">
		<cfargument name="paymentFeatures" type="struct" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
		<cfset local.qryCardTypes = application.objPayments.getProfileCardTypes(siteid=arguments.qryGateWayID.siteid, profilecode=arguments.qryGateWayID.profilecode)>
		
		<!--- put fields into str --->
		<cfset local.strFields = structNew()>
		<cfset local.strFieldsReq = structNew()>
		<cfloop query="arguments.qryGatewayProfileFields">
			<cfset local.strFields["#arguments.qryGatewayProfileFields.fieldName#"] = arguments.qryGatewayProfileFields.fieldID>
			<cfif arguments.qryGatewayProfileFields.isRequired>
				<cfset local.strFieldsReq["#arguments.qryGatewayProfileFields.fieldName#"] = arguments.qryGatewayProfileFields.fieldID>
			</cfif>
		</cfloop>
		<cfset local.defaultTemplateSettings = application.objSiteResource.getSiteResourceSettingsStruct(siteResourceID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultTemplateSiteResourceID)>
	
		<cfset local.strPaymentFeatures = application.objPayments.setDefaultPayFeaturesStruct(usePaymentFeatures=arguments.paymentFeatures)>
		
		<!--- validation js --->
		<cfsavecontent variable="local.returnStruct.head">
			<cfoutput>
			<style type="text/css">
				<cfif (local.defaultTemplateSettings.supportsBootstrap eq "true") or (isdefined("session.enableMobile") and session.enableMobile) >
					.purchaseFormContent .wellContainer { border: 1px solid ##c2c2c2; border-radius: 5px; padding-bottom: 15px; width: 375px;}
					.purchaseFormContent .help-block { margin-bottom: 0; }
					.purchaseFormContent .crdInput { width: 98% !important; height: 37px !important; padding-left: 10px; border-color: ##bebebe;}
					.purchaseFormContent .crdInputSplit { width: 95% !important; height: 37px !important; padding-left: 10px; border-color: ##bebebe;}
					.purchaseFormContent .wellHeading { float: left; font-size: 20px;}
					.purchaseFormContent ##everr { width: 95%;  margin-bottom: 10px; }
					.purchaseFormContent .control-label { margin-bottom: 2px !important; font-size:12px !important; font-weight:bold; }
					.purchaseFormContent .control-group { margin-bottom: 6px !important; }
					.purchaseFormContent .control-group input { margin-bottom: 6px !important;box-sizing:border-box !important; }
					.purchaseFormContent input::placeholder {font-size: 12px;}
					.purchaseFormContent input[type="text"] { border-radius: 4px !important; }
					.purchaseFormContent .disclaimer { padding-right:15px; margin-top:10px; font-size:10px; color:##707070;line-height:1.5em;}
					.purchaseFormContent .disclaimer img { width:20px;padding-right:3px; }
					.purchaseFormContent .tmode { font-size: 10px; color: ##f00; font-weight:bold; }
					@media only screen and (max-width: 766px) and (min-width: 370px) {
						.wellContainer .span6 {width: 50% !important;float: left;}
						.purchaseFormContent .crdInput { width: 95% !important;}
						.purchaseFormContent .crdInputSplit { width: 88% !important;}
					}
					@media only screen and (max-width: 430px) {
						.purchaseFormContent .wellContainer {width:100%}
						.purchaseFormContent .wellCardHolder img {width: 25px !important;}
						.purchaseFormContent .wellHeading { font-size: 18px;}
					}
					@media only screen and (max-width: 369px) {
						.purchaseFormContent .crdInput { width: 89% !important;}
						.purchaseFormContent .crdInputSplit { width: 89% !important;}
					}
					@media only screen and (max-width: 310px) {
						.purchaseFormContent .crdInput { width: 86% !important;}
						.purchaseFormContent .crdInputSplit { width: 86% !important;}
						.purchaseFormContent ##everr { width: 93%;}
						.purchaseFormContent .wellHeading { float: none;}
						.purchaseFormContent .wellCardHolder {text-align: left;}
					}
				<cfelse>
					.alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
					input[type=text], input[type=password], select { margin-top: 4px; margin-bottom: 0px; }
					input[type=text], input[type=password] { border: 1px solid ##7F9DB9; padding: 2px; }
					select { border: 1px solid ##7F9DB9; }
					input[type=text][disabled=disabled] { background-color: ##EBEBE4; }
					input.Disabled { background-color: ##EBEBE4; }
					.FieldGroupSeparator { background-color: ##e0e0e0; font-weight: bold; padding: 5px 10px; margin-bottom: 10px; }
					.FieldGroupSeparatorBillingInfo { margin-top: 15px; }
					.PaymentPadlock { position:absolute; top: 15px; right: 10px; width: 13px; height: 15px; background-image: url('/assets/common/images/padlock.png'); }
					.Comment { font-size: 11px; }
					.EditButtons { background-color: ##e0e0e0; padding: 5px; margin-top: 10px; }
					.tmode { font-size: 11px; color: ##f00; font-weight:bold; }
					.disclaimer { padding-left:5px; margin-top:20px; }
					.disclaimer img { padding-right:8px; }
					.FieldGroupSeparatorPaymentInfo { display: block; }
					.PaymentItemEditData { padding: 4px; }
					.CreditCardInfo .DataLabelEdit, .AddressEdit .DataLabelEdit, .EditButtons .DataLabelEdit { display: inline-block; text-align: right; width: 145px; }
					.DataLabelEdit, .DataLabelEdit2 { font-weight: bold; }
					.DataValEditCardNum input { width: 150px; }
					.DataValEditExpDate input { width: 70px; }
					.DataValEditFirstName input { width: 200px; }
					.DataValEditLastName input { width: 200px; }
					.DataValEditStreet input { width: 200px; }
					.DataValEditCity input { width: 200px; }
					.DataValEditState input { width: 55px; }
					.DataValEditZip input { width: 75px; }
					.DataValEditCountry input { width: 200px; }
				</cfif>
				.cardlogo30 {width:30px !important;margin:0 3px !important;}
				##frmAuthorizeCIM .help-block {font-size:11px;color:##777;margin-top:1px;}
			</style>
			<script language="javascript">
				function hideAlert() { 
					$('##everr').html('').hide(); 
					if (parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame) parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				};
				function showAlert(msg) { 
					$('##everr').html(msg).show(); 
					if (parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame) parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				};
	
				function cancelIt() { 
					var obj = new Object();
						obj.a = 'cancel';
					if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj);
				}
				function _FB_hasValue(obj, obj_type) {
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
						tmp = obj.value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length == 0) return false;
						else return true;
					} else if (obj_type == 'SELECT'){
						for (var i=0; i < obj.length; i++) {
							if (obj.options[i].selected){
								tmp = obj.options[i].value;
								tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
								if (tmp.length > 0) return true;
							}
						}
						return false;	
					} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
						if (obj.checked) return true;
						else return false;	
					} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
						if (obj.length == undefined && obj.checked) return true;
						else{
							for (var i=0; i < obj.length; i++){
								if (obj[i].checked) return true;
							}
						}
						return false;
					}else{
						return true;
					}
				}
				function valExpDate() {
					var bad = false;
					<cfif len(arguments.strPrefill.fld_4_)>if ($('##fld_6_').val() != 'XXXX') {</cfif>
						var ed = $('##fld_6_').val().split(/[\/\-\s]+/);
						var pattern = /^\d+$/;
						if (!pattern.test(ed[0]) || !pattern.test(ed[1])) bad = true;
						if (ed[0] < 1 || ed[0] > 12 || ed[1] < 10 || ed[1] > 99) bad = true;
						if (!bad) $('##fld_6_').val(ed[0] + '/' + ed[1]);
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					return bad;
				}
				function valCardForm() {
					$('form##frmAuthorizeCIM button[type="submit"]').prop('disabled',true);
					hideAlert();
					var arrReq = new Array();
					var thisForm = document.forms["frmAuthorizeCIM"];
					
					<cfif len(arguments.strPrefill.fld_4_)>if (thisForm['fld_#local.strFields["Credit Card Number"]#_'].value.indexOf("XXXX") < 0) {</cfif>
						if (!_CF_checkcreditcard(thisForm['fld_4_'].value, true)) arrReq[arrReq.length] = 'Card Number must be valid.';
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					if (!_FB_hasValue(thisForm['fld_6_'], 'TEXT') || valExpDate()) arrReq[arrReq.length] = 'Expiration Date must be valid.';
	
					if (arrReq.length == 0) {
						if (!_FB_hasValue(thisForm['fld_#local.strFields["First Name"]#_'], 'TEXT')) arrReq[arrReq.length] = 'First Name cannot be blank.';
						if (!_FB_hasValue(thisForm['fld_#local.strFields["Last Name"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Last Name cannot be blank.';
						<cfif StructKeyExists(local.strFields,"Billing Postal Code") and StructKeyExists(local.strFieldsReq,"Billing Postal Code")>
							if (!_FB_hasValue(thisForm['fld_#local.strFields["Billing Postal Code"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Billing Postal Code cannot be blank.';
						</cfif>
						<cfif StructKeyExists(local.strFields,"Billing Address") and StructKeyExists(local.strFieldsReq,"Billing Address")>
							if (!_FB_hasValue(thisForm['fld_#local.strFields["Billing Address"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Billing Address cannot be blank.';
						</cfif>
						<cfif StructKeyExists(local.strFields,"Billing City") and StructKeyExists(local.strFieldsReq,"Billing City")>
							if (!_FB_hasValue(thisForm['fld_#local.strFields["Billing City"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Billing City cannot be blank.';
						</cfif>
						<cfif StructKeyExists(local.strFields,"Billing State/Province") and StructKeyExists(local.strFieldsReq,"Billing State/Province")>
							if (!_FB_hasValue(thisForm['fld_#local.strFields["Billing State/Province"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Billing State/Province cannot be blank.';
						</cfif>
						<cfif StructKeyExists(local.strFields,"Billing Country") and StructKeyExists(local.strFieldsReq,"Billing Country")>
							if (!_FB_hasValue(thisForm['fld_#local.strFields["Billing Country"]#_'], 'TEXT')) arrReq[arrReq.length] = 'Billing Country cannot be blank.';
						</cfif>
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						$('form##frmAuthorizeCIM button[type="submit"]').prop('disabled',false);
						return false;
					}
					return true;
				}

				function onReceivePaymentAppMessage(event) {
					if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.profileid == #arguments.qryGateWayID.profileID#) {
						switch (event.data.messagetype.toLowerCase()) {
							<cfif NOT (local.strPaymentFeatures.processingFee.enable OR local.strPaymentFeatures.surcharge.enable)>
								case 'mcfrontendpaymentevent':
									$('form##frmAuthorizeCIM button[type="submit"]').text(event.data.paymentbuttonname);
									break;
							</cfif>
						}
					} else {
						return false;
					}
				}

				$(function() {
					if (window.addEventListener) {
						window.addEventListener("message", onReceivePaymentAppMessage, false);
					} else if (window.attachEvent) {
						window.attachEvent("message", onReceivePaymentAppMessage);
					}

					<!--- let parent know that payment form is ready and if there is anything to broadcast --->
					parent.postMessage({ success:true, messagetype:'MCPaymentFormLoadEvent', profileid:#arguments.qryGateWayID.profileID# },'#JSStringFormat(getHostNameWithProtocol())#');
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfif (local.defaultTemplateSettings.supportsBootstrap eq "true") or (isdefined("session.enableMobile") and session.enableMobile) >
			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<cfform name="frmAuthorizeCIM" id="frmAuthorizeCIM" method="post" action="/?pg=buyNow&mode=direct&wizard=#arguments.EncSaveCardURL#" onsubmit="return valCardForm();">
				<cfset local.phAddress = StructKeyExists(local.strFieldsReq,"Billing Address") ? 'Billing Address *' : 'Billing Address'>
				<cfset local.phCity = StructKeyExists(local.strFieldsReq,"Billing City") ? 'Billing City *' : 'Billing City'>
				<cfset local.phState = StructKeyExists(local.strFieldsReq,"Billing State/Province") ? 'Billing State/Province *' : 'Billing State/Province'>
				<cfset local.phPostalCode = StructKeyExists(local.strFieldsReq,"Billing Postal Code") ? 'Billing Postal Code *' : 'Billing Postal Code'>
				<cfset local.phCountry = StructKeyExists(local.strFieldsReq,"Billing Country") ? 'Billing Country *' : 'Billing Country'>
				<div class="container-fluid purchaseFormContent">
					<div class="row-fluid">
						<div class="wellContainer span5">
							<div class="well well-sm">
								<span class="wellHeading">Credit/Debit Card</span>
								<span class="wellCardHolder help-block r">
									<cfloop query="local.qryCardTypes">
										<img src="/assets/common/images/payment/#lCase(local.qryCardTypes.cardType)#_brandmark.png" class="cardlogo30" alt="#local.qryCardTypes.cardType#" border="0">
									</cfloop>
								</span>
							</div>
							<div class="cardInputsContainer" style="padding:0 10px;">
								<div class="row-fluid">
									<div class="span6">
										<div class="control-group">
											<label class="control-label" for="fld_4_">Card Number *</label>
											<div class="controls">
												<cfinput autocomplete="off" class="tsAppBodyText crdInput" onkeypress="hideAlert();" id="fld_4_" name="fld_4_" maxLength="16" type="text" value="#arguments.strPrefill.fld_4_#">
											</div>
										</div>
									</div>
									<div class="span6">
										<div class="control-group">
											<label class="control-label" for="fld_6_">Exp Date (MM/YY) *</label>
											<div class="controls">
												<cfinput autocomplete="off" class="tsAppBodyText crdInputSplit" onkeypress="hideAlert();" id="fld_6_" name="fld_6_" maxLength="5" type="text" value="#arguments.strPrefill.fld_6_#" placeholder="MM/YY">
											</div>
										</div>
									</div>
								</div>
								<div class="row-fluid">
									<div class="span6">
										<div class="control-group">
											<label class="control-label" for="fld_#local.strFields['First Name']#_">First Name on Card *</label>
											<div class="controls">
												<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit namefields" id="fld_#local.strFields['First Name']#_" name="fld_#local.strFields['First Name']#_" maxLength="50" type="text" value="#arguments.strPrefill.fld_1_#">
											</div>
										</div>
									</div>
									<div class="span6">
										<div class="control-group">
											<label class="control-label" for="fld_#local.strFields['Last Name']#_">Last Name on Card *</label>
											<div class="controls">
												<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInputSplit namefields" id="fld_#local.strFields['Last Name']#_" name="fld_#local.strFields['Last Name']#_" maxLength="50" type="text" value="#arguments.strPrefill.fld_2_#">
											</div>
										</div>
									</div>
								</div>
								<cfif StructKeyExists(local.strFields,"Billing Address")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_#local.strFields['Billing Address']#_">#local.phAddress#</label>
												<div class="controls">
													<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_#local.strFields['Billing Address']#_" name="fld_#local.strFields['Billing Address']#_" maxLength="60" type="text" value="#arguments.strPrefill.fld_12_#">
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<cfif StructKeyExists(local.strFields,"Billing City")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_#local.strFields['Billing City']#_">#local.phCity#</label>
												<div class="controls">
													<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_#local.strFields['Billing City']#_" name="fld_#local.strFields['Billing City']#_" maxLength="40" type="text" value="#arguments.strPrefill.fld_13_#">
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<cfif StructKeyExists(local.strFields,"Billing State/Province")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_#local.strFields['Billing State/Province']#_">#local.phState#</label>
												<div class="controls">
													<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_#local.strFields['Billing State/Province']#_" name="fld_#local.strFields['Billing State/Province']#_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#">
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<cfif StructKeyExists(local.strFields,"Billing Postal Code")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_#local.strFields['Billing Postal Code']#_">#local.phPostalCode#</label>
												<div class="controls">
													<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_#local.strFields['Billing Postal Code']#_" name="fld_#local.strFields['Billing Postal Code']#_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#">
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<cfif StructKeyExists(local.strFields,"Billing Country")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_#local.strFields['Billing Country']#_">#local.phCountry#</label>
												<div class="controls">
													<cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText crdInput" id="fld_#local.strFields['Billing Country']#_" name="fld_#local.strFields['Billing Country']#_" maxLength="60" type="text" value="#arguments.strPrefill.fld_15_#">
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<cfif StructKeyExists(arguments.strPrefill,"nickname")>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_nickname">Optional Card Nickname</label>
												<div class="controls">
													<cfinput type="text" class="tsAppBodyText crdInput" id="fld_nickname" name="fld_nickname" maxLength="30" value="#arguments.strPrefill.nickname#" autocomplete="off">
													<span class="help-block">For security, do NOT use the card number, expiration date, or security code in the nickname.</span>
												</div>
											</div>
										</div>
									</div>
								</cfif>
								<div id="everr" class="span12 alert alert-error" style="display:none;"></div>
								<div class="row-fluid">
									<div class="span12">
										<button class="btn btn-success" type="submit">Continue</button>
										<cfif arguments.hideCancel is 0><button class="btn btn-secondary" onclick="cancelIt()" type="button">Cancel</button> </cfif>
										<cfif variables.x_testmode is 1><span class="tmode">&nbsp;** SANDBOX ** </span></cfif>
									</div>
								</div>
								<div class="row-fluid">
									<div class="span12 disclaimer">
										<img src="/assets/common/images/padlock.png" align="left"> Your payment information will be processed in accordance with established credit card security standards.
									</div>
								</div>
								<cfif local.strPaymentFeatures.surcharge.enable><div class="disclaimer">#local.strPaymentFeatures.surcharge.msg#</div></cfif>
							</div>
						</div>
					</div>
				</div>
				</cfform>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<cfform name="frmAuthorizeCIM" id="frmAuthorizeCIM" method="post" action="/?pg=buyNow&mode=direct&wizard=#arguments.EncSaveCardURL#" onsubmit="return valCardForm();">
				<div class="tsAppBodyText">
					<div class=PaymentItemEditData>
						<div class="FieldGroupSeparator FieldGroupSeparatorPaymentInfo">Card Details</div>
						<div class=CreditCardInfo>
							<div><span class="DataLabelEdit">Card Number:&nbsp;</span> <span class="DataValEditCardNum"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_4_" name="fld_4_" maxLength="16" type="text" value="#arguments.strPrefill.fld_4_#"> * </span></div>
							<div><span class="DataLabelEdit">Expiration Date:&nbsp;</span> <span class="DataValEditExpDate"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_6_" name="fld_6_" maxLength="5" type="text" value="#arguments.strPrefill.fld_6_#"> * <span class="Comment">(mm/yy)</span></span></div>
							<div style="margin-top: 10px;"><span class="DataLabelEdit">&nbsp;</span> <span><span class="Comment">We accept</span> <cfloop query="local.qryCardTypes"><img src="/assets/common/images/payment/#local.qryCardTypes.cardType#.png" width="24" height="15" alt="#local.qryCardTypes.cardType#" border="0"> </cfloop> </span></div>
						</div>
						<div class="FieldGroupSeparator FieldGroupSeparatorBillingInfo">Billing Information</div>
						<div class="AddressEdit">
							<div><span class="DataLabelEdit">First Name:&nbsp;</span> <span class="DataValEditFirstName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['First Name']#_" name="fld_#local.strFields['First Name']#_" maxLength="50" type="text" value="#arguments.strPrefill.fld_1_#"></span> * </div>
							<div><span class="DataLabelEdit">Last Name:&nbsp;</span> <span class="DataValEditLastName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Last Name']#_" name="fld_#local.strFields['Last Name']#_" maxLength="50" type="text" value="#arguments.strPrefill.fld_2_#"></span> * </div>
							<cfif StructKeyExists(local.strFields,"Billing Address")>
								<div><span class="DataLabelEdit">Address:&nbsp;</span> <span class="DataValEditStreet"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing Address']#_" name="fld_#local.strFields['Billing Address']#_" maxLength="60" type="text" value="#arguments.strPrefill.fld_12_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing Address")>*</cfif> </div>
							</cfif>
							<cfif StructKeyExists(local.strFields,"Billing City")>
								<div><span class="DataLabelEdit">City:&nbsp;</span> <span class="DataValEditCity"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing City']#_" name="fld_#local.strFields['Billing City']#_" maxLength="40" type="text" value="#arguments.strPrefill.fld_13_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing City")>*</cfif> </div>
							</cfif>
							<cfif StructKeyExists(local.strFields,"Billing State/Province") and StructKeyExists(local.strFields,"Billing Postal Code")>
								<div><span class="DataLabelEdit">State/Province:&nbsp;</span> <span class="DataValEditState"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing State/Province']#_" name="fld_#local.strFields['Billing State/Province']#_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing State/Province")>*</cfif> <span class="DataLabelEdit2">&nbsp; Postal Code:&nbsp;</span> <span class="DataValEditZip"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing Postal Code']#_" name="fld_#local.strFields['Billing Postal Code']#_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing Postal Code")>*</cfif> </div>
							<cfelseif StructKeyExists(local.strFields,"Billing State/Province")>
								<div><span class="DataLabelEdit">State/Province:&nbsp;</span> <span class="DataValEditState"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing State/Province']#_" name="fld_#local.strFields['Billing State/Province']#_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing State/Province")>*</cfif> </div>
							<cfelseif StructKeyExists(local.strFields,"Billing Postal Code")>
								<div><span class="DataLabelEdit">Postal Code:&nbsp;</span> <span class="DataValEditZip"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing Postal Code']#_" name="fld_#local.strFields['Billing Postal Code']#_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing Postal Code")>*</cfif> </div>
							</cfif>
							<cfif StructKeyExists(local.strFields,"Billing Country")>
								<div><span class="DataLabelEdit">Country:&nbsp;</span> <span class="DataValEditCountry"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_#local.strFields['Billing Country']#_" name="fld_#local.strFields['Billing Country']#_" maxLength="60" type="text" value="#arguments.strPrefill.fld_15_#"></span> <cfif StructKeyExists(local.strFieldsReq,"Billing Country")>*</cfif> </div>
							</cfif>
							<cfif StructKeyExists(arguments.strPrefill,"nickname")>
								<div>
									<span class="DataLabelEdit">Optional Card Nickname:&nbsp;</span>
									<span style="display:inline-flex;flex-direction:column;">
										<cfinput type="text" class="tsAppBodyText" id="fld_nickname" name="fld_nickname" maxLength="30" value="#arguments.strPrefill.nickname#" autocomplete="off">
										<span class="help-block">For security, do NOT use the card number, expiration date, or security code in the nickname.</span>
									</span>
								</div>
							</cfif>
						</div>
					</div>
					<div id="everr" class="alert" style="display:none;margin:6px 0;"></div>
					<div class="EditButtons">
						<div>
							<span class="DataLabelEdit"><cfif variables.x_testmode is 1><span class="tmode">** SANDBOX **&nbsp;</span><cfelse>&nbsp;</cfif></span>
							<span>
								<button class="tsAppBodyButton" type="submit">Continue</button> 
								<cfif arguments.hideCancel is 0><button class="tsAppBodyButton" onclick="cancelIt()" type="button">Cancel</button> </cfif>
								<cfif variables.x_testmode is 1><span class="tmode">&nbsp;** SANDBOX ** </span></cfif>
							</span>
						</div>
					</div>
					<div class="disclaimer"><img src="/assets/common/images/padlock.png" width="32" height="32" align="left"> Your payment information will be processed in accordance with established credit card security standards.</div>
					<cfif local.strPaymentFeatures.surcharge.enable><div class="disclaimer">#local.strPaymentFeatures.surcharge.msg#</div></cfif>
				</div>
				</cfform>
				</cfoutput>
			</cfsavecontent>
		</cfif>		
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerName" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="fld_1_" type="string" required="yes">
		<cfargument name="fld_2_" type="string" required="yes">
		<cfargument name="fld_4_" type="string" required="yes">
		<cfargument name="fld_6_" type="string" required="yes">
		<cfargument name="fld_7_" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='', errMessage='' }>
	
		<!--- create customer profile id if it is blank/invalid (if we just added 1st card on file, for example) --->
		<cfif NOT isValidCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerProfileid=arguments.customerProfileid)>
			<cfset local.strCustProfile = createCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerID=arguments.customerID, customerName=arguments.customerName)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
				<cfset local.returnStruct.errMessage = local.strCustProfile.errMessage>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>
		
		<!--- Ensure card number is valid formed --->
		<cfset arguments.fld_4_ = rereplace(arguments.fld_4_,"[^0-9]","","ALL")>
		<cfif NOT len(arguments.fld_4_) or NOT isValid("creditcard",arguments.fld_4_)>
			<cfset local.returnStruct.errMessage = 'Unable to Save Card'>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#local.returnStruct.errMessage#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cftry>
			<cfset local.expirationDate = "20" & GetToken(arguments.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.fld_6_,1,'/'),"09")>
			<cfif len(local.expirationDate) is not 7>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct.errMessage = 'Unable to Save Card'>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#local.returnStruct.errMessage#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"createCustomerPaymentProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#arguments.qryGateWayID.gatewayUsername#", 
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#" 
						},
						"customerProfileId": "#arguments.customerProfileID#",
						"paymentProfile": {
							"billTo": {
								"firstName": "#left(encodeForHTML(arguments.fld_1_),50)#",
								"lastName": "#left(encodeForHTML(arguments.fld_2_),50)#",
								<cfif StructKeyExists(arguments,"fld_12_")>"address": "#left(encodeForHTML(arguments.fld_12_),60)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_13_")>"city": "#left(encodeForHTML(arguments.fld_13_),40)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_14_")>"state": "#left(encodeForHTML(arguments.fld_14_),40)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_11_")>"zip": "#left(encodeForHTML(arguments.fld_11_),20)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_15_")>"country": "#left(encodeForHTML(arguments.fld_15_),60)#"</cfif>
							},
							"payment": {
								"creditCard": {
									"cardNumber": "#arguments.fld_4_#",
									"expirationDate": "#local.expirationDate#"
								}
							},
							"defaultPaymentProfile": false
						},
						"validationMode": "none"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [ 
				{ key='createCustomerPaymentProfileRequest.paymentProfile.payment.creditCard.cardNumber', replacement='XXXX#right(arguments.fld_4_,4)#' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile could not be created because it already exists --->
				<cfif findNoCase("E00039",local.strAuthorize.rawAPIResponse)>
					<cfthrow message="We couldn't save that card because it looks like it already exists on the account.">
				<cfelse>
					<cfthrow message="There was an error saving the card.">
				</cfif>
			<cfelse>
				<cfset local.returnStruct.customerPaymentProfileId = local.strAuthorize.strAPIResponse.customerPaymentProfileId>

				<!--- get card type --->
				<cftry>
					<cfset local.cardTypeID = "">
					<cfset local.strPaymentProfile = getCustomerPaymentProfile(gatewayUsername=arguments.qryGateWayID.gatewayUsername, gatewayPassword=arguments.qryGateWayID.gatewayPassword,
						customerProfileid=arguments.customerProfileid, customerPaymentProfileId=local.returnStruct.customerPaymentProfileId)>
					<cfif local.strPaymentProfile.success AND local.strPaymentProfile.data.paymentProfile.payment.creditcard.keyExists("cardType")>
						<cfset local.cofType = getCOFType(cardType=local.strPaymentProfile.data.paymentProfile.payment.creditcard.cardType)>
						<cfquery name="local.qryCardType" datasource="#application.dsn.membercentral.dsn#">
							select dbo.mp_getCardTypeID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cofType#">) as cardTypeID
						</cfquery>
						<cfset local.cardTypeID = local.qryCardType.cardTypeID>
					</cfif>
				<cfcatch type="Any">
					<cfset local.cardTypeID = "">
				</cfcatch>
				</cftry>

				<!--- get bin information from 1st 10 digits --->
				<cfif variables.doBINLookup and arguments.qryGateWayID.enableMCPay is 1>
					<cfset local.strBIN = getBinInfo(cardPAN=left(arguments.fld_4_,10))>
				</cfif>
				
				<cfset local.expMonth = numberformat(GetToken(local.expirationDate,2,'-'),"09")>
				<cfset local.expYear = GetToken(local.expirationDate,1,'-')>
				<cfset local.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>

				<!--- force lookup of activeMemberID in case the member is merged mid-session --->
				<cfquery name="local.qryInsertCC" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @orgID int, @cofMemberID int, @payProfileID int, @profileID int;

						SET @profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryGateWayID.profileID#">;
						SELECT @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);
						SELECT @orgID = orgID FROM dbo.ams_members WHERE memberID = @cofMemberID;

						BEGIN TRAN;
							insert into dbo.ams_memberPaymentProfiles (memberid, profileID, status, detail, expiration, nickname, 
								customerProfileID, paymentProfileID, addedStatsSessionID, addedByMemberID, cardTypeID, checkedForZIP, 
								hasZIP, surchargeEligible)
							values (
								@cofMemberID,
								@profileID,
								'A', 
								<cfqueryparam cfsqltype="cf_sql_varchar" value="XXXX#right(arguments.fld_4_,4)#">,
								<cfqueryparam cfsqltype="cf_sql_date" value="#local.expDate#">,
								'', 
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.customerProfileid#">,
								<cfqueryparam cfsqltype="cf_sql_varchar" value="#left(local.returnStruct.customerPaymentProfileId,50)#">,
								<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
								<cfif session.cfcuser.memberdata.memberid gt 0>
									<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,
								<cfelse>
									@cofMemberID,
								</cfif>
								<cfif len(local.cardTypeID)>
									<cfqueryparam value="#local.cardTypeID#" cfsqltype="CF_SQL_INTEGER">,
								<cfelse>
									null,
								</cfif>
								1, 
								<cfif StructKeyExists(arguments,"fld_11_") and len(arguments.fld_11_)>
									1
								<cfelse>
									0
								</cfif>,
								<cfif isDefined("local.strBIN.success") and local.strBIN.success>
									<cfif local.strBIN.strInfo.mc_surchargeeligible>1<cfelse>0</cfif>
								<cfelse>
									null
								</cfif>
							);

							SELECT @payProfileID = SCOPE_IDENTITY();

							<cfif isDefined("local.strBIN.success") and local.strBIN.success>
								DECLARE @tokenExJSON varchar(max) = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.strBIN.rawjson#">,
									@binType varchar(30) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strBIN.strInfo.binData.type#">, 
									@binFundingSource varchar(30) = <cfif isDefined("local.strBIN.strInfo.binData.fundingSource")><cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strBIN.strInfo.binData.fundingSource#"><cfelse>''</cfif>, 
									@binPrepaid bit = <cfqueryparam cfsqltype="cf_sql_bit" value="#isDefined("local.strBIN.strInfo.binData.prepaid") and local.strBIN.strInfo.binData.prepaid eq 'true'#">;

								INSERT INTO dbo.ams_memberPaymentProfilesBinData (payProfileID, tokenExJSON, type, fundingSource, prepaid)
								VALUES (@payProfileID, @tokenExJSON, @binType, @binFundingSource, @binPrepaid);
							</cfif>
						COMMIT TRAN;

						EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @memberID=@cofMemberID, @profileID=@profileID, @lookupMode='ccprofile';

						SELECT @payProfileID AS payProfileID;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
	
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardAdded';
								objRet.payprofileid = #val(local.qryInsertCC.payProfileID)#;
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			
		<cfcatch type="any">
			<cfset local.returnStruct.errMessage = cfcatch.message>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getBinInfo" access="private" returntype="struct" output="no">
		<cfargument name="cardPAN" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.strResponse = { "success":false, "rawjson": "", "strInfo": {} }>

		<cftry>
			<cfset local.binAPIRequestBody = '{ "PAN": "#arguments.cardPAN#" }'>

			<cfif variables.x_testmode is 1>
				<cfset local.binAPIURL = "https://test-api.tokenex.com/v2/Pci/BinLookup">
			<cfelse>
				<cfset local.binAPIURL = "https://api.tokenex.com/v2/Pci/BinLookup">
			</cfif>
			<cfhttp url="#local.binAPIURL#" method="post" throwonerror="yes" result="local.binAPIResult" charset="utf-8">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="header" name="Accept" value="application/json">
				<cfhttpparam type="header" name="tx-tokenex-id" value="#application.strPlatformAPIKeys.tokenex.id#">
				<cfhttpparam type="header" name="tx-apikey" value="#application.strPlatformAPIKeys.tokenex.key#">
				<cfhttpparam type="body" value="#local.binAPIRequestBody#">
			</cfhttp>

			<cfset local.binAPIFileContent = local.binAPIResult.fileContent>
			<cfset local.binAPIFileContentPOS = find('{',local.binAPIFileContent)>
			<cfif local.binAPIFileContentPOS gt 0>
				<cfset local.binAPIFileContent = RemoveChars(local.binAPIFileContent,1,local.binAPIFileContentPOS-1)>
			</cfif>

			<cfset local.strResponse.rawjson = trim(local.binAPIFileContent)>
			<cfset local.strResponse.strInfo = deserializeJSON(local.strResponse.rawjson)>

			<cftry>
				<cfset local.strRequest = {
					"c":"TokenEx",
					"d": {
						"request": {
							"bodycontent":deserializeJSON(local.binAPIRequestBody)
						},
						"response": {
							"bodycontent":local.strResponse.strInfo,
							"statuscode":local.binAPIResult.status_code
						},
						"timestamp":now()
					}
				}>
				<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
					INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
					VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
				</cfquery>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>

			<cfif isDefined("local.strResponse.strInfo.binData") and local.strResponse.strInfo.success eq "true">
				<cfset local.strResponse.success = true>
				<cfif isDefined("local.strResponse.strInfo.binData.prepaid") and local.strResponse.strInfo.binData.prepaid eq 'true'>
					<cfset local.surchargeEligible = false>
				<cfelseif isDefined("local.strResponse.strInfo.binData.fundingSource") and local.strResponse.strInfo.binData.fundingSource eq 'CREDIT'>
					<cfset local.surchargeEligible = true>
				<cfelseif isDefined("local.strResponse.strInfo.binData.fundingSource") and local.strResponse.strInfo.binData.fundingSource eq '' and local.strResponse.strInfo.binData.type eq 'Credit'>
					<cfset local.surchargeEligible = true>
				<cfelse>
					<cfset local.surchargeEligible = false>
				</cfif>
				<cfset structInsert(local.strResponse.strInfo,"mc_surchargeeligible",local.surchargeEligible)>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="refreshPaymentProfiles" access="private" returntype="void" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrPayProfiles = arrayNew(1)>
		
		<cfquery name="local.qryGetProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @cofMemberID int, @AuthCCCIMGatewayID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);
			select @AuthCCCIMGatewayID = dbo.fn_mp_getGatewayID('AuthorizeCCCIM');

			select distinct mpp.customerProfileID
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
				and mp.gatewayID = @AuthCCCIMGatewayID
			where mpp.memberID = @cofMemberID
			and mpp.[status] = 'A'
			<cfif len(arguments.customerProfileId)>
					union
				select <cfqueryparam value="#arguments.customerProfileId#" cfsqltype="CF_SQL_VARCHAR"> as customerProfileID
			</cfif>
		</cfquery>
		<cfloop query="local.qryGetProfiles">
			<cftry>
				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"getCustomerProfileRequest": { 
							"merchantAuthentication": { 
								"name": "#arguments.qryGateWayID.gatewayUsername#", 
								"transactionKey": "#arguments.qryGateWayID.gatewayPassword#" 
							},
							"customerProfileId": "#local.qryGetProfiles.customerProfileId#",
							"unmaskExpirationDate": true
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>
	
				<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
				
				<cfif arrayLen(local.strAuthorize.arrErrors)>
					<!--- need to catch if profile was not found --->
					<cfif findNoCase("E00040",local.strAuthorize.rawAPIResponse)>
						<!--- do nothing. --->
					<cfelse>
						<cfthrow message="Error retrieving customer profile.">
					</cfif>
				<cfelse>
					<cfif local.strAuthorize.strAPIResponse.profile.keyExists("paymentProfiles")>
						<cfloop array="#local.strAuthorize.strAPIResponse.profile.paymentProfiles#" index="local.thisPaymentProfile">
							<cfset local.strProfile = { cpid=local.qryGetProfiles.customerProfileId, ppid=local.thisPaymentProfile.customerPaymentProfileId, 
								detail=local.thisPaymentProfile.payment.creditcard.cardnumber, cardType="", haszip=0, expDate="" }>
							<cfif structKeyExists(local.thisPaymentProfile.payment.creditcard, "cardType") and len(local.thisPaymentProfile.payment.creditcard.cardType)>
								<cfset local.strProfile.cardType = getCOFType(cardType=local.thisPaymentProfile.payment.creditcard.cardType)>
							</cfif>
							<cfif structKeyExists(local.thisPaymentProfile, "billTo") and structKeyExists(local.thisPaymentProfile.billTo, "zip") and len(local.thisPaymentProfile.billTo.zip)>
								<cfset local.strProfile.haszip = 1>
							</cfif>
							<cfif structKeyExists(local.thisPaymentProfile.payment.creditcard, "expirationDate") and len(local.thisPaymentProfile.payment.creditcard.expirationDate)>
								<cfset local.expMonth = numberformat(GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,2,'-'),"09")>
								<cfset local.expYear = GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,1,'-')>
								<cfset local.strProfile.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
							</cfif>
							<cfset arrayAppend(local.arrPayProfiles,local.strProfile)>
						</cfloop>
					</cfif>
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.arrPayProfiles = arrayNew(1)>
				<cfbreak>
			</cfcatch>
			</cftry>
		</cfloop>
		
		<cfif ArrayLen(local.arrPayProfiles)>
			<cftry>
				<cfquery name="local.qryUpdateMPP" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @cofMemberID int, @profileID int, @customerProfileID varchar(50), @payProfileID int, @systemMemberID int, @performedByMemberID int, @orgID int;
						DECLARE @tblPP TABLE (cpid varchar(50), ppid varchar(50), detail varchar(50), cardTypeID int, expiration date, haszip bit);
						DECLARE @tblPPDelete TABLE (payProfileID int);

						select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);
						set @profileID = <cfqueryparam value="#arguments.qryGateWayID.profileID#" cfsqltype="CF_SQL_INTEGER">;
						select @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
						set @performedByMemberID = <cfif session.cfcuser.memberdata.memberid gt 0>#session.cfcuser.memberdata.memberid#<cfelse>@systemMemberID</cfif>;
						select @orgID = orgID from dbo.ams_members where memberID = @cofMemberID;
					
						<cfloop array="#local.arrPayProfiles#" index="local.pp">
							INSERT INTO @tblPP (cpid, ppid, detail, cardTypeID, expiration, haszip)
							VALUES (
								<cfqueryparam value="#local.pp.cpid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.pp.ppid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.pp.detail#" cfsqltype="CF_SQL_VARCHAR">,
								dbo.mp_getCardTypeID(<cfqueryparam value="#local.pp.cardType#" cfsqltype="CF_SQL_VARCHAR">),
								<cfif len(local.pp.expDate)>
									<cfqueryparam value="#local.pp.expDate#" cfsqltype="CF_SQL_DATE">,
								<cfelse>
									NULL,
								</cfif>
								<cfqueryparam value="#local.pp.haszip#" cfsqltype="CF_SQL_BIT">
							);
						</cfloop>

						BEGIN TRAN
							insert into @tblPPDelete (payProfileID)
							select payProfileID
							from dbo.ams_memberPaymentProfiles as mpp
							where mpp.memberID = @cofMemberID
							and mpp.profileID = @profileID
							and mpp.status = 'A'
							and not exists (
								select cpid
								from @tblPP
								where cpid = mpp.customerProfileID
								and ppid = mpp.paymentProfileID
							);

							update mpp
							set mpp.failedLastDate = null,
								mpp.failedSinceDate = null
							from dbo.ams_memberPaymentProfiles as mpp
							inner join @tblPPDelete as tmp on tmp.payProfileID = mpp.payProfileID;

							select @payProfileID = min(payProfileID) from @tblPPDelete;
							while @payProfileID is not null begin
								exec dbo.ams_deleteCardOnFile @payProfileID=@payProfileID, @recordedByMemberID=@performedByMemberID;
								select @payProfileID = min(payProfileID) from @tblPPDelete where payProfileID > @payProfileID;
							end

							update mpp
							set mpp.status = 'A', 
								mpp.lastUpdatedDate = getdate(),
								mpp.lastUpdatedByMemberID = @performedByMemberID,
								mpp.detail = tbl.detail,
								mpp.cardTypeID = tbl.cardTypeID,
								mpp.expiration = tbl.expiration
							from dbo.ams_memberPaymentProfiles as mpp
							inner join @tblPP as tbl on tbl.ppid = mpp.paymentProfileID and tbl.cpid = mpp.customerProfileID
							where mpp.memberID = @cofMemberID
							and mpp.profileID = @profileID
							and (mpp.status <> 'A' 
								OR mpp.detail <> tbl.detail 
								OR ISNULL(mpp.cardTypeID,0) <> tbl.cardTypeID
								OR ISNULL(mpp.expiration,'1/1/1900') <> ISNULL(tbl.expiration,'1/1/1900')
							);
		
							insert into dbo.ams_memberPaymentProfiles (memberid, profileID, status, detail, cardTypeID, expiration, nickname, 
								customerProfileID, paymentProfileID, addedStatsSessionID, addedByMemberID, checkedForZIP, hasZIP, surchargeEligible)
							select @cofMemberID, @profileID, 'A', detail, cardTypeID, expiration, '', cpid, ppid, 
								<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">, @performedByMemberID, 1, haszip,
								null
							from @tblPP as tbl
							where not exists (
								select payProfileID
								from dbo.ams_memberPaymentProfiles
								where memberid = @cofMemberID
								and profileID = @profileID
								and customerProfileID = tbl.cpid
								and paymentProfileID = tbl.ppid
							);

							IF @@ROWCOUNT > 0 BEGIN
								EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @memberID=@cofMemberID, @profileID=@profileID, @lookupMode='ccprofile';
							END
						COMMIT TRAN
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>		
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>	
		</cfif>
	</cffunction>

	<cffunction name="addPaymentProfileReturn" access="public" returntype="struct" output="no">
		<cfargument name="siteid" type="numeric" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerName" type="string" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="usePopup" type="boolean" required="yes">
		<cfargument name="usePopupDIVName" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="hideSelect" type="boolean" required="no" default="0">
		<cfargument name="offerDelete" type="boolean" required="no" default="0">
		<cfargument name="overrideCustomerID" type="string" required="no" default="">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment|controlPanelManage">
		<cfargument name="paymentFeatures" type="struct" required="no" default="#application.objPayments.setDefaultPayFeaturesStruct()#">
		<cfargument name="chargeInfo" type="struct" required="no" default="#structNew()#">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { head='' }>
	
		<!--- create customer profile id if it is blank/invalid (if we just added 1st card on file, for example) --->
		<cfif NOT isValidCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerProfileid=arguments.customerProfileid)>
			<cfset local.strCustProfile = createCustomerProfile(qryGateWayID=arguments.qryGateWayID, customerID=arguments.customerID, customerName=arguments.customerName)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
			</cfif>
		</cfif>
	
		<!--- get customer profile --->
		<cfif len(arguments.customerProfileId)>
			<cfset refreshPaymentProfiles(qryGateWayID=arguments.qryGateWayID, pmid=arguments.pmid, customerProfileID=arguments.customerProfileid)>
		</cfif>
	
		<!--- get the gather form to update the calling page --->
		<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.qryGateWayID.profileCode)>
		<cfset local.strGatherVars = { siteid=arguments.siteid, profileCode=arguments.qryGateWayID.profileCode, pmid=arguments.pmid, showCOF=arguments.showCOF,
									   hideSelect=arguments.hideSelect, offerDelete=arguments.offerDelete, usePopup=arguments.usePopup, 
									   usePopupDIVName=arguments.usePopupDIVName, qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=local.qryGatewayProfileFields,
									   overrideCustomerID=arguments.overrideCustomerID, editMode=arguments.editMode, paymentFeatures=arguments.paymentFeatures, chargeInfo=arguments.chargeInfo } > 
		<cfinvoke method="gather" argumentcollection="#local.strGatherVars#" returnvariable="local.strGather">
	
		<!--- output html etc --->
		<cfif len(local.strGather.inputForm)>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				#local.returnStruct.head#
				<script language="javascript">
					$(function() {
						parent.$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').parent().html('#JSStringFormat(application.objCommon.minText(local.strGather.inputForm))#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="fld_1_" type="string" required="yes">
		<cfargument name="fld_2_" type="string" required="yes">
		<cfargument name="fld_4_" type="string" required="yes">
		<cfargument name="fld_6_" type="string" required="yes">
		<cfargument name="fld_7_" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- Ensure card number is valid formed --->
		<cfif NOT FindNoCase("XXXX",arguments.fld_4_)>
			<cfset arguments.fld_4_ = rereplace(arguments.fld_4_,"[^0-9]","","ALL")>
			<cfif NOT len(arguments.fld_4_) or NOT isValid("creditcard",arguments.fld_4_)>
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'Unable to Save Card';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cfif NOT FindNoCase("XXXX",arguments.fld_6_)>
			<cftry>
				<cfset local.expirationDate = "20" & GetToken(arguments.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.fld_6_,1,'/'),"09")>
				<cfif len(local.expirationDate) is not 7>
					<cfthrow>
				</cfif>
			<cfcatch type="Any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'Unable to Save Card';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.expirationDate = arguments.fld_6_>
		</cfif>
		
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"updateCustomerPaymentProfileRequest": {
						"merchantAuthentication": {
							"name": "#arguments.qryGateWayID.gatewayUsername#",
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
						},
						"customerProfileId": "#arguments.customerProfileid#",
						"paymentProfile": {
							"billTo": {
								"firstName": "#left(encodeForHTML(arguments.fld_1_),50)#",
								"lastName": "#left(encodeForHTML(arguments.fld_2_),50)#",
								<cfif StructKeyExists(arguments,"fld_12_")>"address": "#left(encodeForHTML(arguments.fld_12_),60)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_13_")>"city": "#left(encodeForHTML(arguments.fld_13_),40)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_14_")>"state": "#left(encodeForHTML(arguments.fld_14_),40)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_11_")>"zip": "#left(encodeForHTML(arguments.fld_11_),20)#",</cfif>
								<cfif StructKeyExists(arguments,"fld_15_")>"country": "#left(encodeForHTML(arguments.fld_15_),60)#"</cfif>
							},
							"payment": {
								"creditCard": {
									"cardNumber": "#arguments.fld_4_#",
									"expirationDate": "#local.expirationDate#"
								}
							},
							"defaultPaymentProfile": false,
							"customerPaymentProfileId": "#arguments.customerPaymentProfileid#"
						},
						"validationMode": "none"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.arrKeysToMask = [ 
				{ key='updateCustomerPaymentProfileRequest.paymentProfile.payment.creditCard.cardNumber', replacement='XXXX#right(arguments.fld_4_,4)#' }
			]>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfthrow message="There was an error saving the card.">
			<cfelse>
				<cfif NOT FindNoCase("XXXX",arguments.fld_4_)>
					<!--- get card type --->
					<cftry>
						<cfset local.cardTypeID = "">
						<cfset local.strPaymentProfile = getCustomerPaymentProfile(gatewayUsername=arguments.qryGateWayID.gatewayUsername, gatewayPassword=arguments.qryGateWayID.gatewayPassword,
							customerProfileid=arguments.customerProfileid, customerPaymentProfileId=arguments.customerPaymentProfileid)>
						<cfif local.strPaymentProfile.success AND local.strPaymentProfile.data.paymentProfile.payment.creditcard.keyExists("cardType")>
							<cfset local.cofType = getCOFType(cardType=local.strPaymentProfile.data.paymentProfile.payment.creditcard.cardType)>
							<cfquery name="local.qryCardType" datasource="#application.dsn.membercentral.dsn#">
								select dbo.mp_getCardTypeID(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.cofType#">) as cardTypeID
							</cfquery>
							<cfset local.cardTypeID = local.qryCardType.cardTypeID>
						</cfif>
					<cfcatch type="Any">
						<cfset local.cardTypeID = "">
					</cfcatch>
					</cftry>

					<!--- get bin information from 1st 10 digits --->
					<cfif variables.doBINLookup and arguments.qryGateWayID.enableMCPay is 1>
						<cfset local.strBIN = getBinInfo(cardPAN=left(arguments.fld_4_,10))>
					</cfif>
				</cfif>

				<cfif NOT FindNoCase("XXXX",arguments.fld_6_)>
					<cfset local.expMonth = numberformat(GetToken(arguments.fld_6_,1,'/'),"09")>
					<cfset local.expYear = "20#GetToken(arguments.fld_6_,2,'/')#">
					<cfset local.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
				</cfif>

				<cfquery name="local.qryUpdateCC" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @systemMemberID int, @performedByMemberID int, @cofMemberID int, @profileID int, @customerProfileID varchar(50), 
							@customerPaymentProfileID varchar(50), @payProfileID int;

						SELECT @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);
						SELECT @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
						SET @performedByMemberID = <cfif session.cfcuser.memberdata.memberid gt 0>#session.cfcuser.memberdata.memberid#<cfelse>@systemMemberID</cfif>;
						SET @profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.profileID#">;
						SET @customerProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerProfileId#">;
						SET @customerPaymentProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerPaymentProfileId#">;
					
						SELECT TOP 1 @payProfileID = payProfileID
						FROM dbo.ams_memberPaymentProfiles
						WHERE memberid = @cofMemberID
						and profileID = @profileID
						and customerProfileID = @customerProfileID
						and paymentProfileID = @customerPaymentProfileID
						and status = 'A';

						IF @payProfileID IS NOT NULL BEGIN
							BEGIN TRAN;
								update dbo.ams_memberPaymentProfiles
								set detail = <cfqueryparam cfsqltype="cf_sql_varchar" value="XXXX#right(arguments.fld_4_,4)#">,
									<cfif NOT FindNoCase("XXXX",arguments.fld_6_)>
										expiration = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.expDate#">,
									</cfif>
									lastUpdatedDate = getdate(),
									lastUpdatedByMemberID = @performedByMemberID, 
									<cfif NOT FindNoCase("XXXX",arguments.fld_4_)>
										cardTypeID = <cfif len(local.cardTypeID)>#local.cardTypeID#<cfelse>null</cfif>, 
									</cfif>
									failedLastDate = null,
									failedSinceDate = null,
									nextAllowedAutoChargeDate = null,
									failedCount = null,
									checkedForZIP = 1,
									hasZIP = <cfif StructKeyExists(arguments,"fld_11_") and len(arguments.fld_11_)>1<cfelse>0</cfif>
									<cfif arguments.keyExists('nickname')>
										, nickname = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.nickname#">
									</cfif>
									<cfif isDefined("local.strBIN.success") and local.strBIN.success>
										, surchargeEligible = <cfif local.strBIN.strInfo.mc_surchargeeligible>1<cfelse>0</cfif>
									<cfelseif isDefined("local.strBIN.success")>
										, surchargeEligible = null
									</cfif>
								where payProfileID = @payProfileID;

								<cfif isDefined("local.strBIN.success") and local.strBIN.success>
									DECLARE @tokenExJSON varchar(max) = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.strBIN.rawjson#">,
										@binType varchar(30) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strBIN.strInfo.binData.type#">, 
										@binFundingSource varchar(30) = <cfif isDefined("local.strBIN.strInfo.binData.fundingSource")><cfqueryparam cfsqltype="cf_sql_varchar" value="#local.strBIN.strInfo.binData.fundingSource#"><cfelse>''</cfif>,
										@binPrepaid bit = <cfqueryparam cfsqltype="cf_sql_bit" value="#isDefined("local.strBIN.strInfo.binData.prepaid") and local.strBIN.strInfo.binData.prepaid eq 'true'#">;

									DELETE FROM dbo.ams_memberPaymentProfilesBinData
									WHERE payProfileID = @payProfileID;

									INSERT INTO dbo.ams_memberPaymentProfilesBinData (payProfileID, tokenExJSON, type, fundingSource, prepaid)
									VALUES (@payProfileID, @tokenExJSON, @binType, @binFundingSource, @binPrepaid);
								<cfelseif isDefined("local.strBIN.success")>
									DELETE FROM dbo.ams_memberPaymentProfilesBinData
									WHERE payProfileID = @payProfileID;
								</cfif>
							COMMIT TRAN;
						END
					
						SELECT @payProfileID AS payProfileID;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardUpdated';
								objRet.payprofileid = #val(local.qryUpdateCC.payProfileID)#;
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reassignPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="formpost" type="string" required="yes">
		<cfargument name="includeOpenInvoices" type="boolean" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @cofMemberID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_BIGINT">);

			select top 1 payProfileID, detail, nickname
			from dbo.ams_memberPaymentProfiles
			where memberID = @cofMemberID
			and profileID = <cfqueryparam value="#arguments.qryGateWayID.profileID#" cfsqltype="CF_SQL_INTEGER">
			and customerProfileID = <cfqueryparam value="#arguments.customerProfileId#" cfsqltype="CF_SQL_VARCHAR">
			and paymentProfileID = <cfqueryparam value="#arguments.customerPaymentProfileId#" cfsqltype="CF_SQL_VARCHAR">
			and [status] = 'A';
		</cfquery>

		<cfif local.qryLookup.recordcount is not 1>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'Unable to reassociate the items associated with this card.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
		<cfset local.qryProfilesOnFile = getProfilesOnFile(profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=1, statsSessionID=session.cfcuser.statsSessionID)>	

		<cfif not isDefined("arguments.newPayProfileID") or not listfind(valuelist(local.qryProfilesOnFile.payProfileID),arguments.newPayProfileID)>
			<!--- Get associations for this payProfileID --->
			<cfset local.qryAssociatedInvoicesThisCard = application.objPayments.getMemberPayProfileInvoices(memberID=arguments.pmid, includeOpenInvoices=arguments.includeOpenInvoices, limitToPayProfileID=local.qryLookup.payProfileID)>
			<cfset local.qryAssociatedSubscriptionTypesThisCard = application.objPayments.getMemberPayProfileSubscriptions(memberID=arguments.pmid, limitToPayProfileID=local.qryLookup.payProfileID)>
			<cfset local.qryAssociatedContributionsThisCard = application.objPayments.getMemberPayProfileContributions(memberID=arguments.pmid, limitToPayProfileID=local.qryLookup.payProfileID)>

			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					function cancelIt() {
						var objErr = new Object();
							objErr.a = 'cancel';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					}
				</script>
				<style type="text/css">
					.reviewmsg { background:##fff6bf; text-align:left; padding:5px 20px; border:2px solid ##fc6; }
					##divInputFormWrapper#arguments.qryGateWayID.profileID# div.cof_meta { font-style:italic; }
					##divInputFormWrapper#arguments.qryGateWayID.profileID# div.cof_invlist { padding-left:30px; padding-top:8px; }
				</style>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<div class="tsAppHeading">Choose Another Card for the Items Associated with <cfif len(local.qryLookup.nickname)>#local.qryLookup.nickname# &nbsp;(#local.qryLookup.detail#)<cfelse>#local.qryLookup.detail#</cfif></div>
				<br/>
				<div class="tsAppBodyText" style="margin-bottom:6px;"><strong>Choose another card from the list below to associate:</strong><br/></div>
				<cfform method="post" action="/?pg=buyNow&wizard=#arguments.formpost#" id="formAuthorizeNetPopup" name="formAuthorizeNetPopup">
					<select name="newPayProfileID" id="newPayProfileID" class="tsAppBodyText">
						<cfloop query="local.qryProfilesOnFile">
							<option value="#local.qryProfilesOnFile.payProfileID#" <cfif local.qryProfilesOnFile.payProfileID eq local.qryLookup.payProfileID>selected</cfif>><cfif len(local.qryProfilesOnFile.nickname)>#local.qryProfilesOnFile.nickname# &nbsp;(#local.qryProfilesOnFile.detail#)<cfelse>#local.qryProfilesOnFile.detail#</cfif></option>
						</cfloop>
					</select>
					<br/><br/>
					<button type="submit" class="tsAppBodyButton">Save</button> 
					<button type="button" class="tsAppBodyButton" onclick="cancelIt();">Cancel</button>
				</cfform>

				<div class="dim50 cof_invlist tsAppBodyText">
					<b>Items to be Re-associated</b>
					<cfif local.qryAssociatedSubscriptionTypesThisCard.recordcount>
						<div style="padding-bottom:10px;">
							<cfloop query="local.qryAssociatedSubscriptionTypesThisCard">
								<div>#local.qryAssociatedSubscriptionTypesThisCard.typename#: #local.qryAssociatedSubscriptionTypesThisCard.subscriptionName#</div>
							</cfloop>
						</div>
					</cfif>
					<cfif local.qryAssociatedContributionsThisCard.recordcount>
						<div style="padding-bottom:10px;">
							<cfloop query="local.qryAssociatedContributionsThisCard">
								<div>#local.qryAssociatedContributionsThisCard.programName#</div>
							</cfloop>
						</div>
					</cfif>
					<cfif local.qryAssociatedInvoicesThisCard.recordcount>
						<div style="padding-bottom:10px;">
						<cfloop query="local.qryAssociatedInvoicesThisCard">
							<cfif local.qryAssociatedInvoicesThisCard.dateDue lt now()>
								<div class="red">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber# &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
							<cfelse>
								<div>Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber# &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
							</cfif>
						</cfloop>
					</cfif>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cftry>
				<cfif arguments.newPayProfileID neq local.qryLookup.payProfileID>
					<cfset application.objPayments.reassignMemberPayProfileApplications(memberID=arguments.pmid, MPProfileID=arguments.qryGateWayID.profileID, payProfileID=local.qryLookup.payProfileID, newPayProfileID=arguments.newPayProfileID)>
				</cfif>	
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardReassigned';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				
			<cfcatch type="any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'Unable to reassociate items.';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>			
			</cfcatch>
			</cftry>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="profileID" type="numeric" required="true">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="cpid" type="numeric" required="yes">
		<cfargument name="cppid" type="numeric" required="yes">
		<cfargument name="overrideRemoveByMemberID" type="numeric" required="no" default="0">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false }>

		<cftry>
			<cfquery name="local.qryGateWayID" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
				SELECT mp.profileID, mp.gatewayUsername, mp.gatewayPassword
				FROM dbo.mp_profiles as mp
				INNER JOIN dbo.mp_gateways as mg ON mp.gatewayID = mg.gatewayID
				WHERE mg.isActive = 1
				AND mg.gatewayType = 'AuthorizeCCCIM'
				AND mp.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				AND mp.[status] IN ('A','I');
	
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select top 1 mpp.payProfileID, mpp.detail, mp.status as mpStatus
				from dbo.ams_memberPaymentProfiles as mpp
				inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
				where mpp.memberID = <cfqueryparam value="#arguments.pmid#" cfsqltype="CF_SQL_INTEGER">
				and mpp.profileID = <cfqueryparam value="#arguments.profileID#" cfsqltype="CF_SQL_INTEGER">
				and mpp.customerProfileID = <cfqueryparam value="#arguments.cpid#" cfsqltype="CF_SQL_VARCHAR">
				and mpp.paymentProfileID = <cfqueryparam value="#arguments.cppid#" cfsqltype="CF_SQL_VARCHAR">
				and mpp.[status] = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"deleteCustomerPaymentProfileRequest": {
						"merchantAuthentication": {
							"name": "#local.qryGateWayID.gatewayUsername#",
							"transactionKey": "#local.qryGateWayID.gatewayPassword#"
						},
						"customerProfileId": "#arguments.cpid#",
						"customerPaymentProfileId": "#arguments.cppid#"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>

			<cfset local.resultCode = StructCount(local.strAuthorize.strAPIResponse) ? local.strAuthorize.strAPIResponse.messages.resultCode : "Error">

			<cfif local.resultCode EQ 'Ok'
				OR (local.resultCode EQ 'Error' AND FindNoCase("The record cannot be found",local.strAuthorize.rawAPIResponse))
				OR (local.resultCode EQ 'Error' AND local.qryLookup.mpStatus eq "D" AND FindNoCase("The account or API user is inactive",local.strAuthorize.rawAPIResponse))
				OR (local.resultCode EQ 'Error' AND local.qryLookup.mpStatus eq "D" AND FindNoCase("invalid authentication values",local.strAuthorize.rawAPIResponse))>
				<!--- update card in case there is a delay in the refresh update --->
				<cfstoredproc procedure="ams_getCardOnFileForDelete" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGateWayID.profileID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.cppid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.cpid#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.payProfileIDToDelete">
				</cfstoredproc>
				<cfif local.payProfileIDToDelete gt 0>
					<cfstoredproc procedure="ams_deleteCardOnFile" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.payProfileIDToDelete#">							
						<cfif arguments.overrideRemoveByMemberID gt 0>
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.overrideRemoveByMemberID#">
						<cfelseif session.cfcuser.memberdata.memberid gt 0>
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
						<cfelse>
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">
						</cfif>
					</cfstoredproc>
				</cfif>

				<cfset local.returnStruct = { "success":true }>
			<cfelse>
				<cfthrow message="Unable to remove card">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct = { "success":false }>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="charge" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryInfoOnFile" type="query" required="no" hint="not required for applepay/googlepay">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="tokenData" type="struct" required="no" hint="required for applepay/googlepay">
		<cfargument name="x_items" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="x_surcharge" type="struct" required="no">
	
		<cfset var local = structNew()>
	
		<!--- sandbox only supports USD, so ensure we send USD when not in prod --->
		<cfif variables.x_testmode is 1>
			<cfset querySetCell(arguments.qryGatewayID, "currencyType", "USD", 1)>
		</cfif>

		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryInfoOnFile')>
		<cfset StructDelete(local.strArgsCopy,'tokenData')>
		<cfset StructDelete(local.strArgsCopy,'x_items')>
		<cfif arguments.keyExists("x_surcharge")>
			<cfset StructDelete(local.strArgsCopy,'x_surcharge')>
		</cfif>

		<!--- return back the amount since applepay/googlepay need it anyway --->
		<cfset arguments.returnStruct.x_amount = arguments.x_amount>

		<cfset local.clientIPAddress = application.objPlatform.getClientIP() />
		<cfset local.clientIsPrivateIPAddress = application.objPlatform.isPrivateIPAddress(local.clientIPAddress)/>

		<cftry>
			<cfif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "applePay">
				<!--- Convert date from Apple's YYMMDD to YYYY-MM --->
				<cfset local.expDate = "20#left(arguments.tokenData.decryptedToken.applicationExpirationDate,2)#-#mid(arguments.tokenData.decryptedToken.applicationExpirationDate, "3", "2")#">

				<cfset local.cofPayProfileID = 0>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.qryGateWayID.gatewayUsername#",
								"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
							},
							"refId": "-MCREFID-",
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.x_amount#",
								"currencyCode": "#arguments.qryGateWayID.currencyType#",
								"payment": {
									"creditCard": {
										"cardNumber": "#arguments.tokenData.decryptedToken.applicationPrimaryAccountNumber#",
										"expirationDate": "#local.expDate#",
										"isPaymentToken": true,
										"cryptogram": "#arguments.tokenData.decryptedToken.paymentdata.onlinePaymentCryptogram#"
									}
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.x_description)#"
								},
								<cfif arrayLen(arguments.x_items)>
									"lineItems": {
										"lineItem": [
										<cfloop array="#arguments.x_items#" index="local.thisIdx" item="local.thisItem">
											{
												"itemId": "#local.thisItem.itemID#",
												"name": "#local.thisItem.name#",
												"description": "#local.thisItem.description#",
												"quantity": "#local.thisItem.quantity#",
												"unitPrice": "#local.thisItem.unitPrice#"
											}<cfif local.thisIdx lt arrayLen(arguments.x_items)>,</cfif>
										</cfloop>
										]
									},
								</cfif>
								"tax": {
									"amount": 0
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"retail": {
									"marketType":0
								},
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>
			
				<cfset local.arrKeysToMask = [ 
					{ key='createTransactionRequest.transactionRequest.payment.creditCard.cardNumber', replacement='REDACTED' },
					{ key='createTransactionRequest.transactionRequest.payment.creditCard.cryptogram', replacement='REDACTED' }
				]>

			<cfelseif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "googlePay">
				<cfset local.cofPayProfileID = 0>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.qryGateWayID.gatewayUsername#",
								"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
							},
							"refId": "-MCREFID-",
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.x_amount#",
								"currencyCode": "#arguments.qryGateWayID.currencyType#",
								"payment": {
									"opaqueData": {
										"dataDescriptor": "COMMON.GOOGLE.INAPP.PAYMENT",
										"dataValue": "#toBase64(arguments.tokenData.paymentData.paymentMethodData.tokenizationData.token)#"
									}
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.x_description)#"
								},
								<cfif arrayLen(arguments.x_items)>
									"lineItems": {
										"lineItem": [
										<cfloop array="#arguments.x_items#" index="local.thisIdx" item="local.thisItem">
											{
												"itemId": "#local.thisItem.itemID#",
												"name": "#local.thisItem.name#",
												"description": "#local.thisItem.description#",
												"quantity": "#local.thisItem.quantity#",
												"unitPrice": "#local.thisItem.unitPrice#"
											}<cfif local.thisIdx lt arrayLen(arguments.x_items)>,</cfif>
										</cfloop>
										]
									},
								</cfif>
								"tax": {
									"amount": 0
								},
								"billTo": {
									"zip": "#arguments.tokenData.paymentData.paymentMethodData.info.billingAddress.postalCode#",
									"country": "#arguments.tokenData.paymentData.paymentMethodData.info.billingAddress.countryCode#"
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"retail": {
									"marketType": 0
								},
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

				<cfset local.arrKeysToMask = [ 
					{ key='createTransactionRequest.transactionRequest.payment.opaqueData.dataValue', replacement='REDACTED' }
				]>

			<cfelse>

				<cfset local.cofPayProfileID = arguments.qryInfoOnFile.payProfileID>

				<cfsavecontent variable="local.apiRequestBody">
					<cfoutput>{
						"createTransactionRequest": {
							"merchantAuthentication": {
								"name": "#arguments.qryGateWayID.gatewayUsername#",
								"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
							},
							"refId": "-MCREFID-",
							"transactionRequest": {
								"transactionType": "authCaptureTransaction",
								"amount": "#arguments.x_amount#",
								"currencyCode": "#arguments.qryGateWayID.currencyType#",
								"profile": {
									"customerProfileId": "#arguments.qryInfoOnFile.customerProfileId#",
									"paymentProfile": { "paymentProfileId": "#arguments.qryInfoOnFile.paymentProfileId#" }
								},
								"solution": {
									"id": "#variables.x_solution_id#"
								},
								"order": {
									"description": "#encodeForHTML(arguments.x_description)#"
								},
								<cfif arrayLen(arguments.x_items)>
									"lineItems": {
										"lineItem": [
										<cfloop array="#arguments.x_items#" index="local.thisIdx" item="local.thisItem">
											{
												"itemId": "#local.thisItem.itemID#",
												"name": "#local.thisItem.name#",
												"description": "#local.thisItem.description#",
												"quantity": "#local.thisItem.quantity#",
												"unitPrice": "#local.thisItem.unitPrice#"
											}<cfif local.thisIdx lt arrayLen(arguments.x_items)>,</cfif>
										</cfloop>
										]
									},
								</cfif>
								"tax": {
									"amount": 0
								},
								<cfif not local.clientIsPrivateIPAddress>
									"customerIP": "#local.clientIPAddress#",
								</cfif>
								"transactionSettings": {
									"setting": {
										"settingName": "recurringBilling",
										"settingValue": "false"
									}
								}
								<cfif arguments.keyExists("x_surcharge")>
									, "surcharge": {
										"amount": "#arguments.x_surcharge.amount#",
										"description": "#encodeForHTML(arguments.x_surcharge.description)#"
									}
								</cfif>
							}
						}
					}</cfoutput>
				</cfsavecontent>
				<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

				<cfset local.arrKeysToMask = []>
			</cfif>

			<cfscript>				
				local.xmlPaymentInfo = '<payment gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
				for (local.fld in local.strArgsCopy) {
					local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
				}
				
				local.apiRequestBodyForHistory = deserializeJSON(local.apiRequestBody);

				// gateway transactionkey in createTransactionRequest commands
				if (isDefined("local.apiRequestBodyForHistory.createTransactionRequest.merchantAuthentication.transactionKey"))
					local.apiRequestBodyForHistory.createTransactionRequest.merchantAuthentication.transactionKey = 'REDACTED';

				// Apple Pay virtual card number
				if (isDefined("local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.creditCard.cardNumber"))
					local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.creditCard.cardNumber = 'REDACTED';

				// single-use applepay cryptogram
				if (isDefined("local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.creditCard.cryptogram"))
					local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.creditCard.cryptogram = 'REDACTED';

				// GooglePay opaquedata value
				if (isDefined("local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.opaqueData.dataValue"))
					local.apiRequestBodyForHistory.createTransactionRequest.transactionRequest.payment.opaqueData.dataValue = 'REDACTED';

				local.apiRequestBodyForHistory = serializeJSON(local.apiRequestBodyForHistory);
				local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBodyForHistory# ]]></gateway></payment>';
				
				local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
					memberPaymentProfileID=local.cofPayProfileID, paymentInfo=local.xmlPaymentInfo,
					gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='payment');

				local.apiRequestBody = local.apiRequestBody.replace('-MCREFID-',local.returnHistory.refId);
			</cfscript>	
	
			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody, arrKeysToMask=local.arrKeysToMask)>
			<cfset local.apiResponse = parseAuthorizeResponse(strAuthorizeResponse=local.strAuthorize, mode="charge")>
			
			<cfset arguments.returnStruct.rawResponse = local.strAuthorize.rawAPIResponse>
			<cfset arguments.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset arguments.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset arguments.returnStruct.publicResponseReasonText = local.apiResponse.publicResponseReasonText>
			<cfset arguments.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset arguments.returnStruct.transactionid = local.apiResponse.transactionid>
			<cfset arguments.returnStruct.approvalCode = local.apiResponse.authorizationCode>
			<cfif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "applePay">
				<cfset arguments.returnStruct.transactionDetail = "Payment by Apple Pay #arguments.tokenData.encryptedToken.paymentMethod.displayname#">
			<cfelseif isDefined("arguments.tokenData.mctokensource") and arguments.tokenData.mctokensource eq "googlePay">
				<cfset arguments.returnStruct.transactionDetail = "Payment by Google Pay #arguments.tokenData.paymentData.paymentMethodData.description#">
			<cfelse>
				<cfset arguments.returnStruct.transactionDetail = "Payment by card #local.apiResponse.accountNumber#">
			</cfif>
			<cfset local.cofType = getCOFType(cardType=local.apiResponse.cardType)>
			<cfset arguments.returnStruct.GLAccountID = application.objPayments.getCardTypeGLAccountFromCardType(profileID=arguments.qryGateWayID.profileid, cardType=local.cofType)>
			<cfif val(arguments.returnStruct.GLAccountID) is 0>
				<cfset arguments.returnStruct.GLAccountID = arguments.qryGateWayID.GLAccountID>
			</cfif>
	
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset arguments.returnStruct.responseReasonText = cfcatch.message>
			<cfset arguments.returnStruct.publicResponseReasonText = "Payment Failed">
			<cfreturn arguments.returnStruct>
		</cfcatch>
		</cftry>
	
		<cfscript>
		// record history
		local.xmlResponseInfo = '<response>';
		for (local.fld in arguments.returnStruct) {
			if (local.fld neq "rawResponse")
				local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
			else
				local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.returnStruct.rawresponse# ]]></rawresponse>";
		}
		local.xmlResponseInfo = local.xmlResponseInfo & '</response>';

		arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
			gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);
		</cfscript>

		<!--- update card on file then consolidate the cards --->
		<cfif NOT isDefined("arguments.tokenData.mctokensource") and arguments.returnStruct.responseCode is 1>
			<cftry>
				<cfquery name="local.qryUpdateAndCheckCards" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgID int, @payProfileID int;
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">;
					SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryInfoOnFile.payProfileID#">;

					<cfif len(local.apiResponse.accountNumber) and local.apiResponse.accountNumber neq arguments.qryInfoOnFile.detail>
						UPDATE dbo.ams_memberPaymentProfiles
						SET detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.apiResponse.accountNumber#">
						WHERE payProfileID = @payProfileID;
					</cfif>

					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT COUNT(otherMPP.payProfileID) AS noOfCards
					FROM dbo.ams_memberPaymentProfiles as COFToCheck
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID 
						AND m.memberID = COFToCheck.memberID
					INNER JOIN dbo.ams_memberPaymentProfiles AS otherMPP ON otherMPP.profileID = COFToCheck.profileID
						AND otherMPP.cardTypeID = COFToCheck.cardTypeID
						AND otherMPP.detail = COFToCheck.detail
						AND otherMPP.[status] = 'A'
						AND otherMPP.payProfileID <> COFToCheck.payProfileID
					INNER JOIN dbo.ams_members AS otherM ON otherM.orgID = @orgID 
						AND otherM.memberID = otherMPP.memberID
						AND otherM.activeMemberID = m.activeMemberID
					WHERE COFToCheck.payProfileID = @payProfileID
					AND COFToCheck.[status] = 'A'
					AND COFToCheck.cardTypeID IS NOT NULL;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfif val(local.qryUpdateAndCheckCards.noOfCards)>
					<cfset consolidateMemberPayProfile(orgID=arguments.qryGateWayID.orgID, siteID=arguments.qryGateWayID.siteID, profileID=arguments.qryGateWayID.profileid, payProfileID=arguments.qryInfoOnFile.payProfileID)>
				</cfif>
			<cfcatch type="Any">
			</cfcatch>
			</cftry>
		</cfif>
			
		<cfreturn arguments.returnStruct>
	</cffunction>

	<cffunction name="credit" access="package" returntype="array" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="arrReturnStructs" type="array" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
		
		<!--- grab empty returnstruct for later --->
		<cfset local.returnstruct = duplicate(arguments.arrReturnStructs[1])>
	
		<!--- 5 testing scenarios:
			1. void success, payment for difference success
			2. void success, payment for difference failure
			3. void success, no payment needed
			4. void failure, refund success
			5. void failure, refund failure
		--->
	
		<!--- sandbox only supports USD, so ensure we send USD when not in prod --->
		<cfif variables.x_testmode is 1>
			<cfset querySetCell(arguments.qryGatewayID, "currencyType", "USD", 1)>
		</cfif>

		<cfscript>
		// copy args for xml history
		local.strArgsCopy = duplicate(arguments);
		StructDelete(local.strArgsCopy,'qryGateWayID');
		StructDelete(local.strArgsCopy,'arrReturnStructs');
		StructDelete(local.strArgsCopy,'qryGatewayProfileFields');
		StructDelete(local.strArgsCopy,'qryPaymentHistory');
	
		// get info about original payment
		local.rawxmlPaymentInfo = XMLParse(arguments.qryPaymentHistory.paymentInfo);
		local.rawxmlGatewayResponse = XMLParse(arguments.qryPaymentHistory.gatewayResponse);
		local.apiRequestBody = XMLSearch(local.rawxmlPaymentInfo,"/payment/gateway")[1];
		local.apiRequestResponse = XMLSearch(local.rawxmlGatewayResponse,"/response/rawresponse")[1];

		if (arrayLen(local.apiRequestBody.XmlChildren)) {
			local.origPaymentAmount = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway//amount/text())");
			local.origPaymentDescription = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway//order/description/text())");
		} else {
			local.origPaymentAmount = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/args/x_amount/text())");
			local.origPaymentDescription = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/args/x_description/text())");
		}
		
		local.origPaymentGLAccountID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/glaccountid/text())");
		local.origPaymentTransactionID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/transactionid/text())");
		local.transactionDetail = xmlSearch(local.rawxmlGatewayResponse,"string(/response/transactiondetail/text())");
		local.origPaymentMemberPaymentProfileID = val(arguments.qryPaymentHistory.memberPaymentProfileID);

		var strPaymentMethods = {
			"CC": { "name": "Credit Card" },
			"ApplePay": { "name": "Apple Pay" },
			"GooglePay": { "name": "Google Pay" }
		};
		
		local.paymentMethod = 'CC';
		if (findNoCase("Apple Pay",local.transactionDetail))
			local.paymentMethod = "ApplePay";
		else if (findNoCase("Google Pay",local.transactionDetail))
			local.paymentMethod = "GooglePay";

		local.strTransactionInfo = getTransaction(qryGateWayID=arguments.qryGateWayID, x_transid=local.origPaymentTransactionID);
		if (local.strTransactionInfo.success) {
			local.transaction = local.strTransactionInfo.data.transaction;
			if (local.paymentMethod EQ 'ApplePay') {
				local.maskedCardNumber = local.transaction.payment.tokenInformation.tokenNumber;
				if (arrayLen(local.apiRequestResponse.XmlChildren)) {
					local.cardType = xmlSearch(local.rawxmlGatewayResponse,"string(/response/rawresponse/createTransactionResponse/transactionResponse/accountType/text())");
				} else {
					local.jsonResponse = deserializeJSON(local.apiRequestResponse.XmlText);
					local.cardType = local.jsonResponse.transactionResponse.accountType;
				}
			} else {
				local.maskedCardNumber = local.transaction.payment.creditCard.cardNumber;
				local.cardType = local.transaction.payment.creditCard.cardType;
			}

			if (listFindNoCase('ApplePay,GooglePay',local.paymentMethod)) {
				local.paymentMethodDesc = "#strPaymentMethods[local.paymentMethod].name# #local.cardType# #local.maskedCardNumber#";
			} else {
				local.paymentMethodDesc = "#lCase(strPaymentMethods[local.paymentMethod].name)# #local.maskedCardNumber#";
			}

			if (local.paymentMethod EQ 'CC') {
				if (local.transaction.keyExists("profile")) {
					local.origPaymentCustomerProfileID = local.transaction.profile.customerProfileId;
					local.origPaymentCustomerPaymentProfileID = local.transaction.profile.customerPaymentProfileId;
				} else {
					local.jsonResponse = deserializeJSON(local.apiRequestResponse.XmlText);
					local.origPaymentCustomerProfileID = local.jsonResponse.transactionResponse.profile.customerProfileId;
					local.origPaymentCustomerPaymentProfileID = local.jsonResponse.transactionResponse.profile.customerPaymentProfileId;					
				}
			}

		} else {
			var errMsg = "";
			local.strTransactionInfo.errors.each(function(thisErr) {
				errMsg = "#errMsg#[#arguments.thisErr.text#]";
			});
			throw(message="Error retrieving transaction details. #errMsg#");
		}
		</cfscript>
	
		<!--- if this is a partial refund, need to check for a valid payment profile used for the repayment --->
		<cfif local.paymentMethod EQ 'CC' AND local.origPaymentAmount-arguments.x_amount gt 0>
			<cfquery name="local.qryCheckProfile" datasource="#application.dsn.membercentral.dsn#">
				select top 1 *
				from dbo.ams_memberPaymentProfiles
				where customerProfileID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.origPaymentCustomerProfileID#">
				and paymentProfileID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.origPaymentCustomerPaymentProfileID#">
				and profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryPaymentHistory.profileID#">
				and status = 'A'
			</cfquery>
		</cfif>

		<cfset local.voidAndRefund = false>
		<!--- if (partial refund and profile exists) OR full refund, try void first --->
		<cfif local.paymentMethod EQ 'CC' AND ((local.origPaymentAmount-arguments.x_amount gt 0 AND local.qryCheckProfile.recordcount is 1) OR local.origPaymentAmount-arguments.x_amount lte 0)>
			<cfset local.voidAndRefund = true>
		<!--- if full refund, try void first --->
		<cfelseif listFindNoCase('ApplePay,GooglePay',local.paymentMethod) AND local.origPaymentAmount-arguments.x_amount lte 0>
			<cfset local.voidAndRefund = true>
		</cfif>
		
		<cfif local.voidAndRefund>
			<cfscript>
			local.voidReturnStruct = { 	responseCode=99999, responseReasonText="Invalid request", publicResponseReasonText="Invalid request", responseReasonCode="", 
				rawResponse="", historyID=0, transactionid="", approvalCode="" };
		
			local.voidReturnStruct = void(qryGateWayID=arguments.qryGateWayID, returnStruct=local.voidReturnStruct, 
								qryGatewayProfileFields=arguments.qryGatewayProfileFields, qryPaymentHistory=arguments.qryPaymentHistory,
								x_amount=local.origPaymentAmount, x_description=local.origPaymentDescription);
			arguments.arrReturnStructs[1].rawResponse = local.voidReturnStruct.rawResponse;
			arguments.arrReturnStructs[1].responseCode = local.voidReturnStruct.responseCode;
			arguments.arrReturnStructs[1].responseReasonText = local.voidReturnStruct.responseReasonText;
			arguments.arrReturnStructs[1].publicResponseReasonText = local.voidReturnStruct.publicResponseReasonText;
			arguments.arrReturnStructs[1].responseReasonCode = local.voidReturnStruct.responseReasonCode;
			arguments.arrReturnStructs[1].transactionid = local.voidReturnStruct.transactionid;
			arguments.arrReturnStructs[1].approvalCode = local.voidReturnStruct.approvalCode;
			arguments.arrReturnStructs[1].refundType = "void";
			arguments.arrReturnStructs[1].status = "Active";
			arguments.arrReturnStructs[1].refundAmt = local.origPaymentAmount;
			arguments.arrReturnStructs[1].GLAccountID = local.origPaymentGLAccountID;
			arguments.arrReturnStructs[1].historyID = local.voidReturnStruct.historyID;
			</cfscript>
			
			<!--- if the void was a success, we need to charge the difference between original amount and refund amount (partial refund) --->
			<cfif arguments.arrReturnStructs[1].responseCode is 1 and (local.origPaymentAmount-arguments.x_amount) gt 0>
				<cfscript>
				local.chargeReturnStruct = { responseCode=99999, responseReasonText="Invalid request", publicResponseReasonText="Invalid request", 
					responseReasonCode="", rawResponse="", historyID=0, transactionDetail="", GLAccountID=0, transactionid="", approvalCode="", status="Active" };
		
				local.qryInfoOnFile = queryNew("payProfileID,customerProfileID,paymentProfileID","integer,varchar,varchar");
				if (queryAddRow(local.qryInfoOnFile)) {
					QuerySetCell(local.qryInfoOnFile,"payProfileID",local.origPaymentMemberPaymentProfileID);
					QuerySetCell(local.qryInfoOnFile,"customerProfileID",local.origPaymentCustomerProfileID);
					QuerySetCell(local.qryInfoOnFile,"paymentProfileID",local.origPaymentCustomerPaymentProfileID);
				}
		
				local.chargeReturnStruct = charge(qryGateWayID=arguments.qryGateWayID, returnStruct=local.chargeReturnStruct, 
					qryGatewayProfileFields=arguments.qryGatewayProfileFields, qryInfoOnFile=local.qryInfoOnFile,
					assignedToMemberID=arguments.assignedToMemberID, x_amount=local.origPaymentAmount-arguments.x_amount, 
					x_description=local.origPaymentDescription);
				arguments.arrReturnStructs[2] = duplicate(local.returnstruct);
				arguments.arrReturnStructs[2].rawResponse = local.chargeReturnStruct.rawResponse;
				arguments.arrReturnStructs[2].responseCode = local.chargeReturnStruct.responseCode;
				arguments.arrReturnStructs[2].responseReasonText = local.chargeReturnStruct.responseReasonText;
				arguments.arrReturnStructs[2].publicResponseReasonText = local.chargeReturnStruct.publicResponseReasonText;
				arguments.arrReturnStructs[2].responseReasonCode = local.chargeReturnStruct.responseReasonCode;
				arguments.arrReturnStructs[2].transactionid = local.chargeReturnStruct.transactionid;
				arguments.arrReturnStructs[2].approvalCode = local.chargeReturnStruct.approvalCode;
				arguments.arrReturnStructs[2].transactionDetail = local.chargeReturnStruct.transactionDetail;
				arguments.arrReturnStructs[2].status = local.chargeReturnStruct.status;
				arguments.arrReturnStructs[2].refundType = "payment";
				arguments.arrReturnStructs[2].refundAmt = local.origPaymentAmount-arguments.x_amount;
				arguments.arrReturnStructs[2].GLAccountID = local.chargeReturnStruct.GLAccountID;
				arguments.arrReturnStructs[2].historyID = local.chargeReturnStruct.historyID;
				</cfscript>		
			
			<!--- else if the void was not a success, try to refund --->
			<cfelseif arguments.arrReturnStructs[1].responseCode is not 1>
			
				<cfscript>
				savecontent variable="local.apiRequestBody" {
					writeOutput('{
						"createTransactionRequest": { 
							"merchantAuthentication": {
								"name": "#arguments.qryGateWayID.gatewayUsername#",
								"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
							},
							"refId": "#arguments.qryPaymentHistory.refId#",
							"transactionRequest": {
								"transactionType": "refundTransaction",
								"amount": "#arguments.x_amount#",
								"currencyCode": "#arguments.qryGateWayID.currencyType#",
								"payment": {
									"creditCard": {
										"cardNumber": "#right(local.maskedCardNumber,4)#",
										"expirationDate": "XXXX"
									}
								},
								"refTransId": "#local.origPaymentTransactionID#"
							}
						}
					}');
				}
				local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all");
				
				local.xmlPaymentInfo = '<refund gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
				for (local.fld in local.strArgsCopy) {
					local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
				}
				local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBody# ]]></gateway></refund>';
				
				local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
					memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo,
					gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='refund');

				local.strRefund = callAuthorize(apiRequestBody=local.apiRequestBody);
				local.apiResponse = parseAuthorizeResponse(strAuthorizeResponse=local.strRefund, mode="refund");

				arguments.arrReturnStructs[2] = duplicate(local.returnstruct);
				arguments.arrReturnStructs[2].rawResponse = local.strRefund.rawAPIResponse;
				arguments.arrReturnStructs[2].responseCode = local.apiResponse.responseCode;
				arguments.arrReturnStructs[2].responseReasonText = local.apiResponse.responseReasonText;
				arguments.arrReturnStructs[2].publicResponseReasonText = local.apiResponse.publicResponseReasonText;
				arguments.arrReturnStructs[2].responseReasonCode = local.apiResponse.responseReasonCode;
				arguments.arrReturnStructs[2].transactionid = local.apiResponse.transactionid;
				arguments.arrReturnStructs[2].approvalCode = local.apiResponse.approvalCode;
				arguments.arrReturnStructs[2].transactionDetail = "Refund by #local.paymentMethodDesc#";
				arguments.arrReturnStructs[2].status = "Active";
				arguments.arrReturnStructs[2].refundType = "refund";
				arguments.arrReturnStructs[2].refundAmt = arguments.x_amount;
				local.cofType = getCOFType(cardType=local.cardType);
				arguments.arrReturnStructs[2].GLAccountID = application.objPayments.getCardTypeGLAccountFromCardType(profileID=arguments.qryGateWayID.profileid, cardType=local.cofType);
				if (val(arguments.arrReturnStructs[2].GLAccountID) is 0)
					arguments.arrReturnStructs[2].GLAccountID = arguments.qryGateWayID.GLAccountID;
				
				// record history
				local.xmlResponseInfo = '<response>';
				for (local.fld in arguments.arrReturnStructs[2]) {
					if (local.fld neq "rawResponse")
						local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.arrReturnStructs[2][local.fld])#</#lcase(local.fld)#>";
					else
						local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.arrReturnStructs[2].rawResponse# ]]></rawresponse>";
				}		
				local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
				
				arguments.arrReturnStructs[2].historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
					gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.arrReturnStructs[2].responseReasonCode);
				</cfscript>

			</cfif>
		
		<cfelse>
		
			<cfscript>
			savecontent variable="local.apiRequestBody" {
				writeOutput('{
					"createTransactionRequest": { 
						"merchantAuthentication": {
							"name": "#arguments.qryGateWayID.gatewayUsername#",
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
						},
						"refId": "#arguments.qryPaymentHistory.refId#",
						"transactionRequest": {
							"transactionType": "refundTransaction",
							"amount": "#arguments.x_amount#",
							"currencyCode": "#arguments.qryGateWayID.currencyType#",
							"payment": {
								"creditCard": {
									"cardNumber": "#right(local.maskedCardNumber,4)#",
									"expirationDate": "XXXX"
								}
							},
							"refTransId": "#local.origPaymentTransactionID#"
						}
					}
				}');
			}
			local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all");

			local.xmlPaymentInfo = '<refund gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
			for (local.fld in local.strArgsCopy) {
				local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
			}
			local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBody# ]]></gateway></refund>';
				
			local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
				memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo,
				gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='refund');

			local.strRefund = callAuthorize(apiRequestBody=local.apiRequestBody);
			local.apiResponse = parseAuthorizeResponse(strAuthorizeResponse=local.strRefund, mode="refund");

			arguments.arrReturnStructs[1] = duplicate(local.returnstruct);
			arguments.arrReturnStructs[1].rawResponse = local.strRefund.rawAPIResponse;
			arguments.arrReturnStructs[1].responseCode = local.apiResponse.responseCode;
			arguments.arrReturnStructs[1].responseReasonText = local.apiResponse.responseReasonText;
			arguments.arrReturnStructs[1].publicResponseReasonText = local.apiResponse.publicResponseReasonText;
			arguments.arrReturnStructs[1].responseReasonCode = local.apiResponse.responseReasonCode;
			arguments.arrReturnStructs[1].transactionid = local.apiResponse.transactionid;
			arguments.arrReturnStructs[1].approvalCode = local.apiResponse.approvalCode;
			arguments.arrReturnStructs[1].transactionDetail = "Refund by #local.paymentMethodDesc#";
			arguments.arrReturnStructs[1].status = "Active";
			arguments.arrReturnStructs[1].refundType = "refund";
			arguments.arrReturnStructs[1].refundAmt = arguments.x_amount;
			local.cofType = getCOFType(cardType=local.cardType);
			arguments.arrReturnStructs[1].GLAccountID = application.objPayments.getCardTypeGLAccountFromCardType(profileID=arguments.qryGateWayID.profileid, cardType=local.cofType);
			if (val(arguments.arrReturnStructs[1].GLAccountID) is 0)
				arguments.arrReturnStructs[1].GLAccountID = arguments.qryGateWayID.GLAccountID;

			// record history
			local.xmlResponseInfo = '<response>';
			for (local.fld in arguments.arrReturnStructs[1]) {
				if (local.fld neq "rawResponse")
					local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.arrReturnStructs[1][local.fld])#</#lcase(local.fld)#>";
				else
					local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.arrReturnStructs[1].rawResponse# ]]></rawresponse>";
			}		
			local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
			
			arguments.arrReturnStructs[1].historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
				gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.arrReturnStructs[1].responseReasonCode);
			</cfscript>

		</cfif>
	
		<cfreturn arguments.arrReturnStructs>
	</cffunction>

	<cffunction name="void" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
	
		<cfset var local = structNew()>
	
		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryPaymentHistory')>
	
		<cfscript>
		// get info about original payment
		local.rawxmlPaymentInfo = XMLParse(arguments.qryPaymentHistory.paymentInfo);
		local.rawxmlGatewayResponse = XMLParse(arguments.qryPaymentHistory.gatewayResponse);
		local.origPaymentTransactionID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/transactionid/text())");
		local.origPaymentMemberPaymentProfileID = val(arguments.qryPaymentHistory.memberPaymentProfileID);

		savecontent variable="local.apiRequestBody" {
			writeOutput('{
				"createTransactionRequest": { 
					"merchantAuthentication": {
						"name": "#arguments.qryGateWayID.gatewayUsername#",
						"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
					},
					"refId": "#arguments.qryPaymentHistory.refId#",
					"transactionRequest": {
						"transactionType": "voidTransaction",
						"refTransId": "#local.origPaymentTransactionID#"
					}
				}
			}');
		}
		local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all");
					
		local.xmlPaymentInfo = '<void gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
		for (local.fld in local.strArgsCopy)
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway><![CDATA[ #local.apiRequestBody# ]]></gateway></void>';
		
		local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.qryPaymentHistory.assignedToMemberID, 
			memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo, 
			gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='void');
		</cfscript>

		<cftry>
			<cfscript>
			// handle response
			local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody);
			local.apiResponse = parseAuthorizeResponse(strAuthorizeResponse=local.strAuthorize, mode="void");
			
			arguments.returnStruct.rawResponse = local.strAuthorize.rawAPIResponse;
			arguments.returnStruct.responseCode = local.apiResponse.responseCode;
			arguments.returnStruct.responseReasonText = local.apiResponse.responseReasonText;
			arguments.returnStruct.publicResponseReasonText = local.apiResponse.publicResponseReasonText;
			arguments.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode;
			arguments.returnStruct.transactionid = local.apiResponse.transactionid;
			arguments.returnStruct.approvalCode = local.apiResponse.approvalCode;

			// record history
			local.xmlResponseInfo = '<response>';
			for (local.fld in arguments.returnStruct) {
				if (local.fld neq "rawResponse")
					local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
				else
					local.xmlResponseInfo = local.xmlResponseInfo & "<rawresponse><![CDATA[ #arguments.returnStruct.rawresponse# ]]></rawresponse>";
			}		
			local.xmlResponseInfo = local.xmlResponseInfo & '</response>';

			arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
				gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);
			</cfscript>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset arguments.returnStruct.responseReasonText = cfcatch.message>
			<cfset arguments.returnStruct.publicResponseReasonText = "Refund Failed">
			<cfreturn arguments.returnStruct>
		</cfcatch>
		</cftry>
	
		<cfreturn arguments.returnStruct>
	</cffunction>

	<cffunction name="history" access="package" returntype="struct" output="no">
		<cfargument name="qryHistory" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="yes">
		<cfargument name="typeID" type="numeric" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStr = { "strHistory"=structNew(), "htmlHistory"='' }>

		<cfset local.rawxmlPaymentInfo = XMLParse(arguments.qryHistory.paymentInfo)>
		<cfset local.apiRequestBody = XMLSearch(local.rawxmlPaymentInfo,"//gateway")[1]>

		<cfif arguments.typeID is not 2>
			<cfset local.returnStr.strHistory['Payment Transaction ID'] = "">
			<cfset local.returnStr.strHistory['Authorize Transaction Type'] = "">
			<cfif arrayLen(local.apiRequestBody.XmlChildren)>
				<cfset local.returnStr.strHistory['Payment Transaction ID'] = XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/x_trans_id/text())")>
				<cfset local.returnStr.strHistory['Authorize Transaction Type'] = XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/x_type/text())")>
			<cfelse>
				<cfset local.jsonRequest = deserializeJSON(local.apiRequestBody.XmlText)>
				<cfif isDefined("local.jsonRequest.createTransactionRequest.transactionRequest.refTransId")>
					<cfset local.returnStr.strHistory['Payment Transaction ID'] = local.jsonRequest.createTransactionRequest.transactionRequest.refTransId>
				</cfif>
				<cfif isDefined("local.jsonRequest.createTransactionRequest.transactionRequest.transactionType")>
					<cfset local.returnStr.strHistory['Authorize Transaction Type'] = local.jsonRequest.createTransactionRequest.transactionRequest.transactionType>
				</cfif>
			</cfif>
		</cfif>

		<cfset StructInsert(local.returnStr.strHistory,"Transaction ID",arguments.qryHistory.gatewayTransactionid,true)>
		<cfset StructInsert(local.returnStr.strHistory,"Approval Code",arguments.qryHistory.gatewayApprovalCode,true)>

		<cfsavecontent variable="local.returnStr.htmlHistory">
			<cfif arguments.typeID is 2>
				<cfoutput>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Transaction ID:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Transaction ID']#</td>
				</tr>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Approval Code:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Approval Code']#</td>
				</tr>
				</cfoutput>
			<cfelse>
				<cfoutput>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Payment Transaction ID:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Payment Transaction ID']#</td>
				</tr>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Authorize Transaction Type:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Authorize Transaction Type']#</td>
				</tr>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Transaction ID:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Transaction ID']#</td>
				</tr>
				<tr class="tsAppBodyText" valign="top">
					<td><b>Approval Code:</b> &nbsp;</td>
					<td>#local.returnStr.strHistory['Approval Code']#</td>
				</tr>
				</cfoutput>			
			</cfif>
		</cfsavecontent>
	
		<cfreturn local.returnStr>
	</cffunction>

	<cffunction name="callAuthorize" access="private" returntype="struct" output="no">
		<cfargument name="apiRequestBody" type="string" required="yes">
		<cfargument name="arrKeysToMask" type="array" required="no" default="#arrayNew(1)#">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = { "success":false, "arrErrors":[], "rawAPIResponse":"", "strAPIResponse":{} }>

		<cfif application.objCommon.getCurrentRequestTimeout() lt variables.requesttimeout>
			<cfsetting requesttimeout="#variables.requesttimeout#">
		</cfif>

		<cfif variables.x_testmode is 1>
			<cfset local.AuthURL = "https://apitest.authorize.net/xml/v1/request.api">
		<cfelse>
			<cfset local.AuthURL = "https://api.authorize.net/xml/v1/request.api">
		</cfif>

		<cfset local.requestTimeout = (variables.requesttimeout-2)>
	
		<cftry>
			<cfhttp url="#local.AuthURL#" method="post" throwonerror="yes" result="local.APIResult" timeout="#local.requestTimeout#" charset="utf-8">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="body" value="#arguments.apiRequestBody#">
			</cfhttp>

			<cfset local.apiFileContent = local.APIResult.fileContent>
			<cfset local.apiFileContentPOS = find('{',local.apiFileContent)>
			<cfif local.apiFileContentPOS gt 0>
				<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>
			</cfif>
			<cfset local.returnStruct.rawAPIResponse = local.apiFileContent>
			<cfset local.returnStruct.strAPIResponse = deserializeJSON(local.returnStruct.rawAPIResponse)>

			<cfset local.strLog = { 
				request = { 
					method="POST", 
					endpoint=local.AuthURL,
					bodycontent=maskRequestBody(apiRequestBody=arguments.apiRequestBody, arrKeysToMask=arguments.arrKeysToMask)
				}, response = {
					bodycontent=local.returnStruct.strAPIResponse,
					headers=local.APIResult.responseheader,
					statuscode=local.APIResult.status_code
				}}>
			<cfset logAPICall(strCall=local.strLog)>

			<cfif local.returnStruct.strAPIResponse.messages.resultCode EQ 'Error' AND isDefined("local.returnStruct.strAPIResponse.messages.message")>
				<cfloop array="#local.returnStruct.strAPIResponse.messages.message#" index="local.thisErr">
					<cfset local.returnStruct.arrErrors.append(local.thisErr)>
				</cfloop>
			</cfif>
			
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset local.returnStruct.arrErrors.append({ "text":cfcatch.message, "code":"callAuthorize_catch" })>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="parseAuthorizeResponse" access="private" output="false" returntype="struct">
		<cfargument name="strAuthorizeResponse" type="struct" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = StructNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="charge">
				<cfset local.response = { "responseCode"=3, "responseSubCode"=0, "responseReasonText"='', "publicResponseReasonText"='', 
					"responseReasonCode"=3, "authorizationCode"='', "avsResponse"='', "transactionid"=0, "description"='', "accountNumber"='', 
					"cardType"='' }>
			</cfcase>
			<cfcase value="refund">
				<cfset local.response = { "responseCode"=3,  "responseReasonText"='', "publicResponseReasonText"='', 
					"responseReasonCode"=3, "transactionid"=0, "approvalCode"='' }>
			</cfcase>
			<cfcase value="void">
				<cfset local.response = { "responseCode"=3,  "responseReasonText"='', "publicResponseReasonText"='', 
					"responseReasonCode"=3, "transactionid"=0, "approvalCode"='' }>
			</cfcase>
			<cfdefaultcase>
				<cfset local.response = {}>
			</cfdefaultcase>
		</cfswitch>

		<cftry>
			<cfif arguments.strAuthorizeResponse.arrErrors.len()>
				<cfset local.response.responseCode = 3>
				<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">				
				<cfloop array="#arguments.strAuthorizeResponse.arrErrors#" index="local.thisErr">
					<cfset local.response.responseReasonText = local.response.responseReasonText & "[#local.thisErr.code#] #local.thisErr.text# ">
				</cfloop>
			<cfelse>

				<cfswitch expression="#arguments.mode#">
					<cfcase value="charge">
						<cfscript>
						local.response.responseCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.responseCode;
						local.response.responseSubCode = '';
						if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("errors")) {
							local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorCode;
							local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorText;
						} else if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("messages")) {
							local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].code;
							local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].description;
						}
						local.response.publicResponseReasonText = "";
						if (local.response.responseCode neq "1") {
							switch (local.response.responseReasonCode) {
								case "8": case "11": case "37":
									local.response.publicResponseReasonText = local.response.responseReasonText;
									break;
								case "27":
									local.response.publicResponseReasonText = "Address does not match cardholder.";
									break;
								default:
									local.response.publicResponseReasonText = "Unable to Complete Transaction";
							}
						}
						local.response.authorizationCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.authCode;
						local.response.avsResponse = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.avsResultCode;
						local.response.transactionID = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.transId;
						local.response.description = "";
						local.response.accountNumber = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.accountNumber; // xxxx9999
						local.response.cardType = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.accountType; // Visa, MasterCard, American Express, Discover, Diners Club, JCB
						</cfscript>
					</cfcase>
					<cfcase value="refund">
						<cfscript>
							local.response.responseCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.responseCode;
							if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("errors")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorCode;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorText;
							} else if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("messages")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].code;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].description;
							}
							local.response.publicResponseReasonText = "";
							if (local.response.responseCode NEQ 1)
								local.response.publicResponseReasonText = "Unable to refund transaction";
							local.response.transactionid = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.transId;
							local.response.approvalCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.authCode;
						</cfscript>
					</cfcase>
					<cfcase value="void">
						<cfscript>
							local.response.responseCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.responseCode;
							if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("errors")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorCode;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.errors[1].errorText;
							} else if (arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.keyExists("messages")) {
								local.response.responseReasonCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].code;
								local.response.responseReasonText = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.messages[1].description;
							}
							local.response.publicResponseReasonText = "";
							if (local.response.responseCode NEQ 1)
								local.response.publicResponseReasonText = "Unable to void transaction";
							local.response.transactionid = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.transId;
							local.response.approvalCode = arguments.strAuthorizeResponse.strAPIResponse.transactionResponse.authCode;
						</cfscript>
					</cfcase>
				</cfswitch>

			</cfif>
		<cfcatch type="any">
			<cfset local.response.responseCode = 3>
			<cfset local.response.responseReasonText = cfcatch.message & " " & cfcatch.detail>
			<cfset local.response.publicResponseReasonText = "Unable to Complete Transaction">
		</cfcatch>
		</cftry>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="maskRequestBody" access="private" output="false" returntype="struct">
		<cfargument name="apiRequestBody" type="string" required="true">
		<cfargument name="arrKeysToMask" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.apiRequestBodyForLogging = deserializeJSON(arguments.apiRequestBody)>

		<cfif arrayLen(arguments.arrKeysToMask)>
			<cfloop array="#arguments.arrKeysToMask#" index="local.thisKeySet">
				<cfset local.thisKeyFullPath = "local.apiRequestBodyForLogging.#listDeleteAt(local.thisKeySet.key,listLen(local.thisKeySet.key,'.'),'.')#">
				<cfif isDefined("#local.thisKeyFullPath#")>
					<cfset structUpdate(structGet(local.thisKeyFullPath),listLast(local.thisKeySet.key,'.'),local.thisKeySet.replacement)>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn local.apiRequestBodyForLogging>
	</cffunction>

	<cffunction name="consolidateMemberPayProfile" access="private" returntype="void" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>

		<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)>
		<cfif NOT local.recordedByMemberID>
			<cfset local.recordedByMemberID = application.objCommon.getMCSystemMemberID()>
		</cfif>

		<cftry>
			<cfstoredproc procedure="ams_consolidateMemberPayProfiles" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
				<cfprocresult name="local.qryConsolidatedProfiles">
			</cfstoredproc>

			<cfquery name="local.qryConsolidateCardsLog" datasource="#application.dsn.platformstatsMC.dsn#">
				SET NOCOUNT ON;

				INSERT INTO dbo.tr_authorizeConsolidateLogs (payProfileID, matchingPayProfileIDs, recordedByMemberID, dateEntered)
				VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">,
					<cfif local.qryConsolidatedProfiles.recordcount>
						<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#valueList(local.qryConsolidatedProfiles.payProfileID)#">,
					<cfelse>
						NULL,
					</cfif>
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">,
					GETDATE()
				);

				SELECT SCOPE_IDENTITY() AS logID;
			</cfquery>

			<cfif local.qryConsolidatedProfiles.recordcount>
				<cfloop query="local.qryConsolidatedProfiles">
					<cfset local.removeResult = removePaymentProfile(profileID=local.qryConsolidatedProfiles.profileID, pmid=local.qryConsolidatedProfiles.memberID,
													cpid=local.qryConsolidatedProfiles.customerProfileID, cppid=local.qryConsolidatedProfiles.paymentProfileID,
													overrideRemoveByMemberID=local.recordedByMemberID)>
					
					<cfif local.removeResult.success>
						<cfquery name="local.qryUpdateLog" datasource="#application.dsn.platformstatsMC.dsn#">
							UPDATE dbo.tr_authorizeConsolidateLogs
							SET removedPayProfileIDs = ISNULL(removedPayProfileIDs + ',','') + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryConsolidatedProfiles.payProfileID#">
							WHERE logID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryConsolidateCardsLog.logID#">
						</cfquery>
					</cfif>
				</cfloop>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="testAPI" access="public" returntype="struct" output="false">
		<cfargument name="profileID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.response = { "success": true, "errMessage": "", "extraInfo": {} }>
		
		<cftry>
			<cfquery name="local.qryPaymentProfiles" datasource="#application.dsn.membercentral.dsn#">
				SELECT mp.profileID, s.siteCode, s.siteName, mp.profileCode, mp.profileName, 
					mp.gatewayUsername, mp.gatewayPassword, ct.currencyType
				FROM dbo.mp_profiles as mp
				INNER JOIN dbo.mp_gateways as mg ON mp.gatewayID = mg.gatewayID
				INNER JOIN dbo.sites as s ON s.siteID = mp.siteID
				INNER JOIN dbo.currencyTypes AS ct ON ct.currencyTypeID = s.defaultCurrencyTypeID
				WHERE mg.isActive = 1
				AND mg.gatewayType = 'AuthorizeCCCIM'
				AND mp.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.profileID#">
				AND mp.status IN ('A','I')
			</cfquery>

			<cfquery name="local.qryPaymentProfilesCardTypes" datasource="#application.dsn.membercentral.dsn#">
				SELECT ct.cardType
				FROM dbo.mp_profileCardTypes pct
				INNER JOIN dbo.mp_cardTypes ct ON pct.cardTypeID = ct.cardTypeID				
				WHERE pct.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.profileID#">
			</cfquery>
			
			<!--- Ensure Profile Exists --->
			<cfif local.qryPaymentProfiles.recordCount EQ 0>
				<cfset local.response.success = false>
				<cfset local.response.errMessage = "Profile ID #arguments.profileID# not found or inactive.">
			</cfif>

			<cfset local.response.extraInfo.siteName = local.qryPaymentProfiles.siteName>
			<cfset local.response.extraInfo.siteCode = local.qryPaymentProfiles.siteCode>
			<cfset local.response.extraInfo.profileID = local.qryPaymentProfiles.profileID>
			<cfset local.response.extraInfo.profileName = local.qryPaymentProfiles.profileName>
			<cfset local.response.extraInfo.profileCode = local.qryPaymentProfiles.profileCode>

			<!--- ----------------------------- --->
			<!--- test 1: Save merchant Details --->
			<!--- ----------------------------- --->
			<cfif local.response.success>
				<cfset local.strMerchantInfo = getMerchantDetails(gatewayUsername=local.qryPaymentProfiles.gatewayUsername, gatewayPassword=local.qryPaymentProfiles.gatewayPassword)>
				<cfif NOT local.strMerchantInfo.success>
					<cfset local.response.success = false>
					<cfset local.response.errMessage = "Unable to get Merchant Details. This is usually an issue with the connection credentials.">
				<cfelse>
					<cfquery name="local.qryUpdateAuthPayProfile" datasource="#application.dsn.membercentral.dsn#">
						UPDATE dbo.mp_profiles
						SET merchantDetailsJSON = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strMerchantInfo.rawdata#">
						WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryPaymentProfiles.profileID#">;
					</cfquery>
				</cfif>
			</cfif>

			<!--- ------------------------------------------------------------------------------------------- --->
			<!--- test 2: Check merchant Currencies (sandbox only supports USD so skip this when not in prod) --->
			<!--- ------------------------------------------------------------------------------------------- --->
			<cfif local.response.success and variables.x_testmode is 0>
				<cfif NOT ArrayFindNoCase(local.strMerchantInfo.data.currencies,local.qryPaymentProfiles.currencyType)>
					<cfset local.response.success = false> 
					<cfset local.response.errMessage = "Default currency for the site is #local.qryPaymentProfiles.currencyType# is not in Authorize.">
				</cfif>
			</cfif>

			<!--- --------------------------------- --->
			<!--- test 3: Check merchant card types --->
			<!--- --------------------------------- --->
			<cfif local.response.success>
				<cfloop list="#valueList(local.qryPaymentProfilesCardTypes.cardType)#" index="local.thisCardType">
					<cfif NOT ArrayFindNoCase(local.strMerchantInfo.data.paymentMethods,replace(local.thisCardType,' ','','all'))>
						<cfset local.response.success = false> 
						<cfset local.response.errMessage = "Enabled card type #local.thisCardType# is not in Authorize.">
					</cfif>
				</cfloop>
			</cfif>
			
			<cfif local.response.success eq false>
				<cfquery name="local.qryInsertAPITestResult" datasource="#application.dsn.platformstatsMC.dsn#">
					insert into dbo.apilog_testing (testDate, testCFC, testName, results)
					values (getDate(), 'AuthorizeCCCIM', 'Cust_#local.qryPaymentProfiles.siteCode#_#local.qryPaymentProfiles.profileCode#', <cfqueryparam value="#local.response.errMessage#" cfsqltype="CF_SQL_LONGVARCHAR">)
				</cfquery>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.response.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="getTransaction" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="x_transid" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errors":[], "data":{}, "rawdata":"" }>
	
		<!--- create request --->
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getTransactionDetailsRequest": {
						"merchantAuthentication": {
							"name": "#arguments.qryGateWayID.gatewayUsername#",
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
						},
						"transId": "#encodeForHTML(arguments.x_transid)#"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfset local.returnStruct = { "success":false, "errors":local.strAuthorize.arrErrors, "data":{}, "rawdata":"" }>
			<cfelse>
				<cfset local.returnStruct = { "success":true, "errors":[], "data":local.strAuthorize.strAPIResponse, "rawdata":local.strAuthorize.rawAPIResponse }>
			</cfif>
	
		<cfcatch type="any">
			<cfset local.returnStruct = { "success":false, "errors":[{ "text":cfcatch.message, "code":"getTransaction_catch" }] }>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="recordMissingTransaction" access="public" returntype="boolean" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">
		<cfargument name="profileid" type="numeric" required="yes">
		<cfargument name="transaction" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfset local.AuthorizeTransactionID = arguments.transaction.transId>
		<cfset local.cardType = arguments.transaction.payment.creditcard.cardType>
		<cfset local.transactionDate = replaceNoCase(arguments.transaction.submitTimeLocal,"T"," ")>
		<cfset local.cardNumber = arguments.transaction.payment.creditcard.cardNumber>
		<cfset local.amount = arguments.transaction.authAmount>
		<cfset local.orderDescription = arguments.transaction.order.description>
		<cfset local.customerProfileId = arguments.transaction.profile.customerProfileId>
		<cfset local.customerPaymentProfileId = arguments.transaction.profile.customerPaymentProfileId>
		<cfset local.responsereasoncode = arguments.transaction.responseReasonCode>
		<cfset local.approvalcode = arguments.transaction.authCode>
		<cfset local.responsecode = arguments.transaction.responseCode>
		<cfset local.AVSResponse = arguments.transaction.AVSResponse>
		<cfset local.CAVVResponse = arguments.transaction.CAVVResponse ?: "">
		<cfset local.networkTransId = arguments.transaction.networkTransId>
		<cfset local.transactionDetail = 'Payment by card #local.cardNumber#'>

		<!--- sandbox only supports USD, so ensure we send USD when not in prod --->
		<cfif variables.x_testmode is 1>
			<cfset querySetCell(arguments.qryGatewayID, "currencyType", "USD", 1)>
		</cfif>

		<!--- identify batch --->
		<cfset local.cofType = getCOFType(cardType=local.cardType)>
		<cfset local.GLAccountID = application.objPayments.getCardTypeGLAccountFromCardType(profileID=arguments.profileid, cardType=local.cofType)>
		<cfif val(local.GLAccountID) is 0>
			<cfset local.GLAccountID = arguments.qryGateWayID.GLAccountID>
		</cfif>
		<cfset local.strGLAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=local.GLAccountID, orgID=arguments.qryGateWayID.orgid)>
		<cfset local.batchCode = "#dateformat(local.transactionDate,"YYYYMMDD")#_#arguments.qryGateWayID.profileID#_#local.GLAccountID#_IMP">
		<cfset local.batchName = "#dateformat(local.transactionDate,"YYYYMMDD")# #arguments.qryGateWayID.profileCode# #local.strGLAccount.qryAccount.accountName#">
		<cfset local.stepNum = 0>
		<cfset local.batchFound = 0>
		<cfloop condition="local.batchFound eq 0">
			<cfset local.stepNum = local.stepNum + 1>
			<cfquery name="local.qryFindBatch" datasource="#application.dsn.membercentral.dsn#">
				select batchID, statusID
				from dbo.tr_batches
				where orgID = <cfqueryparam value="#arguments.qryGateWayID.orgid#" cfsqltype="CF_SQL_INTEGER">
				and batchTypeID = 6
				and batchCode = <cfqueryparam value="#local.batchCode#_#local.stepNum#" cfsqltype="CF_SQL_VARCHAR">
				and isSystemCreated = 1
			</cfquery>
			<cfif local.qryFindBatch.recordcount is 0>
				<cfstoredproc procedure="tr_createBatch" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.profileID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="6">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.batchCode#_#local.stepNum#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#left(local.batchName,396)# #local.stepNum#">
					<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="0">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="0">
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.transactionDate#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.batchID">
				</cfstoredproc>
				<cfset local.batchFound = 1>
			<cfelseif local.qryFindBatch.statusID is 1>
				<cfset local.batchID = local.qryFindBatch.batchID>
				<cfset local.batchFound = 1>
			</cfif>
		</cfloop>

		<!--- record card on file --->
		<cfquery name="local.qryCheckCC" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			declare @cofMemberID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);

			select top 1 payProfileID
			from dbo.ams_memberPaymentProfiles
			where memberID = @cofMemberID
			and profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.profileid#">
			and status = 'A'
			and customerProfileID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.customerProfileId#">
			and paymentProfileID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.customerPaymentProfileId#">;
		</cfquery>
		<cfif local.qryCheckCC.recordcount is 0>
			<cfquery name="local.qryCardType" datasource="#application.dsn.membercentral.dsn#">
				select dbo.mp_getCardTypeID('#local.cofType#') as cardTypeID
			</cfquery>
			<cfset local.cardTypeID = local.qryCardType.cardTypeID>

			<cfquery name="local.qryInsertCC" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @cofMemberID int, @payProfileID int, @profileID int;

					SET @profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.profileid#">;
					SELECT @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);
					SELECT @orgID = orgID FROM dbo.ams_members WHERE memberID = @cofMemberID;

					insert into dbo.ams_memberPaymentProfiles (memberid, profileID, status, detail, nickname, customerProfileID, 
						paymentProfileID, addedStatsSessionID, addedByMemberID, cardTypeID, checkedForZIP, hasZIP, surchargeEligible)
					values (
						@cofMemberID,
						@profileID,
						'A', 
						<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.cardNumber#">,
						'', 
						<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.customerProfileId#">,
						<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.customerPaymentProfileId#">,
						<cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,
						<cfif len(local.cardTypeID)>
							<cfqueryparam value="#local.cardTypeID#" cfsqltype="CF_SQL_INTEGER">
						<cfelse>
							null
						</cfif>,
						0, 0, null
					);

					select @payProfileID = SCOPE_IDENTITY();

					EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @memberID=@cofMemberID, @profileID=@profileID, @lookupMode='ccprofile';

					select @payProfileID as payProfileID;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.payProfileID = local.qryInsertCC.payProfileID>

			<!--- refresh payment profiles so we have the updated zip information --->
			<cfset refreshPaymentProfiles(qryGateWayID=arguments.qryGateWayID, pmid=arguments.memberid, customerProfileId=local.customerProfileId)>

		<cfelse>
			<cfset local.payProfileID = local.qryCheckCC.payProfileID>
		</cfif>

		<!--- record history. We dont know exact details so only provide what we can. --->
		<cfsavecontent variable="local.apiRequestBody">
			<cfoutput>{
				"createTransactionRequest": {
					"merchantAuthentication": {
						"name": "#arguments.qryGateWayID.gatewayUsername#",
						"transactionKey": "#arguments.qryGateWayID.gatewayPassword#"
					},
					"refId": "-MCREFID-",
					"transactionRequest": {
						"transactionType": "authCaptureTransaction",
						"amount": "#local.amount#",
						"currencyCode": "#arguments.qryGateWayID.currencyType#",
						"profile": {
							"customerProfileId": "#local.customerProfileId#",
							"paymentProfile": { "paymentProfileId": "#local.customerPaymentProfileId#" }
						},
						"solution": {
							"id": "#variables.x_solution_id#"
						},
						"order": {
							"description": "#encodeForHTML(local.orderDescription)#"
						},
						"tax": {
							"amount": 0
						},
						"transactionSettings": {
							"setting": {
								"settingName": "recurringBilling",
								"settingValue": "false"
							}
						}
					}
				}
			}</cfoutput>
		</cfsavecontent>
		<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

		<cfsavecontent variable="local.xmlPaymentInfo">
			<cfoutput>
			<payment gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#">
				<args>
					<orgid>#arguments.qryGateWayID.orgID#</orgid>
					<siteid>#arguments.qryGateWayID.siteID#</siteid>
					<x_description>#xmlFormat(local.orderDescription)#</x_description>
					<recordedbymemberid>#session.cfcuser.memberdata.memberid#</recordedbymemberid>
					<profilecode>#xmlFormat(arguments.qryGateWayID.profileCode)#</profilecode>
					<x_amount>#xmlFormat(local.amount)#</x_amount>
					<assignedtomemberid>#arguments.memberid#</assignedtomemberid>
				</args>
				<gateway><![CDATA[ #local.apiRequestBody# ]]></gateway>
			</payment>
			</cfoutput>
		</cfsavecontent>
			
		<cfset local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.memberid, memberPaymentProfileID=local.payProfileID, 
			paymentInfo=local.xmlPaymentInfo, gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='payment')>
		<cftry>
			<cfsavecontent variable="local.apiResponseBody">
				<cfoutput>{
					"transactionResponse": {
						"responseCode": "#local.responsecode#",
						"authCode": "#local.approvalcode#",
						"avsResultCode": "#local.AVSResponse#",
						"cavvResultCode": "#local.CAVVResponse#",
						"transId": "#local.AuthorizeTransactionID#",
						"refTransID": "",
						"transHash": "",
						"testRequest": "0",
						"accountNumber": "#local.cardNumber#",
						"accountType": "#local.cardType#",
						"messages": [
							{
							"code": "1",
							"description": "This transaction has been approved."
							}
						],
						"transHashSha2": "",
						"profile": {
							"customerProfileId": "#local.customerProfileId#",
							"customerPaymentProfileId": "#local.customerPaymentProfileId#"
						},
						"SupplementalDataQualificationIndicator": 0,
						"networkTransId": "#local.networkTransId#"
						},
						"refId": "#local.returnHistory.refId#",
						"messages": {
						"resultCode": "Ok",
						"message": [
							{
							"code": "I00001",
							"text": "Successful."
							}
						]
					}
				 }</cfoutput>
			</cfsavecontent>
			<cfset local.apiResponseBody = REReplace(trim(local.apiResponseBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfsavecontent variable="local.xmlResponseInfo">
				<cfoutput>
				<response>
					<transactiondetail>#XMLFormat(local.transactionDetail)#</transactiondetail>
					<glaccountid>#local.GLAccountID#</glaccountid>
					<rawresponse><![CDATA[ #local.apiResponseBody# ]]></rawresponse>
					<transactionid>#XMLFormat(local.AuthorizeTransactionID)#</transactionid>
					<responsereasoncode>#XMLFormat(local.responsereasoncode)#</responsereasoncode>
					<approvalcode>#XMLFormat(local.approvalcode)#</approvalcode>
					<responsecode>#XMLFormat(local.responsecode)#</responsecode>
					<responsereasontext>This transaction has been approved.</responsereasontext>
					<status>Active</status>
					<historyid>0</historyid>
				</response>
				</cfoutput>
			</cfsavecontent>
				
			<cfset local.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
				gatewayResponse=local.xmlResponseInfo, responseReasonCode=local.responseReasonCode)>

			<!--- record payment --->
			<cfset local.strTemp = { ownedByOrgID=arguments.qryGateWayID.orgID, recordedOnSiteID=arguments.qryGateWayID.siteID, 
									assignedToMemberID=arguments.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid, 
									statsSessionID=session.cfcuser.statsSessionID, status='Active', detail=local.transactionDetail, amount=local.amount, 
									transactionDate=local.transactionDate, debitGLAccountID=local.GLAccountID, profileCode=arguments.qryGateWayID.profileCode, 
									historyID=local.historyID, batchID=local.batchID }>
			<cfset local.strPayment = CreateObject("component","model.system.platform.accounting").recordPayment(argumentcollection=local.strTemp)>
			<cfif local.strPayment.rc is not 0 or local.strPayment.transactionID is 0>
				<cfthrow message="The payment could not be recorded.">
			</cfif>
			<cfset local.paymentTransactionID = local.strPayment.transactionID>

			<cfset local.success = true>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="isValidCustomerProfile" access="private" returntype="boolean" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="customerProfileID" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfif NOT len(arguments.customerProfileID)>
			<cfreturn false>
		</cfif>

		<cfset local.validCustomerProfileId = "">
		
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#arguments.qryGateWayID.gatewayUsername#", 
							"transactionKey": "#arguments.qryGateWayID.gatewayPassword#" 
						},
						"customerProfileId": "#arguments.customerProfileID#",
						"includeIssuerInfo": false
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile was not found --->
				<cfif findNoCase("E00040",local.strAuthorize.rawAPIResponse)>
					<!--- do nothing. --->
				<cfelse>
					<cfthrow message="Error retrieving customer profile.">
				</cfif>
			<cfelse>
				<cfset local.validCustomerProfileId = local.strAuthorize.strAPIResponse.profile.customerProfileId>
			</cfif>
		<cfcatch type="any">
			<cfset local.validCustomerProfileId = "">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn len(local.validCustomerProfileId) GT 0>
	</cffunction>

	<cffunction name="getMerchantDetails" access="public" returntype="struct" output="no">
		<cfargument name="gatewayUsername" type="string" required="yes">
		<cfargument name="gatewayPassword" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errors":[], "data":{}, "rawdata":"" }>
	
		<cftry>
			<cfsavecontent variable="local.apiRequestBody"><cfoutput>{"getMerchantDetailsRequest": { "merchantAuthentication": { "name": "#arguments.gatewayUsername#", "transactionKey": "#arguments.gatewayPassword#" }}}</cfoutput></cfsavecontent>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfset local.returnStruct = { "success":false, "errors":local.strAuthorize.arrErrors, "data":{}, "rawdata":"" }>
			<cfelse>
				<cfset local.returnStruct = { "success":true, "errors":[], "data":local.strAuthorize.strAPIResponse, "rawdata":local.strAuthorize.rawAPIResponse }>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct = { "success":false, "errors":[{ "text":cfcatch.message, "code":"getMerchantDetails_catch" }] }>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getCustomerPaymentProfile" access="private" returntype="struct" output="no">
		<cfargument name="gatewayUsername" type="string" required="yes">
		<cfargument name="gatewayPassword" type="string" required="yes">
		<cfargument name="customerProfileid" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errors":[], "data":{}, "rawdata":"" }>
	
		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerPaymentProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#arguments.gatewayUsername#", 
							"transactionKey": "#arguments.gatewayPassword#" 
						},
						"customerProfileId": "#arguments.customerProfileid#",
						"customerPaymentProfileId": "#arguments.customerPaymentProfileId#",
						"includeIssuerInfo": "false"
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<cfset local.returnStruct = { "success":false, "errors":local.strAuthorize.arrErrors, "data":{}, "rawdata":"" }>
			<cfelse>
				<cfset local.returnStruct = { "success":true, "errors":[], "data":local.strAuthorize.strAPIResponse, "rawdata":local.strAuthorize.rawAPIResponse }>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct = { "success":false, "errors":[{ "text":cfcatch.message, "code":"getCustomerPaymentProfile_catch" }] }>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="existsApplePayDomain" access="private" output="false" returntype="boolean">
		<cfargument name="profileID" type="numeric" required="true">

		<cfset var qryApplePayDomain = "">

		<cfquery name="qryApplePayDomain" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#CreateTimeSpan(0,0,30,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @merchantIdentifier varchar(1024);
			
			SELECT @merchantIdentifier = LOWER(tier + '.#session.mcstruct.sitecode#.AuthorizeCCCIM.#arguments.profileID#') 
			FROM dbo.fn_getServerSettings();

			SELECT hostName
			FROM dbo.tr_applePayDomains
			WHERE merchantIdentifier = @merchantIdentifier
			AND hostName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#getHostName()#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryApplePayDomain.recordCount EQ 1>
	</cffunction>

	<cffunction name="registerCurrentHostNameWithApplePay" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="profileID" type="numeric" required="true">

		<cfscript>
			var local = structnew();
			local.hostName = getHostName();
			local.applePayUtils = new model.system.platform.gateways.applePayUtils(PAYMENTUTILITIESURL=application.paths.paymentUtilities.url);
			local.addDomainResult = local.applePayUtils.addDomainToMerchant(profileID=arguments.profileID, siteCode=session.mcstruct.siteCode, domainName=local.hostName);
			if (NOT local.addDomainResult.success) {
				return false;
			}

			local.applePayMerchantIdentifier = local.applePayUtils.getDerivedApplePayMerchantIdentifier(profileID=arguments.profileID, siteID=arguments.siteID);

			var qryAddApplePayDomain = queryExecute("EXEC dbo.tr_addApplePayDomains @merchantIdentifier = :merchantIdentifier, @hostNameList = :hostName;", 
				{ 
					merchantIdentifier = { value=local.applePayMerchantIdentifier, cfsqltype="CF_SQL_VARCHAR" },
					hostName = { value=local.hostName, cfsqltype="CF_SQL_VARCHAR" }
				},
				{ datasource="#application.dsn.membercentral.dsn#" }
			);

			return true;
		</cfscript>
	</cffunction>

	<cffunction name="getCOFType" access="private" output="false" returntype="string">
		<cfargument name="cardType" type="string" required="true">

		<cfset var cofType = "">

		<cfswitch expression="#arguments.cardType#">
			<cfcase value="Mastercard"><cfset cofType = "Mastercard"></cfcase>
			<cfcase value="Visa"><cfset cofType = "Visa"></cfcase>
			<cfcase value="American Express,AmericanExpress" delimiters=","><cfset cofType = "American Express"></cfcase>
			<cfcase value="Discover,JCB,DinersClub,Diners Club" delimiters=","><cfset cofType = "Discover"></cfcase>
			<cfdefaultcase><cfset cofType = ""></cfdefaultcase>
		</cfswitch>

		<cfreturn cofType>
	</cffunction>

	<cffunction name="getHostName" access="private" output="false" returntype="string">
		<cfreturn application.MCEnvironment eq "production" ? application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname : application.objPlatform.getCurrentHostname()>
	</cffunction>

	<cffunction name="getHostNameWithProtocol" access="private" output="false" returntype="string">
		<cfreturn (application.objPlatform.isRequestSecure() ? "https://" : "http://") & getHostName()>
	</cffunction>

	<cffunction name="logAPICall" output="false" access="private" returntype="void">
		<cfargument name="strCall" type="struct" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.strRequest = {
				"c":"AuthorizeNet",
				"d": {
					"request": {
						"method":arguments.strCall.request.method,
						"endpoint":arguments.strCall.request.endpoint,
						"bodycontent":arguments.strCall.request.bodycontent
					},
					"response": {
						"bodycontent":arguments.strCall.response.bodycontent,
						"headers":arguments.strCall.response.headers,
						"statuscode":arguments.strCall.response.statuscode
					},
					"timestamp":now()
				}
			}>
	
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.dsn.membercentral.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getCardPermissions" access="public" output="false" returntype="struct">
		<cfargument name="editMode" type="string" required="true">
	
		<cfset var local = structNew()>
	
		<cfswitch expression="#arguments.editmode#">
			<cfcase value="frontEndPayment">
				<cfset local.showFailureInfo = 0>
				<cfset local.showAssociatedItems = 0>
				<cfset local.showAddedBy = 0>
				<cfset local.showlastUpdater = 0>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 0>
				<cfset local.allowReassociate = 0>
			</cfcase>	
			<cfcase value="frontEndManage,frontEndManageV2">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 1>
				<cfset local.showAddedBy = 0>
				<cfset local.showlastUpdater = 0>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 0>
				<cfset local.allowReassociate = 1>
			</cfcase>	
			<cfcase value="controlPanelPayment">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 0>
				<cfset local.showAddedBy = 1>
				<cfset local.showlastUpdater = 1>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 1>
				<cfset local.allowReassociate = 0>
			</cfcase>	
			<cfcase value="controlPanelManage">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 1>
				<cfset local.showAddedBy = 1>
				<cfset local.showlastUpdater = 1>
				<cfset local.showPayOverdueNow = 1>
				<cfset local.includeOpenInvoices = 1>
				<cfset local.allowReassociate = 1>
			</cfcase>	
		</cfswitch>
	
		<cfreturn local>
	</cffunction>

	<cffunction name="getCommonStyles" access="public" output="false" returntype="string">
		<cfset var commonStyles = "">
		
		<cfsavecontent variable="commonStyles">
			<cfoutput>
			<style type="text/css">
				.mcp-authcim-card-box { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal;
					-ms-flex-direction: column;flex-direction: column; min-width: 0; word-wrap: break-word; background-color: ##fff; background-clip: border-box; 
					border: 0 solid rgba(122, 123, 151, 0.3); border-radius: 0.65em; box-shadow: 0 .46875em 2.1875em rgba(0,0,0,.03),0 .9375em 1.40625em rgba(0,0,0,.03),0 .25em .53125em rgba(0,0,0,.05),0 .125em .1875em rgba(0,0,0,.03); }
				.mcp-authcim-card-header { padding:.75em;margin-bottom:0; }
				.mcp-authcim-card-body {padding:1.25em;}
				.mcp-authcim-text-danger {color:##f83245!important}
				.mcp-authcim-text-center {text-align:center !important;}
				.mcp-authcim-text-dim {color:##808080!important}
				.mcp-authcim-d-none{display:none !important;}
				.mcp-authcim-d-flex {display:flex !important;}
				.mcp-authcim-col {flex-basis:0;flex-grow:1;max-width:100%;padding-right:5px;padding-left:5px;}
				.mcp-authcim-col-auto {flex:0 0 auto;width:auto;max-width:100%;padding-right:5px;padding-left:5px;}
				.mcp-authcim-p-0 {padding:0!important;}
				.mcp-authcim-pt-0 {padding-top:0!important;}
				.mcp-authcim-pr-0 {padding-right:0!important;}
				.mcp-authcim-pr-2 {padding-right:.5em!important;}
				.mcp-authcim-p-1 {padding:.25em!important;}
				.mcp-authcim-p-2 {padding:.5em!important;}
				.mcp-authcim-p-2 {padding:.5em!important;}
				.mcp-authcim-p-3 {padding:1em!important;}
				.mcp-authcim-pt-3 {padding-top:1em!important;}
				.mcp-authcim-pl-0 {padding-left:0!important;}
				.mcp-authcim-pl-1 {padding-left:.25em!important;}
				.mcp-authcim-pl-2 {padding-left:.5em!important;}
				.mcp-authcim-pl-3 {padding-left:1em!important;}
				.mcp-authcim-pl-5 {padding-left:2em!important;}
				.mcp-authcim-pb-0 {padding-bottom:0!important;}
				.mcp-authcim-pb-1 {padding-bottom:.25em!important;}
				.mcp-authcim-pb-2 {padding-bottom:.5em!important;}
				.mcp-authcim-m-0 {margin:0!important;}
				.mcp-authcim-m-1 {margin:.25em!important;}
				.mcp-authcim-m-2 {margin:.5em!important;}
				.mcp-authcim-mt-0 {margin-top:0!important;}
				.mcp-authcim-mt-1 {margin-top:.25em!important;}
				.mcp-authcim-mt-2 {margin-top:.5em!important;}
				.mcp-authcim-mt-3 {margin-top:1em!important;}
				.mcp-authcim-mt-4 {margin-top:1.5em!important;}
				.mcp-authcim-mt-5 {margin-top:2em!important;}
				.mcp-authcim-mb-0 {margin-bottom:0!important;}
				.mcp-authcim-mb-1 {margin-bottom:.25em!important;}
				.mcp-authcim-mb-2 {margin-bottom:.5em!important;}
				.mcp-authcim-mb-3 {margin-bottom:1em!important;}
				.mcp-authcim-mb-4 {margin-bottom:1.5em!important;}
				.mcp-authcim-mb-5 {margin-bottom:2em!important;}
				.mcp-authcim-ml-1 {margin-left:.25em!important;}
				.mcp-authcim-ml-2 {margin-left:.5em!important;}
				.mcp-authcim-mr-1 {margin-right:.25em!important;}
				.mcp-authcim-mr-2 {margin-right:.5em!important;}
				.mcp-authcim-mr-3 {margin-right:1em!important;}
				.mcp-authcim-mr-4 {margin-right:1.5em!important;}
				.mcp-authcim-mr-5 {margin-right:2em!important;}
				.mcp-authcim-align-items-center {align-items:center;}
				.mcp-authcim-align-self-center{align-self:center !important;}
				.mcp-authcim-flex-column {flex-direction: column !important;}
				.mcp-authcim-font-weight-bold {font-weight:bold!important;}
				.mcp-authcim-font-italic {font-style: italic !important;}
				.mcp-authcim-ml-auto {margin-left:auto !important;}
				.mcp-authcim-mr-auto {margin-right:auto !important;}
				.mcp-authcim-mx-auto {margin-left:auto !important;margin-right:auto !important;}
				.mcp-authcim-font-size-xs { font-size:.79em!important; }
				.mcp-authcim-font-size-sm { font-size:.85em!important; }
				.mcp-authcim-font-size-md {font-size:.95em!important;}
				.mcp-authcim-font-size-lg {font-size:1.1875em!important;}
				.mcp-authcim-font-size-xl {font-size:1.425em!important;}
				.mcp-authcim-line-height-1 {line-height:1!important;}
				.mcp-authcim-opacity-2 {opacity: 0.2 !important;}
				.mcp-authcim-card-selected { border-color: rgb(60, 115, 205) !important;border-width: 2px !important;background-color: rgba(18, 101, 241, 0.07); }
				.mcp-authcim-bg-secondary {background-color: ##f8f9ff !important;}
				.mcp-authcim-btn {display:flex;align-items:center;gap:12px;background-color:##000;padding:7px;border-radius:4px;border:none;cursor:pointer;justify-content:center;}
				.mcp-authcim-btn img {height:28px;object-fit:contain;}
				.mcp-authcim-btn:hover {background-color:##3b3b3b;}
				.mcp-authcim-cof_fail { margin-top:4px; color:##F00;background:url(/assets/common/images/exclamation.png) no-repeat left center; padding-left:20px;}
				.mcp-authcim-procfee-radio {margin:3px 5px 0 0 !important;}
				.mcp-authcim-shadow-none {-webkit-box-shadow: none !important;box-shadow: none !important;}
				.mcp-authcim-border-1 {border-width:1px;border-style:solid;}
				.mcp-authcim-border-2 {border-width:2px !important;border-style:solid;}
				.mcp-authcim-border-bottom {border-bottom: 1px solid ##ccc !important;}
				.mcp-authcim-border-gray {border-color:gray!important;}
				.mcp-authcim-border-danger {border-color:##f83245 !important;}
				.mcp-authcim-alert-warning { color:##824224;background-color:##fde4d5;border-color:##fcd9c4; }
				.mcp-authcim-dropdown {display: inline-block;position:relative;outline:none;}
				.mcp-authcim-dropbtn {cursor: pointer;transition: 0.35s ease-out;}
				.mcp-authcim-dropdown .mcp-authcim-dropdown-content {position: absolute;top:10%;right:50%;background-color: ##f7f7f7;min-width:150px;box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
					z-index: 100000;visibility: hidden;opacity: 0;transition: 0.35s ease-out;}
				.mcp-authcim-dropdown-content a {color: black;padding: 12px 16px;display: block;text-decoration: none;transition: 0.35s ease-out;}
				.mcp-authcim-dropdown-content a:hover {background-color: ##eaeaea;}
				.mcp-authcim-dropdown:focus .mcp-authcim-dropdown-content { outline: none;transform: translateY(20px);visibility: visible;opacity: 1;}
				.mcp-authcim-dropbtn:hover, .mcp-authcim-dropdown:focus .mcp-authcim-dropbtn {transform: translateY(-2px);}
				.mcp-authcim-dropdown .mcp-authcim-dropbtnmask {position: absolute;  top: 0; right: 0; bottom: 0; left: 0; opacity: 0;cursor: pointer;z-index: 10;display: none;}
				.mcp-authcim-dropdown:focus .mcp-authcim-dropbtnmask {display: inline-block;}
				.mcp-authcim-dropdown .mcp-authcim-dropbtnmask:focus .mcp-authcim-dropdown-content {outline: none;visibility: hidden;opacity: 0;}
				a.mcp-authcim-dropbtn {text-decoration:none !important;}
				.mcp-authcim-dropbtn i {width:30px;height:30px;text-align:center;line-height:30px;}
				.mcp-authcim-dropbtn i:hover {background-color:##eee;border-radius: 50%;}
				.mcp-authcim-dropdown i {float:none !important;}
				/*mobile styles*/
				@media (max-width: 576px) {
					.mcp-authcim-w-sm-100 {width:100% !important;}
				}
			</style>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn commonStyles>
	</cffunction>

</cfcomponent>