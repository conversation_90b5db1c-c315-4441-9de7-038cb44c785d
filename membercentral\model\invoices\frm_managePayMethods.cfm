<cfinclude template="frm_managePayMethods_commonJS.cfm">

<cfoutput>
<div class="pm-container">
	<div class="tsAppHeading pm-mb-2">Manage Scheduled Payments and Methods of Payment</div>
	<cfif len(local.qryManagePayMethodsContent.rawContent)>
		<div class="pm-mb-2">#local.qryManagePayMethodsContent.rawContent#</div>
	</cfif>

	<cfif local.arrScheduledPayments.len()>
		<div class="tsAppHeading subheading pm-mb-4">Your Scheduled Payments</div>
		<div class="payment-table">
			<cfloop array="#local.arrScheduledPayments#" item="local.thisPayment">
				<div id="#local.thisPayment.type#_#local.thisPayment.referenceID#" class="paymethod-row
					<cfif local.thisPayment.keyExists('cardFailedLastDate') and len(local.thisPayment.cardFailedLastDate)> status-error
					<cfelseif local.thisPayment.type eq "sub" and len(local.thisPayment.overDueInvDueDate)> status-error
					<cfelseif local.thisPayment.type eq "cp" and len(local.thisPayment.overDueInvDueDate)> status-error
					<cfelseif local.thisPayment.type eq "inv" and local.thisPayment.isOverDue> status-error
					<cfelseif not local.thisPayment.payProfileID> status-warning
					</cfif>
					">
					<div class="pm-flex-row">
						<cfif local.thisPayment.type eq "sub">
							<div class="info-col pm-mt-3">
								<span class="pm-font-weight-bold">#local.thisPayment.subscriptionName#</span><br/>
								<span>#local.thisPayment.rateName#</span><br/>
								<span><i>#local.thisPayment.subStartDate# - #local.thisPayment.subEndDate#</i></span>
							</div>
							<div class="info-col pm-mt-3">
								<cfif listFindNoCase("Billed, Renewal Not Sent", local.thisPayment.statusName)>
									<span>Billed: #local.thisPayment.billedAmt#</span><br>
									<span>Due: #local.thisPayment.dueAmt#</span><br>
								<cfelseif listFindNoCase("Active,Accepted", local.thisPayment.statusName)>
									<cfif len(local.thisPayment.overDueInvDueDate)>
										<span class="pm-text-red">Overdue: #local.thisPayment.totalOverdueAmt# <small>(since #local.thisPayment.earliestOverdueInvoiceDate#)</small></span><br>
									</cfif>
									<span>Balance: #local.thisPayment.dueAmt#</span><br>
									<cfif len(local.thisPayment.nextInvDueDate)>
										<span>Upcoming: #local.thisPayment.nextInvDueAmt# <small>(#local.thisPayment.nextInvDueDate#)</small></span><br>
									</cfif>
								</cfif>
							</div>
						<cfelseif local.thisPayment.type eq "cp">
							<div class="info-col pm-mt-3">
								<span class="pm-font-weight-bold">#local.thisPayment.programName#</span><br/>
								<span>#local.thisPayment.rateName#</span> (#local.thisPayment.frequency#)<br/>
								#local.thisPayment.amtFreqInfo#
							</div>
							<div class="info-col pm-mt-3">
								<cfif len(local.thisPayment.overDueInvDueDate)>
									<span class="pm-text-red">Overdue: #local.thisPayment.totalOverdueAmt# <small>(since #local.thisPayment.earliestOverdueInvoiceDate#)</small></span><br>
								</cfif>
								<cfif len(local.thisPayment.nextInvDueDate)>
									<span>Upcoming: #local.thisPayment.nextInvDueAmt# <small>(#local.thisPayment.nextInvDueDate#)</small></span><br>
								<cfelseif len(local.thisPayment.nextInstallmentDueDate)>
									<span>Upcoming: #local.thisPayment.nextInstallmentDueAmt# <small>(#local.thisPayment.nextInstallmentDueDate#)</small></span><br>
								</cfif>
							</div>
						<cfelseif local.thisPayment.type eq "inv">
							<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisPayment.invoicenumber#|#right(GetTickCount(),5)#|#local.thisPayment.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
							<div class="info-col pm-mt-3">
								<span class="pm-font-weight-bold">Invoice #local.thisPayment.invoiceNumber#</span><br/>
								<span><a href="/?pg=invoices&va=show&item=#local.stInvEnc#&mode=stream">Download Invoice</a></span>
							</div>
							<div class="info-col pm-mt-3">
								<cfif local.thisPayment.isOverDue>
									<span class="pm-text-red">Overdue: #local.thisPayment.invDueAmt# <small>(since #local.thisPayment.invDueDate#)</small></span><br>
								<cfelse>
									<span>Upcoming: #local.thisPayment.invDueAmt# <small>(#local.thisPayment.invDueDate#)</small></span><br>
								</cfif>
							</div>
						<cfelse>
							<div class="info-col pm-mt-3"></div>
							<div class="info-col pm-mt-3"></div>
						</cfif>
						<div class="info-col pm-mt-3">
							<cfif local.thisPayment.payProfileID>
								<cfset local.paymethodLabel = local.thisPayment.gatewayClass eq "bankdraft" ? "Bank Account" : "Card">
								<cfif len(local.thisPayment.nickname)>
									<cfset local.formTitleEnding = '#local.paymethodLabel#: #local.thisPayment.nickname# (**** #right(local.thisPayment.cardDetail,4)#)'>
								<cfelse>
									<cfset local.formTitleEnding = '#local.paymethodLabel# (**** #right(local.thisPayment.cardDetail,4)#)'>
								</cfif>
								<cfset local.profileID = structKeyExists(local.thisPayment,"profileID") ? local.thisPayment.profileID : 0>
								<cfset local.thisPayment.arrActions = [
									{ "title":"Change Method of Payment", "feIconClass":"icon-random", "onclick":"changePayMethod(#local.thisPayment.payProfileID#,#local.profileID#,'#local.thisPayment.type#_#local.thisPayment.referenceID#');" },
									{ "title":"Update #local.paymethodLabel#", "feIconClass":"icon-pencil", "onclick":"updatePayMethod(#local.thisPayment.payProfileID#,#local.profileID#,'#local.thisPayment.type#_#local.thisPayment.referenceID#','#local.paymethodLabel#','Update #encodeForJavascript(local.formTitleEnding)#');" },
									{ "title":"Remove #local.paymethodLabel#", "feIconClass":"icon-trash", "onclick":"removePayMethod(#local.thisPayment.payProfileID#,#local.profileID#,'#local.thisPayment.type#_#local.thisPayment.referenceID#','#local.paymethodLabel#','Remove #encodeForJavascript(local.formTitleEnding)#');" }
								]>
								<cfset local.showAddProcessingFeeDonationAction = local.thisPayment.enableProcessingFeeDonation and val(local.thisPayment.processFeeDonationFeePercent) gt 0>
								<cfif local.thisPayment.gatewayClass eq "creditcard" and local.showAddProcessingFeeDonationAction>
									<cfset local.actionLabel = (local.thisPayment.payProcessFee eq 0 ? "Add" : "Remove") & " " & local.thisPayment.processingFeeLabel>
									<cfset local.updateMode = local.thisPayment.payProcessFee eq 0 ? "add" : "remove">
									<cfset arrayAppend(local.thisPayment.arrActions, { "title":"#local.actionLabel#", "feIconClass":"icon-dollar", "onclick":"addProcessingFeeDonation(#local.thisPayment.payProfileID#,#local.profileID#,'#local.thisPayment.type#_#local.thisPayment.referenceID#','#local.updateMode#');" })>
								</cfif>
								<cfif local.thisPayment.gatewayClass eq "bankdraft">
									<cfmodule template="frm_managePayMethods_bd.cfm" data="#local.thisPayment#">
								<cfelseif local.thisPayment.gatewayClass eq "creditcard">
									<cfmodule template="frm_managePayMethods_cc.cfm" data="#local.thisPayment#">
								</cfif>
							<cfelse>
								<div class="pm-cardbox">
									<div class="pm-d-flex pm-align-items-center">
										<div class="pm-text-orange pm-mb-1" style="width:240px;"><i>No method of payment</i></div>
										<div class="pm-ml-auto">
											<div class="pm-dropdown" tabindex="1">
												<i class="pm-dropbtnmask" tabindex="1"></i>
												<span class="pm-dropbtn pm-font-size-xl" role="listbox" aria-label="Actions"><i class="icon-ellipsis-horizontal"></i></span>
												<div class="pm-dropdown-content pm-font-size-md">
													<a href="##" onclick="changePayMethod(0,0,'#local.thisPayment.type#_#local.thisPayment.referenceID#');$('.pm-dropdown').blur();" class="pm-col-auto" title="Assign Method of Payment">
														<i class="icon-money" style="width:20px;"></i>
														<span class="pm-ml-2">Assign Method of Payment</span>
													</a>
												</div>
											</div>
										</div>
									</div>
								</div>
							</cfif>
						</div>
					</div>
					<div id="#local.thisPayment.type#_#local.thisPayment.referenceID#_actionform" class="pm-action-form" style="display:none;"
						data-payprofileid="#local.thisPayment.payProfileID#"
						data-type="#local.thisPayment.type#"
						data-referenceid="#local.thisPayment.referenceID#">
					</div>
				</div>
			</cfloop>
		</div>
		<br/>
	</cfif>
	<cfif local.arrScheduledPayments.len() and local.payMethodsAssociatedToAtLeastOneItem.listLen()>
		<cfset local.pmSectionTitle = "Additional Methods of Payment">
	<cfelse>
		<cfset local.pmSectionTitle = "Methods of Payment">
	</cfif>
	<div id="additionalPMSection" class="tsAppHeading subheading pm-mt-4">#local.pmSectionTitle#</div>
	<a href="javascript:addPayMethod('mempaymethodaction');"><i class="icon-plus-sign"></i> Add a New Method of Payment</a><br/><br/>

	<cfif arrayLen(local.arrOtherPayMethods)>
		<cfif local.arrScheduledPayments.len()>
			<p class="tsAppBodyText">These methods of payment are on your account, but are not assigned to be used for any scheduled payments</p>
		</cfif>
		<div id="memberPayMethodsList" class="pm-d-flex pm-flex-wrap">
			<cfloop array="#local.arrOtherPayMethods#" item="local.thisPayMethod">
				<div class="pm-card-container">
					<cfset local.paymethodLabel = local.thisPayMethod.gatewayClass eq "bankdraft" ? "Bank Account" : "Card">
					<cfif len(local.thisPayMethod.nickname)>
						<cfset local.formTitleEnding = '#local.paymethodLabel#: #local.thisPayMethod.nickname# (**** #right(local.thisPayMethod.cardDetail,4)#)'>
					<cfelse>
						<cfset local.formTitleEnding = '#local.paymethodLabel# (**** #right(local.thisPayMethod.cardDetail,4)#)'>
					</cfif>

					<cfset local.thisPayMethod.arrActions = [
						{ "title":"Update #local.paymethodLabel#", "feIconClass":"icon-pencil", "onclick":"updatePayMethod(#local.thisPayMethod.payProfileID#,#local.thisPayMethod.profileID#,'mempaymethodaction','#local.paymethodLabel#','Update #encodeForJavascript(local.formTitleEnding)#');" },
						{ "title":"Remove #local.paymethodLabel#", "feIconClass":"icon-trash", "onclick":"removePayMethod(#local.thisPayMethod.payProfileID#,#local.thisPayMethod.profileID#,'mempaymethodaction','#local.paymethodLabel#','Remove #encodeForJavascript(local.formTitleEnding)#');" }
					]>
					<cfif local.thisPayMethod.gatewayClass eq "bankdraft">
						<cfmodule template="frm_managePayMethods_bd.cfm" data="#local.thisPayMethod#">
					<cfelseif local.thisPayMethod.gatewayClass eq "creditcard">
						<cfmodule template="frm_managePayMethods_cc.cfm" data="#local.thisPayMethod#">
					</cfif>
				</div>
			</cfloop>
		</div>
	<cfelse>
		<div id="memberPayMethodsList" class="pm-no-records">None found.</div>
	</cfif>
	<div id="mempaymethodaction">
		<div id="mempaymethodaction_actionform" class="pm-action-form" style="display:none;"></div>
	</div>
</div>

<script id="mc_assocForm" type="text/x-handlebars-template">
	<div class="pm-mx-2 pm-mt-3 pm-mb-4">
		<div id="assocPMSelection" class="assocNewPMSection">
			<input type="hidden" id="newPayProfileID" name="newPayProfileID" value="0">
			<div class="changePayMethodFormTitle pm-mb-2">Choose another method of payment:</div>
			<div id="assocPayMethodSelection" class="pm-custom-dropdown pm-mx-2">
				<div class="dropdown-label">Select Pay Method</div>
				<ul class="dropdown-list">
					<li class="dropdown-option" data-value="0"><i class="icon-plus-sign pm-mr-1"></i> Add New Method of Payment</li>
				</ul>
			</div>
		</div>
		<div id="assocNewPMTypeSelection" class="assocNewPMSection" style="display:none;">
			<input type="hidden" id="createdPayProfileID" name="createdPayProfileID" value="0">
			<div class="pm-mb-2">Choose a method of payment type to add:</div>
			<select name="profileID" id="profileID" class="custom-select">
				<cfif local.qryMerchantProfiles.recordCount eq 0>
					<option value="0" data-gatewayclass="">Choose an option</option>
				</cfif>
				<cfloop query="local.qryMerchantProfiles">
					<option value="#local.qryMerchantProfiles.profileID#" data-gatewayclass="#local.qryMerchantProfiles.gatewayClass#">#local.qryMerchantProfiles.onScreenLabel#</option>
				</cfloop>
			</select>
		</div>
		<div id="assocNewPMCOFForm" class="assocNewPMSection" style="display:none;">
			<div class="addCOFFormTitle pm-mb-4 pm-font-weight-bold">Add Method of Payment:</div>
			<div class="addPayMethodInputFormHolder" style="display:none;"></div>
		</div>
		<div id="assocNewPMPostCOF" class="assocNewPMSection" style="display:none;">
			<div class="addCOFFormTitle pm-text-forestgreen pm-font-weight-bold pm-mb-4">Card Added Successfully</div>
			<div id="addedPayMethodInfo" class="pm-mb-3 pm-cardinfo-box"></div>
		</div>
		<div class="payMethodTiedSchedPayments" style="display:none;">
			<div class="pm-mb-2 pm-mt-3">The method of payment being changed is also tied to the scheduled payments below. You may optionally choose to move those to the new method of payment as well:</div>
			<div class="checkbox-container pm-ml-2"></div>
		</div>
		<div id="assocNewPMProcessingFee" class="assocNewPMSection" style="display:none;"></div>
		<div class="profileErrorForm pm-alert-error" style="display:none;">
			<div class="msg"></div>
			<button id="cancel-button" class="pm-act-button pm-mt-3" onclick="closePayProfileActionForm(this);">Close</button>
		</div>
		<div class="pm-form-footer-actions">
			<button id="next-button" class="pm-nav-buttons pm-act-button pm-act-button-primary pm-mr-1" onclick="toggleStepAssocPM(this,'next');" disabled>Save</button>
			<button id="prev-button" class="pm-nav-buttons pm-act-button pm-mr-1" onclick="toggleStepAssocPM(this,'prev');" style="display:none;">Go Back</button>
			<button id="cancel-button" class="pm-act-button" onclick="closePayProfileActionForm(this);">Cancel</button>
		</div>
	</div>
</script>
<script id="mc_updPayMethodForm" type="text/x-handlebars-template">
	<div class="pm-mx-2 pm-mt-3 pm-mb-4">
		<div class="updateCOFFormTitle pm-mb-4 pm-font-weight-bold">Update Card:</div>
		<div class="profileErrorForm pm-alert-error" style="display:none;">
			<div class="msg"></div>
			<button id="cancel-button" class="pm-act-button pm-mt-3" onclick="closePayProfileActionForm(this);">Close</button>
		</div>
		<div class="updatePayMethodInputFormHolder" style="display:none;"></div>
	</div>
</script>
<script id="mc_removePayMethodForm" type="text/x-handlebars-template">
	<div class="pm-mx-2 pm-mt-3 pm-mb-4">
		<div class="removePayMethodFormTitle pm-mb-4 pm-font-weight-bold">Remove Card:</div>
		<div id="removePMStep1a" class="removePMSteps" style="display:none;">
			<div class="pm-mb-2">Please choose from the options below:</div>
			<div class="checkbox-container pm-radio-group pm-ml-2">
				<label class="remCardStep1Opts"><input type="radio" name="removeMode" value="DELETE"/><span id="remCardStep1Opt1Label">Delete this card from my profile</span></label>
				<label class="remCardStep1Opts"><input type="radio" name="removeMode" value="DETACH"/><span id="remCardStep1Opt2Label">Keep this card, but remove it from scheduled payment(s)</span></label>
			</div>
		</div>
		<div id="removePMStep1b" class="removePMSteps"style="display:none;">
			<div class="pm-alert-warning">
				<span id="permDeleteWarningText">Deleting this card is permanent. Are you sure you want to delete this card?</span>
			</div>
		</div>
		<div id="removePMStep2" class="removePMSteps" style="display:none;">
			<div class="schedPaymentsCheckOptionsToDissociate" style="display:none;">
				<div class="pm-mb-4 dissociateSchedsOptionTitle">The method of payment being removed is tied to the scheduled payment(s) below. You may optionally move other scheduled payments to a new method of payment as well:</div>
				<div class="checkbox-container pm-ml-2"></div>
			</div>
		</div>
		<div id="removePMStep3" class="removePMSteps" style="display:none;">
			<div class="pm-alert-warning pm-mb-4">
				<div id="step2WarningText" class="pm-mb-2">Deleting this card will leave the following upcoming scheduled payment(s) with no method of payment:</div>
				<div class="schedPaymentsListForPayProfile"></div>
			</div>
			<div class="pm-mb-2">
				<div class="pm-mb-2">Please choose from the options below:</div>
				<div class="checkbox-container pm-radio-group pm-ml-2">
					<label class="remCardStep2Opts"><input type="radio" name="assignNewPMForScheds" value="1"/><span id="remCardStep2Opt1Label">Delete card & change method of payment for upcoming scheduled payment(s)</span></label>
					<label class="remCardStep2Opts"><input type="radio" name="assignNewPMForScheds" value="0"/><span id="remCardStep2Opt2Label">Delete card & #"don't"# assign a new method of payment to upcoming scheduled payment(s)</span></label>
				</div>
			</div>
		</div>
		<div id="removePMStep4" class="removePMSteps" style="display:none;">
			<div id="removePMSubstitutePMSelection" class="pm-mt-3 pm-mb-4">
				<input type="hidden" id="substitutePayProfileID" name="substitutePayProfileID" value="0">
				<div class="pm-mb-2">Choose another method of payment:</div>
				<div id="removePMNewPayMethodSelection" class="pm-custom-dropdown pm-mx-2">
					<div class="dropdown-label">Select Pay Method</div>
					<ul class="dropdown-list">
						<li class="dropdown-option" data-value="0"><i class="icon-plus-sign pm-mr-1"></i> Add New Method of Payment</li>
					</ul>
				</div>
			</div>
			<div id="removePMNewPMTypeSelection" class="removePMNewPMSection" style="display:none;">
				<input type="hidden" id="createdPayProfileID" name="createdPayProfileID" value="0">
				<div class="pm-mb-2">Choose a method of payment type to add:</div>
				<div class="pm-d-flex" style="align-items:center;">
					<select name="profileID" id="profileID" class="custom-select" style="margin-bottom:0;">
					</select>
					<button id="continue-button-newpm" class="pm-nav-buttons pm-act-button pm-ml-2" onclick="toggleNewPMSectionsForRemovePM(this,2);">Add</button>
				</div>
			</div>
			<div id="removePMNewPMCOFForm" class="removePMNewPMSection" style="display:none;">
				<div class="addCOFFormTitle pm-mb-4 pm-font-weight-bold">Add Method of Payment:</div>
				<div class="addPayMethodInputFormHolder" style="display:none;"></div>
			</div>
		</div>
		<div id="removePMStep5" class="removePMSteps" style="display:none;"></div>
		<div id="removePMStep6" class="removePMSteps" style="display:none;">
			<div id="removePMNewPMPostCOF" class="removePMStep6Sections" style="display:none;">
				<div class="addCOFFormTitle pm-text-forestgreen pm-font-weight-bold pm-mb-4">Card Added Successfully</div>
				<div id="addedPayMethodInfo" class="pm-mb-3 pm-cardinfo-box"></div>
			</div>
			<div id="removePMSubstitutePMSelectionRO" class="removePMStep6Sections" style="display:none;">
				<div class="pm-mb-2">Choose another method of payment:</div>
				<div class="pm-custom-dropdown pm-custom-dropdown-disabled pm-mx-2 pm-mb-4">
					<div class="dropdown-label">[dropdownlabel]</div>
				</div>
			</div>
		</div>
		<div class="payMethodTiedSchedPayments" style="display:none;">
			<div class="pm-mb-2">Confirm Scheduled Payments for new method of payment:</div>
			<div class="checkbox-container pm-ml-2"></div>
		</div>
		<div class="pm-form-footer-actions">
			<button id="next-button" class="pm-nav-buttons pm-act-button pm-mr-1" onclick="toggleStepRemovePM(this,'next');">Continue</button>
			<button id="prev-button" class="pm-nav-buttons pm-act-button pm-mr-1" onclick="toggleStepRemovePM(this,'prev');" style="display:none;">Go Back</button>
			<button id="cancel-button" class="pm-act-button" onclick="closePayProfileActionForm(this);">Cancel</button>
		</div>
		<div class="profileErrorForm pm-alert-error pm-mt-2" style="display:none;">
			<div class="msg"></div>
		</div>
		<div class="removePayMethodInputFormHolder" style="display:none;"></div>
	</div>
</script>
<script id="mc_addPayMethodForm" type="text/x-handlebars-template">
	<div class="pm-mx-2 pm-mt-3 pm-mb-4">
		<div id="addPMStep1" class="addPMSteps">
			<input type="hidden" id="createdPayProfileID" name="createdPayProfileID" value="0">
			<div class="pm-mb-4 pm-font-weight-bold">Add a New Method of Payment</div>
			<div class="pm-mb-2">Choose a method of payment type to add:</div>
			<select name="profileID" id="profileID" class="custom-select">
				<cfif local.qryMPProfilesForAdd.recordCount eq 0>
					<option value="0" data-gatewayclass="">Choose an option</option>
				</cfif>
				<cfloop query="local.qryMPProfilesForAdd">
					<option value="#local.qryMPProfilesForAdd.profileID#" data-gatewayclass="#local.qryMPProfilesForAdd.gatewayClass#">#local.qryMPProfilesForAdd.onScreenLabel#</option>
				</cfloop>
			</select>
		</div>
		<div id="addPMStep2" class="addPMSteps" style="display:none;">
			<div class="addCOFFormTitle pm-mb-4 pm-font-weight-bold">Add Method of Payment:</div>
			<div class="addPayMethodInputFormHolder" style="display:none;"></div>
		</div>
		<div id="addPMStep3" class="addPMSteps" style="display:none;">
			<div class="addCOFFormTitle pm-text-forestgreen pm-font-weight-bold pm-mb-4">Card Added Successfully</div>
			<div id="addedPayMethodInfo" class="pm-mb-3 pm-cardinfo-box"></div>
			<div class="optAssignToSchedPayments" style="display:none;">
				<div class="pm-mt-4 pm-mb-4">Would you like to tie this method of payment to scheduled payments? Please note that some already have methods of payment assigned to them. You may change those as desired.</div>
				<div class="checkbox-container checkbox-with-sublabel pm-ml-2"></div>
			</div>
		</div>
		<div class="pm-form-footer-actions">
			<button id="continue-button" class="pm-act-button pm-act-button-primary pm-mr-1" onclick="continueStepAddPM(this);" disabled>Continue</button>
			<button id="cancel-button" class="pm-act-button" onclick="closePayProfileActionForm(this);">Cancel</button>
		</div>
		<div class="profileErrorForm pm-alert-error pm-mt-2" style="display:none;">
			<div class="msg"></div>
			<button id="cancel-button" class="pm-act-button pm-mt-3" onclick="closePayProfileActionForm(this);">Close</button>
		</div>
	</div>
</script>
<script id="mc_paymethodInfoCard" type="text/x-handlebars-template">
	<div class="pm-cardbox">
		<div class="pm-d-flex pm-align-items-center">
			<img src="{{iconfullpath}}" style="width:40px;" class="pm-mr-3">
			<div class="pm-d-flex pm-flex-column pm-font-size-lg">
				<div><span class="pm-font-weight-bold">{{cardnumber}}</span>{{##compare expdate '!=' ''}} {{expdate}}{{/compare}}</div>
				<div class="pm-text-gray">{{profileonscreenlabel}}</div>
				<div class="pm-text-gray">Added {{addeddate}}</div>
				{{##compare conditionalSurchargeDiv '!=' ''}}{{{conditionalSurchargeDiv}}}{{/compare}}
			</div>
		</div>
	</div>
</script>
<script id="mc_merchantProfileOptions" type="text/x-handlebars-template">
	<cfif local.qryMerchantProfiles.recordCount eq 0>
		<option value="0" data-gatewayclass="">Choose an option</option>
	</cfif>
	<cfloop query="local.qryMerchantProfiles">
		<option value="#local.qryMerchantProfiles.profileID#" data-gatewayclass="#local.qryMerchantProfiles.gatewayClass#">#local.qryMerchantProfiles.onScreenLabel#</option>
	</cfloop>
</script>
<script id="mc_addProcessingFeeForm" type="text/x-handlebars-template">
	<div class="addProcessingFeeFormContainer">
		<div class="pm-font-size-md pm-font-weight-bold pm-mb-2 pm-border-bottom pm-pb-1">[title]</div>
		<div class="pm-mb-4">[msg]</div>
		<div class="pm-d-flex pm-align-items-center pm-mb-3">
			<img src="[cardiconpath]" style="width:40px;" class="pm-mr-3 cardbox-info-icon">
			<div class="pm-d-flex pm-flex-column">
				<div class="pm-font-weight-bold cardbox-info-detail">**** [carddetail]</div>
				<div class="cardbox-info-exp">Exp [cardexp]</div>
				<div class="pm-font-size-md cardbox-info-nickname">[cardnickname]</div>
			</div>
		</div>
		<div class="pm-d-flex pm-line-height-1 pm-mb-2">
			<span>
				<input type="radio" name="processFeeDonation" id="processFeeDonation_yes" class="pm-procfee-radio" value="1" autocomplete="off" [isChecked]>
			</span>
			<label class="pm-font-size-md pm-m-0 pm-align-self-center pm-ml-1" for="processFeeDonation_yes">[felabel][feLabelExtra]</label>
		</div>
		<div class="pm-d-flex pm-line-height-1">
			<span>
				<input type="radio" name="processFeeDonation" id="processFeeDonation_no" class="pm-procfee-radio" value="0" autocomplete="off">
			</span>
			<label class="pm-font-size-md pm-m-0 pm-align-self-center pm-ml-1" for="processFeeDonation_no">[denylabel]</label>
		</div>
		<div class="pm-d-flex pm-align-items-center pm-mt-3 footerActions">
			<a href="##" class="pm-font-size-sm pm-text-gray pm-text-decoration-none" onclick="closePayProfileActionForm(this);return false;">Go Back</a>
			<div class="pm-ml-auto">
				<button id="save-button" class="pm-act-button pm-act-button-primary" onclick="[fnSaveOnclick]" disabled>Save</button>
			</div>
		</div>
	</div>
</script>
</cfoutput>