<cfset local.alternateGuestLink = arguments.event.getValue('mc_siteinfo.alternateGuestAccountCreationLink','')>

<cfif local.resultsData.qryMemberLocator.recordcount>
	<cfif len(local.alternateGuestLink) GT 0>
		<cfset local.newMemberDispMsg = 'click <a href="#local.alternateGuestLink#">here</a> to enter a new purchaser'>
	<cfelse>	
		<cfset local.newMemberDispMsg = '<a href="javascript:showNAForm();">enter a new purchaser</a>'>
	</cfif>
	
	<!--- get result fields --->
	<cfset local.resultsFieldsetID = local.objLocator.getLocatorFieldsetID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),area='results')>
	<cfset local.xmlResultFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=local.resultsFieldsetID, usage="accountLocaterResults")>
	<div class="row-fluid">
		<div style="margin-top:3px;">
			<cfif local.resultsData.qryMemberLocator.recordcount gt 1>
				Based on your search, the top <cfoutput>#local.resultsData.qryMemberLocator.recordcount#</cfoutput> matches are shown below.
			<cfelse>
				Based on your search, we located one possible match.
			</cfif>
		</div>
	</div>
	<div class="row-fluid">
		<div style="margin-top:2px;">
			<strong>You may also <a href="javascript:showSearchForm();">try your search again</a> or <cfoutput>#local.newMemberDispMsg#</cfoutput>.</strong>
		</div>
		<br/>
	</div>

	
	<cfoutput query="local.resultsData.qryMemberLocator">
		<cfset local.nameFieldFound = false>
		<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
			<cfif ListFindNoCase("m_prefix,m_firstname,m_middlename,m_lastname,m_suffix,m_professionalsuffix",local.thisField.xmlAttributes.fieldcode)>
				<cfif NOT local.nameFieldFound>
					<div><strong>#local.resultsData.qryMemberLocator.mc_combinedName#</strong></div>
					<cfset local.nameFieldFound = true>
				</cfif>
			<cfelseif len(local.resultsData.qryMemberLocator[local.thisField.xmlattributes.fieldLabel][local.resultsData.qryMemberLocator.currentrow])>
				<div><strong>#local.thisField.xmlattributes.fieldLabel#:</strong> #local.resultsData.qryMemberLocator[local.thisField.xmlattributes.fieldLabel][local.resultsData.qryMemberLocator.currentrow]#</div>
			</cfif>
		</cfloop>
		<br />
		<button type="button" class="btn btn-success" style="width:100%;max-width:350px;" onClick="useMember(#memberid#)">Choose</button>

		<hr />
	</cfoutput>

	<br/>
		<a name="#notlisted"></a>
	<fieldset>
		<legend>Not Listed?</legend> 
		<button class="btn btn-info" style="width:100%;max-width:350px;" onclick="showSearchForm();">Search Again</button> <br/><br/>
		<cfoutput>#local.newMemberDispMsg#</cfoutput>
	</fieldset>
	<br/>
	<div>
		<cfoutput>
		<b>For technical assistance, contact #local.storeSupportInfo.supportProviderName#.</b><br/>
		Call #local.storeSupportInfo.supportProviderPhone# or e-mail <a href="mailto:#local.storeSupportInfo.supportProviderEmail#">#local.storeSupportInfo.supportProviderEmail#</a>
		</cfoutput>
	</div>
<cfelse>
	<cfoutput>
	#local.objLocator.displayLocatorNoResultsFound(criteriaMet=local.resultsData.criteriaMet, NApostURL='#arguments.event.getValue("mainregurl")#&regaction=newacct&mode=stream', bypna=arguments.event.getValue('bypna',0))#
	</cfoutput>
</cfif>
