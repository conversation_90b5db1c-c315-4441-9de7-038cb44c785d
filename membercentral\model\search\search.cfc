<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>

		<cfif (
				application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true" 
				or 
				(isdefined("session.enableMobile") and session.enableMobile)
				)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>

		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)> 
			<cfset refreshTSNetworkStatus()>
		</cfif>

		<!--- catch spammy searches and redirect to homepage --->
		<cfif structKeyExists(local.rc,"s_key_all")>
		 	<cfif findNoCase("What can we help you find?", local.rc.s_key_all)
		 		or findNoCase("ORDER BY ", local.rc.s_key_all)
		 		or findNoCase("UNION SELECT", local.rc.s_key_all)
		 		or findNoCase("/UNION/", local.rc.s_key_all)
		 		or findNoCase("/ORDER/", local.rc.s_key_all)
		 		or findNoCase("*/AND/*", local.rc.s_key_all)
		 		or findNoCase("hex(", local.rc.s_key_all)
		 		or findNoCase("print(", local.rc.s_key_all)
		 		or findNoCase("concat(", local.rc.s_key_all)
		 		or findNoCase("write(", local.rc.s_key_all)
		 		or findNoCase("gethostbyname", local.rc.s_key_all)
		 		or findNoCase("echo ", local.rc.s_key_all)
		 		or findNoCase("type and press enter", local.rc.s_key_all)
		 		or findNoCase("sleep(", local.rc.s_key_all)
		 		or findNoCase("WAITFOR ", local.rc.s_key_all)
		 		or findNoCase("CHR(", local.rc.s_key_all)
		 		or findNoCase("CHAR(", local.rc.s_key_all)
		 		or findNoCase("assert(", local.rc.s_key_all)
		 		or findNoCase("1' AND ", local.rc.s_key_all)
		 		or findNoCase("1 AND ", local.rc.s_key_all)
		 		or findNoCase("1zqjqk", local.rc.s_key_all)
		 		or findNoCase("search our site", local.rc.s_key_all)
		 		or findNoCase("search our website", local.rc.s_key_all)
		 		or findNoCase("123456", local.rc.s_key_all)
		 	>
				<cflocation url="/?pg=search" addtoken="No">
		 	</cfif>
		</cfif>

		<cfif arguments.event.getValue('searchType','') eq "quicksearch">
			<cfset redirectOldSearchLinks(arguments.event.getValue('searchType',''),arguments.event,1)>
		<cfelseif arguments.event.getValue('searchType','') eq "documentBank">
			<cfset redirectOldSearchLinks(arguments.event.getValue('searchType',''),arguments.event,1)>
		<cfelse>
			<cfset local.returnStruct = structNew()>	

			<cfset local.returnStruct.searchAction = event.getValue('s_a','')>
			<cfset local.returnStruct.adID = arguments.event.getValue('adID',0)>

			<!--- get bucket list and current bucket info --->
			<cfset local.objBucket = CreateObject("component","model.search.bucket")>						
			<cfset local.returnStruct.qryBuckets = getSiteBuckets(local.rc.mc_siteinfo.siteID)>
			<cfset local.returnStruct.hasDepositionBucket = getBucketIDByType(siteID=local.rc.mc_siteinfo.siteID, bucketType='Depositions')>
	
			<!--- get bucketID and verify if passed in --->
			<cfset local.returnStruct.intBucketID = verifyBucketID(local.returnStruct.qryBuckets,arguments.event,int(val(event.getValue('bid',0))))>

			<cfif local.returnStruct.intBucketID>
				<cfset arguments.event.setValue('bid',local.returnStruct.intBucketID)>
			<cfelse>
				<cfset event.setValue('s_a','NoKnownBucket')>
				<cfset local.returnStruct.searchAction = "NoKnownBucket">
			</cfif>

			<cfset local.returnStruct.qryBucketInfo = local.objBucket.getBucketInfo(local.returnStruct.intBucketID)>
			
			<!--- get searchID and verify if passed in --->
			<cfset local.returnStruct.intSearchID = verifySearchID(int(val(event.getValue('sid',0))))>
	
			<!--- if verified searchid is different than passed in searchid, redirect so page refreshes dont keep creating a new searchid --->
			<cfif local.returnStruct.intSearchID neq int(val(event.getValue('sid',0)))>
				<cflocation url="/?#replaceNoCase(cgi.query_string,'&sid=#event.getValue('sid')#','')#&sid=#local.returnStruct.intSearchID#" addtoken="no">
			</cfif>
	
			<!--- load cached counts --->
			<cfif local.returnStruct.intSearchID gt 0 and local.returnStruct.searchAction eq "doSearch">
				<cfset local.returnStruct.qryCachedCounts = getCachedBucketCounts(local.returnStruct.intSearchID)>
			</cfif>
	
			<!--- check existance of bucket cfc --->
			<cftry>
				<cfset local.returnStruct.objCurrentBucket = CreateObject("component","model.search.#local.returnStruct.qryBucketInfo.bucketCFC#")>
			<cfcatch type="any">
				<cfif cfcatch.message contains "Could not find the ColdFusion Component model.search" 
					OR cfcatch.message contains "model.search.">
					<cfset event.setValue('s_a','NoKnownBucket')>
				<cfelse>
					<cfrethrow>
				</cfif>
			</cfcatch>
			</cftry>
			
			<!--- show bucket list? --->
			<cfif local.returnStruct.searchAction eq "NoKnownBucket" OR local.returnStruct.searchAction eq "OtherTools">
				<cfset local.returnStruct.bitShowBucketList = false>
			<cfelse>
				<cfset local.returnStruct.bitShowBucketList = true>
			</cfif>
	
			<!--- save search if necessary --->
			<cfif local.returnStruct.searchAction eq "doSearch" and event.getValue('s_frm',0) is 1>
				<cfset local.returnStruct.intSearchID = local.returnStruct.objCurrentBucket.saveSearchForm(local.rc.mc_siteinfo.siteID,local.rc)>
				<cfset local.tmpQS = cgi.query_string>
				<cfif NOT structKeyExists(url,'s_a')>
					<cfset local.tmpQS = listAppend(local.tmpQS,'s_a=#local.returnStruct.searchAction#','&')>
				</cfif>
				<!--- bid here to support BIDs passed in form fields not url string --->
				<cfif NOT structKeyExists(url,'bid')>
					<cfset local.tmpQS = listAppend(local.tmpQS,'bid=#event.getValue('bid')#','&')>
				</cfif>
				<!--- ensure s_frm is no longer in URL --->
				<cfif structKeyExists(url,'s_frm')>
					<cfset local.tmpQS = replaceNoCase(local.tmpQS,'&s_frm=#event.getValue('s_frm')#','')>
				</cfif>
				<cflocation url="/?#local.tmpQS#&sid=#local.returnStruct.intSearchID#" addtoken="no">
			</cfif>
	
			<!--- save search into session for 'return to search' links --->
			<cfif local.returnStruct.searchAction eq "doSearch">
				<cfset session.mcstruct.lastSearch = { sid=local.returnStruct.intSearchID, bid=local.returnStruct.qryBucketInfo.bucketID } >
			</cfif>
	
			<!--- record webhit --->
			<cfset application.objPlatformStats.recordAppHit(appname="search",appsection="bucket#local.returnStruct.qryBucketInfo.bucketID#")>

			<cfset local.showAds = getShowAds(local.rc.mc_siteinfo.orgCode)>	
			
			<cfset local.returnStruct.BucketTypesForSearchReport = getBucketTypesForSearchReport()>
	
			<!--- decisis redirect --->
			<cfif local.returnStruct.searchAction eq "doCaseLawSearch" and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>

				<!--- record app hit --->
				<cfset application.objPlatformStats.recordAppHit(appname="caselaw",appsection="caselaw")>

				<!--- redirect to Decisis --->
				<cfset local.data = local.returnStruct.objCurrentBucket.redirectToDecisis(siteCode=local.rc.mc_siteinfo.sitecode, searchID=local.returnStruct.intSearchID, bucketID=local.returnStruct.qryBucketInfo.bucketID, viewDirectory=local.viewDirectory)>

				<cfreturn returnAppStruct(local.data,"echo")>

			<!--- one click purchase billing info --->
			<cfelseif local.returnStruct.searchAction eq "oneClickPurchaseBillingInfo" and application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
				<cfif structKeyExists(local.returnStruct.objCurrentBucket,"handleOneClickPurchase")>
					<cfset local.data = local.returnStruct.objCurrentBucket.handleOneClickPurchase(event=arguments.event)>
				<cfelse>
					<cfset local.data = showBillingInfo(bucketID=local.returnStruct.qryBucketInfo.bucketID)>
				</cfif>

				<cfreturn returnAppStruct(local.data,"echo")>

			<cfelse>
				<cfif local.returnStruct.searchAction eq "otherTools">
					<cfset local.returnStruct.strOtherTools = showOtherTools(local.returnStruct.qryBucketInfo.bucketID, local.rc.mc_siteInfo.orgCode, int(val(local.returnStruct.adID)))>
				</cfif>
				<cfset local.returnStruct.strTopBanner = getTopBanner(s_a=local.returnStruct.searchAction, bucketID=local.returnStruct.qryBucketInfo.bucketID, searchID=local.returnStruct.intSearchID)>

				<!--- check tax info --->
				<cfset local.billingState = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
				<cfset local.billingZip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>
				<cfset local.returnStruct.hasMissingTaxInfo = len(local.billingState) eq 0 OR len(local.billingZip) eq 0>

				<cfset local.viewToUse = "search/#local.viewDirectory#/searchApp">
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.returnStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="refreshTSNetworkStatus" access="public" output="no" returntype="void">
		<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).loginNetworkCode eq "TrialSmith" and (
			not application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithAllowed')
			or not application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithPending')
			or not application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithExpired')
			or not application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='TrialSmithNoPlan')
			)>
			<cfif not structKeyExists(session.cfcuser, "TrialSmithStatusLastRefreshed") or not isDate(session.cfcuser.TrialSmithStatusLastRefreshed) or datediff("s",session.cfcuser.TrialSmithStatusLastRefreshed,now()) gt 60>
				<cfset session.cfcuser.TrialSmithStatusLastRefreshed= now()>
				<cfset application.objUser.refreshTrialSmithSubscription(cfcuser=session.cfcuser, orgCode=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgCode)>
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="getShowAds" access="public" output="no" returntype="numeric">
		<cfargument name="orgCode" type="string">
	
		<cfset var local = structNew()>

		<cfset local.showAds = 0>
		
		<!--- Left the extra returned values in case we ever want to restrict to old site/new site, launch/not launched --->
		<cfquery name="local.qryShowAds" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			select tla.state, tla.shortname, tla.description, tla.isPortalCreated, tla.isPortalLaunched, tla.isLiveOnNewPlatform,
				case when exists (select s.siteID from membercentral.dbo.sites as s where s.sitecode = tla.state) then 1 else 0 end as isCreatedOnNewPlatform,
				count(sat.orgCode) as adCount
			from depotla as tla
			left outer join search.dbo.tblSearchAdsTLA sat 
				inner join search.dbo.tblSearchAds sa on sa.searchAdID = sat.searchAdID 
				inner join membercentral.dbo.cms_siteResources sr on sr.siteResourceID = sa.siteResourceID 
					and sr.siteResourceStatusID = 1
				on sat.orgCode = tla.state
			where tla.state = <cfqueryparam value="#arguments.orgCode#" cfsqltype="CF_SQL_VARCHAR">
			group by tla.state, tla.shortname, tla.description, tla.isPortalCreated, tla.isPortalLaunched, tla.isLiveOnNewPlatform, tla.url
			order by tla.state
		</cfquery>
		
		<cfif int(val(local.qryShowAds.adCount)) GT 0>
			<cfset local.showAds = 1>
		</cfif>
		
		<cfreturn local.showAds>
	</cffunction>
	
	<cffunction name="getCachedBucketCounts" access="public" returntype="query" output="false">
		<cfargument name="searchID" required="yes" type="numeric">
		
		<cfset var qryStats = "">

		<cfstoredproc procedure="up_getCachedBucketCountsBySearchID" datasource="#application.dsn.tlasites_search.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.searchID#" null="no">
			<cfprocresult name="qryStats" resultset="1">
		</cfstoredproc>

		<cfreturn qryStats>
	</cffunction>
	
	<cffunction name="verifyBucketID" access="public" output="no" returntype="numeric">
		<cfargument name="qryOrgBuckets" required="yes" type="query">
		<cfargument name="event" type="any">
		<cfargument name="bucketID" required="yes" type="numeric">
		
		<cfset var local = structNew()>
		<cfif arguments.bucketID gt 0>
			<cfquery name="local.qryCheck" dbtype="query" maxrows="1">
				select bucketID
				from arguments.qryOrgBuckets
				where bucketID = #arguments.bucketID#
			</cfquery>
			
			<cfif local.qryCheck.recordcount is 1>
				<cfreturn local.qryCheck.bucketID>
			<cfelse>
				<cfreturn 0>
			</cfif>
		<cfelseif arguments.bucketID is 0 and len(arguments.event.getValue('searchType',''))>
			<!--- if we are here, the bucketID is not valid on the site (or there isnt one defined). Attempt to redirect to the right spot --->
			<cfset redirectOldSearchLinks(arguments.event.getValue('searchType'),arguments.event,0)>
		<cfelseif arguments.bucketID is 0 and arguments.qryOrgBuckets.recordcount>
			<!--- return first bucketID--->
			<cfreturn arguments.qryOrgBuckets.bucketID />
		</cfif>
		<cfreturn 0>
	</cffunction>

	<cffunction name="verifySearchID" access="public" output="no" returntype="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		
		<cfset var local = structNew()>
		
		<cfif arguments.searchID gt 0>
			<cfset local.qrySearch = application.objSearchTranslate.getSearchFromSearchID(arguments.searchID)>
			
			<!--- if the saved search was a guest and current user is a guest, return searchID --->
			<cfif not len(local.qrySearch.profileID) and session.cfcuser.memberData.profileID is 0>
				<cfreturn val(local.qrySearch.searchID)>

			<!--- if users match and saved search was logged in, return searchid --->
			<cfelseif local.qrySearch.profileID is session.cfcuser.memberData.profileID and local.qrySearch.loggedin is 1>
				<cfreturn local.qrySearch.searchID>
			
			<!--- otherwise create new search and return it --->
			<cfelse>
				<cfset local.newSearchID = copySearch(
					searchID=arguments.searchID,
					depoMemberDataID=session.cfcuser.memberData.depoMemberDataID,
					profileID=session.cfcuser.memberdata.profileID,
					statsSessionID=session.cfcuser.statsSessionID,
					loggedIn=application.objUser.isLoggedIn(cfcuser=session.cfcuser),
					disabled=application.objUser.isSuperUser(cfcuser=session.cfcuser))>
				<cfreturn local.newSearchID>
			</cfif>
		<cfelse>
			<cfreturn 0>
		</cfif>
	</cffunction>

	<cffunction name="copySearch" access="private" output="no" returntype="numeric">
		<cfargument name="searchID" required="yes" type="numeric">
		<cfargument name="depoMemberDataID" required="yes" type="numeric">
		<cfargument name="profileID" required="yes" type="numeric">
		<cfargument name="statsSessionID" required="yes" type="numeric">
		<cfargument name="loggedIn" required="yes" type="boolean">
		<cfargument name="disabled" required="yes" type="boolean">
	
		<cfset var local = structNew()>
	
		<cfstoredproc procedure="up_copySearch" datasource="#application.dsn.tlasites_search.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.searchID#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statsSessionID#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.loggedIn#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.disabled#" null="no">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1" null="no">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newSearchID" null="no">
		</cfstoredproc>
	
		<cfreturn local.newSearchID>
	</cffunction>
	
	<cffunction name="getSiteBuckets" access="public" output="no" returntype="query">
		<cfargument name="siteID" required="yes" type="string">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryMemberGroups" datasource="#application.dsn.membercentral.dsn#">
			select distinct groupID 
			from dbo.cache_members_groups
			where memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">
			order by groupID
		</cfquery>

		<cfquery name="local.qryBuckets" datasource="#application.dsn.tlasites_search.dsn#">
			SELECT *
			FROM (
				SELECT sb.bucketID, sb.bucketTypeID, sb.hideUntilSearched, sbg.bucketGroup, sb.bucketName, sbt.bucketCFC, sbt.bucketType, isnull(sb.bucketSettings,'<settings/>') as bucketSettings, 
					isnull(sb.restrictToGroupID,0) as restrictToGroupID, sb.hideIfRestricted, sbg.bucketGroupOrder, sb.bucketOrder,
					case when isnull(sb.restrictToGroupID,0) in (<cfqueryparam value="0#valueList(local.qryMemberGroups.groupID)#" cfsqltype="CF_SQL_INTEGER" list="yes">) OR membercentral.dbo.fn_isSuperUser(<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">) = 1 then 1
					else 0 end as isMemberInRestrictedGroup
				FROM dbo.tblSearchBucketTypes AS sbt 
				INNER JOIN dbo.tblSearchBuckets AS sb ON sbt.bucketTypeID = sb.bucketTypeID 
				INNER JOIN dbo.tblSearchBucketGroups AS sbg ON sb.bucketGroupID = sbg.bucketGroupID
				WHERE sb.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
				AND sbt.isActive = 1
				AND sb.isActive = 1
			) tmp
			WHERE hideIfRestricted = 0 OR isMemberInRestrictedGroup = 1
			ORDER BY bucketGroupOrder, bucketOrder, bucketName
		</cfquery>
		
		<cfreturn local.qryBuckets>
	</cffunction>
	
	<cffunction name="getBucketID" access="public" output="no" returntype="numeric">
		<cfargument name="siteID" required="yes" type="string">

		<cfset var local = structNew() />
		<cfset local.bucketList = getSiteBuckets(arguments.siteID) />

		<cfreturn val(local.bucketList.bucketID)>
	</cffunction>
	
	<cffunction name="getBucketIDByName" access="public" output="no" returntype="numeric">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketName" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryBuckets" datasource="#application.dsn.tlasites_search.dsn#">
			SELECT bucketID
			FROM dbo.tblSearchBuckets
			WHERE siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND bucketName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.bucketName#">
			AND isActive = 1
		</cfquery>

		<cfreturn local.qryBuckets.bucketID>
	</cffunction>
	
	<cffunction name="getBucketIDByType" access="public" output="no" returntype="numeric">
		<cfargument name="siteID" required="yes" type="numeric">
		<cfargument name="bucketType" required="yes" type="string">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryBuckets" datasource="#application.dsn.tlasites_search.dsn#" cachedWithin="#createtimespan(0,1,0,0)#">
			SELECT bucketID
			FROM dbo.tblSearchBuckets sb
			inner join dbo.tblSearchBucketTypes sbt on sb.bucketTypeID = sbt.bucketTypeID and sbt.isActive = 1
				AND sbt.bucketType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.bucketType#">
			WHERE sb.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			AND sb.isActive = 1
		</cfquery>

		<cfreturn val(local.qryBuckets.bucketID)>
	</cffunction>

	<cffunction name="isExpertSearch" access="public" returntype="boolean" output="false">
		<cfargument name="searchID" required="yes" type="numeric">
		
		<cfset var local = structNew()>

		<cfset local.qrySearch = getSearchVerbose(arguments.searchID)>
		<cfif len(local.qrySearch.s_fname) or len(local.qrySearch.s_lname)>
			<cfset local.isExpertSearch = true>
		<cfelse>
			<cfset local.isExpertSearch = false>
		</cfif>
		
		<cfreturn local.isExpertSearch>
	</cffunction>

	<cffunction name="getSearchVerbose" access="public" output="no" returntype="query">
		<cfargument name="searchID" required="yes" type="numeric">
		
		<cfset var local = structNew()>

		<cfquery name="local.qrySearchParam" datasource="#application.dsn.tlasites_search.dsn#">
			select 
				sh.dateEntered, 
				sh.searchVerbose,
				sb.bucketName,
				sbt.bucketType,
				b.value('.','varchar(50)') as bid,
				s1.value('.','varchar(50)') as s_applicationinstanceid,
				s2.value('.','varchar(50)') as s_fname,
				s3.value('.','varchar(50)') as s_key_all,
				s4.value('.','varchar(50)') as s_key_one,
				s5.value('.','varchar(50)') as s_key_phrase,
				s6.value('.','varchar(50)') as s_key_x,
				s7.value('.','varchar(50)') as s_lname,
				case
					when len(s2.value('.','varchar(50)')) > 1 and len(s7.value('.','varchar(50)')) > 0 then s2.value('.','varchar(50)') + ' ' + s7.value('.','varchar(50)')
					when len(s2.value('.','varchar(50)')) > 1 and len(s7.value('.','varchar(50)')) = 0 then s2.value('.','varchar(50)')
					when len(s2.value('.','varchar(50)')) = 0 and len(s7.value('.','varchar(50)')) > 0 then s7.value('.','varchar(50)')
				end as combinedTxt
			from dbo.tblSearchHistory sh
			inner join tblSearchBuckets sb on sh.bucketIDorigin = sb.bucketID
			inner join tblSearchBucketTypes sbt on sbt.bucketTypeID = sb.bucketTypeID and sbt.isActive = 1
			outer apply searchXML.nodes('/search/bid') AS x1(b)
			outer apply searchXML.nodes('/search/s_applicationinstanceid') AS x2(s1)
			outer apply searchXML.nodes('/search/s_fname') AS x3(s2)
			outer apply searchXML.nodes('/search/s_key_all') AS x4(s3)
			outer apply searchXML.nodes('/search/s_key_one') AS x5(s4)
			outer apply searchXML.nodes('/search/s_key_phrase') AS x6(s5)
			outer apply searchXML.nodes('/search/s_key_x') AS x7(s6)
			outer apply searchXML.nodes('/search/s_lname') AS x8(s7)
			where sh.searchID = <cfqueryparam value="#arguments.searchID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qrySearchParam>		
	</cffunction>

	<!--- these functions handle the redirect from old links to the new search templates --->
	<cffunction name="redirectOldSearchLinks" access="public" returntype="void" output="false">
		<cfargument name="searchType" required="yes" type="string">
		<cfargument name="event" type="any">
		<cfargument name="gotoResults" required="yes" type="boolean">

		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset local.qryOrgBuckets = getSiteBuckets(local.rc.mc_siteinfo.siteID)>

		<cfif arguments.searchtype eq "quicksearch">
			<cfset local.searchtype = event.getValue('qsearchType','')>
		<cfelse>
			<cfset local.searchtype = arguments.searchType>
		</cfif>

		<cfheader name="X-Robots-Tag" value="nofollow, noindex">

		<cfswitch expression="#local.searchtype#">
			<cfcase value="website">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Website')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.website").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="nameappearsview">
				<cfset local.bid = redirect_getBIDByName(local.qryOrgBuckets,'Related Testimony')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.depositions").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="depositions,name,inquiryview,Interragatoriesview,backgroundView">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Depositions')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.depositions").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="documentbank,documentbankfolders">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'DocumentBank')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.CourtDocuments").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="mdex">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Mdex')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.mdex").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="briefsView">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'DocumentBank')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.CourtDocuments").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="similarSearchView">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Searches')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.Searches").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="listservview">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Listservers')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.lyris").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="discActions">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'DisciplinaryActions')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.DisciplinaryActions").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="caselawview">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Decisis')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset redirect_BID(local.bid)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="myDocumentsView">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'MyDocuments')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.MyDocuments").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="jrc">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'FileShare2')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.MyDocuments").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="semweb">
				<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'SeminarWeb')>
				<cfif local.bid gt 0 and arguments.gotoResults>
					<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
					<cfset local.searchID = CreateObject("component","model.search.SeminarWeb").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
					<cfset redirect_SID(local.bid,local.searchID)>
				<cfelseif local.bid gt 0>
					<cfset redirect_BID(local.bid)>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfcase>
			<cfcase value="uploadDocs">
				<cflocation url="/?pg=uploadDocuments" addtoken="no">
			</cfcase>
			<cfcase value="verdictreports">
				<cflocation url="/?pg=myDocuments&tab=PV" addtoken="no">
			</cfcase>
			<cfcase value="contributed">
				<cflocation url="/?pg=myDocuments&tab=CD" addtoken="no">
			</cfcase>
			<cfcase value="purchaseddocs,downloads,accountSummary">
				<cflocation url="/?pg=myDocuments&tab=PD" addtoken="no">
			</cfcase>
			<cfcase value="memberUpgrade,memberNotAllowed">
				<cflocation url="/?pg=upgradeTrialSmith" addtoken="no">
			</cfcase>
			<cfdefaultcase>
				<cfif not len(arguments.searchType)>
					<cfset local.bid = redirect_getBID(local.qryOrgBuckets,'Depositions')>
					<cfif local.bid gt 0 and arguments.gotoResults>
						<cfset local.strForm = redirect_formToStruct(local.bid,arguments.event)>
						<cfset local.searchID = CreateObject("component","model.search.depositions").saveSearchForm(local.rc.mc_siteinfo.siteID,local.strForm)>
						<cfset redirect_SID(local.bid,local.searchID)>
					<cfelseif local.bid gt 0>
						<cfset redirect_BID(local.bid)>
					<cfelse>
						<cfset redirect_unknown(arguments.event.getCollection())>
					</cfif>
				<cfelse>
					<cfset redirect_unknown(arguments.event.getCollection())>
				</cfif>
			</cfdefaultcase>
		</cfswitch>
	</cffunction>

	<cffunction name="redirect_getBID" access="private" returntype="numeric" output="no">
		<cfargument name="qryOrgBuckets" required="yes" type="query">
		<cfargument name="bucketType" required="yes" type="string">
		<cfset var local = structNew()>
		<cfquery name="local.qryGetBID" dbtype="query" maxrows="1">
			select bucketID
			from arguments.qryOrgBuckets
			where bucketType = '#arguments.bucketType#'
			and hideUntilSearched = 0
			order by bucketID
		</cfquery>
		<cfreturn val(local.qryGetBID.bucketID)>
	</cffunction>

	<cffunction name="redirect_getBIDByName" access="private" returntype="numeric" output="no">
		<cfargument name="qryOrgBuckets" required="yes" type="query">
		<cfargument name="bucketName" required="yes" type="string">

		<cfset var local = structNew()>

		<cfquery name="local.qryGetBID" dbtype="query" maxrows="1">
			select bucketID
			from arguments.qryOrgBuckets
			where bucketName = '#arguments.bucketName#'
			order by bucketID
		</cfquery>

		<cfreturn val(local.qryGetBID.bucketID)>
	</cffunction>

	<cffunction name="redirect_formToStruct" access="private" returntype="struct" output="no">
		<cfargument name="bid" required="yes" type="numeric">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local["bid"] = arguments.bid>
		<cfif len(trim(arguments.event.getValue('searchfirstname','')))>
			<cfset local["s_fname"] = trim(arguments.event.getValue('searchfirstname',''))>
		<cfelseif len(trim(arguments.event.getValue('firstname','')))>
			<cfset local["s_fname"] = trim(arguments.event.getValue('firstname',''))>
		<cfelse>
			<cfset local["s_fname"] = "">
		</cfif>
		<cfif len(trim(arguments.event.getValue('searchlastname','')))>
			<cfset local["s_lname"] = trim(arguments.event.getValue('searchlastname',''))>
		<cfelseif len(trim(arguments.event.getValue('lastname','')))>
			<cfset local["s_lname"] = trim(arguments.event.getValue('lastname',''))>
		<cfelse>
			<cfset local["s_lname"] = "">
		</cfif>
		<cfset local["s_ownership"] = arguments.event.getValue('bankid','')>
		<cfset local["s_type"] = arguments.event.getValue('documentTypeID','')>
		<cfset local["s_cause"] = arguments.event.getValue('caseTypeID','')>
		<cfif arguments.event.getValue('searchType','') eq "briefsView" or arguments.event.getValue('qsearchType','') eq "briefsView">
			<cfset local["s_key_all"] = trim(local["s_fname"] & " " & local["s_lname"] & " " & arguments.event.getValue('searchRequired','') & " " & arguments.event.getValue('searchRequiredAllNames',''))>
		<cfelse>
			<cfset local["s_key_all"] = trim(arguments.event.getValue('searchRequired','') & " " & arguments.event.getValue('searchRequiredAllNames',''))>
		</cfif>
		<cfset local["s_key_one"] = arguments.event.getValue('searchRequiredAtLestOne','')>
		<cfset local["s_key_phrase"] = arguments.event.getValue('searchRequiredPhrase','')>
		<cfset local["s_key_x"] = trim(arguments.event.getValue('searchWithout','') & " " & arguments.event.getValue('searchWithoutPhrase',''))>

		<cfreturn local>
	</cffunction>

	<cffunction name="redirect_SID" access="private" returntype="void" output="no">
		<cfargument name="bid" required="yes" type="numeric">
		<cfargument name="sid" required="yes" type="numeric">
		<cflocation url="/?pg=search&bid=#arguments.bid#&s_a=doSearch&sid=#arguments.sid#" addtoken="no">
	</cffunction>

	<cffunction name="redirect_BID" access="private" returntype="void" output="no">
		<cfargument name="bid" required="yes" type="numeric">
		<cflocation url="/?pg=search&bid=#arguments.bid#" addtoken="no">
	</cffunction>

	<cffunction name="redirect_unknown" access="private" returntype="void" output="no">
		<cfargument name="eventArgs" required="yes" type="struct">
		<cflocation url="/?pg=search" addtoken="no">
	</cffunction>

	<cffunction name="getTopBanner" access="public" output="no" returntype="struct" hint="This is a separate fn since spiders may call this directly without proper params">
		<cfargument name="s_a" required="no" type="string">
		<cfargument name="bucketID" required="no" type="string" hint="string to support no value (1st time on page)">
		<cfargument name="searchID" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["resultArr"] = ArrayNew(1)>
		<cfset local.returnStruct["success"] = false>

		<cfset local.isValidArguments = true>
		
		<cfif 
			(NOT StructKeyExists(arguments, "s_a") OR NOT len(trim(arguments.s_a))) OR 
			(NOT StructKeyExists(arguments, "bucketID") OR NOT len(trim(arguments.bucketID))) OR 
			(NOT StructKeyExists(arguments, "searchID") OR NOT IsNumeric(arguments.searchID))>
				<cfset local.isValidArguments = false>
		</cfif>

		<cfif local.isValidArguments>
			<cfset local.returnStruct = generateTopBanner(s_a=arguments.s_a, bucketID=arguments.bucketID, searchID=arguments.searchID)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="generateTopBanner" access="private" output="no" returntype="struct">
		<cfargument name="s_a" required="yes" type="string">
		<cfargument name="bucketID" required="yes" type="string" hint="string to support no value (1st time on page)">
		<cfargument name="searchID" required="yes" type="numeric">

		<cfset var local = structNew()>

		<cfset local.qryCartCount = CreateObject("component","model.viewcart.viewcart").getDocuments(
			depomemberdataid=session.cfcuser.memberData.depoMemberDataID,
			membertype=val(application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='memberType')),
			billingstate=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingState'),
			billingzip=application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID='4', key='billingZip'),
			orgcode=session.mcStruct.siteCode)>

		<cfset local.qryBucketInfo = CreateObject("component","bucket").getBucketInfo(bucketID=val(arguments.bucketID))>

		<cfif val(local.qryBucketInfo.hideUntilSearched)>
			<cfset local.qrySearch = getSearchVerbose(arguments.searchID)>
			<cfset local.bucketIDForSearchForm = local.qrySearch.bid>
		<cfelse>
			<cfset local.bucketIDForSearchForm = arguments.bucketID>
		</cfif>

		<cfset local.cart = {
				tab: "VC", label: "Doc Cart", shortlabel: "Cart", url: "/?pg=viewCart", count=local.qryCartCount.recordcount, icon="icon-shopping-cart", showOnMobile=true,
				tablink: "<a href=""javascript:cart_loadInline();""><img src=""/assets/common/images/search/cart.png"" width=""16"" height=""16"" border=""0"" align=""absmiddle""> Doc Cart (#local.qryCartCount.recordcount#)</a>"				
			}>
		<cfset local.searchMap = {
				tab: "SM", label: "Search Map", shortlabel: "Map", url: "/?pg=search", icon="icon-compass", showOnMobile=false,
				tablink: "<a href=""/?pg=search""><img src=""/assets/common/images/search/compass.png"" width=""16"" height=""16"" border=""0"" align=""absmiddle""> Search Map</a>"				
			}>
		<cfset local.newSearch = {
				tab: "NS", label: "New Search", shortlabel: "New", url: "/?pg=search&bid=#local.bucketIDForSearchForm#", icon="icon-search", showOnMobile=false,
				tablink: "<a href=""/?pg=search&bid=#local.bucketIDForSearchForm#""><img src=""/assets/common/images/search/magnifier.png"" width=""16"" height=""16"" border=""0"" align=""absmiddle""> New Search</a>"				
			}>
		<cfset local.refineSearch = {
				tab: "RS", label: "Refine Search", shortlabel: "Refine", url: "/?pg=search&bid=#local.bucketIDForSearchForm#&sid=#arguments.searchID#&s_a=refineSearch", icon="icon-filter", showOnMobile=true,
				tablink: "<a href=""/?pg=search&bid=#local.bucketIDForSearchForm#&sid=#arguments.searchID#&s_a=refineSearch""><img src=""/assets/common/images/search/magnifier_zoom_in.png"" width=""16"" height=""16"" border=""0"" align=""absmiddle""> Refine Search</a>"				
			}>
		<cfset local.searchResults = {
				tab: "SR", label: "Search Results", shortlabel: "Results", url: "/?pg=search&bid=#arguments.bucketID#&s_a=doSearch&sid=#arguments.searchID#", icon="icon-file-text", showOnMobile=false,
				tablink: "<a href=""/?pg=search&bid=#arguments.bucketID#&s_a=doSearch&sid=#arguments.searchID#""><img src=""/assets/common/images/search/page_white_text.png"" width=""16"" height=""16"" border=""0"" align=""absmiddle""> Search Results</a>"				
			}>
		<cfset local.myDocuments = {
				tab: "MD", label: "My Purchased Docs", shortlabel: "Purchases", url: "/?pg=myDocuments&tab=PD", icon="icon-save", showOnMobile=false,
				tablink: "<a href=""/?pg=myDocuments&tab=PD""><i class='icon-file-text' style='vertical-align: inherit;'></i> My Purchased Docs</a>"
			}>

		<cfset local.returnArr = arrayNew(1)>
		
		<!--- if search performed --->
		<cfif (arguments.SearchID gt 0 and listFindNoCase('doSearch,ajx',arguments.s_a))>
			<cfset ArrayAppend(local.returnArr, local.searchResults)>
			<cfset ArrayAppend(local.returnArr, local.newSearch)>
			<cfset ArrayAppend(local.returnArr, local.refineSearch)>

			<!--- if search performed and items in cart --->
			<cfif local.qryCartCount.recordcount gt 0>
				<cfset ArrayAppend(local.returnArr, local.cart)>
			</cfif>
		<!--- if no search performed but items in cart --->
		<cfelseif local.qryCartCount.recordcount gt 0>
			<cfset ArrayAppend(local.returnArr, local.newSearch)>
			<cfset ArrayAppend(local.returnArr, local.cart)>
		<!--- if no bucket passed in --->
		<cfelseif len(arguments.bucketID) is 0>
			<cfset ArrayAppend(local.returnArr, local.searchMap)>
		<!--- else just new search --->
		<cfelse>
			<cfset ArrayAppend(local.returnArr, local.newSearch)>
		</cfif>

		<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).loginNetworkCode eq "TrialSmith">
			<cfset ArrayAppend(local.returnArr, local.myDocuments)>
		</cfif>

		<cfset local.returnStruct = { resultArr=local.returnArr, success=true }>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAppLink" access="public" output="false" returntype="string">
		<cfargument name="applicationInstanceID" type="numeric" required="FALSE" default="#this.appInstanceID#">
		<cfargument name="siteID" type="numeric" required="FALSE" default="#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteID#">

		<cfreturn getAppBaseLink(arguments.applicationInstanceID,arguments.siteID)>
	</cffunction>

	<cffunction name="showOtherTools" access="public" output="yes" returntype="string">
		<cfargument name="bucketID" required="yes" type="numeric">
		<cfargument name="orgCode" required="yes" type="string">
		<cfargument name="adID" required="no" type="numeric" default=0>
		
		<cfset var local = StructNew()>
		<cfset local.stHTML = "">
		
		<cfif arguments.adID GT 0>
			<cfset local.objSearchAd = CreateObject('component', 'model.admin.ads.ads')> 
			<cfset local.currAdData = local.objSearchAd.getAdContent(arguments.adID)>
		</cfif>
		
		<cfquery name="local.qrySearchAds" datasource="#application.dsn.membercentral.dsn#">
			select a.adID, a.adname, a.adShortText, a.imageUrl, CONVERT(VARCHAR, a.dateCreated, 101) as dateCreated, '1' as statusID
			from dbo.ad_ads a
			inner join dbo.ad_types t on t.adTypeID = a.adTypeID
				and t.adTypeName = 'Search Ad'
			inner join dbo.ad_adPlacements ap on a.adID = ap.adID
			inner join dbo.ad_zones z on z.zoneID = ap.zoneID
				and z.siteCode = <cfqueryparam value="#arguments.orgCode#" cfsqltype="CF_SQL_VARCHAR">
			inner join dbo.ad_zoneTypes zt on zt.zoneTypeID = z.zoneTypeID
				and zt.zoneTypeName = 'Search'
			where a.status = 'A'
			order by a.adName
		</cfquery>	
		
		<cfif arguments.adID>
			<cfquery name="local.qryThisSearchAd" dbtype="query">
				select adID, adname, adShortText, imageUrl, dateCreated, statusID
				from [local].qrySearchAds
				where adID = <cfqueryparam value="#arguments.adID#" cfsqltype="cf_sql_integer">	
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.stHTML">
			<cfoutput>
			<table cellspacing="0" cellpadding="0" width="100%">
			<tr valign="top">
				<td class="tsAppBodyText tsAppBR50" width="210" style="padding:8px 10px 8px 8px;">
					<div id="bk_list">
						<div style="font-weight:bold;padding:0 0 4px 0;"><span style="float:right;"></span>Other Tools</div>
						<cfloop query="local.qrySearchAds">
						<div id="bk_#local.qrySearchAds.adID#_" style="padding:1px 0;">
							<span id="bk_#local.qrySearchAds.adID#_cnt" style="float:right;height:18px;"></span><a href="/?pg=search&bid=#arguments.bucketID#&s_a=otherTools&adID=#local.qrySearchAds.adID#" class="tsAppBodyText"><img src="/assets/common/images/search/lightbulb.png" width="19" height="16" align="absmiddle" border="0" />
							<cfif local.qrySearchAds.adID eq arguments.adID><b>#local.qrySearchAds.adShortText#</b><cfelse>#local.qrySearchAds.adShortText#</cfif></a><br clear="all"/>
						</div>
						</cfloop>
						<br/>
					</div>

					<cfif arguments.adID and fileExists(ExpandPath("#local.qryThisSearchAd.imageUrl#"))>
						<div id="divAdImage" name="divAdImage" align="center">
							<img src="#local.qryThisSearchAd.imageUrl#?cb=#getTickCount()#" alt="Ad Image" border="0">
						</div>
					</cfif>
				</td>
				<td class="tsAppBodyText" style="padding:8px 8px 8px 10px;" id="bk_content">
					<cfif arguments.adID GT 0>
						<cfif len(trim(local.currAdData.adContentObject))>
							#local.currAdData.adContentObject#
						<cfelse>
							[There is no content for this tool]
						</cfif>
					<cfelse>
						Please select an item on the left for information about TrialSmith's litigation tools.
					</cfif>
				</td>
			</tr>
			</table>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn trim(local.stHTML)>
	</cffunction>	
	
	<cffunction name="getNextAdImage" access="public" output="yes" returntype="void">
		<cfargument name="bucketID" required="yes" type="string">
		<cfargument name="orgCode" required="yes" type="string">

		<cfscript>
			var local = StructNew();
			
			local.firstAdID = 0;
			local.secondAdID = 0;
			local.adIDToUse1 = 0;
			local.adIDToUse2 = 0;
			
			if (NOT isDefined('session.currAdRotation'))
				local.currAdOrder = 0;
			else
				local.currAdOrder = session.currAdRotation;
		</cfscript>
		
		<!--- get all the possible ads --->
		<cfquery name="local.qrySearchAds" datasource="#application.dsn.tlasites_search.dsn#">
			select sa.searchAdID, satFilter.adOrder
			from dbo.tblSearchAds sa
			inner join membercentral.dbo.cms_siteResources sr on sr.siteResourceID = sa.siteResourceID 
				and sr.siteResourceStatusID = 1
			inner join dbo.tblSearchAdsTLA satFilter on satFilter.searchAdID = sa.searchAdID 
				and satFilter.orgCode = <cfqueryparam value="#arguments.orgCode#" cfsqltype="CF_SQL_VARCHAR">
			order by satFilter.adOrder
		</cfquery>
		
		<cfloop query="local.qrySearchAds">		
			<cfif local.firstAdID is 0 and fileexists(application.paths.localUserAssetRoot.path & "common/searchAds/" & local.qrySearchAds.searchAdID & ".jpg")>
				<cfset local.firstAdID = local.qrySearchAds.searchAdID>
			</cfif>
			
			<cfif local.secondAdID is 0 and local.firstAdID GT 0 and local.firstAdID neq local.qrySearchAds.searchAdID and 
					fileexists(application.paths.localUserAssetRoot.path & "common/searchAds/" & local.qrySearchAds.searchAdID & ".jpg")>
				<cfset local.secondAdID = local.qrySearchAds.searchAdID>
			</cfif>
			
			<cfif local.adIDToUse1 is 0 and
					(local.qrySearchAds.adOrder GT local.currAdOrder) AND fileexists(application.paths.localUserAssetRoot.path & "common/searchAds/" & local.qrySearchAds.searchAdID & ".jpg")>
				<cfset local.adIDToUse1 = local.qrySearchAds.searchAdID>
			</cfif>

			<cfif local.adIDToUse2 is 0 and local.adIDToUse1 GT 0 and (local.adIDToUse1 neq local.qrySearchAds.searchAdID) and
					(local.qrySearchAds.adOrder GT local.currAdOrder) AND fileexists(application.paths.localUserAssetRoot.path & "common/searchAds/" & local.qrySearchAds.searchAdID & ".jpg")>
				<cfset local.adIDToUse2 = local.qrySearchAds.searchAdID>
			</cfif>
		</cfloop>
		
		<cfif local.adIDToUse1 GT 0 and local.adIDToUse2 GT 0>
			<!--- both got set, use them as they are --->
			<cfset local.finalIDToUse = local.adIDToUse2>
		<cfelseif local.adIDToUse1 GT 0 and local.adIDToUse2 is 0>
			<!--- adIDToUse1 got the end of the list, adIDToUse2 needs to be setto beginning --->
			<cfset local.adIDToUse2 = local.firstAdID>
			<cfset local.finalIDToUse = local.adIDToUse2>
		<cfelseif local.adIDToUse1 is 0 and local.adIDToUse2 is 0>
			<!--- neither got set, either no images or previous set was at the end of the list --->
			<cfset local.adIDToUse1 = local.firstAdID>
			<cfset local.adIDToUse2 = local.secondAdID>
			
			<cfset local.finalIDToUse = local.adIDToUse2>
		</cfif>
		
		<cfquery dbtype="query" name="local.qryAdOrderToUse">
			select adOrder
			from [local].qrySearchAds
			where searchAdID = #local.finalIDToUse#
		</cfquery>
		<cfset session.currAdRotation = local.qryAdOrderToUse.adOrder>
		
		<cfoutput>
			<cfif local.adIDToUse1 GT 0>
				<a href="/?pg=search&bid=#arguments.bucketID#&s_a=otherTools&adID=#local.adIDToUse1#"><img src="/userassets/common/searchAds/#local.adIDToUse1#.jpg?cb=#getTickCount()#" alt="Ad Image" border=0></a>
			</cfif>
			<cfif local.adIDToUse2 GT 0 and local.adIDToUse2 neq local.adIDToUse1>
				<br /><br />
				<a href="/?pg=search&bid=#arguments.bucketID#&s_a=otherTools&adID=#local.adIDToUse2#"><img src="/userassets/common/searchAds/#local.adIDToUse2#.jpg?cb=#getTickCount()#" alt="Ad Image" border=0></a>
			</cfif>
		</cfoutput>
	</cffunction>
	
	<cffunction name="getOptions" access="public" output="no" returntype="query">
		<cfargument name="contentTitle" required="false" default="Trialsmith_Options_UploadDocuments_Step1" type="string">

		<cfset var qryOptions = "">
		
		<cfquery name="qryOptions" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select top 1 c.contentID, c.siteResourceID, cv.rawContent
			from membercentral.dbo.admin_toolTypes tt
			inner join membercentral.dbo.admin_sitetools st on st.toolTypeID = tt.toolTypeID
				and tt.toolType = 'SearchAdsAdmin'
			inner join dbo.sites s on s.siteID = st.siteID
				and s.siteCode = 'TS'
			inner join membercentral.dbo.cms_siteResources sr on sr.siteResourceID = st.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join membercentral.dbo.cms_siteResources contentSR on contentSR.parentSiteResourceID = sr.siteResourceID
				and contentSR.siteResourceStatusID = 1
			inner join membercentral.dbo.cms_content c on c.siteResourceID = contentSR.siteResourceID
			inner join dbo.cms_contentLanguages cl on c.contentID = cl.contentID
				and cl.contentTitle = <cfqueryparam value="#arguments.contentTitle#" cfsqltype="CF_SQL_VARCHAR">
			inner join dbo.cms_languages l on l.languageID = cl.languageID
			inner join dbo.cms_contentVersions cv on cv.contentLanguageID = cl.contentLanguageID
			order by cv.contentVersionID desc;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn qryOptions>
	</cffunction>

	<cffunction name="getDepoSearchBucketID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" required="yes" type="numeric">
		
		<cfset var qryGetBucketID = "">
		
		<cfquery name="qryGetBucketID" datasource="#application.dsn.tlasites_search.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteid#">;

			SELECT sb.bucketID AS bucketid
			FROM dbo.tblSearchBuckets AS sb
			INNER JOIN dbo.tblSearchBucketGroups AS bg on bg.bucketGroupID = sb.bucketGroupID
			INNER JOIN dbo.tblSearchBucketTypes bt ON bt.bucketTypeID = sb.bucketTypeID AND bt.isActive = 1
			WHERE sb.siteID = @siteID
				and sb.isActive = 1
				and bt.bucketType = 'Depositions'
				and 1 = CASE WHEN (SELECT min(bucketOrder) FROM dbo.tblSearchBuckets 
							WHERE siteID = @siteID
							AND bucketGroupID = sb.bucketGroupID) = sb.bucketOrder
					THEN 1 ELSE 0 END;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn val(qryGetBucketID.bucketid)>
	</cffunction>

	<cffunction name="getBucketTypesForSearchReport" access="private" returntype="query" output="no">
		<cfset var local = structNew()>
		<cfquery name="local.qryGetBucketTypes" datasource="#application.dsn.tlasites_search.dsn#" cachedwithin="#createTimeSpan(0,1,0,0)#">
			select bucketTypeID, bucketType
			from tblSearchBucketTypes
			where bucketType in ('Listservers','Depositions','Mdex','Searches','DocumentBank','DisciplinaryActions','Introduction')
		</cfquery>
		<cfreturn local.qryGetBucketTypes>
	</cffunction>

	<cffunction name="showBillingInfo" access="private" output="false" returntype="string">
		<cfargument name="bucketID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.cfcuser_depomemberdataid = val(session.cfcuser.memberData.depoMemberDataID)>
		<cfset local.qryTSMemberData = application.objMember.getTSMemberData(depoMemberDataID=local.cfcuser_depomemberdataid)>
		
		<cfquery name="local.qryCCInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT payProfileID
			FROM dbo.ccMemberPaymentProfiles
			WHERE depoMemberDataID = <cfqueryparam value="#local.cfcuser_depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
			AND orgcode = 'TS'
			AND declined = 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.qryStates = application.objCommon.getTSStates()>
		<cfset local.user_billingstate = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
		<cfset local.user_billingzip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>
		<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.user_billingzip, billingState=local.user_billingstate)>
		<cfif NOT local.strBillingZip.isvalidzip>
			<cfset local.user_billingzip = "">
		</cfif>
		
		<cfset local.showBillingContactScreen = NOT len(local.user_billingstate) OR NOT len(local.user_billingzip) OR NOT len(local.qryTSMemberData.Email)>
		<cfset local.showCCScreen = local.qryTSMemberData.paymenttype EQ "C" AND NOT local.qryCCInfo.recordCount>
		<cfset local.showIntroScreen = local.showBillingContactScreen OR local.showCCScreen>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/search/responsive/buckets/billingInfo.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
</cfcomponent>