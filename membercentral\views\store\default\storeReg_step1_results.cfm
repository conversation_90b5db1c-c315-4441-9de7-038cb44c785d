<cfset local.alternateGuestLink = arguments.event.getValue('mc_siteinfo.alternateGuestAccountCreationLink','')>

<cfif local.resultsData.qryMemberLocator.recordcount>
	<cfif len(local.alternateGuestLink) GT 0>
		<cfset local.newMemberDispMsg = 'click <a href="#local.alternateGuestLink#">here</a> to enter a new purchaser'>
	<cfelse>	
		<cfset local.newMemberDispMsg = '<a href="javascript:showNAForm();">enter a new purchaser</a>'>
	</cfif>
	
	<!--- get result fields --->
	<cfset local.resultsFieldsetID = local.objLocator.getLocatorFieldsetID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),area='results')>
	<cfset local.xmlResultFields = CreateObject("component","model.system.platform.memberFieldsets").getMemberFieldsXML(fieldsetid=local.resultsFieldsetID, usage="accountLocaterResults")>

	<div class="tsAppBodyText" style="margin-top:3px;">
		<cfif local.resultsData.qryMemberLocator.recordcount gt 1>
			Based on your search, the top <cfoutput>#local.resultsData.qryMemberLocator.recordcount#</cfoutput> matches are shown below.
		<cfelse>
			Based on your search, we located one possible match.
		</cfif>
	</div>
	<div class="tsAppBodyText" style="margin-top:2px;">
		<strong>You may also <a href="javascript:showSearchForm();">try your search again</a> or <cfoutput>#local.newMemberDispMsg#</cfoutput>.</strong>
	</div>
	<br/>

	<table cellpadding="4" cellspacing="0">
	<tr>
		<td class="tsAppBR50">&nbsp;</td>
		<cfset local.nameFieldFound = false>
		<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
			<cfif ListFindNoCase("m_prefix,m_firstname,m_middlename,m_lastname,m_suffix,m_professionalsuffix",local.thisField.xmlAttributes.fieldcode)>
				<cfif NOT local.nameFieldFound>
					<td class="tsAppBG tsAppBodyText tsAppBB50 tsAppBT50"><b>Name</b></td>
					<cfset local.nameFieldFound = true>
				</cfif>
			<cfelse>
				<td class="tsAppBG tsAppBodyText tsAppBB50 tsAppBT50"><cfoutput><b>#htmlEditFormat(local.thisField.xmlattributes.fieldLabel)#</b></cfoutput></td>
			</cfif>
		</cfloop>
		<td class="tsAppBL50">&nbsp;</td>
	</tr>

	<cfoutput query="local.resultsData.qryMemberLocator">
		<tr valign="top">
			<td class="tsAppBR50"><button type="button" class="tsAppBodyButton" onClick="useMember(#local.resultsData.qryMemberLocator.memberid#)">Choose</button></td>
			
			<cfset local.nameFieldFound = false>
			<cfloop array="#local.xmlResultFields.xmlRoot.xmlChildren#" index="local.thisField">
				<cfif ListFindNoCase("m_prefix,m_firstname,m_middlename,m_lastname,m_suffix,m_professionalsuffix",local.thisField.xmlAttributes.fieldcode)>
					<cfif NOT local.nameFieldFound>
						<td class="tsAppBodyText tsAppBB20">#local.resultsData.qryMemberLocator.mc_combinedName#&nbsp;</td>
						<cfset local.nameFieldFound = true>
					</cfif>
				<cfelse>
					<td class="tsAppBodyText tsAppBB20">#local.resultsData.qryMemberLocator[local.thisField.xmlattributes.fieldLabel][local.resultsData.qryMemberLocator.currentrow]#&nbsp;</td>
				</cfif>
			</cfloop>
			<td class="tsAppBL50">&nbsp;</td>
		</tr>
	</cfoutput>

	</table>
	<br/>
	<div class="tsAppBodyText">
		<cfoutput>
		<b>For technical assistance, contact #local.storeSupportInfo.supportProviderName#.</b><br/>
		Call #local.storeSupportInfo.supportProviderPhone# or e-mail <a href="mailto:#local.storeSupportInfo.supportProviderEmail#">#local.storeSupportInfo.supportProviderEmail#</a>
		</cfoutput>
	</div>

<cfelse>
	<cfoutput>
	#local.objLocator.displayLocatorNoResultsFound(criteriaMet=local.resultsData.criteriaMet, NApostURL='#arguments.event.getValue("mainregurl")#&regaction=newacct&mode=stream', bypna=arguments.event.getValue('bypna',0))#
	</cfoutput>
</cfif>
