<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// for this tool, it needs to be the siteResourceID of the site, not the websiteAdmin tool.
			// should be the websiteAdmin tool for addSite,insertSite
			if (NOT listFindNoCase("addSite,insertSite",arguments.event.getValue('mca_ta')))
				this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
		</cfscript>
		
		<cfscript>
			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.edit = buildCurrentLink(arguments.event,"edit");
			this.link.saveSettings = buildCurrentLink(arguments.event,"saveSettings");
			this.link.addSite = buildCurrentLink(arguments.event,"addSite");
			this.link.addSiteLog = buildCurrentLink(arguments.event,"addSiteLog");
			this.link.insertSite = buildCurrentLink(arguments.event,"insertSite");
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.saveFeature = buildCurrentLink(arguments.event,"saveFeature");
			this.link.codeDeploy = buildCurrentLink(arguments.event,"codeDeploy");
			this.link.enableBetaAccess = buildCurrentLink(arguments.event,"enableBetaAccess");
			this.link.disableBetaAccess = buildCurrentLink(arguments.event,"disableBetaAccess");
			this.link.extendBetaAccess = buildCurrentLink(arguments.event,"extendBetaAccess");

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
		
	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objWebsite = CreateObject("component","model.admin.website.website");
			local.objAdmin = CreateObject('component', 'model.admin.admin');
			local.permsGotoLink = local.objAdmin.buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			local.enableBetaAccessLink = this.link.enableBetaAccess;
			local.disableBetaAccessLink = this.link.disableBetaAccess;
			local.extendBetaAccessLink = this.link.extendBetaAccess;

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.ManageAdvancedSettings = checkRights(arguments.event,'ManageAdvancedSettings');
			local.security.editSite = checkRights(arguments.event,'editSite');
			if (NOT local.security.editSite)
				application.objCommon.redirect('#this.link.message#&message=1');

			// GET DATA --------------------------------------------------------------------------------- ::
			local.qryNetworks = local.objWebsite.getNetworks();
			local.qryLanguages = local.objWebsite.getLanguages();
			local.qryTimeZones = local.objWebsite.getTimeZones();
			local.qryWebsite = local.objWebsite.getSettings(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'));
			local.qryDomains = local.objWebsite.getDomains(val(local.qryWebsite.siteID));
			local.qryMainHostName = local.objWebsite.getMainHost(val(local.qryWebsite.siteID));
			local.qryFeatures = local.objWebsite.getFeatures(val(local.qryWebsite.siteID));
			local.qryFastCaseRedirect = application.objSiteResource.getApplicationInstanceFromPageName(pageName='fastCaseRedirect', siteID=arguments.event.getValue('mc_siteinfo.siteID'));
			if (local.qryFastCaseRedirect.recordCount) {
				local.ssocompany = xmlSearch(local.qryFastCaseRedirect.settingsXML,'string(/settings/setting[@name="ssocompany"]/@value)');
				local.ssooffset = xmlSearch(local.qryFastCaseRedirect.settingsXML,'string(/settings/setting[@name="ssooffset"]/@value)');
				local.ssomultiplier = xmlSearch(local.qryFastCaseRedirect.settingsXML,'string(/settings/setting[@name="ssomultiplier"]/@value)');
			}
			local.qryLoginLimitModes = local.objWebsite.getLoginLimitModes();
			// Default Paramaters ----------------------------------------------------------------------- ::
			arguments.event.paramValue('tab','site');
			
			local.featuresList = valueList(local.qryFeatures.toolType);

			local.gridRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=websiteJSON&mode=stream&meth=";
			local.domainListLink = "#local.gridRootLink#getDomainList";

			// User Logins
			local.editLoginPolicyLink = buildCurrentLink(arguments.event,"editLoginPolicy") & '&mode=direct';
			local.editLoginPolicyMethodLink = buildCurrentLink(arguments.event,"editLoginPolicyMethod") & '&mode=direct';
			local.saveUserLoginSettingsLink = buildCurrentLink(arguments.event,"saveUserLoginSettings");
			local.loginPoliciesListLink = "#local.gridRootLink#getLoginPolicies";
			
			// Webhooks
			local.webhookURLsLink = "#local.gridRootLink#getSiteWebhooks";
			local.addWebhookLink = buildCurrentLink(arguments.event,"addWebhook") & '&mode=direct';
			local.editWebhookLink = buildCurrentLink(arguments.event,"editWebhook") & '&mode=stream';
			local.websiteAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='WebsiteAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteID'));
			local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.websiteAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteID'));
			local.canManageWebhook = val(local.websiteAdminRights.ManageWebhook);

			if (listFind(local.featuresList,"APIAccess")) {
				local.apiTokensListLink = "#local.gridRootLink#getApiTokens";
				local.editAPITokenLink = "#buildCurrentLink(arguments.event,"editAPIToken")#&mode=stream";
				local.qrySiteAPIAccessFees = local.objWebsite.getSiteAPIAccessFees(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'));
				local.showEstimatedMonthlyBillingAPI = local.qrySiteAPIAccessFees.recordCount AND local.qrySiteAPIAccessFees.noFee EQ 0;
				if (local.showEstimatedMonthlyBillingAPI) {
					local.showEstimatedMonthlyBillingAPILink = "#buildCurrentLink(arguments.event,"showEstimatedMonthlyBillingAPI")#&mode=direct";
				}
			}
			if (local.security.ManageAdvancedSettings is 1) {
				local.editDomainLink = buildCurrentLink(arguments.event,"editDomain") & '&mode=direct';
				local.deleteDomainLink = buildCurrentLink(arguments.event,"deleteDomain") & '&mode=stream';
			}

			if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
				local.qrySendGridSubUserDomains = local.objWebsite.getSendGridSubUserDomains(siteID=arguments.event.getValue('mc_siteinfo.siteID'));

				local.universalRolesListLink = "#local.gridRootLink#getUniversalRoles";
				local.addRoleAssignmentLink = buildCurrentLink(arguments.event,"addRoleAssignment") & '&mode=stream';
				local.sendGridSubUsersLink = "#local.gridRootLink#getSendGridSubUsers";
				local.sendGridSubUserDomainDNSEntriesLink = "#local.gridRootLink#getSendGridSubUserDomainDNSEntries";

				local.strDepoTLA = local.objWebsite.getTrialSmithInfo(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'));
			}

			local.objOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector");
			local.strOrgIdentitySelector = local.objOrgIdentitySelector.getOrgIdentitySelector(orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="frm_defaultOrgIdentityID", selectedValueID=local.qryWebsite.defaultOrgIdentityID, allowBlankOption=false);
			local.strLoginOrgIdentitySelector = local.objOrgIdentitySelector.getOrgIdentitySelector(orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="frm_loginOrgIdentityID", selectedValueID=local.qryWebsite.loginOrgIdentityID, allowBlankOption=false);

			local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages");
			local.pageSectionFtdImgConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.event.getValue('mc_siteinfo.siteID'), referenceType="websitePageSection");
			local.pageFtdImgConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.event.getValue('mc_siteinfo.siteID'), referenceType="websitePage");
			local.qrySiteFeaturedImageConfigs = local.objFeaturedImages.getFeaturedImageConfigsForSite(siteID=arguments.event.getValue('mc_siteInfo.siteID'), excludeEmpty=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_website.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addSite" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<!--- extra check -- this should only be accessed from MC --->
		<cfif arguments.event.getValue('mc_siteinfo.sitecode') NEQ "MC">
			<cflocation url="#this.link.edit#" addtoken="no">
		</cfif>
		
		<!--- permission check --->
		<cfif NOT checkRights(arguments.event,'addSite')>
			<cflocation url="#this.link.message#&message=1" addtoken="no">
		</cfif>

		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.qryTimeZones = local.objWebsite.getTimeZones()>
		<cfset local.qryStates = application.objCommon.getStates()>
		<cfset local.qryOrganizations = local.objWebsite.getOrganizations()>
		<cfset local.qryNetworks = local.objWebsite.getNetworks()>
		<cfset local.qryCurrencyTypes = local.objWebsite.getCurrencyTypes()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_addwebsite.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="addSiteLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_addwebsiteLog.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getSiteLog" access="public" output="false" returntype="struct">
		<cfargument name="uid" type="uuid" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfstoredproc procedure="admin_getSiteCreationLog" datasource="#application.dsn.datatransfer.dsn#">
				<cfprocparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.uid#">
				<cfprocresult name="local.qryLog" resultset="1">
			</cfstoredproc>
			
			<cfset local.data.isDone = false>
			<cfsavecontent variable="local.data.loghtml">
				<cfoutput query="local.qryLog">
					<div><span class="d">#timeformat(local.qryLog.stepDate,"h:mm:ss:L")#</span> <span class="m">#local.qryLog.stepMsg#</span></div>
					<cfif local.qryLog.stepMsg eq "Site creation completed">
						<cfset local.data.isDone = true>
					</cfif>
				</cfoutput>
			</cfsavecontent>
			<cfset local.data.loghtml = application.objCommon.minText(local.data.loghtml)>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertSite" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<!--- extra check -- this should only be accessed from MC --->
		<cfif arguments.event.getValue('mc_siteinfo.sitecode') NEQ "MC">
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.location.href = '#this.link.edit#';
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
		
		<!--- permission check --->
		<cfif NOT checkRights(arguments.event,'addSite')>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.location.href = '#this.link.message#&message=1';
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>

		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>

		<cftry>
			<!--- validations --->
			<cfset local.validate = structNew()>
			<!--- Force orgcode and sitecode to be uppercase --->
			<cfset arguments.event.setValue('frm_siteCode',ucase(arguments.event.getValue('frm_siteCode')))>
			<cfif arguments.event.valueExists('frm_orgCode')>
				<cfset arguments.event.setValue('frm_orgCode',ucase(arguments.event.getValue('frm_orgCode')))>
			</cfif>

			<cfset local.validate.doesSiteCodeExist = local.objWebsite.doesSiteCodeExist(arguments.event.getValue('frm_siteCode'))>
			<cfif arguments.event.getValue('frm_orgChoice') eq "new">
				<cfset local.validate.doesOrgCodeExist = local.objWebsite.doesOrgCodeExist(arguments.event.getValue('frm_orgCode'))>
			</cfif>
	
			<cfif local.validate.doesSiteCodeExist>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.location.href = '#this.link.message#&message=2';
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo")>
			<cfelseif arguments.event.getValue('frm_orgChoice') eq "new" and local.validate.doesOrgCodeExist>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.location.href = '#this.link.message#&message=4';
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo")>
			<cfelse>
				<cfsetting requesttimeout="3500">
				<cfset local.strResult = local.objWebsite.insertSite(arguments.event)>
				<cfsavecontent variable="local.data">
					<cfoutput>
					Site has been added.
					</cfoutput>
				</cfsavecontent>
				<cfreturn returnAppStruct(local.data,"echo")>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					top.location.href = '#this.link.message#&message=5';
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.data = "";
			local.objWebsite = CreateObject("component","model.admin.website.website");

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSite = checkRights(arguments.event,'editSite');
			if(NOT local.security.editSite){
				application.objCommon.redirect('#this.link.message#&message=1');
			}

			// update Site Settings --------------------------------------------------------------------- ::
			local.loadSiteSettings = local.objWebsite.updateSite(arguments.event);
			
			if( application.objUser.isSuperUser(cfcuser=session.cfcuser) ) {
				if( arguments.event.getValue('frm_newMainHostID') neq arguments.event.getValue('frm_oldMainHostID') ){
					local.objWebsite.updateNewMainHost(arguments.event.getValue('mc_siteinfo.siteid'),arguments.event.getValue('frm_newMainHostID'),arguments.event.getValue('frm_oldMainHostID'));
				}
			}

			// build redirect URL -----------------------------------------------------------------------
			application.objCommon.redirect(this.link.edit);
		</cfscript>
	</cffunction>

	<cffunction name="saveUserLoginSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// security
			local.security.editSite = checkRights(arguments.event,'editSite');
			if (NOT local.security.editSite) {
				application.objCommon.redirect('#this.link.message#&message=1');
			}

			// update site user login settings 
			local.loadSiteSettings = CreateObject("component","website").updateSiteUserLoginSettings(event=arguments.event);
			
			// build redirect URL
			application.objCommon.redirect("#this.link.edit#&tab=userlogins");
		</cfscript>
	</cffunction>

	<cffunction name="editDomain" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.security.ManageAdvancedSettings = checkRights(arguments.event,'ManageAdvancedSettings')>

		<cfif local.security.ManageAdvancedSettings is 1>
			<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
			<cfset local.qrySiteRedirects = local.objWebsite.getSiteRedirects(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
			<cfset local.qryDomain = local.objWebsite.getDomainDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), hostNameID=arguments.event.getValue('hid',0))>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_editDomain.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput><b>You do not have rights to this area of Control Panel.</b></cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveDomain" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="hostNameID" type="numeric" required="true">
		<cfargument name="hostName" type="string" required="true">
		<cfargument name="useRedirect" type="string" required="true">

		<cfscript>
			var local = structNew();
			local.returnStruct = structNew();

			local.tmpRights = buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID);
			try{


			if(structKeyExists(local.tmpRights,"editSite") and structKeyExists(local.tmpRights,"ManageAdvancedSettings") and local.tmpRights.editSite is 1 and local.tmpRights.ManageAdvancedSettings is 1){
				local.objWebsite = CreateObject("component","website");
				local.isReservedHostname = local.objWebsite.isReservedHostName(nameToCheck=arguments.hostName);
				local.doesMainHostNameExist = local.objWebsite.doesMainHostNameExist(nameToCheck=arguments.hostName, hostNameID=arguments.hostNameID)

				if(arguments.hostNameID is 0 and not local.isReservedHostname and not local.doesMainHostNameExist){
					switch("#application.MCEnvironment#"){
						case "beta": 
						case "newbeta" : 
							local.internalIPAddress = '';
							local.hasSSL = 1;
							local.sslCertFileName = 'mc-wildcard.membercentral.com.chained.crt';
							local.sslPrivateKeyFileName = 'mc-wildcard.membercentral.com.privatekey';
							break;
						case 'production':
							local.internalIPAddress = '***********';
							local.hasSSL = 1;
							local.sslCertFileName = 'mc-wildcard.membercentral.com.chained.crt';
							local.sslPrivateKeyFileName = 'mc-wildcard.membercentral.com.privatekey';
							break;
						default:
							local.internalIPAddress = '';
							local.hasSSL = 0;
							local.sslCertFileName = '';
							local.sslPrivateKeyFileName = '';
							break;
					}

					local.returnStruct.hostNameID = local.objWebsite.insertDomain(siteID=arguments.mcproxy_siteID, hostName=arguments.hostName, 
					useRedirect=arguments.useRedirect, internalIPAddress=local.internalIPAddress, hasSSL=local.hasSSL, 
					sslCertFileName=local.sslCertFileName, sslPrivateKeyFileName=local.sslPrivateKeyFileName)

					local.returnStruct.success = true;
				} else if(arguments.hostNameID gt 0 and not local.doesMainHostNameExist){
					local.qryHostDetails = local.objWebsite.getDomainDetails(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostnameID);
					local.environment = local.objWebsite.getEnvironment(environmentID=local.qryHostDetails.environmentID);
					local.internalIPAddress = local.qryHostDetails.internalIPAddress;
					
					switch("#local.environment#"){
						case "beta": 
						case "newbeta" : 
							local.hasSSL = 1;
							local.sslCertFileName = 'mc-wildcard.membercentral.com.chained.crt';
							local.sslPrivateKeyFileName = 'mc-wildcard.membercentral.com.privatekey';
							break;
						case 'production':
							local.hasSSL = 1;
							local.sslCertFileName = 'mc-wildcard.membercentral.com.chained.crt';
							local.sslPrivateKeyFileName = 'mc-wildcard.membercentral.com.privatekey';
							break;
						default:
							local.hasSSL = 0;
							local.sslCertFileName = '';
							local.sslPrivateKeyFileName = '';
							break;
					}

					local.objWebsite.updateDomain(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostNameID, hostName=arguments.hostName, 
					useRedirect=arguments.useRedirect, internalIPAddress=local.internalIPAddress, hasSSL=local.hasSSL, 
					sslCertFileName=local.sslCertFileName, sslPrivateKeyFileName=local.sslPrivateKeyFileName)

					local.returnStruct.success = true;
				} else local.returnStruct.success = false;
			} else local.returnStruct.success = false;
		} catch(any){
			application.objError.sendError(cfcatch=cfcatch);
			local.returnStruct.success = false;
		}
		</cfscript>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addHostnameToCloudflare" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="hostnameID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success": false }>

		<cftry>
			<cfset local.objWebsite = CreateObject("component","website")>

			<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"ManageAdvancedSettings") or local.tmpRights.ManageAdvancedSettings is not 1 or application.MCEnvironment neq "Production">
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.qryHostName = local.objWebsite.getDomainDetails(siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID, hostNameID=arguments.hostnameID)>
			<cfif local.qryHostName.recordcount is 1>
				<cfset local.strCustomHostName = { 
					"hostname": local.qryHostName.hostname, 
					"ssl": {
						"method": "http",
						"type": "dv",
						"settings": {
							"http2": "on",
							"min_tls_version": "1.2",
							"tls_1_3": "on",
							"ciphers": [],
							"early_hints": "on"
						},
						"bundle_method": "ubiquitous",
						"wildcard": false
					} 
				}>
				<cfset local.apiResult = application.objCloudflare.createCustomHostname(zoneId="74e80848392f8237b4b01860fe77590d", customhostname=local.strCustomHostName)>

				<!--- note we are not updating cloudflare_sslvalidation_url or cloudflare_sslvalidation_body here since ssl will be initializing and not be returned in this response --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateHostname">
					UPDATE dbo.siteHostNames
					SET cloudflare_customHostnameID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.apiResult.data.result.id#">,
						cloudflare_hostnamestatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.apiResult.data.result.status#">,
						cloudflare_sslstatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.apiResult.data.result.ssl.status#">,
						cloudflare_httpownershipverfication_url = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.apiResult.data.result.ownership_verification_http.http_url#">,
						cloudflare_httpownershipverfication_body = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.apiResult.data.result.ownership_verification_http.http_body#">
					WHERE hostNameID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryHostName.hostNameID#">
				</cfquery>

				<cfset local.strReturn.success = true>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="recheckCloudflareHostnameStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="hostnameID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success": false }>

		<cftry>
			<cfset local.objWebsite = CreateObject("component","website")>
			<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"ManageAdvancedSettings") or local.tmpRights.ManageAdvancedSettings is not 1 or application.MCEnvironment neq "Production">
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.qryHostName = local.objWebsite.getDomainDetails(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostnameID)>
			<cfif len(local.qryHostName.cloudflare_customHostnameID)>
				<cfset local.strCustomHostName = { 
					"ssl": {
						"method": "http",
						"type": "dv",
						"settings": {
							"http2": "on",
							"min_tls_version": "1.2",
							"tls_1_3": "on",
							"ciphers": ["ECDHE-RSA-AES128-GCM-SHA256","AES128-SHA"],
							"early_hints": "on"
						},
						"bundle_method": "ubiquitous",
						"wildcard": false
					} 
				}>

				<cfset local.apiResult = application.objCloudflare.editCustomHostname(zoneId="74e80848392f8237b4b01860fe77590d", hostnameId=local.qryHostName.cloudflare_customHostnameID, customhostname=local.strCustomHostName)>
				<cfif local.apiResult.data.result.len() and local.apiResult.data.result.id EQ local.qryHostName.cloudflare_customHostnameID>
					<cfset local.strArgs = { "hostNameID":local.qryHostName.hostNameID, "hostnamestatus":"", "sslstatus":"", "sslvalidation_url":"", 
						"sslvalidation_body":"", "httpownershipverfication_url":"", "httpownershipverfication_body":"" }>
					<cfif local.apiResult.data.result.keyExists('status')>
						<cfset local.strArgs["hostnamestatus"] = local.apiResult.data.result.status>
					</cfif>
					<cfif local.apiResult.data.result.keyExists('ssl') AND local.apiResult.data.result.ssl.keyExists('status')>
						<cfset local.strArgs["sslstatus"] = local.apiResult.data.result.ssl.status>
					</cfif>
					<cfif local.apiResult.data.result.keyExists('ssl') AND local.apiResult.data.result.ssl.keyExists('validation_records') AND arrayLen(local.apiResult.data.result.ssl.validation_records)>
						<cfset local.strArgs["sslvalidation_url"] = local.apiResult.data.result.ssl.validation_records[1].http_url>
						<cfset local.strArgs["sslvalidation_body"] = local.apiResult.data.result.ssl.validation_records[1].http_body>
					</cfif>
					<cfif local.apiResult.data.result.keyExists('ownership_verification_http')>
						<cfset local.strArgs["httpownershipverfication_url"] = local.apiResult.data.result.ownership_verification_http.http_url>
						<cfset local.strArgs["httpownershipverfication_body"] = local.apiResult.data.result.ownership_verification_http.http_body>
					</cfif>

					<cfset local.objWebsite.updateHostnameDetails(argumentCollection=local.strArgs)>

					<cfset local.strReturn.success = true>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="refreshHostnameDataFromCloudflare" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="hostnameID" type="numeric" required="true">
		<cfargument name="isScheduledTask" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success": false }>

		<cftry>
			<cfif application.MCEnvironment neq "Production">
				<cfthrow message="invalid request">
			<cfelseif not arguments.isScheduledTask>
				<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
				<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
				<cfif not structKeyExists(local.tmpRights,"ManageAdvancedSettings") or local.tmpRights.ManageAdvancedSettings is not 1>
					<cfthrow message="invalid request">
				</cfif>
			</cfif>
			
			<cfset local.objWebsite = CreateObject("component","website")>
			<cfset local.qryHostName = local.objWebsite.getDomainDetails(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostnameID)>
			<cfif len(local.qryHostName.cloudflare_customHostnameID)>
				<cfset local.apiResult = application.objCloudflare.getCustomHostnamesById(zoneId="74e80848392f8237b4b01860fe77590d", id=local.qryHostName.cloudflare_customHostnameID)>
				<cfif local.apiResult.data.result.len() and local.apiResult.data.result[1].id EQ local.qryHostName.cloudflare_customHostnameID>
					<cfset local.strArgs = { "hostNameID":local.qryHostName.hostNameID, "hostnamestatus":"", "sslstatus":"", "sslvalidation_url":"", 
						"sslvalidation_body":"", "httpownershipverfication_url":"", "httpownershipverfication_body":"" }>
					<cfif local.apiResult.data.result[1].keyExists('status')>
						<cfset local.strArgs["hostnamestatus"] = local.apiResult.data.result[1].status>
					</cfif>
					<cfif local.apiResult.data.result[1].keyExists('ssl') AND local.apiResult.data.result[1].ssl.keyExists('status')>
						<cfset local.strArgs["sslstatus"] = local.apiResult.data.result[1].ssl.status>
					</cfif>
					<cfif local.apiResult.data.result[1].keyExists('ssl') AND local.apiResult.data.result[1].ssl.keyExists('validation_records') AND arrayLen(local.apiResult.data.result[1].ssl.validation_records)>
						<cfset local.strArgs["sslvalidation_url"] = local.apiResult.data.result[1].ssl.validation_records[1].http_url>
						<cfset local.strArgs["sslvalidation_body"] = local.apiResult.data.result[1].ssl.validation_records[1].http_body>
					</cfif>
					<cfif local.apiResult.data.result[1].keyExists('ownership_verification_http')>
						<cfset local.strArgs["httpownershipverfication_url"] = local.apiResult.data.result[1].ownership_verification_http.http_url>
						<cfset local.strArgs["httpownershipverfication_body"] = local.apiResult.data.result[1].ownership_verification_http.http_body>
					</cfif>
					
					<cfset local.objWebsite.updateHostnameDetails(argumentCollection=local.strArgs)>

					<cfset local.strReturn.success = true>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="removeHostnameFromCloudflare" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="hostnameID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { "success": false }>

		<cftry>
			<cfset local.objWebsite = CreateObject("component","website")>

			<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"ManageAdvancedSettings") or local.tmpRights.ManageAdvancedSettings is not 1 or application.MCEnvironment neq "Production">
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.qryHostName = local.objWebsite.getDomainDetails(siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID, hostNameID=arguments.hostnameID)>
			<cfif local.qryHostName.recordcount is 1>
				<cfset local.apiResult = application.objCloudflare.deleteCustomHostname(zoneId="74e80848392f8237b4b01860fe77590d", hostnameId=local.qryHostName.cloudflare_customHostnameID)>

				<cfif local.apiResult.data.result.id eq local.qryHostName.cloudflare_customHostnameID>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateHostname">
						UPDATE dbo.siteHostNames
						SET cloudflare_customHostnameID = null,
							cloudflare_hostnamestatus = null,
							cloudflare_sslstatus = null,
							cloudflare_httpownershipverfication_url = null,
							cloudflare_httpownershipverfication_body = null
						WHERE hostNameID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryHostName.hostNameID#">
					</cfquery>

					<cfset local.strReturn.success = true>
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="deleteDomain" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="hostNameID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfset local.tmpRights = buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cfif structKeyExists(local.tmpRights,"editSite") and structKeyExists(local.tmpRights,"ManageAdvancedSettings") and local.tmpRights.editSite is 1 and local.tmpRights.ManageAdvancedSettings is 1>
			<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
			<cfset local.qryMainHostName = local.objWebsite.getMainHost(siteID=arguments.mcproxy_siteID)>
			<cfset local.qryDomain = local.objWebsite.getDomainDetails(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostNameID)>
			<cfset local.isReservedHostname = local.objWebsite.isReservedHostName(nameToCheck=local.qryDomain.hostName)>

			<cfif arguments.hostNameID neq local.qryMainHostName.mainHostID and not local.isReservedHostname>
				<cfset local.objWebsite.deleteDomain(siteID=arguments.mcproxy_siteID, hostNameID=arguments.hostNameID)>
				<cfset removeHostnameFromCloudflare(mcproxy_siteID=arguments.mcproxy_siteID, mcproxy_siteCode=arguments.mcproxy_siteCode, hostnameID=arguments.hostNameID)>
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this area of Control Panel.</b></cfcase>
							<cfcase value="2"><b>A site with that sitecode already exists.</b></cfcase>
							<cfcase value="3"><b>A site with the same main hostname already exists.</b></cfcase>
							<cfcase value="4"><b>An organization with that orgcode already exists.</b></cfcase>
							<cfcase value="5"><b>An error occurred and details have been sent to exceptions.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveFeature" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.data = "";
			local.objWebsite = CreateObject("component","model.admin.website.website");
			local.toolTypeList = arguments.event.getValue('toolType','');
			local.associateOrgLists = arguments.event.getValue('associateOrgLists',0);
			local.siteID = arguments.event.getValue('mc_siteInfo.siteid');
			local.siteCode = arguments.event.getValue('mc_siteInfo.siteCode');
			local.siteName = arguments.event.getValue('mc_siteInfo.siteName');
			local.modifiedFeatures = []; // Array to store feature changes for the email

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSite = checkRights(arguments.event,'editSite');
			if(NOT local.security.editSite){
				application.objCommon.redirect('#this.link.message#&message=1');
			}
			
			// Associate Org Lists
			if( local.associateOrgLists eq 1 ){
				local.objWebsite.associateOrganizationLists(siteCode=local.siteCode);
			}

			// Add Features
			if( listLen(local.toolTypeList) ){
				local.objWebsite.insertFeature(siteID=local.siteID, toolTypeList=local.toolTypeList);
				
				for (local.toolType in listToArray(local.toolTypeList)) {
					// Map toolTypes to their descriptions
					switch(local.toolType) {
						case 'APIAccess':
							local.toolDesc = 'API Access';
							break;
						case 'BadgeDeviceAdmin':
							local.toolDesc = 'Badge Printers';
							break;
						case 'ContributionAdmin':
							local.toolDesc = 'Contributions';
							break;
						case 'EmailBlast':
							local.toolDesc = 'Email Blast';
							break;
						case 'MemberDocs':
							local.toolDesc = 'Member Documents';
							break;
						case 'MemberHistoryAdmin':
							local.toolDesc = 'Member History';
							break;
						case 'referralsSMS':
							local.toolDesc = 'Referrals SMS';
							break;
						case 'RelationshipAdmin':
							local.toolDesc = 'Relationships';
							break;
						case 'Reports':
							local.toolDesc = 'Reports';
							break;
						case 'SubscriptionAdmin':
							local.toolDesc = 'Subscriptions';
							break;
						case 'TaskAdmin':
							local.toolDesc = 'Tasks';
							break;
						case 'AdsAdmin':
							local.toolDesc = 'Sponsor Ad Management';
							break;
						case 'EnableSitePasswords':
							local.toolDesc = 'Site Specific Passwords';
							break;
						case 'RecurringEvents':
							local.toolDesc = 'Recurring Events';
							break;
						default:
							local.toolDesc = 'Unknown Tool';
							break;
					}
					// Append to modifiedFeatures array
					arrayAppend(local.modifiedFeatures, "- #local.toolDesc# has been enabled");
				}
				
				if (arrayLen(local.modifiedFeatures)) {
					local.emailContent = local.siteName & " (" & local.siteCode & ") has modified the features on their website under the Enable Site Features tool:<br/><br/>#arrayToList(local.modifiedFeatures,'<br/>')#<br/>Ensure Cole W knows about this change as it may affect billing for this client.";
					application.objEmailWrapper.sendMailESQ(
						emailfrom={ name="MemberCentral", email='<EMAIL>' },
						emailto=[{ name="", email='<EMAIL>' }],
						emailreplyto='',
						emailsubject="[#application.MCEnvironment#] #local.siteCode# - Website Enabled Features have been modified",
						emailtitle="#local.siteCode# - Website Enabled Features have been modified",
						emailhtmlcontent=local.emailContent,
						siteID=local.siteID,
						memberID=session.cfcuser.memberdata.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="WEBSITEFEATURES"),
						sendingSiteResourceID=this.siteResourceID
						);
				}

				application.objSiteInfo.triggerClusterWideReload();
				application.objCommon.redirect(this.link.edit & "&tab=features&addedFunc=1&refreshAdminNavData=1");
			}

			application.objCommon.redirect(this.link.edit & "&tab=features");
		</cfscript>
	</cffunction>
	
	<cffunction name="setupDeployPaths" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>

		<cfscript>
		local.sampleFileStructNode = {filepath = "",fileContent = "",lastModified = "",timestamp =""};
		local.sampleRepoStructNode = {
			logFolderPath = "",
			files = {
				gitStatusFile = duplicate(local.sampleFileStructNode),
				gitBranchFile = duplicate(local.sampleFileStructNode),
				gitDiffFile =duplicate(local.sampleFileStructNode)
			}
		};

		local.repoInfoStruct = structNew();

		local.repoInfoStruct.mc = {logSets = {}, deployCommandFile = duplicate(local.sampleFileStructNode)};
		local.repoInfoStruct.mc.deployCommandFile.filepath = application.paths.gitDeploy.path & "deploy-mc.txt";
		local.sortedList = listSort(application.paths.gitDeploy.logPaths.mc,"textnocase");
		for (local.i=1;local.i<=ListLen(local.sortedList);local.i++) {
			local.thisLogPath = ListGetAt(local.sortedList,local.i);
			local.thisStructKey = local.i;
			local.repoInfoStruct.mc.logSets[local.thisStructKey] = duplicate(local.sampleRepoStructNode);
			local.repoInfoStruct.mc.logSets[local.thisStructKey].logFolderPath = local.thisLogPath;
			local.repoInfoStruct.mc.logSets[local.thisStructKey].files.gitStatusFile.filepath = local.thisLogPath & "deploy-mc-gitstatus.log";
			local.repoInfoStruct.mc.logSets[local.thisStructKey].files.gitBranchFile.filepath = local.thisLogPath & "deploy-mc-gitbranch.log";
			local.repoInfoStruct.mc.logSets[local.thisStructKey].files.gitDiffFile.filepath = local.thisLogPath & "deploy-mc-gitdiff.log";
		}
		</cfscript>

		<cfreturn local.repoInfoStruct>
	</cffunction>

	<cffunction name="populateDeployNode" access="public" output="false" returntype="string">
		<cfargument name="s" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>

		<cfset local.repoInfoStruct = setupDeployPaths()>

		<cftry>
			<cfset local.fileNode = structGet("local.repoInfoStruct.#ucase(arguments.s)#")>
			<cfset local.fileNode.lastModified = getFileInfo(local.fileNode.filepath).lastModified>
			<cfset local.fileNode.timestamp = dateFormat(local.fileNode.lastModified, "m/d/yyyy") & " " & timeFormat(local.fileNode.lastModified, "h:mm tt")>
			
			<cffile action="read" file="#local.fileNode.filepath#" variable="local.fileContent">
			<cfset local.fileNode.fileContent = paragraphFormat(reReplace(local.fileContent, "(\r\n|\n)", "#chr(13)##chr(10)##chr(13)##chr(10)#", "all"))>

			<cfset local.strReturn['success'] = true>
		<cfcatch type="any">
			<cfset local.fileNode.lastModifed = "">
			<cfset local.fileNode.timestamp = "">
			<cfset local.fileNode.fileContent = cfcatch.message>
			<cfset local.strReturn['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfset local.strReturn['data'] = local.fileNode>
		<cfreturn SerializeJSON(local.strReturn)>
	</cffunction>

	<cffunction name="codeDeploy" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objWebsite = CreateObject("component","model.admin.website.website");

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSite = checkRights(arguments.event,'editSite');
			if (NOT local.security.editSite)
				application.objCommon.redirect('#this.link.message#&message=1');
				
			local.allowDeploy = false;
			if (application.MCEnvironment eq "production") {
				local.allowDeploy = true;

				if (arguments.event.valueExists('codeDeployed'))
					local.jsonStr = local.objWebsite.generateDeployCommands(event=arguments.event);
			}

			local.showLogs = false;
			if (application.MCEnvironment neq "localDevelopment")
				local.showLogs = true;

			local.repoInfoStruct = setupDeployPaths();
		</cfscript>

		<cfif local.allowDeploy>
			<cftry>
				<cfhttp url="https://api.github.com/repos/TrialSmith/MC/branches/master" method="get" useragent="MemberCentral.com" result="local.strAPIJSON">
					<cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#" encoded="false">
				</cfhttp>
				<cfset local.strBranch = DeSerializeJSON(local.strAPIJSON.fileContent)>
				<cfset local.latestSHA = local.strBranch.commit.sha>
			<cfcatch type="any">
				<cfset local.latestSHA = "">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_codeDeploy.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getAllBetaAccess" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.strCNAMES = {}>

		<cfset local.filters = { per_page: 4000, content="beta.membercentral.com" }>
		<cfset local.cfRecordSets = application.objCloudflare.listDnsRecords(zoneId='dfb34caa319e0a55e5f8892217a10f7c', filters=local.filters)>
		<cfloop array="#local.cfRecordSets.data.result#" index="local.thisItem">
			<cfset local.tmpSiteCode = replaceNoCase(local.thisItem.Name,"beta.membercentral.com","")>
			<cfif structKeyExists(application.objSiteInfo.mc_siteinfo,local.tmpSiteCode)>
				<cfset local.strCNAMES[local.tmpSiteCode] = 1>
			</cfif>
		</cfloop>

		<cfreturn local.strCNAMES>
	</cffunction>

	<cffunction name="getBetaAccessStatus" access="public" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data.success = false>
		<cfset local.data['enablebetaaccess'] = false>
		<cfset local.data['disablebetaaccess'] = false>
		
		<cfif listFindNoCase("beta,newbeta",application.MCEnvironment) AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.strCNAMES = getAllBetaAccess()>
			<cfset local.data['disablebetaaccess'] = structKeyExists(local.strCNAMES,arguments.siteCode)>
			<cfif NOT local.data['disablebetaaccess']>
				<cfset local.data['enablebetaaccess'] = true>
			</cfif>

			<cfset local.data.success = true>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="enableBetaAccess" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.redirectURL = "#this.link.edit#&tab=settings">
		<cfset local.siteCode = arguments.event.getValue("mc_siteInfo.siteCode")>
		<cfset local.siteID = arguments.event.getValue("mc_siteInfo.siteID")>
		
		<cfset local.strCNAMES = getAllBetaAccess()>
		<cfif NOT structKeyExists(local.strCNAMES,local.siteCode)>
			<cfset local.record = { proxied = true, type = "CNAME", ttl=1800, name='#local.siteCode#beta.membercentral.com', content='beta.membercentral.com' } >
			<cfif application.objCloudflare.createDnsRecord(zoneId='dfb34caa319e0a55e5f8892217a10f7c', record=local.record).data.success>
				<cfset local.betaHostDomainID = local.objWebsite.insertDomain(siteID=local.siteID, hostName='#local.siteCode#beta.membercentral.com', useRedirect='', 
					internalIPAddress='', hasSSL=1, sslCertFileName='mc-wildcard.membercentral.com.chained.crt', sslPrivateKeyFileName='mc-wildcard.membercentral.com.privatekey')>
				<cfif val(local.betaHostDomainID)>
					<cfset local.qryMainHostName = local.objWebsite.getMainHost(siteID=local.siteID)>
					<cfset local.objWebsite.updateNewMainHost(siteID=local.siteID, newMainHostID=local.betaHostDomainID, oldMainHostID=val(local.qryMainHostName.mainHostID))>
					<cfset local.objWebsite.updatePublicBetaAccessExpires(siteID=local.siteID, action='enable')>
				<cfelse>
					<cfset local.redirectURL = local.redirectURL & "&errorID=1">
				</cfif>
			<cfelse>
				<cfset local.redirectURL = local.redirectURL & "&errorID=1">
			</cfif>
		<cfelse>
			<cfset local.redirectURL = local.redirectURL & "&errorID=2">
		</cfif>
		
		<cfset application.objCommon.redirect(local.redirectURL)>
	</cffunction>
	
	<cffunction name="disableBetaAccess" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.redirectURL = "#this.link.edit#&tab=settings">
		<cfset local.siteCode = arguments.event.getValue("mc_siteInfo.siteCode")>
		<cfset local.siteID = arguments.event.getValue("mc_siteInfo.siteID")>
		<cfset local.deleteResponse = false>

		<cfset local.strCNAMES = getAllBetaAccess()>
		<cfif structKeyExists(local.strCNAMES,local.siteCode)>
			<cfset local.deleteResponse = disableBetaAccessForSite(siteCode=local.siteCode)>
			<cfif NOT local.deleteResponse>
				<cfset local.redirectURL = local.redirectURL & "&errorID=1">
			</cfif>
		<cfelse>
			<cfset local.redirectURL = local.redirectURL & "&errorID=3">
		</cfif>
		
		<cfset application.objCommon.redirect(local.redirectURL)>
	</cffunction>

	<cffunction name="extendBetaAccess" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.redirectURL = "#this.link.edit#&tab=settings">
		
		<cfset local.objWebsite.updatePublicBetaAccessExpires(siteID=arguments.event.getValue("mc_siteInfo.siteID"), action='extend')>

		<cfset application.objCommon.redirect(local.redirectURL)>
	</cffunction>

	<cffunction name="disableBetaAccessForSite" access="public" output="false" returntype="boolean">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		
		<cftry>
			<cfset local.deleteResponse = false>
			<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
			<cfset local.siteID = application.objSiteInfo.mc_siteinfo[arguments.siteCode].siteID>
			<cfset local.qryCurrentMainHostName = local.objWebsite.getMainHost(siteID=local.siteID)>
			<cfset local.qryDomains = local.objWebsite.getDomains(siteID=local.siteID)>
			<cfset local.newMainHostID = 0>

			<cfif local.qryDomains.recordCount gte 2>
				<cfloop query="local.qryDomains">
					<cfif FindNoCase("mcinternal.com", local.qryDomains.hostname)>
						<cfset local.objWebsite.updateNewMainHost(siteID=local.siteID, newMainHostID=local.qryDomains.hostNameID, oldMainHostID=local.qryCurrentMainHostName.mainHostID)>
						<cfset local.objWebsite.deleteDomain(siteID=local.siteID, hostNameID=local.qryCurrentMainHostName.mainHostID)>
						<cfset local.objWebsite.updatePublicBetaAccessExpires(siteID=local.siteID, action='disable')>
						<cfset local.cfRecord = application.objCloudflare.getDnsRecordsByName(zoneId='dfb34caa319e0a55e5f8892217a10f7c', name='#arguments.siteCode#beta.membercentral.com')>
						<cfset local.deleteResponse = application.objCloudflare.deleteDnsRecord(zoneId='dfb34caa319e0a55e5f8892217a10f7c', recordID=local.cfRecord.data.result[1].id).data.success>
						<cfbreak>
					</cfif>
				</cfloop>
			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.deleteResponse = false>
		</cfcatch>
		</cftry>

		<cfreturn local.deleteResponse>
	</cffunction>

	<cffunction name="disableAllBetaAccess" access="public" output="false" returntype="void">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.siteCode) and structKeyExists(application.objSiteInfo.mc_siteinfo,arguments.siteCode)>
			<cfset disableBetaAccessForSite(siteCode=arguments.siteCode)>
		<cfelseif NOT len(arguments.siteCode)>
			<cfset local.strCNAMES = getAllBetaAccess()>
			<cfloop collection="#local.strCNAMES#" item="local.thisSiteCode">
				<cfset disableBetaAccessForSite(siteCode=local.thisSiteCode)>
			</cfloop>
		</cfif>
	</cffunction>

	<cffunction name="editAPIToken" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryAPIToken" datasource="#application.dsn.memberCentral.dsn#">
			SELECT userID, nickname
			FROM dbo.api_users
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			AND userID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('userID',0)#">
			AND tokenStatus <> 'D';
		</cfquery>

		<cfquery name="local.qryRouteMethods" datasource="#application.dsn.memberCentral.dsn#">
			select rm.rmid, concat('/v1',replace(replace(replace(replace(replace(replace(r.[route],':api_id','{api_id}'),
				':registrant_id','{registrant_id}'),':membernumber','{membernumber}'),':authtype_code','{authtype_code}'),
				':listname','{listname}'),':subscriber_id','{subscriber_id}')) as [route], rm.method, rm.purpose,
				case when r.[route] in ('/authenticate','/authenticate/me') then 1 else 0 end as isRequired
			from dbo.api_routes as r
			inner join dbo.api_routeMethods as rm on rm.routeID = r.routeID
			order by [route], case rm.method when 'GET' then 1 when 'POST' then 2 when 'PUT' then 3 when 'DELETE' then 4 else 5 end
		</cfquery>

		<cfquery name="local.qryUserRouteMethods" datasource="#application.dsn.memberCentral.dsn#">
			SELECT rmid
			FROM dbo.api_userRouteMethods
			WHERE userID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryAPIToken.userID)#">
		</cfquery>
		<cfset local.userRouteMethodIDList = ValueList(local.qryUserRouteMethods.rmid)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editAPIToken.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveAPIToken" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="userID" type="numeric" required="true">
		<cfargument name="nickname" type="string" required="true">
		<cfargument name="rmIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif arguments.userID gt 0>
			<cfset updateAPIToken(siteID=arguments.mcproxy_siteID, userID=arguments.userID, nickname=arguments.nickname, rmIDList=arguments.rmIDList)>
			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct = insertAPIToken(siteID=arguments.mcproxy_siteID, nickname=arguments.nickname, rmIDList=arguments.rmIDList)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertAPIToken" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="nickname" type="string" required="true">
		<cfargument name="rmIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="api_createLogin" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.nickname#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rmIDList#">
				<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.returnStruct.userName">
				<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.returnStruct.password">
			</cfstoredproc>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateAPIToken" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="userID" type="numeric" required="true">
		<cfargument name="nickname" type="string" required="true">
		<cfargument name="rmIDList" type="string" required="true">

		<cfset var qryUpdateAPIToken = "">

		<cfquery name="qryUpdateAPIToken" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @userID int, @nickname varchar(100), @memberID int, @regexNameFields varchar(40), @rmIDList varchar(max);
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @userID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.userID#">;
				SET @nickname = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.nickname)#">;
				SET @rmIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.rmIDList#">;

				SELECT @memberID = memberID
				FROM dbo.api_users
				WHERE siteID = @siteID
				AND userID = @userID
				AND isSystemToken = 0;

				SELECT @regexNameFields = regexNameFields
				FROM dbo.fn_getServerSettings();

				IF @memberID IS NULL
					RAISERROR('Invalid API Token',16,1);

				IF dbo.fn_RegExReplace(@nickname,@regexNameFields,'') <> @nickname
					RAISERROR('API Token has a disallowed character in NickName.',16,1);

				BEGIN TRAN;
					UPDATE dbo.api_users
					SET nickName = @nickname
					WHERE userID = @userID;

					-- update member name (PMI skips updates to protected member, so updating directly)
					UPDATE dbo.ams_members
					SET lastName = LEFT(@nickname,75)
					WHERE memberID = @memberID;

					DELETE FROM dbo.api_userRouteMethods
					WHERE userID = @userID
					AND rmID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="0#arguments.rmIDList#" list="true">);

					INSERT INTO dbo.api_userRouteMethods (userID, rmID)
					SELECT @userID, tmp.listitem
					FROM dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rmIDList#">,',') tmp
					LEFT JOIN dbo.api_userRouteMethods pt ON pt.userID = @userID AND pt.rmID = tmp.listitem
					WHERE pt.urmID IS NULL;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="activateAPIToken" access="public" output="false" returntype="struct">
		<cfargument name="userID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="api_activateLogin" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#arguments.userID#">
			</cfstoredproc>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="inactivateAPIToken" access="public" output="false" returntype="struct">
		<cfargument name="userID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="api_inactivateLogin" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#arguments.userID#">
			</cfstoredproc>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteAPIToken" access="public" output="false" returntype="struct">
		<cfargument name="userID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfstoredproc procedure="api_deleteLogin" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" value="#arguments.userID#">
			</cfstoredproc>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addRoleAssignment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif not application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfreturn returnAppStruct("You do not have rights to this area of Control Panel.","echo")>
		</cfif>

		<cfset local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>
		<cfset local.qryRoles = createObject("component","website").getUniversalRoles()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_addRoleAssignment.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRoleAssignments" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="universalRoleIDList" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfquery name="local.qryInsertRoleAssignments" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @siteID int, @siteResourceID int, @groupID int, @roleID int;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					set @groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">;

					select @siteResourceID = siteResourceID from dbo.sites where siteID = @siteID;

					<cfloop list="#arguments.universalRoleIDList#" index="local.thisRoleID">
						set @roleID = null;
						set @roleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRoleID#">;

						EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, @functionIDList=null, 
							@roleID=@roleID, @groupID=@groupID, @inheritedRightsResourceID=null, @inheritedRightsFunctionID=null;
					</cfloop>

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteRoleAssignment" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="siteResourceRightID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfquery name="local.qryDeleteRoleAssignment" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @siteID int, @siteResourceID int, @siteResourceRightID int;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				set @siteResourceRightID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceRightID#">;

				select @siteResourceID = siteResourceID from dbo.sites where siteID = @siteID;

				EXEC dbo.cms_deleteSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @siteResourceRightID=@siteResourceRightID;
			</cfquery>

			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="manageNetworks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.editNetworkLink = buildCurrentLink(arguments.event,"editNetwork") & "&mode=direct";
			local.platformNetworksLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=websiteJSON&meth=getPlatformNetworks&mode=stream';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_manageNetworks.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editNetwork" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('networkID',int(val(arguments.event.getValue('networkID',0))))>
		<cfset local.qryNetwork = createObject("component","website").getNetworkDetails(networkID=arguments.event.getValue('networkID'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editNetwork.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="checkNetworkNameExists" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">
		<cfargument name="networkName" type="string" required="yes">

		<cfset var qryNetwork = "">

		<cfquery name="qryNetwork" datasource="#application.dsn.memberCentral.dsn#">
			select networkID
			from dbo.networks
			where networkName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.networkName#">
			<cfif arguments.networkID gt 0>
				and networkID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">
			</cfif>
		</cfquery>

		<cfreturn { "success":true, "networkexists":qryNetwork.recordCount gt 0 }>
	</cffunction>

	<cffunction name="saveNetwork" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">
		<cfargument name="networkName" type="string" required="yes">
		<cfargument name="accountName" type="string" required="yes">
		<cfargument name="supportProviderName" type="string" required="yes">
		<cfargument name="supportProviderPhone" type="string" required="yes">
		<cfargument name="emailFrom" type="string" required="yes">
		<cfargument name="networkCode" type="string" required="yes">
		<cfargument name="addNewAccountsToApprovals" type="boolean" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "networkID":arguments.networkID }>

		<cftry>
			<cfif arguments.networkID GT 0>
				<cfquery name="qryNetwork" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;

					DECLARE @networkID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">,
						@networkName varchar(100) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.networkName#">,
						@existingNetworkID int;
					
					SELECT @existingNetworkID = networkID 
					FROM dbo.networks 
					WHERE networkName = @networkName
					AND networkID <> @networkID;

					IF @existingNetworkID IS NULL
						UPDATE dbo.networks
						SET networkName = @networkName,
							networkCode = <cfif len(trim(arguments.networkCode)) eq 0>
												NULL
											<cfelse>
												<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.networkCode#">
											</cfif>,
							accountName = <cfif len(trim(arguments.accountName)) eq 0>
												NULL
											<cfelse>
												<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountName#">
											</cfif>,
							supportProviderName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderName#">,
							supportProviderPhone = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderPhone#">,
							supportProviderEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderEmail#">,
							emailFrom = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailFrom#">,
							addNewAccountsToApprovals = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.addNewAccountsToApprovals#">
						WHERE networkID = @networkID;
				</cfquery>
			<cfelse>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createNetwork">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.networkName#">
					<cfif len(trim(arguments.networkCode)) eq 0>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.networkCode#">
					</cfif>
					<cfif len(trim(arguments.accountName)) eq 0>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountName#">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderPhone#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.supportProviderEmail#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailFrom#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.addNewAccountsToApprovals#">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.returnStruct.networkID">
				</cfstoredproc>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="deleteNetwork" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">

		<cfset var qryDeleteNetwork = "">

		<cfquery name="qryDeleteNetwork" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @networkID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">;
			
			IF NOT EXISTS (select networkSiteID from dbo.networkSites where networkID = @networkID)
				DELETE FROM dbo.networks
				WHERE networkID = @networkID;
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="getNetworkSites" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">

		<cfset var strReturn = { "success":true, "arrdata":[] }>

		<cfquery name="strReturn.arrdata" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
			select ns.networkSiteID as networksiteid, s.siteID as siteid, s.siteCode as sitecode, 
				s.siteName as sitename, ns.isLoginNetwork as isloginnetwork
			from dbo.networkSites as ns
			inner join dbo.sites as s on s.siteID = ns.siteID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			where ns.networkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">
			order by s.siteCode, s.siteName
		</cfquery>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="getAvailableSites" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">

		<cfset var strReturn = { "success":true, "arrNetworkSites":[] }>

		<cfquery name="strReturn.arrNetworkSites" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
			select s.siteID as siteid, s.siteCode as sitecode, s.siteName as sitename
			from dbo.sites as s
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
			left outer join dbo.networkSites as ns on ns.siteID = s.siteID 
				and networkID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">
			where ns.networkSiteID is null
			order by s.siteCode, s.siteName
		</cfquery>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="addNetworkSite" access="public" output="false" returntype="struct">
		<cfargument name="networkID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local = structnew()>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="createNetworkSite">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.networkID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteNetworkSite" access="public" output="false" returntype="struct">
		<cfargument name="networkSiteID" type="numeric" required="yes">

		<cfset var qryDeleteNetworkSite = "">

		<cfquery name="qryDeleteNetworkSite" datasource="#application.dsn.memberCentral.dsn#">
			DELETE FROM dbo.networkSites
			WHERE networkSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.networkSiteID#">
			AND isLoginNetwork = 0;
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="getDataFromWufoo" access="public" output="yes" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="wufooEntryId" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.data = structNew()>
		<cfset local.data['success'] = true>
		<cfset local.data['results'] = structNew()>
	
		<cfset local.apiurl = "https://memcentral.wufoo.com/api/v3/forms/z17ixsrh0e3cjvu/entries.json?Filter1=EntryId+Is_equal_to+#arguments.wufooEntryId#">
		<cfset local.apiAuthorization = "Basic #ToBase64('VFG9-DBXS-RWXA-9MQP:membercentral')#"/>

		<cftry>
			<cfset local.siteSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='WebsiteAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfif not structKeyExists(local.tmpRights,"addSite") or local.tmpRights.addSite is not 1 or session.mcStruct.siteCode neq 'MC'>
				<cfthrow message="invalid request">
			</cfif>

			<cfhttp method="GET" url="#local.apiurl#" result="local.apiResult" encodeurl="false">
				<cfhttpparam type="HEADER" name="accept" value="application/json">
				<cfhttpparam type="HEADER" name="Content-Type" value="application/json">
				<cfhttpparam type="HEADER" name="Authorization" value="#local.apiAuthorization#">
			</cfhttp>

			<cfset local.APIResponse = DeserializeJSON(local.apiResult.fileContent)>

			<cfif isStruct(local.APIResponse) AND StructKeyExists(local.APIResponse,"Entries") AND ArrayLen(local.APIResponse.Entries)>
				<cfset local.data['results'] = local.APIResponse.Entries[1]>
			</cfif>
		<cfcatch type="any">
			<cfset local.data['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>			
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberDocumentsCount" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
				
		<cfset var qryMemberDocumentsCount = "">

		<cfquery name="qryMemberDocumentsCount" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @orgID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
			SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

			SELECT COUNT(md.memberDocumentID) AS docsCount
			FROM dbo.ams_memberDocuments AS md
			INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberID = md.memberID
			INNER JOIN dbo.cms_documents AS d ON d.documentID = md.documentID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = d.siteResourceID 
				AND sr.siteResourceStatusID = 1
			WHERE d.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { "success":true, "docscount":val(qryMemberDocumentsCount.docsCount) }>
	</cffunction>

	<cffunction name="disableSiteFeature" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="siteResourceId" type="numeric" required="yes">
		<cfargument name="toolType" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.triggerReset = 0>

		<cftry>
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<cfsetting requesttimeout="300">
				<cfswitch expression="#arguments.toolType#">
					<cfcase value="APIAccess">
						<cfstoredproc procedure="sf_disableAPIAccess" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'API Access'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="BadgeDeviceAdmin">
						<cfstoredproc procedure="sf_disableBadgePrinters" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Badge Printers'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="ContributionAdmin">
						<cfstoredproc procedure="sf_disableContributions" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Contributions'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="MemberDocs">
						<cfstoredproc procedure="sf_disableMemberDocs" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Member Documents'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="RecurringEvents">
						<cfquery name="local.qryRecurringEvents" datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.siteFeatures
							SET recurringEvents = 0
							WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							AND recurringEvents = 1;
						</cfquery>
						<cfset local.toolDesc = 'Recurring Events'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="MemberHistoryAdmin">
						<cfstoredproc procedure="sf_disableMemberHistory" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Member History'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="RelationshipAdmin">
						<cfstoredproc procedure="sf_disableRelationships" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Relationships'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="SubscriptionAddV2">
						<cfquery name="local.qryDisableSubsAddV2" datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.siteFeatures
							SET subsAddV2 = 0
							WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							AND subsAddV2 = 1;
						</cfquery>
						<cfset local.toolDesc = 'Add Subscription V2'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="SubscriptionAdmin">
						<cfstoredproc procedure="sf_disableSubscriptions" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Subscriptions'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="TaskAdmin">
						<cfstoredproc procedure="sf_disableTasks" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
						</cfstoredproc>
						<cfset local.toolDesc = 'Tasks'>
						<cfset local.triggerReset = 1>
					</cfcase>
					<cfcase value="referralsSMS">
						<cfquery name="local.qryDisableReferralsSMS" datasource="#application.dsn.membercentral.dsn#">
							UPDATE dbo.siteFeatures
							SET referralsSMS = 0
							WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
							AND referralsSMS = 1;
						</cfquery>
						<cfset local.toolDesc = 'Referrals SMS'>
						<cfset local.triggerReset = 1>
					</cfcase>
				</cfswitch>
			</cfif>
			
			<cfif local.triggerReset>
				<cfset application.objSiteInfo.triggerClusterWideReload()>

				<cfset local.emailContent = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteName & " (" & arguments.mcproxy_siteCode & ") has modified the features on their website under the Enable Site Features tool:<br/>- #local.toolDesc# has been disabled<br/><br/>Ensure Cole W knows about this change as it may affect billing for this client.">
				
				<cfset application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="MemberCentral", email='<EMAIL>' },
					emailto=[{ name="", email='<EMAIL>' }],
					emailreplyto='',
					emailsubject="[#application.MCEnvironment#] #arguments.mcproxy_siteCode# - Website Enabled Features have been modified",
					emailtitle="#arguments.mcproxy_siteCode# - Website Enabled Features have been modified",
					emailhtmlcontent=local.emailContent,
					siteID=arguments.mcproxy_siteID,
					memberID=session.cfcuser.memberdata.memberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="WEBSITEFEATURES"),
					sendingSiteResourceID=arguments.siteResourceID
					)>

				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>
		<cfcatch>
			<cfset local.returnStruct['success'] = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>		
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="manageOrgIdentities" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objAdmin = CreateObject("component","model.admin.admin");
			local.linkExportOrgIdentities = buildCurrentLink(arguments.event,"exportOrgIdentities") & "&mode=stream";
			local.orgIdentitiesLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=websiteJSON&meth=getOrgIdentities&mode=stream';
			local.editOrgIdentityLink = local.objAdmin.buildLinkToTool(toolType='OrganizationAdmin',mca_ta='editOrgIdentity')& "&mode=direct";
			local.previewOrgIdentityLink = local.objAdmin.buildLinkToTool(toolType='OrganizationAdmin',mca_ta='previewOrgIdentity') & "&mode=stream";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_orgIdentities.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="exportOrgIdentities" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "OrgIdentity.csv">

		<cfset local.searchValue = arguments.event.getValue('searchVal','')>

		<cfquery name="local.qryOrgIdentity" datasource="#application.dsn.memberCentral.dsn#">
			SET nocount on;
			DECLARE @searchValue varchar(500);

			IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL
	      		DROP TABLE ##tmpExport;

			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			SELECT data.orgIdentityID,data.orgID,data.orgCode,data.organizationName,data.address1,data.address2,data.city,data.stateID,data.postalCode,data.phone,data.fax,data.email,data.website,data.organizationShortName
			INTO ##tmpExport
			FROM (
				SELECT  i.orgIdentityID,i.orgID,o.orgCode,i.organizationName,i.address1,i.address2,i.city,i.stateID,i.postalCode,i.phone,i.fax,i.email,i.website,i.organizationShortName
				FROM organizations o 			
				INNER JOIN orgIdentities i ON i.orgID = o.orgID
				WHERE 1 = 1 
				<cfif len(local.searchValue)>
					AND (i.organizationName LIKE @searchValue OR o.orgCode LIKE @searchValue)
				</cfif>
			) AS data

			DECLARE @selectsql varchar(max) = '
				SELECT *, ROW_NUMBER() OVER(order by orgIdentityID) as mcCSVorder 
				*FROM* ##tmpExport';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL
	      		DROP TABLE ##tmpExport;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportOrgIdentity('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addWebhook" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_addWebhook.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editWebhook" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.webhookUID = arguments.event.getTrimValue('wuid');
			local.strAPIResponse = application.objMCAPI.api(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), method="GET", endpoint="webhook/#local.webhookUID#");
			
			// error
			if (local.strAPIResponse.error) {
				return returnAppStruct(arrayToList(local.strAPIResponse.messages),"echo");
			}
			
			local.webhookID = CreateObject("component","website").getWebhookIDByUID(siteID=arguments.event.getValue('mc_siteInfo.siteid'), webhookUID=local.webhookUID);
			local.strWebhook = local.strAPIResponse.data.webhook;

			// links
			local.gridRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=websiteJSON&wid=#local.webhookID#&mode=stream&meth=";
			local.manageWebhookGrpsLink = buildCurrentLink(arguments.event,"manageWebhookGroups") & '&wid=#local.webhookID#&mode=direct';
			local.addWebhookSubsLink = buildCurrentLink(arguments.event,"addWebhookSubscription") & '&wid=#local.webhookID#&mode=direct';
			local.showSamplePayload = buildCurrentLink(arguments.event,"showSamplePayload") & '&mode=direct';
			local.webhookGrpsListLink = "#local.gridRootLink#getWebhookGroups";
			local.webhookSubsListLink = "#local.gridRootLink#getWebhookSubscriptions";
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editWebhook.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showSamplePayload" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfset var local=StructNew()>
		<cfset local.whichEvent = arguments.event.getValue('whichEvent','')>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showSamplePayload.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveWebhook" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="hookURL" type="string" required="yes">
		<cfargument name="hookUID" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false }>

		<cftry>
			<cfif arguments.webhookID>
				<cfset local.qryWebhook = CreateObject("component","website").getWebHookDetails(siteID=arguments.mcproxy_siteID, webhookID=arguments.webhookID)>
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfset local.payLoadData = { "payload-uri":arguments.hookURL, "api_id":"#arguments.hookUID#" }>
				<cfelse>
					<cfset local.payLoadData = { "payload-uri":arguments.hookURL }>
				</cfif>
				<cfset local.strUpdateWebHook = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="webhook/#local.qryWebhook.uid#", payload=serializeJSON(local.payloadData))>
				<cfif local.strUpdateWebHook.error>
					<cfthrow message="#arrayToList(local.strUpdateWebHook.messages, '<br/>')#">
				</cfif>
				<cfset local.data.success = true>
			<cfelse>
				<cfset local.payLoadData = { "payload-uri":arguments.hookURL }>
				<cfset local.strInsertResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="POST", endpoint="webhook", payload=serializeJSON(local.payloadData))>
				<cfif local.strInsertResponse.error>
					<cfthrow message="#arrayToList(local.strInsertResponse.messages, '<br/>')#">
				</cfif>
				<cfset local.data.success = true>
			</cfif>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Payload URL already exists", cfcatch.detail)>
				<cfset local.data.errmsg = "Payload URL already exists.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeWebhook" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="webhookUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': false }>

		<cftry>
			<cfset local.strDeleteResponse = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="DELETE", endpoint="webhook/#arguments.webhookUID#")>
			<cfif local.strDeleteResponse.error>
				<cfthrow message="#arrayToList(local.strDeleteResponse.messages, '<br/>')#">
			</cfif>

			<cfset local.returnStruct['success'] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct['success'] = false>
			<cfset local.returnStruct['errmsg'] = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveWebHooksEvent" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="webHookEventName" type="string" required="yes">
		<cfargument name="triggerWebhookEvent" type="boolean" required="yes">
		<cfargument name="arrGroupUIDs" type="array" required="no">
		<cfargument name="subRules" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false }>
		<cfset local.objWebsite = CreateObject("component","website")>

		<cftry>
			<cfswitch expression="#arguments.webHookEventName#">
				<cfcase value="memCreated">
					<cfset local.payLoadData = { "event": { "membercreate": { "enabled":arguments.triggerWebhookEvent } } }>
				</cfcase>
				<cfcase value="memRecordChanges">
					<cfset local.payLoadData = { "event": { "memberupdate": { "enabled":arguments.triggerWebhookEvent } } }>
				</cfcase>
				<cfcase value="memMerge">
					<cfset local.payLoadData = { "event": { "membermerge": { "enabled":arguments.triggerWebhookEvent } } }>
				</cfcase>
				<cfcase value="delMem">
					<cfset local.payLoadData = { "event": { "memberdelete": { "enabled":arguments.triggerWebhookEvent } } }>
				</cfcase>
				<cfcase value="grpMemChange">
					<cfset local.payLoadData = { 
						"event": { 
							"membergroupchange": { 
								"enabled": arguments.triggerWebhookEvent,
								"group_api_id": arguments.triggerWebhookEvent 
													? arguments.keyExists('arrGroupUIDs') 
														? arguments.arrGroupUIDs 
														: local.objWebsite.getWebhookGroupUIDsArray(siteID=arguments.mcproxy_siteID, webhookID=arguments.webhookID)
													: []
								}
						} 
					}>
				</cfcase>
				<cfcase value="subStatusChange">
					<cfif arguments.keyExists('subRules')>
						<cfset local.strSubRules = DeserializeJSON(arguments.subRules)>
					</cfif>
					<cfset local.payLoadData = { 
						"event": { 
							"subscriptionstatuschange": { 
								"enabled": arguments.triggerWebhookEvent,
								"status": arguments.triggerWebhookEvent 
											? arguments.keyExists('subRules') 
												? local.objWebsite.getWebHookSubsInfo(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, webhookID=arguments.webhookID,
														ruleID=local.strSubRules.ruleID, arrSubTypes=local.strSubRules.arrSubTypes, arrSubs=local.strSubRules.arrSubs, 
														arrSubRates=local.strSubRules.arrSubRates, arrSubStatuses=local.strSubRules.arrSubStatuses, paymentStatusID=local.strSubRules.paymentStatusID)
												: local.objWebsite.getWebHookSubsInfo(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, webhookID=arguments.webhookID)
											: []
								}
						} 
					}>
				</cfcase>
				<cfdefaultcase>
					<cfset local.payLoadData = "">
				</cfdefaultcase>
			</cfswitch>

			<cfset local.qryWebhook = local.objWebsite.getWebHookDetails(siteID=arguments.mcproxy_siteID, webhookID=arguments.webhookID)>
			<cfset local.strUpdateWebHook = application.objMCAPI.api(siteCode=arguments.mcproxy_siteCode, method="PUT", endpoint="webhook/#local.qryWebhook.uid#", payload=serializeJSON(local.payloadData))>
			<cfif local.strUpdateWebHook.error>
				<cfthrow message="#arrayToList(local.strUpdateWebHook.messages, '<br/>')#">
			</cfif>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageWebhookGroups" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.webhookID = val(arguments.event.getValue('wid',0));
			local.grpsSelectorLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=websiteJSON&meth=getGrpsForWebhookGrpsSelection&wid=#local.webhookID#&mode=stream";
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_manageWebhookGroups.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addWebhookSubscription" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
			local.qrySubTypes = local.objSubs.getSubscriptionTypes(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.qrySubStatuses = local.objSubs.getSubStatuses();
			local.webhookID = val(arguments.event.getValue('wid',0));
			local.ruleID = val(arguments.event.getValue('rid',0));
			local.qryWebHookSubs = CreateObject("component","website").getWebHookSubsDetails(webhookID=local.webhookID, ruleID=local.ruleID);
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPaymentStatuses">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
			SELECT statusID, statusName
			FROM dbo.sub_paymentStatuses
			ORDER BY statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_addWebhookSubs.cfm">
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeSubTypeFromWebhooks" access="public" output="false" returntype="struct">
		<cfargument name="webhookID" type="numeric" required="yes">
		<cfargument name="ruleID" type="numeric" required="yes">

		<cfset var success = CreateObject("component","website").removeSubTypeFromWebhooks(webhookID=arguments.webhookID, ruleID=arguments.ruleID)>

		<cfreturn { "success":success }>
	</cffunction>

	<cffunction name="hasEditLoginPolicyRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="loginPolicySiteResourceID" type="numeric" required="true">
		<cfargument name="isMCStaffControlled" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<!--- Add --->
		<cfif NOT arguments.loginPolicySiteResourceID AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfset local.hasRights = true>

		<!--- Edit --->
		<cfelseif arguments.loginPolicySiteResourceID>
			<cfset local.loginPolicyRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.loginPolicySiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>
			
			<cfif local.loginPolicyRights.manageMemberSettings EQ 1>
				<cfif arguments.isMCStaffControlled EQ 1>
					<cfset local.hasRights = application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<cfelse>
					<cfset local.hasRights = true>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.hasRights>
	</cffunction>

	<cffunction name="isMCStaffControlledEditableForLoginPolicy" access="private" output="false" returntype="boolean">
		<cfargument name="qryLoginPolicy" type="query" required="true">
		
		<cfset var local = structNew()>
		<cfset local.isMCStaffControlledEditable = false>

		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<cfif arguments.qryLoginPolicy.recordCount>
				<cfset local.isMCStaffControlledEditable = (arguments.qryLoginPolicy.isMCStaffControlled EQ 1 AND arguments.qryLoginPolicy.isLowestRankedMCStaffControlledPolicyID EQ 1)
															OR
															(arguments.qryLoginPolicy.isMCStaffControlled EQ 0 AND arguments.qryLoginPolicy.isHighestRankedNonMCStaffControlledPolicyID EQ 1)>
			<cfelse>
				<cfset local.isMCStaffControlledEditable = true>
			</cfif>
		</cfif>

		<cfreturn local.isMCStaffControlledEditable>
	</cffunction>

	<cffunction name="editLoginPolicy" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>

		<cfset local.qryLoginPolicy = local.objWebsite.getLoginPolicy(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicyID=arguments.event.getValue('policyID',0))>
		<cfset local.loginPolicyID = val(local.qryLoginPolicy.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif local.hasRights>
			<cfset local.qryLoginLimitModes = local.objWebsite.getLoginLimitModes()>
			<cfset local.isMCStaffControlledEditable = isMCStaffControlledEditableForLoginPolicy(qryLoginPolicy=local.qryLoginPolicy)>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_editLoginPolicy.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput><b>You do not have rights to this area of Control Panel.</b></cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveLoginPolicy" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="policyName" type="string" required="yes">
		<cfargument name="isMCStaffControlled" type="boolean" required="yes">
		<cfargument name="loginLimitModeID" type="numeric" required="yes">
		<cfargument name="maxDaysBetweenVerifications" type="numeric" required="yes">
		<cfargument name="complianceDeadline" type="string" required="yes">
		<cfargument name="complianceDaysForNewAccounts" type="string" required="yes">
		<cfargument name="reqCountOfFactors" type="numeric" required="yes">
		<cfargument name="recCountOfFactors" type="numeric" required="yes">
		<cfargument name="noOfDaysForRecCountPrompts" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
	
		<cfset local.qryLoginPolicy = local.objWebsite.getLoginPolicy(siteID=arguments.mcproxy_siteID, loginPolicyID=arguments.loginPolicyID)>
		<cfset local.loginPolicyID = val(local.qryLoginPolicy.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.mcproxy_siteID, loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif NOT local.hasRights>
			<cfthrow message="invalid request">
		</cfif>

		<cfset local.isMCStaffControlledEditable = isMCStaffControlledEditableForLoginPolicy(qryLoginPolicy=local.qryLoginPolicy)>

		<cftry>
			<cfquery name="local.qrySaveLoginPolicy" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@loginPolicyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">,
					@policyName varchar(100) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.policyName#">,
					@isMCStaffControlled bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isMCStaffControlled#">,
					@loginLimitModeID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginLimitModeID#">,0),
					@maxDaysBetweenVerifications int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.maxDaysBetweenVerifications#">,
					@reqCountOfFactors int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.reqCountOfFactors#">,
					@recCountOfFactors int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.recCountOfFactors#">,
					@noOfDaysForRecCountPrompts int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.noOfDaysForRecCountPrompts#">,
					@complianceDeadline datetime, @complianceDaysForNewAccounts int;

				<cfif len(arguments.complianceDeadline)>
					SET @complianceDeadline = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.complianceDeadline# 23:59:59.997">
				</cfif>
				<cfif val(arguments.complianceDaysForNewAccounts)>
					SET @complianceDaysForNewAccounts = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.complianceDaysForNewAccounts#">
				</cfif>

				<cfif arguments.loginPolicyID>
					UPDATE dbo.siteLoginPolicies
					SET policyName = @policyName, 
						<cfif local.isMCStaffControlledEditable>
							isMCStaffControlled = @isMCStaffControlled, 
						</cfif>
						loginLimitModeID = @loginLimitModeID, 
						maxDaysBetweenVerifications = @maxDaysBetweenVerifications, 
						complianceDeadline = @complianceDeadline,
						complianceDaysForNewAccounts = @complianceDaysForNewAccounts,
						dateLastUpdated = GETDATE(),
						reqCountOfFactors = @reqCountOfFactors, 
						recCountOfFactors = @recCountOfFactors,
						noOfDaysForRecCountPrompts = @noOfDaysForRecCountPrompts
					WHERE loginPolicyID = @loginPolicyID
					AND siteID = @siteID;
				<cfelse>
					<cfif NOT local.isMCStaffControlledEditable>
						SET @isMCStaffControlled = 0;
					</cfif>

					EXEC dbo.site_createLoginPolicy @siteID=@siteID, @policyName=@policyName, @policyCode=NULL, @isMCStaffControlled=@isMCStaffControlled,
						@loginLimitModeID=@loginLimitModeID, @maxDaysBetweenVerifications=@maxDaysBetweenVerifications, @complianceDeadline=@complianceDeadline, 
						@complianceDaysForNewAccounts=@complianceDaysForNewAccounts, @reqCountOfFactors=@reqCountOfFactors, @recCountOfFactors=@recCountOfFactors, @noOfDaysForRecCountPrompts=@noOfDaysForRecCountPrompts, @loginPolicyID=@loginPolicyID OUTPUT;
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editLoginPolicyMethod" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>

		<cfset local.qryLoginPolicy = local.objWebsite.getLoginPolicy(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicyID=arguments.event.getValue('policyID',0))>
		<cfset local.loginPolicyID = val(local.qryLoginPolicy.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif local.hasRights>
			<cfset local.qryAllLoginPolicyMethods = local.objWebsite.getLoginPolicyMethods(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicyID=local.loginPolicyID)>
			<cfset local.qryLoginPolicyMethod = local.objWebsite.getLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), loginPolicyID=local.loginPolicyID, 
												policyMethodID=arguments.event.getValue('policyMethodID',0))>
			<cfset local.qryLoginVerificationMethods = local.objWebsite.getLoginVerificationMethods()>

			<cfset local.takenVerificationMethods = valueList(local.qryAllLoginPolicyMethods.verificationMethodID)>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="frm_editLoginPolicyMethod.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput><b>You do not have rights to this area of Control Panel.</b></cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveLoginPolicyMethod" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="policyMethodID" type="numeric" required="yes">
		<cfargument name="verificationMethodID" type="numeric" required="yes">
		<cfargument name="isRequired" type="boolean" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
	
		<cfset local.qryLoginPolicy = local.objWebsite.getLoginPolicy(siteID=arguments.mcproxy_siteID, loginPolicyID=arguments.loginPolicyID)>
		<cfset local.loginPolicyID = val(local.qryLoginPolicy.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.mcproxy_siteID, loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif NOT local.hasRights>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveLoginPolicyMethod" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@loginPolicyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">,
					@policyMethodID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.policyMethodID#">,0),
					@verificationMethodID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.verificationMethodID#">,
					@isRequired int = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRequired#">;

				<cfif arguments.policyMethodID>
					UPDATE dbo.siteLoginPolicyVerificationMethods
					SET isRequired = @isRequired
					WHERE policyMethodID = @policyMethodID
					AND loginPolicyID = @loginPolicyID
					AND siteID = @siteID;
				<cfelse>
					-- existing policy method
					SELECT @policyMethodID = policyMethodID
					FROM dbo.siteLoginPolicyVerificationMethods
					WHERE loginPolicyID = @loginPolicyID
					AND siteID = @siteID
					AND verificationMethodID = @verificationMethodID;

					IF @policyMethodID IS NULL
						INSERT INTO dbo.siteLoginPolicyVerificationMethods (loginPolicyID, siteID, verificationMethodID, isRequired)
						VALUES (@loginPolicyID, @siteID, @verificationMethodID, @isRequired);
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeLoginPolicy" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.qryLoginPolicy = CreateObject("component","website").getLoginPolicy(siteID=arguments.mcproxy_siteID, loginPolicyID=arguments.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.mcproxy_siteID, loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif NOT local.hasRights>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qryDeleteLoginPolicy" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.site_deleteLoginPolicy @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeLoginPolicyMethod" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="policyMethodID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.qryLoginPolicy = CreateObject("component","website").getLoginPolicy(siteID=arguments.mcproxy_siteID, loginPolicyID=arguments.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.mcproxy_siteID, loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif NOT local.hasRights>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qryDeleteLoginPolicyMethod" datasource="#application.dsn.membercentral.dsn#">
				DELETE FROM dbo.siteLoginPolicyVerificationMethods
				WHERE policyMethodID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.policyMethodID#">
				AND loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">
				AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveLoginPolicy" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="loginPolicyID" type="numeric" required="yes">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.qryLoginPolicy = CreateObject("component","website").getLoginPolicy(siteID=arguments.mcproxy_siteID, loginPolicyID=arguments.loginPolicyID)>
		
		<cfset local.hasRights = hasEditLoginPolicyRights(siteID=arguments.mcproxy_siteID, loginPolicySiteResourceID=val(local.qryLoginPolicy.siteResourceID), 
									isMCStaffControlled=val(local.qryLoginPolicy.isMCStaffControlled))>

		<cfif NOT local.hasRights>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qryMoveLoginPolicy" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.site_moveLoginPolicy @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@loginPolicyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.loginPolicyID#">,
					@dir = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showEstimatedMonthlyBillingAPI" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.firstOfThisMonth = "#month(now())#/1/#year(now())#">
		<cfset local.yesterday = dateAdd("d",-1,now())>
		<cfset local.endDate = local.yesterday GT local.firstOfThisMonth ? DateFormat(local.yesterday,"m/d/yyyy") : local.firstOfThisMonth>
		<cfset local.firstOfNextMonth = DateAdd('m',1,local.firstOfThisMonth)>

		<cfset local.qrySiteAPIAccessFees = CreateObject("component","website").getSiteAPIAccessFees(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'))>
		
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="api_generateMonthlyBillingBySite">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.firstOfThisMonth#">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.endDate#">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.firstOfNextMonth#">
			<cfprocresult name="local.qryBillingData" resultset="1">
			<cfprocresult name="local.qryAPITokenBilableRequests" resultset="2">
		</cfstoredproc>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showEstimatedMonthlyBillingAPI.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="SaveTsSemwebInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="priceGroupID" type="numeric" required="no">
		<cfargument name="defaultDepoGroupID" type="numeric" required="no">
		<cfargument name="overrideDoctypes" type="string" required="no">
		<cfargument name="display" type="numeric" required="no">
		<cfargument name="includeInTSEmailMarketing" type="boolean" required="no">
		<cfargument name="includeInSWEmailMarketing" type="boolean" required="no">
		<cfargument name="includeInOtherMessages" type="boolean" required="no">
		<cfargument name="searchJoinURL" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveTsContractInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				update dbo.depotla 
				set overrideDoctypes = NULLIF(<cfqueryparam value="#arguments.overrideDoctypes?:''#" cfsqltype="CF_SQL_VARCHAR">,''),
					<cfif val(arguments.pricegroupID) is 0>
						pricegroupID = <cfqueryparam value="" cfsqltype="CF_SQL_INTEGER" null="Yes">,
					<cfelse>
						pricegroupID = <cfqueryparam value="#arguments.pricegroupID#" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					<cfif val(arguments.defaultDepoGroupID)>
						defaultDepoGroupID = <cfqueryparam value="#arguments.defaultDepoGroupID#" cfsqltype="CF_SQL_INTEGER">,
					<cfelse>
						defaultDepoGroupID = <cfqueryparam null="true" cfsqltype="CF_SQL_INTEGER">,
					</cfif>
					display = <cfqueryparam value="#arguments.display#" cfsqltype="CF_SQL_INTEGER">,
					includeInTSEmailMarketing = <cfqueryparam value="#arguments.includeInTSEmailMarketing#" cfsqltype="CF_SQL_BIT">,
					includeInSWEmailMarketing = <cfqueryparam value="#arguments.includeInSWEmailMarketing#" cfsqltype="CF_SQL_BIT">,
					searchJoinURL = <cfqueryparam value="#trim(arguments.searchJoinURL)#" cfsqltype="CF_SQL_VARCHAR">,
					includeInOtherMessages = <cfqueryparam value="#arguments.includeInOtherMessages#" cfsqltype="CF_SQL_BIT">
				where state = <cfqueryparam value="#arguments.mcproxy_siteCode#" cfsqltype="CF_SQL_VARCHAR">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="SaveTsAssContactInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="contactname" type="string" required="no">
		<cfargument name="contactemail" type="string" required="no">
		<cfargument name="address1" type="string" required="no">
		<cfargument name="address2" type="string" required="no">
		<cfargument name="addresscity" type="string" required="no">
		<cfargument name="addressstate" type="string" required="no">
		<cfargument name="addresszip" type="string" required="no">
		<cfargument name="phone" type="string" required="no">
		<cfargument name="fax" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>	

		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveTsContractInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				update dbo.depotla 
				set contactname = <cfqueryparam value="#trim(arguments.contactname)#" cfsqltype="CF_SQL_VARCHAR">,
					contactemail = <cfqueryparam value="#trim(arguments.contactemail)#" cfsqltype="CF_SQL_VARCHAR">,
					address1 = <cfqueryparam value="#trim(arguments.address1)#" cfsqltype="CF_SQL_VARCHAR">,
					address2 = <cfqueryparam value="#trim(arguments.address2)#" cfsqltype="CF_SQL_VARCHAR">,
					addresscity = <cfqueryparam value="#trim(arguments.addresscity)#" cfsqltype="CF_SQL_VARCHAR">,
					addressstate = <cfqueryparam value="#trim(arguments.addressstate)#" cfsqltype="CF_SQL_VARCHAR">,
					addresszip = <cfqueryparam value="#trim(arguments.addresszip)#" cfsqltype="CF_SQL_VARCHAR">,
					phone = <cfqueryparam value="#trim(arguments.phone)#" cfsqltype="CF_SQL_VARCHAR">,
					fax = <cfqueryparam value="#trim(arguments.fax)#" cfsqltype="CF_SQL_VARCHAR">
				where state = <cfqueryparam value="#arguments.mcproxy_siteCode#" cfsqltype="CF_SQL_VARCHAR">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="SaveTsContractInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="swContract" type="string" required="no">
		<cfargument name="swStartDate" type="string" required="no">
		<cfargument name="swExpirationDate" type="string" required="no">
		<cfargument name="memberCentralContract" type="string" required="no">
		<cfargument name="memberCentralStartDate" type="string" required="no">
		<cfargument name="memberCentralExpirationDate" type="string" required="no">
		<cfargument name="webSiteContract" type="string" required="no">
		<cfargument name="webSiteStartDate" type="string" required="no">
		<cfargument name="webSiteExpirationDate" type="string" required="no">
		<cfargument name="MarketingContract" type="string" required="no">
		<cfargument name="MarketingStartDate" type="string" required="no">
		<cfargument name="MarketingExpirationDate" type="string" required="no">
		<cfargument name="ListsContract" type="string" required="no">
		<cfargument name="listServerStartDate" type="string" required="no">
		<cfargument name="listServerExpirationDate" type="string" required="no">
		<cfargument name="eclipsContract" type="string" required="no">
		<cfargument name="eclipsStartDate" type="string" required="no">
		<cfargument name="eclipsExpirationDate" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>	

		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveTsContractInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				update dbo.depotla 
				set swContract = <cfqueryparam value="#trim(arguments.swContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					swStartDate = 
						<cfif len(trim(arguments.swStartDate))>
							<cfqueryparam value="#trim(arguments.swStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					swExpirationDate = 
						<cfif len(trim(arguments.swExpirationDate))>
							<cfqueryparam value="#trim(arguments.swExpirationDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					memberCentralContract = <cfqueryparam value="#trim(arguments.memberCentralContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					memberCentralStartDate = 
						<cfif len(trim(arguments.memberCentralStartDate))>
							<cfqueryparam value="#trim(arguments.memberCentralStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					memberCentralExpirationDate = 
						<cfif len(trim(arguments.memberCentralExpirationDate))>
							<cfqueryparam value="#trim(arguments.memberCentralExpirationDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					webSiteContract = <cfqueryparam value="#trim(arguments.webSiteContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					webSiteStartDate = 
						<cfif len(trim(arguments.webSiteStartDate))>
							<cfqueryparam value="#trim(arguments.webSiteStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					webSiteExpirationDate = 
						<cfif len(trim(arguments.webSiteExpirationDate))>
							<cfqueryparam value="#trim(arguments.webSiteExpirationDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					MarketingContract = <cfqueryparam value="#trim(arguments.MarketingContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					marketingStartDate = 
						<cfif len(trim(arguments.MarketingStartDate))>
							<cfqueryparam value="#trim(arguments.MarketingStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					MarketingExpirationDate = 
						<cfif len(trim(arguments.MarketingExpirationDate))>
							<cfqueryparam value="#trim(arguments.MarketingExpirationDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					listServerContract = <cfqueryparam value="#trim(arguments.ListsContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					listServerStartDate = 
						<cfif len(trim(arguments.listServerStartDate))>
							<cfqueryparam value="#trim(arguments.listServerStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					listServerExpirationDate = 
						<cfif len(trim(arguments.listServerExpirationDate))>
							<cfqueryparam value="#trim(arguments.listServerExpirationDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					eclipsContract = <cfqueryparam value="#trim(arguments.eclipsContract)#" cfsqltype="CF_SQL_VARCHAR">, 
					eclipsStartDate = 
						<cfif len(trim(arguments.eclipsStartDate))>
							<cfqueryparam value="#trim(arguments.eclipsStartDate)#" cfsqltype="CF_SQL_DATE">,
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">,
						</cfif>
					eclipsExpirationDate = 
						<cfif len(trim(arguments.eclipsExpirationDate))>
							<cfqueryparam value="#trim(arguments.eclipsExpirationDate)#" cfsqltype="CF_SQL_DATE">
						<cfelse>
							<cfqueryparam null="true" cfsqltype="CF_SQL_DATE">
						</cfif>
				where state = <cfqueryparam value="#arguments.mcproxy_siteCode#" cfsqltype="CF_SQL_VARCHAR">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="SaveTsAccNotesInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="note" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>	
		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveTsAccNotesInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				insert into dbo.depotlaNotes (OrgCode, NoteTypeID, Note)
				values(
					<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">,
					1,
					<cfqueryparam value="#arguments.note# - by #session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname#" cfsqltype="CF_SQL_LONGVARCHAR">
				)
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveFastCaseRedirect" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="ssocompany" type="string" required="yes">
		<cfargument name="ssooffset" type="numeric" required="yes">
		<cfargument name="ssomultiplier" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>	
		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfset local.qryFastCaseRedirect = application.objSiteResource.getApplicationInstanceFromPageName(pageName='fastCaseRedirect', siteID=arguments.mcproxy_siteID)>

			<cfquery name="local.qrySaveFastCaseRedirectInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @settingsXML xml;
				SET @settingsXML = '<settings><setting name="ssocompany" value="'+<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ssoCompany#">+'" />' +
						'<setting name="ssooffset" value="'+<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ssoOffset#">+'" />' +
						'<setting name="ssomultiplier" value="'+<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.ssomultiplier#">+'" />' +
						'</settings>';

				UPDATE dbo.cms_applicationInstances
				SET settingsXML = @settingsXML
				WHERE applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFastCaseRedirect.applicationInstanceID#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeFastCaseRedirect" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.siteSRID = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteSiteResourceID>
		<cfset local.websiteAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>	
		<cfif NOT local.websiteAdminRights.EditSite>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfset local.qryFastCaseRedirect = application.objSiteResource.getApplicationInstanceFromPageName(pageName='fastCaseRedirect', siteID=arguments.mcproxy_siteID)>

			<cfquery name="local.qryDeleteFastCaseRedirect" datasource="#application.dsn.membercentral.dsn#">
				EXEC dbo.cms_deleteSiteResourceAndChildren 
					@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@siteResourceID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFastCaseRedirect.parentSiteResourceID#">;
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>