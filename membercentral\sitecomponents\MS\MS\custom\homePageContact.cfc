<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">
		<cfargument name="Event" type="any">

		<cfscript>
		/* ************************* */
		/* Custom Page Custom Fields */
		/* ************************* */
		local.arrCustomFields = [];
		local.tmpField = { name="EmailTo", type="STRING", desc="Email To", value="" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="MailSubject", type="STRING", desc="Mail Subject", value="Website - Contact Request" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="SuccessMessage", type="STRING", desc="Success Message", value="Thank you for getting in touch! We appreciate you contacting us. We will get back in touch with you soon!" }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		local.tmpField = { name="ErrorMessage", type="STRING", desc="Error Message", value="An attempt to send message failed. Please try again after some time." }; 
		arrayAppend(local.arrCustomFields, local.tmpField);
		variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);
		
		variables.captchaDetails = application.objCustomPageUtils.displayCaptcha(length="6",fonts="verdana,arial,times new roman,courier",height="75",width="263",formname="contactus");
		</cfscript>
		
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset session.mcstruct.LandingPage = ""/>
		</cfif>
		
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">


		<cfswitch expression="#arguments.event.getValue('fa','')#">
			<cfcase value="sendcontact">
				<cfset local.isSuccess = false>
                <cfif cgi.REQUEST_METHOD eq "POST" AND len(event.getValue('name','')) AND len(event.getValue('phoneNumber',''))  
					AND len(event.getValue('email','')) AND IsValid("email",trim(event.getValue('email',''))) AND len(event.getValue('message',''))
					AND (len(event.getValue('captcha','')) AND len(event.getValue('captcha_check','')) AND application.objCustomPageUtils.validateCaptcha(code=event.getValue('captcha',''), captcha=event.getValue('captcha_check','')).response EQ "success")>
                    <cfsavecontent variable="local.mailContent">
                        <cfoutput>
							<p><b>Name :</b> #event.getValue('name')#</p>
							<p><b>Email :</b> #event.getValue('email')#</p>
							<p><b>Phone :</b> #event.getValue('phoneNumber')#</p>
							<p><b>Message :</b> #event.getValue('message')#</p>										
						</cfoutput>
                    </cfsavecontent>
					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={name:trim(event.getValue('name','')), email:trim(event.getValue('email'))},
						emailto=[{email:variables.strPageFields.EmailTo }],
						emailreplyto=trim(event.getValue('email','')),
						emailsubject=variables.strPageFields.MailSubject,
						emailtitle=variables.strPageFields.MailSubject,
						emailhtmlcontent=local.mailContent,
						siteID=arguments.event.getValue('mc_siteinfo.siteID'),
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
						sendingSiteResourceID=this.siteResourceID
					)>
					 
                    <cfif local.responseStruct.success>						
						<cfset local.isSuccess = true>
					</cfif>
                </cfif>
               <cfset local.message = "">
				<cfif local.isSuccess>
					<cfset local.message = "&sucMsg=#variables.strPageFields.SuccessMessage#">
				<cfelse>
					<cfset local.message = "&errMsg=#variables.strPageFields.ErrorMessage#">
				</cfif>
				<cflocation url="/?pg=homePageContact&mode=direct#local.message#" addtoken="false">
			</cfcase>
			<cfdefaultcase>
				<cfsavecontent variable="local.pageHead">
					<cfoutput>
						<style>
								.btnSubmit {
									text-align: center;
								}
                                .contactFrmHolder .frmMob .row.row-flex>.span4 {
                                    padding: 0 !important;
                                    margin-left: 0px !important;
                                    max-width: 100%;
                                    width: 100%;;
                                    padding: 0 15px;
                                }
                                
                                .contactFrmHolder .row.row-flex>.span4 {
                                    padding: 0 !important;
                                    margin-left: 20px !important;
                                    flex: 0 0 33.33%;
                                    max-width: 31.8%;
                                    width: 31.8%;
                                    padding: 0 15px;
                                }

						</style>
						<script>
							function checkCaptchaAndValidate(){
								var thisForm = document.forms["contactFrm"];
								var errMsg = "";
								var status = false;
								var captcha_callback = function(captcha_response){
									if (captcha_response.response && captcha_response.response != 'success') {
										status = false;
									} else {
										status = true;
									}
								}
								if ($.trim($(thisForm['captcha']).val()).length == 0) {
									errMsg = errMsg + "Please enter the correct code shown in the graphic.<br>";
									$("##errContactMessage div").html(errMsg);
									$("##errContactMessage").removeClass('hidden');
									top.resizeContactIframe();
									top.scrollContact();
									return false;
								} else {
									#variables.captchaDetails.jsvalidationcode#
								}
								if(status){
									return validateContactForm();
								} else {
									errMsg = errMsg + "Please enter the correct code shown in the graphic.<br>";
									$("##errContactMessage div").html(errMsg);
									$("##errContactMessage").removeClass('hidden');
									top.resizeContactIframe();
									top.scrollContact();
									return false;
								}
								
							}
							function validateEmail(sEmail) {
								var filter = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
								if (filter.test(sEmail)) {
									return true;
								} else {
									return false;
								}
							}
							function validateContactForm(){							
								$("##messageHolder").remove();
								var errMsg = "";
								if($('##name').val().length == 0){
									errMsg = errMsg + "Please enter a valid Full Name.<br>";
								}else if($('##name').val().length > 30){
									errMsg = errMsg + "Full Name should not be more than 30 characters.<br>";
								}else if(!isNaN($('##name').val())){
									errMsg = errMsg + "Full Name is invalid.<br>";
								}
								
								if($('##phoneNumber').val().length == 0){
									errMsg = errMsg + "Please enter a valid Phone.<br>";
								}else if($('##phoneNumber').val().length > 30){
									errMsg = errMsg + "Phone should not be more than 30 characters.<br>";
								}
								if($('##email').val().length == 0){
									errMsg = errMsg + "Please enter a valid Email.<br>";
								} else if(!validateEmail($('##email').val())) {
									errMsg = errMsg + "Please enter a valid Email.<br>";
								}								
								if($('##message').val().trim().length == 0){
									errMsg = errMsg + "Please enter a valid Message.<br>";
								}else if($('##message').val().length > 300){
									errMsg = errMsg + "Message text should not be more than 300 characters.<br>";
								}
								
								if(errMsg.length > 0) {
									$("##errContactMessage div").html(errMsg);
									$("##errContactMessage").removeClass('hidden');
									top.resizeContactIframe();
									top.scrollContact();
									return false;
								} else {
									$("##errContactMessage").remove();
									$("##sendBtn").prop('disabled', false).html('Sending..');
									top.resizeContactIframe();
									return true;
								}
							}
							$(document).ready(function () {
								$(".messageSuccessHolder").delay(5000).fadeOut('slow', function() {top.resizeContactIframe();});								
								showCaptcha();
								top.resizeContactIframe();
								<cfif len(event.getValue('errMsg','')) OR len(event.getValue('sucMsg',''))>
									top.scrollContact();
								</cfif>
								// Define the div selector where the image is expected to be present
								const containerDiv = $('##captcha-wrapper'); // Replace 'yourDivId' with the actual ID or class of your div

								// Set a timer to check for the image element
								const timer = setInterval(function() {
									const img = containerDiv.find('img[src^="data:image/png;base64,"]');

									if (img.length > 0) { // Check if the image element exists
										// Apply the load event
										img.on('load', function() {
											top.resizeContactIframe();
										}).each(function() {
											if (this.complete) $(this).trigger('load');
										});
										
										// Clear the interval since the image element is found
										clearInterval(timer);
									}
								}, 500); // Check every 500 milliseconds (adjust as needed)
							});
							
							window.onload = function() {
								top.resizeContactIframe();								
							}
							$(window).on('resize', function(){
								top.resizeContactIframe();
                                if(parent.window.innerWidth < 980){
                                    $('##contactFrm').addClass('frmMob');
                                }else{
                                    $('##contactFrm').removeClass('frmMob');
                                }
							});
                            if(parent.window.innerWidth < 980){
                                $('##contactFrm').addClass('frmMob');
                            }else{
                                $('##contactFrm').removeClass('frmMob');
                            }
						</script>   

					</cfoutput>
				</cfsavecontent>						

				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					
					<cfhtmlhead text="#local.pageHead#">
					<span class="formwrapper contactFrmHolder" style="padding:0px">					
                        <form autocomplete="off" action="/?pg=homePageContact&mode=direct" method="POST" id="contactFrm" class="form-design-1" name="contactFrm" onsubmit="return checkCaptchaAndValidate();">
                            <input type="hidden" name="fa" value="sendcontact">
                             <div class="row row-flex">
                                 <div class="span12 text-center">
                                    <cfif len(event.getValue('sucMsg',''))>
                                        <div class="alert alert-success messageSuccessHolder" id="messageHolder"><div>#event.getValue('sucMsg','')#</div></div>						
                                    <cfelseif len(event.getValue('errMsg',''))>
                                        <div class="alert alert-error" id="messageHolder"><div>#event.getValue('errMsg','')#</div></div>
                                    </cfif>
                                    <div class="alert alert-error hidden" id="errContactMessage"><div></div></div>
                                    <span class="hide contactPopFooterWrapper">#variables.strPageFields.ErrorMessage#</span>
                                 </div>
                            </div>
                            <div class="row row-flex">
                                <div class="span4">
                                <input type="text" name="name" id="name" placeholder="Name" required autocomplete="false">
                                </div>
                                <div class="span4">
                                <input type="email" name="email" id="email" placeholder="E-mail" required autocomplete="false">
                                </div>
                                <div class="span4">
                                <input type="text" name="phoneNumber" id="phoneNumber" placeholder="Phone" required autocomplete="false">
                                </div>
                                <div class="span12">
                                <textarea name="message" id="message" placeholder="Message" rows="5" required></textarea>
                                </div>
                                <div class="span12">
                                    #variables.captchaDetails.htmlContent#
                                </div>
                                <div class="span12 text-center">
                                <button type="button" class="MAJButton-2" onclick="$('##contactFrm').submit();">SUBMIT</button>
                                </div>
                            </div>                       
                        </form>
					</span>
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>

</cfcomponent>